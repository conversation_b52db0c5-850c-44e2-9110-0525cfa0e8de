#pragma once

#include "CoreMinimal.h"


#include "RodinSetting.generated.h"

UCLASS(config = Plugins, defaultconfig, DisplayName = "Rodin")
class RODIN_API URodinSetting : public UDeveloperSettings
{
	GENERATED_BODY()
public:
	static const URodinSetting* Get() { return GetDefault<URodinSetting>(); }

public:
	UPROPERTY(config, EditAnywhere, Category = "Rodin")
	FString BaseURL = TEXT("https://hyperhuman.deemos.com/api/v2");

	UPROPERTY(config, EditAnywhere, Category = "Rodin")
	FString APIKey;

	UPROPERTY(config, EditAnywhere, Category = "Rodin")
	FString ModelType;

	UPROPERTY(config, EditAnywhere, Category = "Rodin")
	FString MatType;

	UPROPERTY(config, EditAnywhere, Category = "Rodin")
	FString QualityType;

	UPROPERTY(config, EditAnywhere, Category = "Rodin")
	FString TierType;

	UPROPERTY(config, EditAnywhere, Category = "Rodin", meta = (AllowedClasses = "/Script/Engine.MaterialInterface"))
	FSoftObjectPath DefaultMaterial;
};