#pragma once

#include "RenderEntityBase.h"

#include "SurfaceRenderEntity.generated.h"

class UDynamicMeshComponent;
class UHBSSurfaceSceneEntity;
class UHBSFlooringSceneEntity;

UCLASS()
class HOMESTEADBUILDINGSYSTEM_API AHBSSurfaceRenderEntity : public AHBSRenderEntityBase
{
	GENERATED_BODY()

public:
	AHBSSurfaceRenderEntity(const FObjectInitializer& ObjectInitializer);

	void UpdateSurfaceMeshInfo(UHBSSurfaceSceneEntity* InSurfaceEntity);

protected:
	UPROPERTY(EditAnywhere, Category = "Surface Render Entity")
	UDynamicMeshComponent* SurfaceMeshComponent = nullptr;
};