#pragma once

#include "CoreMinimal.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "Components/PrimitiveComponent.h"

#include "RenderEntityFunctionLibrary.generated.h"

UCLASS()
class URenderEntityFunctionLibrary : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()

public:
	static void SetupPrimitiveComponentCollision(UPrimitiveComponent* Component)
	{
		Component->SetCollisionProfileName(UCollisionProfile::BlockAllDynamic_ProfileName);
		Component->SetCollisionResponseToChannel(ECollisionChannel::ECC_Visibility, ECollisionResponse::ECR_Overlap);
	}
};