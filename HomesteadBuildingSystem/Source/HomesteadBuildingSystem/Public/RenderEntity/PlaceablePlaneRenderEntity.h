#pragma once
#include "HBSConfig.h"
#include "CoreMinimal.h"
#include "RenderEntityBase.h"
#include "Components/DynamicMeshComponent.h"

#include "PlaceablePlaneRenderEntity.generated.h"

class UHBSPlaceablePlaneSE;

UCLASS()
class HOMESTEADBUILDINGSYSTEM_API AHBSPlaceablePlaneRenderEntity : public AHBSRenderEntityBase
{
	GENERATED_BODY()

public:
	AHBSPlaceablePlaneRenderEntity(const FObjectInitializer& ObjectInitializer)
	{
		PrimaryActorTick.bCanEverTick = false;
		
		RootComponent = PlaceablePlaneMeshComponent = CreateDefaultSubobject<UDynamicMeshComponent>(TEXT("PlaceablePlaneMeshComponent"));
		PlaceablePlaneMeshComponent->SetCollisionEnabled(ECollisionEnabled::NoCollision);
	}

	void UpdatePlaceablePlaneInfo(UHBSPlaceablePlaneSE* InPlaceablePlane);

protected:
	UPROPERTY(EditAnywhere, Category = "Placeable Plane Render Entity")
	class UDynamicMeshComponent* PlaceablePlaneMeshComponent = nullptr;

	UPROPERTY(EditAnywhere, Category = "Placeable Plane Render Entity")
	UHBSPlaceablePlaneSE* PlaceablePlaneSE = nullptr;
};