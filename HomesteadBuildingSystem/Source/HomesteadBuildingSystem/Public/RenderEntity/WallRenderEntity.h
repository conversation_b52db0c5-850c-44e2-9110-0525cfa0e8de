#pragma once

#include "RenderEntityBase.h"
#include "Components/DynamicMeshComponent.h"
#include "GeometryScript/MeshBooleanFunctions.h"
#include "RenderEntityProcessor/WallRenderEntityProcessor.h"
#include "SceneEntity/WallSceneEntity.h"

#include "WallRenderEntity.generated.h"

class UUGCProfile;
class UUGCComponent;
class UHBSWallSceneEntity;
class UHBSWindowSceneEntity;

UCLASS()
class HOMESTEADBUILDINGSYSTEM_API AHBSWallRenderEntity : public AHBSRenderEntityBase
{
	GENERATED_BODY()

public:
	AHBSWallRenderEntity(const FObjectInitializer& ObjectInitializer);

	void UpdateWallInfo(const FHBSProcessedWallVertices& InWallVertices, UHBSWallSceneEntity* InWallSceneEntity, const TArray<UHBSWindowSceneEntity*>& InWindowSceneEntity);

	void SetWallVisibility(EHBSWallVisibility InVisibility);

	const FHBSProcessedWallVertices& GetWallVertices() const { return ProcessedWallVertices; }

private:
	void GenerateDynamicMesh();
	void InitUGC(UHBSWallSceneEntity* InWallSceneEntity);

protected:
	UPROPERTY(EditAnywhere, Category = "Wall Properties")
	UDynamicMeshComponent* WallMesh = nullptr;

	UPROPERTY(EditAnywhere, Category = "Wall Properties")
	FHBSProcessedWallVertices ProcessedWallVertices;

	UPROPERTY(EditAnywhere, Category = "Wall Properties")
	UHBSWallSceneEntity* WallSceneEntity;

	UPROPERTY(EditAnywhere, Category = "Wall Properties")
	TArray<UHBSWindowSceneEntity*> WindowSceneEntityList;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly)
	EHBSWallVisibility CurrentVisibility = EHBSWallVisibility::Visibile;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly)
	UDynamicMesh* TempDynamicMesh = nullptr;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly)
	UUGCComponent* UGCComponent = nullptr;

	UPROPERTY(VisibleAnywhere)
	UUGCProfile* UGCProfile = nullptr;
};
