#pragma once

#include "CoreMinimal.h"
#include "RenderEntityBase.generated.h"

class UHBSSceneEntityBase;

UCLASS(Abstract, Blueprintable, BlueprintType)
class HOMESTEADBUILDINGSYSTEM_API AHBSRenderEntityBase : public AActor
{
	GENERATED_BODY()

public:
	TArray<UHBSSceneEntityBase*> GetRelativeSceneEntityList() const { return RelativeSceneEntityList; }

	void SetStencilValue(int32 StencilValue)
	{
		TArray<UPrimitiveComponent*> PrimitiveComponentList;
		GetComponents(PrimitiveComponentList);
		for (UPrimitiveComponent* PrimitiveComponent : PrimitiveComponentList)
		{
			if (StencilValue != PrimitiveComponent->CustomDepthStencilValue)
			{
				PrimitiveComponent->bRenderCustomDepth = StencilValue != 0;
				PrimitiveComponent->CustomDepthStencilWriteMask = ERendererStencilMask::ERSM_Default;
				PrimitiveComponent->CustomDepthStencilValue = StencilValue;
				PrimitiveComponent->MarkRenderStateDirty();
			}
		}
	}

	void SetRenderEntityVisible(bool InVisibility)
	{
		SetActorHiddenInGame(!InVisibility);
		SetActorEnableCollision(InVisibility);
	}

protected:
	UPROPERTY(VisibleAnywhere, Category = "RenderEntity")
	TArray<UHBSSceneEntityBase*> RelativeSceneEntityList;
};
