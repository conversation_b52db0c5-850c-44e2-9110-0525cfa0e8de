#pragma once

#include "CoreMinimal.h"
#include "RenderEntityBase.h"
#include "RodinEngineSubsystem.h"
#include "HBSConfig.h"
#include "Library/RenderEntityFunctionLibrary.h"
#include "AIMeshRenderEntity.generated.h"

class UHBSSceneEntityBase;

UCLASS()
class HOMESTEADBUILDINGSYSTEM_API AHBSAIMeshRenderEntity : public AHBSRenderEntityBase
{
	GENERATED_BODY()

public:
	AHBSAIMeshRenderEntity()
	{
		PrimaryActorTick.bCanEverTick = false;
		RootComponent = CreateDefaultSubobject<USceneComponent>(TEXT("RootComponent"));
	}

	bool GetIsPreview() const { return IsPreview; }

	void UpdateAIMeshInfo(FGuid InAIMeshTaskGuid, FTransform InTransform, UHBSSceneEntityBase* InSceneEntity, bool InIsPreview)
	{
		SetActorTransform(InTransform);
		IsPreview = InIsPreview;
		RelativeSceneEntityList = TArray<UHBSSceneEntityBase*>{ InSceneEntity };

		if (AIMeshTaskGuid != InAIMeshTaskGuid)
		{
			AIMeshTaskGuid = InAIMeshTaskGuid;
			GetGameInstance()->GetSubsystem<URodinEngineSubsystem>()->AttachProceduralMeshForActor(this, AIMeshTaskGuid);
		}

		UMaterialInterface* Material = GetGameInstance()->GetSubsystem<URodinEngineSubsystem>()->GetMaterialInstanceByGuid(AIMeshTaskGuid);
		if (IsPreview)
			Material = UHBSConfig::Get()->GetPreviewMaterial();

		TArray<UProceduralMeshComponent*> ProceduralMeshComponentList;
		GetComponents(UProceduralMeshComponent::StaticClass(), ProceduralMeshComponentList);

		for (UProceduralMeshComponent* ProceduralMesh : ProceduralMeshComponentList)
		{
			URenderEntityFunctionLibrary::SetupPrimitiveComponentCollision(ProceduralMesh);
			for (int32 i = 0; i != ProceduralMesh->GetNumMaterials(); ++i)
				ProceduralMesh->SetMaterial(i, Material);
		}
	}

protected:
	UPROPERTY(EditAnywhere, Category = "AIMesh Properties")
	bool IsPreview = false;

	UPROPERTY(EditAnywhere, Category = "AIMesh Properties")
	FGuid AIMeshTaskGuid;
};