#pragma once

#include "RenderEntityBase.h"
#include "HBSConfig.h"
#include "Library/RenderEntityFunctionLibrary.h"

#include "StaticMeshRenderEntity.generated.h"

class UHBSSceneEntityBase;

UCLASS()
class HOMESTEADBUILDINGSYSTEM_API AHBSStaticMeshRenderEntity : public AHBSRenderEntityBase
{
	GENERATED_BODY()

public:
	AHBSStaticMeshRenderEntity(const FObjectInitializer& ObjectInitializer)
	{
		PrimaryActorTick.bCanEverTick = false;

		RootComponent = StaticMeshComponent = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("StaticMesh"));
		URenderEntityFunctionLibrary::SetupPrimitiveComponentCollision(StaticMeshComponent);
	}

	bool GetIsPreview() const { return IsPreview; }

	void UpdateStaticMeshInfo(UStaticMesh* InStaticMesh, FTransform InTransform, UHBSSceneEntityBase* InSceneEntity, bool InIsPreview, bool IsErrorPreview = false)
	{
		StaticMeshComponent->SetStaticMesh(InStaticMesh);
		SetActorTransform(InTransform);
		IsPreview = InIsPreview;
		RelativeSceneEntityList = TArray<UHBSSceneEntityBase*>{ InSceneEntity };

		if (IsPreview)
		{
			UMaterialInterface* PreviewMaterial = IsErrorPreview ? UHBSConfig::Get()->GetPreviewErrorMaterial() : UHBSConfig::Get()->GetPreviewMaterial();
			for (int32 i = 0; i != StaticMeshComponent->GetNumMaterials(); ++i)
			{
				StaticMeshComponent->SetMaterial(i, PreviewMaterial);
			}
		}
		else
		{
			StaticMeshComponent->EmptyOverrideMaterials();
		}
	}

protected:
	UPROPERTY(EditAnywhere, Category = "Static Mesh Properties")
	UStaticMeshComponent* StaticMeshComponent = nullptr;

	UPROPERTY(EditAnywhere, Category = "Static Mesh Properties")
	bool IsPreview = false;
};