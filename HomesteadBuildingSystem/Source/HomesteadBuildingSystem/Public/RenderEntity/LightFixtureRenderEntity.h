#pragma once

#include "CoreMinimal.h"
#include "RenderEntityBase.h"
#include "HBSConfig.h"
#include "SceneEntity/LightFixtureSceneEntity.h"
#include "LightFixtureRenderEntity.generated.h"

UCLASS()
class HOMESTEADBUILDINGSYSTEM_API AHBSLightFixtureRenderEntity : public AHBSRenderEntityBase
{
	GENERATED_BODY()

public:
	void SetRelatedSceneEntity(UHBSLightFixtureSceneEntity* InRelatedSceneEntity)
	{
		RelativeSceneEntityList = TArray<UHBSSceneEntityBase*>{ InRelatedSceneEntity };
	}

	void SetLightState(bool InIsLightOn)
	{
		if (InIsLightOn != IsLightOn)
		{
			IsLightOn = InIsLightOn;
			OnLightSwitchStateChanged(IsLightOn);
		}
	}

	UFUNCTION(BlueprintImplementableEvent)
	void OnLightSwitchStateChanged(bool InIsNowOn);

protected:
	UPROPERTY(VisibleAnywhere)
	bool IsLightOn = true;
};
