#pragma once

#include "CoreMinimal.h"
#include "SceneEntity/SceneEntityBase.h"
#include "SceneEntity/WallSceneEntity.h"
#include "WallFirstPlacementOperator.generated.h"

struct FAdjacentEdge
{
	int32 EdgeIndex;
	float AngleDot;
};

struct FDirectedEdge
{
	FVector2D Start;
	FVector2D End;
	FVector2D Direction;

	TArray<FAdjacentEdge> AdjacentEdgeList;
};

UCLASS(Blueprintable, BlueprintType)
class HOMESTEADBUILDINGSYSTEM_API UHBSWallFirstPlacementOperator : public UHBSSceneEntityOperatorBase
{
	GENERATED_BODY()

public:
	void Initialize(UHBSSceneEntityBase* InParentEntity) override;

	void Tick(float DeltaTime) override;

	UFUNCTION(BlueprintCallable, Category = "HBS|WallBuildOperator")
	void BeginOperator();

	UFUNCTION(BlueprintCallable, Category = "HBS|WallBuildOperator")
	void BeginOperatorWithStart(FVector2D InStartPos, int32 InWallStartHeight);

	UFUNCTION(BlueprintCallable, Category = "HBS|WallBuildOperator")
	void EndOperatorAndDestroy(FVector2D& OutWallEnd, int32& OutWallStartHeight, bool& OutIsGenerateCloseSpace);

	UFUNCTION(BlueprintCallable, Category = "HBS|WallBuildOperator")
	void EnableWallAngleGrid(bool InEnable) { bEnableWallAngleGrid = InEnable; }

	UFUNCTION(BlueprintCallable, Category = "HBS|WallBuildOperator")
	bool IsWallAngleGridEnabled() const { return bEnableWallAngleGrid; }

	void CancelOperator() override;

protected:
	FVector2D GenerateSnapedPos(int32* OutHeight = nullptr);
	bool IsGenerateCloseSpace() const;
	bool IsWallIntersected(UHBSWallSceneEntity* WallA, UHBSWallSceneEntity* WallB, FVector2D& OutIntersection) const;

	bool PerformBFS(int CurrentIndex, int StartIndex, const TArray<FDirectedEdge>& EdgeList, const TArray<int32>& EndEdges, TArray<bool>& Visited, bool HasNonParallel) const;

	UPROPERTY()
	FVector2D WallStart;

	UPROPERTY()
	FVector2D WallEnd;

	UPROPERTY()
	int32 WallStartHeight = 0;

	UPROPERTY()
	bool bEnableWallAngleGrid = true;

	UPROPERTY()
	bool IsBeginOperator = false;

	UPROPERTY()
	UHBSWallSceneEntity* PreviewWallEntity = nullptr;
};
