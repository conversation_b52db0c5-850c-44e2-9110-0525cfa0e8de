#pragma once

#include "CoreMinimal.h"
#include "SceneEntity/SceneEntityBase.h"
#include "SceneEntity/WallSceneEntity.h"
#include "WallMoveOperator.generated.h"

UCLASS(Blueprintable, BlueprintType)
class HOMESTEADBUILDINGSYSTEM_API UHBSWallMoveOperator : public UHBSSceneEntityOperatorBase
{
	GENERATED_BODY()

public:
	void Initialize(UHBSSceneEntityBase* InTargetEntity) override;

	void Tick(float DeltaTime) override;

	UFUNCTION(BlueprintCallable, Category = "HBS|WallMoveOP")
	void PlaceWallAndDestroy();

	void CancelOperator() override;

	UFUNCTION(BlueprintCallable, Category = "HBS|WallMoveOP")
	void AddYaw(float InYawDelta);

	UFUNCTION(BlueprintCallable, Category = "HBS|WallMoveOP")
	void SetYaw(float InYaw);

	UFUNCTION(BlueprintCallable, Category = "HBS|WallMoveOP")
	float GetYaw() const;

protected:
	void UpdateWallEntity(UHBSWallSceneEntity* InWall);

protected:
	UPROPERTY()
	UHBSWallSceneEntity* PreviewWallEntity = nullptr;

	UPROPERTY()
	FVector2D MouseOffset = FVector2D(0, 0);

	UPROPERTY()
	float Yaw = 0.f;

	UPROPERTY()
	float WallLength = 0.f;
};
