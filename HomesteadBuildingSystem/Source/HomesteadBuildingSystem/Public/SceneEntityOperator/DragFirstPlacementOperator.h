#pragma once

#include "CoreMinimal.h"
#include "SceneEntity/SceneEntityBase.h"
#include "SceneEntity/StaticMeshSceneEntity.h"
#include "HBSWorldSystem.h"
#include "TransformOperator/MeshMoveOperator.h"

#include "DragFirstPlacementOperator.generated.h"

UCLASS(Blueprintable, BlueprintType)
class HOMESTEADBUILDINGSYSTEM_API UHBSDragFirstPlacementOperator : public UHBSMeshMoveOperator
{
	GENERATED_BODY()

public:
	void Initialize(UHBSSceneEntityBase* InSceneEntity) override
	{
		Super::Initialize(InSceneEntity);
		MouseOffset = FVector2D();
	}

	void PlaceEntityAndDestroy() override
	{
		Super::PlaceEntityAndDestroy();

		GetTargetEntity()->MarkPlacementDone();
		UHBSWorldSystem::Get(this)->MarkSceneEntityChanged(GetTargetEntity());
	}

	void CancelOperator() override
	{
		Super::CancelOperator();

		UHBSWorldSystem::Get(this)->RemoveSceneEntity(TargetEntity);
		DestroySelf();
	}
};
