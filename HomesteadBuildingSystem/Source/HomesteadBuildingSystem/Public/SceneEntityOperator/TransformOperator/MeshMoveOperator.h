#pragma once

#include "CoreMinimal.h"
#include "SceneEntity/SceneEntityBase.h"
#include "SceneEntity/PlaceablePlaneSceneEntity.h"
#include "TransformOperatorBase.h"

#include "MeshMoveOperator.generated.h"

class UHBSWallSceneEntity;
class UHBSFloorSceneEntity;
class UHBSTransformSceneEntity;

UENUM(BlueprintType)
enum class EHBSMeshMoveFlag : uint8
{
	None = 0,
	MoveX = 0x01,
	MoveY = 0x02,
	Rotate = 0x04,
	All = MoveX | MoveY | Rotate
};
ENUM_CLASS_FLAGS(EHBSMeshMoveFlag);

UCLASS(Blueprintable, BlueprintType)
class HOMESTEADBUILDINGSYSTEM_API UHBSMeshMoveOperator : public UHBSTransformOperatorBase
{
	GENERATED_BODY()

public:
	UFUNCTION(BlueprintCallable, Category = "HBS|TransformOperator")
	void SetMoveOperatorFlag(EHBSMeshMoveFlag InMoveFlag) { MoveFlag = InMoveFlag; }

	virtual void Initialize(UHBSSceneEntityBase* InSceneEntity) override;

	virtual void Tick(float DeltaTime) override;

	UFUNCTION(BlueprintCallable, Category = "HBS|MeshMoveOperator")
	virtual void PlaceEntityAndDestroy();

	UFUNCTION(BlueprintCallable, Category = "HBS|MeshMoveOperator")
	void AddYaw(float InYawDelta);

	UFUNCTION(BlueprintCallable, Category = "HBS|MeshMoveOperator")
	void SetYaw(float InYaw);

	UFUNCTION(BlueprintCallable, Category = "HBS|MeshMoveOperator")
	float GetYaw() const;

	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "HBS|WindowMoveOperator")
	FTransform GetCurrentTransform() { return TargetTransform; }

	virtual void CancelOperator() override;

protected:
	FTransform AutoAlignedTransform(const FTransform& InTransform);
	UHBSWallSceneEntity* FindNearestWall(const FTransform& InTransform, float MaxSnapDistance, FVector2D& OutWallNormal);

protected:
	UPROPERTY()
	EHBSMeshMoveFlag MoveFlag = EHBSMeshMoveFlag::All;

	UPROPERTY()
	TArray<EHBSPlaceablePlaneType> PlaceablePlaneTypes = { EHBSPlaceablePlaneType::Floor };

	UPROPERTY()
	UHBSFloorSceneEntity* CurrentLevelEntity = nullptr;

	UPROPERTY()
	FTransform TargetTransform;

	UPROPERTY()
	FVector2D MouseOffset = FVector2D(0, 0);
};
