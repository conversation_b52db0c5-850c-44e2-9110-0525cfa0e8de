#pragma once

#include "CoreMinimal.h"
#include "SceneEntity/SceneEntityBase.h"

#include "TransformOperatorBase.generated.h"

class UHBSTransformSceneEntity;

UCLASS(Abstract)
class HOMESTEADBUILDINGSYSTEM_API UHBSTransformOperatorBase : public UHBSSceneEntityOperatorBase
{
	GENERATED_BODY()

public:
	void Initialize(UHBSSceneEntityBase* InTargetEntity) override;

protected:
	UPROPERTY()
	UHBSTransformSceneEntity* PreviewEntity = nullptr;
};
