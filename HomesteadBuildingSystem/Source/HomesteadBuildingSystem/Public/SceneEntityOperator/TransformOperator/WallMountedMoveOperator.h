#pragma once

#include "CoreMinimal.h"
#include "SceneEntity/SceneEntityBase.h"
#include "SceneEntity/PlaceablePlaneSceneEntity.h"
#include "TransformOperatorBase.h"

#include "WallMountedMoveOperator.generated.h"

UCLASS(Blueprintable, BlueprintType)
class HOMESTEADBUILDINGSYSTEM_API UHBSWallMountedMoveOperator : public UHBSTransformOperatorBase
{
	GENERATED_BODY()

public:
	void Initialize(UHBSSceneEntityBase* InTargetEntity) override;

	void Tick(float DeltaTime) override;

	UFUNCTION(BlueprintCallable, Category = "HBS|Wall Mounted Move Operator")
	virtual bool TryPlaceEntityAndDestroy();

	void CancelOperator() override;

	UFUNCTION(BlueprintCallable, Category = "HBS|Wall Mounted Move Operator")
	void AddRoll(float InDeltaRoll);

	UFUNCTION(BlueprintCallable, Category = "HBS|Wall Mounted Move Operator")
	void SetRoll(float InRoll);

	UFUNCTION(BlueprintCallable, Category = "HBS|Wall Mounted Move Operator")
	float GetRoll() const { return CurrentRoll; }

	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "HBS|Wall Mounted Move Operator")
	FTransform GetCurrentTransform() { return TargetTransform; }

protected:
	bool TryUpdateSceneEntity(UHBSTransformSceneEntity* InEntity);

protected:
	UPROPERTY()
	float CurrentRoll = 0.f;

	UPROPERTY()
	FTransform TargetTransform;

	UPROPERTY()
	FVector2D MouseOffset = FVector2D(0, 0);
};