#pragma once

#include "CoreMinimal.h"
#include "WallMountedMoveOperator.h"
#include "HBSWorldSystem.h"

#include "WallMountedMoveFirstPlacementOperator.generated.h"

UCLASS(Blueprintable, BlueprintType)
class HOMESTEADBUILDINGSYSTEM_API UHBSWallMountedMoveFirstPlacementOperator : public UHBSWallMountedMoveOperator
{
	GENERATED_BODY()

public:
	void Initialize(UHBSSceneEntityBase* InTargetEntity) override
	{
		Super::Initialize(InTargetEntity);
		MouseOffset = FVector2D();
	}

	bool TryPlaceEntityAndDestroy() override
	{
		if (Super::TryPlaceEntityAndDestroy())
		{
			GetTargetEntity()->MarkPlacementDone();
			UHBSWorldSystem::Get(this)->MarkSceneEntityChanged(GetTargetEntity());
			return true;
		}
		return false;
	}

	void CancelOperator() override
	{
		Super::CancelOperator();

		UHBSWorldSystem::Get(this)->RemoveSceneEntity(GetTargetEntity());
		DestroySelf();
	}
};