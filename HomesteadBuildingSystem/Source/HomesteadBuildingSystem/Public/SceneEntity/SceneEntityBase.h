#pragma once

#include "CoreMinimal.h"
#include "Tickable.h"
#include "Stats/Stats2.h"
#include "SceneEntityBase.generated.h"

class UHBSSceneEntityBase;
class UHBSSceneEntityOperatorBase;

USTRUCT(Blueprintable, BlueprintType)
struct FHBSSceneEntityOperatorMetaData
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "HBS|SceneEntity")
	TSubclassOf<UHBSSceneEntityOperatorBase> OperatorClass;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "HBS|SceneEntity")
	int32 Identify = 0;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "HBS|SceneEntity")
	FVector Position;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "HBS|SceneEntity")
	FVector Direction;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "HBS|SceneEntity")
	FString Description;
};

UCLASS(Blueprintable, BlueprintType)
class UHBSSceneEntityOperatorBase : public UObject
{
	GENERATED_BODY()

public:
	virtual void Initialize(UHBSSceneEntityBase* InTargetEntity);

	virtual bool IsTickable() const { return IsValid(); }
	virtual void Tick(float DeltaTime) {}

	UHBSSceneEntityBase* GetTargetEntity() const { return TargetEntity; }

	template<typename SceneEntityType = UHBSSceneEntityBase>
	SceneEntityType* GetTypedTargetEntity() const { return Cast<SceneEntityType>(TargetEntity); }

	UFUNCTION(BlueprintCallable, Category = "HBS|WallBuildOperator")
	virtual void CancelOperator() {}

protected:
	bool IsValid() const { return TargetEntity && bIsValid; }

	void DestroySelf();

	//TStatId GetStatId() const override { RETURN_QUICK_DECLARE_CYCLE_STAT(UHBSSceneEntityOperatorBase, STATGROUP_Tickables); }

protected:
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "HBS|Operator")
	UHBSSceneEntityBase* TargetEntity = nullptr;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "HBS|Operator")
	bool bIsValid = true;
};

DECLARE_DYNAMIC_DELEGATE_ThreeParams(FHBSPreOperatorInitCallback, UHBSSceneEntityOperatorBase*, NewOperator, TSubclassOf<UHBSSceneEntityOperatorBase>, OperatorClass, int32, Identify);

UCLASS(Abstract, Blueprintable, BlueprintType)
class HOMESTEADBUILDINGSYSTEM_API UHBSSceneEntityBase : public UObject
{
	GENERATED_BODY()

public:
#pragma region 初始化
	UFUNCTION(BlueprintCallable, Category = "HBS|SceneEntity")
	UHBSSceneEntityOperatorBase* InitializeAndGenerateFirstPlacementOperator();

	void InitializeImmediate();
#pragma endregion

#pragma region 杂项
	virtual bool IsTickable() const { return false; }
	virtual void Tick(float DeltaTime) {}

	virtual void OnPreDestroy() {}
	virtual void Destroy();

	UFUNCTION(BlueprintCallable, Category = "HBS|SceneEntity")
	virtual FBox CalcEntityBound() const { return FBox(EForceInit::ForceInit); }
#pragma endregion

#pragma region 信息标识
	void MarkPlacementDone() { IsPlacementDone = true; }
	bool GetIsPlacementDone() const { return IsPlacementDone; }

	void SetIsPreview(bool InIsPreview) { IsPreview = InIsPreview; }
	bool GetIsPreview() const { return IsPreview; }
#pragma endregion

#pragma region 操作对象
	UFUNCTION(BlueprintCallable, Category = "HBS|SceneEntity")
	TArray<FHBSSceneEntityOperatorMetaData> GetOperatorsMetaDataList() const { return OperatorsMetaDataList; }

	UFUNCTION(BlueprintCallable, Category = "HBS|SceneEntity")
	virtual bool CanCreateOperator(TSubclassOf<UHBSSceneEntityOperatorBase> OperatorClass, int32 Identify = 0);

	UFUNCTION(BlueprintCallable, Category = "HBS|SceneEntity")
	virtual UHBSSceneEntityOperatorBase* CreateOperatorByClass(TSubclassOf<UHBSSceneEntityOperatorBase> OperatorClass, int32 Identify, FHBSPreOperatorInitCallback PreInitCallback);

	virtual void RefreshOperatorsMetaDataList();
#pragma endregion

#pragma region 父子关系
	UFUNCTION(BlueprintCallable, Category = "HBS|SceneEntity")
	void SetParentEntity(UHBSSceneEntityBase* InParentSE);

	UFUNCTION(BlueprintCallable, Category = "HBS|SceneEntity")
	UHBSSceneEntityBase* GetParentEntity() const { return ParentSE; }

	template<typename SEType>
	SEType* GetTypedParentEntity() const
	{
		for (UHBSSceneEntityBase* SE = GetParentEntity(); SE != nullptr; SE = SE->GetParentEntity())
		{
			if (SEType* TypedSE = Cast<SEType>(SE))
			{
				return TypedSE;
			}
		}

		return nullptr;
	}

	bool IsChildOf(UHBSSceneEntityBase* OtherSE);

	UFUNCTION(BlueprintCallable, Category = "HBS|SceneEntity")
	TArray<UHBSSceneEntityBase*> GetChildrenEntityList() const { return ChildrenSEList; }

	template<typename SEType>
	SEType* GetTypedChild() const
	{
		for (UHBSSceneEntityBase* Child : ChildrenSEList)
			if (SEType* TypedSE = Cast<SEType>(Child))
				return TypedSE;

		return nullptr;
	}

	template<typename SEType>
	TArray<SEType*> GetTypedChildrenList() const
	{
		TArray<SEType*> Result;
		for (UHBSSceneEntityBase* Child : ChildrenSEList)
			if (SEType* TypedSE = Cast<SEType>(Child))
				Result.Add(TypedSE);

		return Result;
	}

#pragma endregion

protected:
	virtual void OnInitialize() {}

	virtual UHBSSceneEntityOperatorBase* CreateFirstPlacementOperator() { return nullptr; }

	virtual void PreInitializeOperator(UHBSSceneEntityOperatorBase* NewOperator, TSubclassOf<UHBSSceneEntityOperatorBase> OperatorClass, int32 Identify) {}

protected:
	UPROPERTY(VisibleAnywhere, BlueprintReadWrite, Category = "HBS|SceneEntity")
	TArray<FHBSSceneEntityOperatorMetaData> OperatorsMetaDataList;

	UPROPERTY(VisibleAnywhere, Category = "HBS|SceneEntity")
	bool IsPlacementDone = false;

	UPROPERTY(VisibleAnywhere, Category = "HBS|SceneEntity")
	bool IsPreview = false;

	UPROPERTY(VisibleAnywhere, Category = "HBS|SceneEntity")
	UHBSSceneEntityBase* ParentSE = nullptr;

	UPROPERTY(VisibleAnywhere, Category = "HBS|SceneEntity")
	TArray<UHBSSceneEntityBase*> ChildrenSEList;
};
