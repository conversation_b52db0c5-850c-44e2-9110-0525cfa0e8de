#pragma once

#include "CoreMinimal.h"
#include "TransformSceneEntity.h"
#include "SceneEntity/SceneEntityBase.h"
#include "SceneEntityInterface/AssetInitializableSceneEntityInterface.h"
#include "HBSAsset/HBSStaticMeshAsset.h"

#include "StaticMeshSceneEntity.generated.h"

UCLASS(Blueprintable, BlueprintType)
class HOMESTEADBUILDINGSYSTEM_API UHBSStaticMeshSceneEntity : public UHBSTransformSceneEntity, public IHBSAssetInitializableSceneEntityInterface
{
	GENERATED_BODY()

public:
	UHBSStaticMeshSceneEntity(const FObjectInitializer& ObjectInitializer)
		: Super(ObjectInitializer)
	{
		StaticMesh = nullptr;
	}

	void SetupInitInfoByAsset(class UHBSAssetBase* InAsset) override
	{
		if (InAsset == nullptr)
		{
			StaticMeshAsset = nullptr;
			StaticMesh = nullptr;
			return;
		}

		StaticMeshAsset = Cast<UHBSStaticMeshAsset>(InAsset);
		StaticMesh = StaticMeshAsset ? StaticMeshAsset->StaticMesh : nullptr;
	}

	UFUNCTION(BlueprintCallable, Category = "HBS|SceneEntity|StaticMesh", meta = (DeprecatedFunction))
	void SetupInitInfo(UStaticMesh* InStaticMesh)
	{
		StaticMesh = InStaticMesh;
	}

	UFUNCTION(BlueprintCallable, Category = "HBS|SceneEntity|StaticMesh")
	UStaticMesh* GetStaticMesh() { return StaticMesh; }

	UFUNCTION(BlueprintCallable, Category = "HBS|SceneEntity|StaticMesh")
	void SetStaticMesh(UStaticMesh* InStaticMesh) { StaticMesh = InStaticMesh; }

	FHBSBackfaceOrientationInfo GetBackfaceOrientationInfo() const override
	{
		if (StaticMeshAsset)
			return StaticMeshAsset->BackfaceOrientation;

		return FHBSBackfaceOrientationInfo();
	}

public:
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "HBS|SceneEntity|StaticMesh")
	UHBSStaticMeshAsset* StaticMeshAsset = nullptr;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "HBS|SceneEntity|StaticMesh")
	UStaticMesh* StaticMesh = nullptr;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "HBS|SceneEntity|StaticMesh")
	bool IsErrorPreview = false;
};
