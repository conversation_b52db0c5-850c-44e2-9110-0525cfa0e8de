#pragma once

#include "CoreMinimal.h"
#include "SceneEntityBase.h"
#include "HBSConfig.h"
#include "Data/UGCUserData.h"

#include "WallSceneEntity.generated.h"

class UUGCProfile;

UENUM(BlueprintType)
enum class EHBSWallVisibility: uint8
{
	Visibile,
	Hidden,
	FootOnly,
};

UENUM(BlueprintType)
enum class EHBSWallType : uint8
{
	NonEnclosingWall = 0x00,
	OuterFaceOutward = 0x01,
	InsideFaceOutward = 0x02,
	FullyEnclosedWall = 0x03,
};
ENUM_CLASS_FLAGS(EHBSWallType);

UCLASS(Blueprintable, BlueprintType)
class HOMESTEADBUILDINGSYSTEM_API UHBSWallSceneEntity : public UHBSSceneEntityBase
{
	GENERATED_BODY()

public:
	UHBSWallSceneEntity(const FObjectInitializer& ObjectInitializer)
		: Super(ObjectInitializer)
	{
		WallStart = FVector2D(0, 0);
		WallEnd = FVector2D(0, 0);
		WallHeight = 300;
	}

	UFUNCTION(BlueprintCallable, Category = "HBS|SceneEntity|Wall")
	void SetupInitInfo(int32 InWallHeight, UUGCProfile* InWallUGCProfile = nullptr);

	UFUNCTION(BlueprintCallable, Category = "HBS|SceneEntity|UGC")
	void SetUGCUserData(UUGCUserData* InUserData);

	UFUNCTION(BlueprintCallable, Category = "HBS|SceneEntity|UGC")
	UUGCUserData* GetOrCreateUGCUserData();

	virtual bool IsTickable() const override { return true; }
	virtual void Tick(float DeltaTime) override;

	virtual void OnPreDestroy() override;

	void SetWallType(EHBSWallType InWallType) { WallType = InWallType; }

	void CopyUGCData(UUGCUserData* InSourceUGCData);

	virtual FBox CalcEntityBound() const override;

	FVector2D GetWallStart() const
	{
		return WallStart;
	}

	FVector GetWallStart3D() const
	{
		return FVector(WallStart.X, WallStart.Y, WallStartHeight);
	}

	FVector2D GetWallEnd() const
	{
		return WallEnd;
	}

	FVector GetWallEnd3D() const
	{
		return FVector(WallEnd.X, WallEnd.Y, WallStartHeight);
	}

	FVector2D GetWallVector() const
	{
		if (IsPointWall())
			return FVector2D(1, 0);
		return (WallEnd - WallStart).GetSafeNormal();
	}

	FVector GetWallVector3D() const
	{
		FVector2D WallVector = GetWallVector();
		return FVector(WallVector.X, WallVector.Y, 0);
	}

	FVector2D GetWallNormal() const
	{
		FVector2D WallVector = GetWallVector();
		return FVector2D(-WallVector.Y, WallVector.X);
	}

	FVector GetWallNormal3D() const
	{
		FVector2D WallNormal = GetWallNormal();
		return FVector(WallNormal.X, WallNormal.Y, 0);
	}

	bool IsPointWall() const
	{
		return FVector2D::DistSquared(WallStart, WallEnd) < 1;
	}

	float GetWallLength() const
	{
		return FVector2D::Distance(WallStart, WallEnd);
	}

	int32 GetWallHeight() const { return WallHeight; }

	int32 GetWallStartHeight() const { return WallStartHeight; }

	FVector CalcWorldPositionOnWall(FVector2D InLocalPosition);

	bool IsWallVisible() const { return CurrentVisibility == EHBSWallVisibility::Visibile; }

protected:
	virtual UHBSSceneEntityOperatorBase* CreateFirstPlacementOperator() override;
	virtual void () override;
	virtual void OnInitialize() override;
	virtual void PreInitializeOperator(UHBSSceneEntityOperatorBase* NewOperator, TSubclassOf<UHBSSceneEntityOperatorBase> OperatorClass, int32 Identify) override;

	EHBSWallVisibility CalcWallVisible();

public:
	UPROPERTY(EditAnywhere, Category = "Wall Properties")
	FVector2D WallStart = FVector2D();

	UPROPERTY(EditAnywhere, Category = "Wall Properties")
	FVector2D WallEnd = FVector2D();

	UPROPERTY(EditAnywhere, Category = "Wall Properties")
	int32 WallHeight = 0;

	UPROPERTY(EditAnywhere, Category = "Wall Properties")
	int32 WallStartHeight = 0;

	UPROPERTY(EditAnywhere, Category = "Wall Properties")
	UUGCProfile* WallUGCProfile = nullptr;

	UPROPERTY(EditAnywhere, Category = "Wall Properties")
	UUGCUserData* WallUserData = nullptr;

	UPROPERTY(Transient, EditAnywhere, Category = "Wall Properties")
	EHBSWallType WallType = EHBSWallType::NonEnclosingWall;

	UPROPERTY(Transient, EditAnywhere, Category = "Wall Properties")
	EHBSWallVisibility CurrentVisibility = EHBSWallVisibility::Visibile;
};
