// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "SceneEntityBase.h"
#include "HBSAsset/HBSStaticMeshAsset.h"
#include "TransformSceneEntity.generated.h"

/**
 *
 */
UCLASS(Abstract, Blueprintable, BlueprintType)
class HOMESTEADBUILDINGSYSTEM_API UHBSTransformSceneEntity : public UHBSSceneEntityBase
{
	GENERATED_BODY()

public:
	UFUNCTION(BlueprintCallable, Category = "HBS|SceneEntity")
	FTransform GetTransform() const { return Transform; }

	UFUNCTION(BlueprintCallable, Category = "HBS|SceneEntity")
	void SetTransform(const FTransform& InTransform) { Transform = InTransform; }

	FBox CalcEntityBound() const override;

	virtual FHBSBackfaceOrientationInfo GetBackfaceOrientationInfo() const { return FHBSBackfaceOrientationInfo(); }

public:
	void SetIsVisible(bool InIsVisible);

protected:
	UHBSSceneEntityOperatorBase* CreateFirstPlacementOperator() override;
	void RefreshOperatorsMetaDataList() override;

private:
	UPROPERTY(EditAnywhere, Category = "HBS|SceneEntity")
	FTransform Transform = FTransform::Identity;
};
