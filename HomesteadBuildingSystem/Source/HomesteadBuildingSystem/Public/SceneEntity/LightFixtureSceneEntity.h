#pragma once

#include "CoreMinimal.h"
#include "SceneEntityBase.h"
#include "SceneEntityInterface/AssetInitializableSceneEntityInterface.h"
#include "HBSAsset/HBSLightFixtureAsset.h"
#include "TransformSceneEntity.h"

#include "LightFixtureSceneEntity.generated.h"

UCLASS(Blueprintable, BlueprintType)
class HOMESTEADBUILDINGSYSTEM_API UHBSLightFixtureSceneEntity : public UHBSTransformSceneEntity, public IHBSAssetInitializableSceneEntityInterface
{
	GENERATED_BODY()

public:
	void SetupInitInfoByAsset(class UHBSAssetBase* InAsset)
	{
		LightFixtureAsset = Cast<UHBSLightFixtureAsset>(InAsset);
	}

	UHBSLightFixtureAsset* GetLightFixtureAsset() const { return LightFixtureAsset; }
	bool GetIsLightOn() const { return IsLightOn; }

protected:
	UHBSSceneEntityOperatorBase* CreateFirstPlacementOperator() override;
	void RefreshOperatorsMetaDataList() override;

protected:
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "HBS|LightFixtureSceneEntity")
	UHBSLightFixtureAsset* LightFixtureAsset;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "HBS|LightFixtureSceneEntity")
	bool IsLightOn = true;
};