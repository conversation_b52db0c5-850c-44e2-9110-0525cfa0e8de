#pragma once

#include "CoreMinimal.h"
#include "SceneEntity/SceneEntityBase.h"
#include "HBSMathLibrary.h"

#include "PlaceablePlaneSceneEntity.generated.h"

UENUM(BlueprintType)
enum class EHBSPlaceablePlaneType : uint8
{
	Ground,
	Foundation,
	Floor,
	Tabletop,
	Ceiling,
};

UCLASS(Blueprintable, BlueprintType)
class HOMESTEADBUILDINGSYSTEM_API UHBSPlaceablePlaneSE : public UHBSSceneEntityBase
{
	GENERATED_BODY()

public:
	UFUNCTION(BlueprintCallable, Category = "HBS|PlaceablePlane")
	void SetupInitInfo(const TArray<FVector2D>& InVertices, int32 InPlaneHeight, EHBSPlaceablePlaneType InType, int32 InPriority = 0)
	{
		Vertices = InVertices;
		PlaneHeight = InPlaneHeight;
		Priority = InPriority;
		Type = InType;

		if (Vertices.Num() > 0)
			BoundingBox = FBox2D(Vertices);
		else
			BoundingBox = FBox2D(EForceInit::ForceInitToZero);
	}

	UFUNCTION(BlueprintCallable, Category = "HBS|PlaceablePlane")
	int32 GetPlaneHeight() const { return PlaneHeight; }

	UFUNCTION(BlueprintCallable, Category = "HBS|PlaceablePlane")
	TArray<FVector2D> GetVertices() const { return Vertices; }

	UFUNCTION(BlueprintCallable, Category = "HBS|PlaceablePlane")
	FBox2D GetBoundingBox() const { return BoundingBox; }

	UFUNCTION(BlueprintCallable, Category = "HBS|PlaceablePlane")
	int32 GetPriority() const { return Priority; }

	EHBSPlaceablePlaneType GetPlaneType() const { return Type; }

	UFUNCTION(BlueprintCallable, Category = "HBS|PlaceablePlane")
	bool IsInPlane(FVector2D Point) const
	{
		return UHBSMathLibrary::IsPointInPolygon(Point, Vertices);
	}

	bool IsVisible() const { return IsVisibility; }

	bool IsTickable() const override { return true; }
	void Tick(float DeltaTime) override;

protected:
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "HBS|PlaceablePlane")
	int32 PlaneHeight = 0;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "HBS|PlaceablePlane")
	TArray<FVector2D> Vertices;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "HBS|PlaceablePlane")
	FBox2D BoundingBox;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "HBS|PlaceablePlane")
	int32 Priority = 0;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "HBS|PlaceablePlane")
	EHBSPlaceablePlaneType Type = EHBSPlaceablePlaneType::Foundation;

	UPROPERTY(Transient, EditAnywhere, BlueprintReadWrite, Category = "HBS|PlaceablePlane")
	bool IsVisibility = true;
};
