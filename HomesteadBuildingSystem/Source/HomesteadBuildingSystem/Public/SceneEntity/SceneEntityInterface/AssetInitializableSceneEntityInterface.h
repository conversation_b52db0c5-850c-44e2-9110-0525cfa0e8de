#pragma once
#include "CoreMinimal.h"

#include "AssetInitializableSceneEntityInterface.generated.h"

UINTERFACE(NotBlueprintable)
class UHBSAssetInitializableSceneEntityInterface : public UInterface
{
	GENERATED_BODY()
};

class IHBSAssetInitializableSceneEntityInterface
{
	GENERATED_BODY()

public:
	UFUNCTION(BlueprintCallable, Category = "HBS|SceneEntity")
	virtual void SetupInitInfoByAsset(class UHBSAssetBase* InAsset) PURE_VIRTUAL(IHBSAssetInitializableSceneEntityInterface::SetupInitInfoByAsset, );
};
