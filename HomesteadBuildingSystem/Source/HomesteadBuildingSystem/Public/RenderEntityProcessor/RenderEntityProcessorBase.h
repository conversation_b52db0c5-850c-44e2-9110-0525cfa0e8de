#pragma once

#include "CoreMinimal.h"
#include "RenderEntity/RenderEntityBase.h"
#include "SceneEntity/SceneEntityBase.h"
#include "RenderEntityProcessorBase.inl"
#include "RenderEntityProcessorBase.generated.h"

class UHBSSceneEntityBase;
class AHBSRenderEntityBase;

USTRUCT(Blueprintable, BlueprintType)
struct FRelatedRenderEntityList
{
	GENERATED_BODY()

	UPROPERTY(VisibleAnywhere, Category = "HBS|RenderEntityProcessorContext")
	TArray<AHBSRenderEntityBase*> RenderEntityList;
};

UCLASS(Blueprintable, BlueprintType)
class UHBSRenderEntityProcessorContext : public UObject
{
	GENERATED_BODY()

public:
	UFUNCTION(BlueprintCallable, Category = "HBS|RenderEntityProcessorContext")
	bool HasAnyEntityChangedByClass(TSubclassOf<UHBSSceneEntityBase> EntityClass, bool IsPreview = false);

	UFUNCTION(BlueprintCallable, Category = "HBS|RenderEntityProcessorContext")
	const TArray<UHBSSceneEntityBase*>& GetNewSceneEntityList(bool IsPreview = false);

	UFUNCTION(BlueprintCallable, Category = "HBS|RenderEntityProcessorContext")
	const TArray<UHBSSceneEntityBase*>& GetModifiedSceneEntityList(bool IsPreview = false);

	UFUNCTION(BlueprintCallable, Category = "HBS|RenderEntityProcessorContext")
	const TArray<UHBSSceneEntityBase*>& GetRemovedSceneEntityList(bool IsPreview = false);

	UFUNCTION(BlueprintCallable, Category = "HBS|RenderEntityProcessorContext")
	const TArray<UHBSSceneEntityBase*>& GetAllSceneEntityList(bool IsPreview = false);

	UFUNCTION(BlueprintCallable, Category = "HBS|RenderEntityProcessorContext")
	const TArray<AHBSRenderEntityBase*>& GetAllRenderEntityList();

	GENERATE_SCENE_ENTITY_LIST_ACCESS_FUNC_FOR_RENDER_ENTITY_PROCESSOR(NewSceneEntityList)
		GENERATE_SCENE_ENTITY_LIST_ACCESS_FUNC_FOR_RENDER_ENTITY_PROCESSOR(ModifiedSceneEntityList)
		GENERATE_SCENE_ENTITY_LIST_ACCESS_FUNC_FOR_RENDER_ENTITY_PROCESSOR(RemovedSceneEntityList)
		GENERATE_SCENE_ENTITY_LIST_ACCESS_FUNC_FOR_RENDER_ENTITY_PROCESSOR(AllSceneEntityList)

		template<typename SEType> TArray<SEType*> GetTypedSceneEntityListFromSEList(const TArray<UHBSSceneEntityBase*>& InSEList)
	{
		return GetTypedAllSceneEntityListFromSEList<SEType>(InSEList, SEType::StaticClass());
	}

	template<typename SEType> TArray<SEType*> GetTypedAllSceneEntityListFromSEList(const TArray<UHBSSceneEntityBase*>& InSEList, UClass* InClass)
	{
		return GetTypedAllSceneEntityListFromSEListByPred<SEType>(InSEList, [](SEType* InEntity) { return true; });
	}

	template<typename SEType, typename PredType> TArray<SEType*> GetTypedAllSceneEntityListFromSEListByPred(const TArray<UHBSSceneEntityBase*>& InSEList, PredType InPredFunc)
	{
		return GetTypedAllSceneEntityListFromSEListByPred<SEType>(InSEList, SEType::StaticClass(), InPredFunc);
	}

	template<typename SEType, typename PredType> TArray<SEType*> GetTypedAllSceneEntityListFromSEListByPred(const TArray<UHBSSceneEntityBase*>& InSEList, UClass* InClass, PredType InPredFunc)
	{
		TArray<SEType*> Result;
		for (UHBSSceneEntityBase* Entity : InSEList)
		{
			if (Entity->IsA(InClass) == false)
				continue;

			if (SEType* TypedEntity = Cast<SEType>(Entity))
			{
				if (InPredFunc(TypedEntity))
					Result.Add(TypedEntity);
			}
		}
		return Result;
	}

	UFUNCTION(BlueprintCallable, Category = "HBS|RenderEntityProcessorContext")
	TArray<AHBSRenderEntityBase*> GetRelatedRenderEntityList(const UHBSSceneEntityBase* SceneEntity);

	UFUNCTION(BlueprintCallable, Category = "HBS|RenderEntityProcessorContext")
	AHBSRenderEntityBase* GetRelatedRenderEntityByClass(const UHBSSceneEntityBase* SceneEntity, TSubclassOf<AHBSRenderEntityBase> RenderEntityClass);

	UFUNCTION(BlueprintCallable, Category = "HBS|RenderEntityProcessorContext")
	AHBSRenderEntityBase* GetRelatedRenderEntity(const UHBSSceneEntityBase* SceneEntity);

	template<typename REType>
	REType* GetRelatedRenderEntity(const UHBSSceneEntityBase* SceneEntity)
	{
		return Cast<REType>(GetRelatedRenderEntityByClass(SceneEntity, REType::StaticClass()));
	}

	template<typename REType>
	REType* GetRelatedRenderEntityByClass(const UHBSSceneEntityBase* SceneEntity, TSubclassOf<AHBSRenderEntityBase> RenderEntityClass)
	{
		return Cast<REType>(GetRelatedRenderEntityByClass(SceneEntity, RenderEntityClass));
	}

	UFUNCTION(BlueprintCallable, Category = "HBS|RenderEntityProcessorContext")
	AHBSRenderEntityBase* RequestRenderEntityByClass(TSubclassOf<AHBSRenderEntityBase> RenderEntityClass);

	template<typename REType>
	REType* RequestRenderEntity()
	{
		return Cast<REType>(RequestRenderEntityByClass(REType::StaticClass()));
	}

	template<typename REType>
	REType* RequestRenderEntity(TSubclassOf<REType> RenderEntityClass)
	{
		return Cast<REType>(RequestRenderEntityByClass(RenderEntityClass));
	}

	UFUNCTION(BlueprintCallable, Category = "HBS|RenderEntityProcessorContext")
	void MarkRenderEntityModified(AHBSRenderEntityBase* RenderEntity);

	UFUNCTION(BlueprintCallable, Category = "HBS|RenderEntityProcessorContext")
	void RemoveRenderEntity(AHBSRenderEntityBase* RenderEntity);

	// 由HBSSubsystem调用--------------
	bool NeedUpdateRenderEntity() const;

	void AddSceneEntity_Internal(UHBSSceneEntityBase* SceneEntity, bool IsPreview = false);
	void ModifySceneEntity_Internal(UHBSSceneEntityBase* SceneEntity, bool IsPreview = false);
	void RemoveSceneEntity_Internal(UHBSSceneEntityBase* SceneEntity, bool IsPreview = false);

	void PreProcess();
	void RefreshRelatedRenderEntityMap_Internal();
	void PostProcess();
	//--------------

private:
	UPROPERTY(VisibleAnywhere, Category = "HBS|RenderEntityProcessorContext")
	TArray<UHBSSceneEntityBase*> NewSceneEntityList;

	UPROPERTY(VisibleAnywhere, Category = "HBS|RenderEntityProcessorContext")
	TArray<UHBSSceneEntityBase*> ModifiedSceneEntityList;

	UPROPERTY(VisibleAnywhere, Category = "HBS|RenderEntityProcessorContext")
	TArray<UHBSSceneEntityBase*> RemovedSceneEntityList;

	UPROPERTY(VisibleAnywhere, Category = "HBS|RenderEntityProcessorContext")
	TArray<UHBSSceneEntityBase*> NewPreviewSceneEntityList;

	UPROPERTY(VisibleAnywhere, Category = "HBS|RenderEntityProcessorContext")
	TArray<UHBSSceneEntityBase*> ModifiedPreviewSceneEntityList;

	UPROPERTY(VisibleAnywhere, Category = "HBS|RenderEntityProcessorContext")
	TArray<UHBSSceneEntityBase*> RemovedPreviewSceneEntityList;

	UPROPERTY(VisibleAnywhere, Category = "HBS|RenderEntityProcessorContext")
	TArray<UHBSSceneEntityBase*> AllSceneEntityList;

	UPROPERTY(VisibleAnywhere, Category = "HBS|RenderEntityProcessorContext")
	TArray<UHBSSceneEntityBase*> AllPreviewSceneEntityList;

	UPROPERTY(VisibleAnywhere, Category = "HBS|RenderEntityProcessorContext")
	TMap<UHBSSceneEntityBase*, FRelatedRenderEntityList> RelatedRenderEntityMap;

	UPROPERTY(VisibleAnywhere, Category = "HBS|RenderEntityProcessorContext")
	TArray<AHBSRenderEntityBase*> AllRenderEntityList;

	UPROPERTY(VisibleAnywhere, Category = "HBS|RenderEntityProcessorContext")
	TArray<AHBSRenderEntityBase*> ModifiedRenderEntityList;

	UPROPERTY(VisibleAnywhere, Category = "HBS|RenderEntityProcessorContext")
	TArray<AHBSRenderEntityBase*> OrphanedRenderEntityList;
};

UCLASS(Abstract, Blueprintable, BlueprintType)
class HOMESTEADBUILDINGSYSTEM_API UHBSRenderEntityProcessorBase : public UObject
{
	GENERATED_BODY()

public:
	virtual void Process(UHBSRenderEntityProcessorContext* Context) PURE_VIRTUAL(UHBSRenderEntityProcessorBase::Process, );

public:
	int32 GetPriority() const { return Priority; }

protected:
	UPROPERTY()
	int32 Priority = 0;
};
