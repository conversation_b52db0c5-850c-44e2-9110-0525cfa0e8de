// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "RenderEntityProcessorBase.h"
#include "UGCObjectRenderEntityProcessor.generated.h"

/**
 * 
 */
UCLASS()
class HOMESTEADBUILDINGSYSTEM_API UHBSUGCObjectRenderEntityProcessor : public UHBSRenderEntityProcessorBase
{
	GENERATED_BODY()

public:
	virtual void Process(UHBSRenderEntityProcessorContext* Context) override;
};
