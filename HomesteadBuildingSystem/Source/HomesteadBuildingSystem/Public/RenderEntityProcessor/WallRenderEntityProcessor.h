#pragma once

#include "CoreMinimal.h"
#include "RenderEntityProcessorBase.h"

#include "WallRenderEntityProcessor.generated.h"

class UHBSWallSceneEntity;
class UHBSWindowSceneEntity;
class UHBSSurfaceSceneEntity;

USTRUCT()
struct FHBSWallEndPointInfo
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, Category = "Wall Calc")
	FVector2D EndPoint;

	UPROPERTY(EditAnywhere, Category = "Wall Calc")
	FVector2D AnotherEndPoint;

	UPROPERTY(EditAnywhere, Category = "Wall Calc")
	int32 WallIndex = INDEX_NONE;

	UPROPERTY(EditAnywhere, Category = "Wall Calc")
	bool IsWallStart;

	UPROPERTY(EditAnywhere, Category = "Wall Calc")
	FVector2D WallVector;

	UPROPERTY(EditAnywhere, Category = "Wall Calc")
	FVector2D WallNormal;
};

USTRUCT()
struct FHBSWallMeetingPoint
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, Category = "Wall Calc")
	int32 EndPointIndex0;

	UPROPERTY(EditAnywhere, Category = "Wall Calc")
	int32 EndPointIndex1;

	UPROPERTY(EditAnywhere, Category = "Wall Calc")
	FVector2D MeetingPoint;

	UPROPERTY(EditAnywhere, Category = "Wall Calc")
	float AngleDot;
};

USTRUCT()
struct FHBSEndPointWallGroup
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, Category = "Wall Calc")
	TArray<int32> EndPointIndex;
};

USTRUCT()
struct FHBSProcessedWallVertices
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, Category = "Wall Calc")
	FVector2D OutsideStartVertex;

	UPROPERTY(EditAnywhere, Category = "Wall Calc")
	FVector2D InsideStartVertex;

	UPROPERTY(EditAnywhere, Category = "Wall Calc")
	FVector2D OutsideEndVertex;

	UPROPERTY(EditAnywhere, Category = "Wall Calc")
	FVector2D InsideEndVertex;

	UPROPERTY(EditAnywhere, Category = "Wall Calc")
	FVector2D Vertex0;

	UPROPERTY(EditAnywhere, Category = "Wall Calc")
	FVector2D Vertex1;

	UPROPERTY(EditAnywhere, Category = "Wall Calc")
	FVector2D Vertex2;

	UPROPERTY(EditAnywhere, Category = "Wall Calc")
	FVector2D Vertex3;

	UPROPERTY(EditAnywhere, Category = "Wall Calc")
	int32 OutsideWallIndex = 0;

	UPROPERTY(EditAnywhere, Category = "Wall Calc")
	int32 InsideWallIndex = 1;

	UPROPERTY(EditAnywhere, Category = "Wall Calc")
	UHBSWallSceneEntity* WallSceneEntity;
};

UCLASS(Blueprintable, BlueprintType)
class HOMESTEADBUILDINGSYSTEM_API UHBSWallRenderEntityProcessor : public UHBSRenderEntityProcessorBase
{
	GENERATED_BODY()

public:
	void Process(UHBSRenderEntityProcessorContext* Context) override;

protected:
	void GenerateEndPointWallGroup(const TArray<UHBSWallSceneEntity*>& WallList, TArray<FHBSWallEndPointInfo>& OutWallEndPointInfoList, TArray<FHBSEndPointWallGroup>& OutEndPointWallGroupList);

	void GenerateProcessedWallVerticesList(const TArray<UHBSWallSceneEntity*>& InWallList, const TArray<FHBSWallEndPointInfo>& InWallEndPointInfoList, const TArray<FHBSEndPointWallGroup>& InEndPointWallGroupList, TArray<FHBSProcessedWallVertices>& OutProcessedWallVerticesList);

	void GenerateRelativeWindowList(const TArray<UHBSWallSceneEntity*>& InWallList, const TArray<UHBSWindowSceneEntity*>& InWindowList, TArray <TArray<UHBSWindowSceneEntity*> >& OutRelativeWindowList);

	FHBSProcessedWallVertices GenerateProcessedWallVerticesForSingleWall(UHBSWallSceneEntity* InWall);

	void EmitProcessedWallVerticesList(TArray<FHBSProcessedWallVertices>& ProcessedWallVerticesList, bool IsStartPoint, int32 WallIndex, FVector2D OutsideVertice, FVector2D InsideVertice);

	void SortProcessedWallVertices(FHBSProcessedWallVertices& InOutWallVertices);

	void CalcAndMergeIntersectionForRayIntersection(FVector2D WallStart, FVector2D WallVector, FVector2D MainWallStart, FVector2D MainWallVector, FVector2D& OutIntersection, float& OutDistance);

	void CalcAndMergeIntersectionForLineRayIntersection(FVector2D WallStart, FVector2D WallVector, FVector2D MainWallStart, FVector2D MainWallVector, FVector2D& OutIntersection, float& OutDistance);
};
