#define GENERATE_SCENE_ENTITY_LIST_ACCESS_FUNC_FOR_RENDER_ENTITY_PROCESSOR(InSceneEntityList)																												\
	template<typename SEType> TArray<SEType*> GetTyped##InSceneEntityList(bool IsPreview = false)																				\
	{																																											\
		return GetTypedSceneEntityListFromSEList<SEType>(Get##InSceneEntityList(IsPreview));																					\
	}																																											\
																																												\
	template<typename SEType> TArray<SEType*> GetTyped##InSceneEntityList(UClass* InClass, bool IsPreview = false)																\
	{																																											\
		return GetTypedSceneEntityListFromSEList<SEType>(InSceneEntityList, InClass);																							\
	}																																											\
																																												\
	template<typename SEType, typename PredType> TArray<SEType*> GetTyped##InSceneEntityList##ByPred(PredType InPredFunc, bool IsPreview = false)								\
	{																																											\
		return GetTypedSceneEntityListByPredFromSEList<SEType>(InSceneEntityList, InPredFunc);																					\
	}																																											\
																																												\
	template<typename SEType, typename PredType> TArray<SEType*> GetTyped##InSceneEntityList##ByPred(UClass* InClass, PredType InPredFunc, bool IsPreview = false)				\
	{																																											\
		return GetTypedSceneEntityListByPredFromSEList<SEType>(InSceneEntityList, InClass, InPredFunc);																			\
	}																																											\
