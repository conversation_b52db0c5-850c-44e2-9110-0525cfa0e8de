#pragma once

#include "CoreMinimal.h"

#include "HBSConfig.generated.h"

UCLASS(Config = "HomesteadBuildingSystem", DefaultConfig, meta = (DisplayName = "家园建造系统"))
class UHBSConfig : public UDeveloperSettings
{
	GENERATED_BODY()

public:
	static const UHBSConfig* Get() { return GetDefault<UHBSConfig>(); }

	UMaterialInterface* GetDefaultWallMaterial() const { return Cast<UMaterialInterface>(DefaultWallMaterial.TryLoad()); }

	UMaterialInterface* GetDefaultFloorMaterial() const { return Cast<UMaterialInterface>(DefaultFloorMaterial.TryLoad()); }

	UMaterialInterface* GetPlaceablePlaneMaterial() const { return Cast<UMaterialInterface>(PlaceablePlaneMaterial.TryLoad()); }

	UMaterialInterface* GetPreviewMaterial() const { return Cast<UMaterialInterface>(PreviewMaterial.TryLoad()); }

	UMaterialInterface* GetPreviewErrorMaterial() const { return Cast<UMaterialInterface>(PreviewErrorMaterial.TryLoad()); }

public:
	UPROPERTY(config, EditAnywhere)
	float GridSize = 10;

	UPROPERTY(config, EditAnywhere)
	float WallWidth = 8;

	UPROPERTY(config, EditAnywhere, meta = (AllowedClasses = "/Script/Engine.MaterialInterface", ExactClass = false))
	FSoftObjectPath DefaultWallMaterial;

	UPROPERTY(config, EditAnywhere, meta = (AllowedClasses = "/Script/Engine.MaterialInterface", ExactClass = false))
	FSoftObjectPath DefaultFloorMaterial;

	UPROPERTY(config, EditAnywhere, meta = (AllowedClasses = "/Script/Engine.MaterialInterface", ExactClass = false))
	FSoftObjectPath PlaceablePlaneMaterial;

	UPROPERTY(config, EditAnywhere, meta = (AllowedClasses = "/Script/Engine.MaterialInterface", ExactClass = false))
	FSoftObjectPath PreviewMaterial;

	UPROPERTY(config, EditAnywhere, meta = (AllowedClasses = "/Script/Engine.MaterialInterface", ExactClass = false))
	FSoftObjectPath PreviewErrorMaterial;


};
