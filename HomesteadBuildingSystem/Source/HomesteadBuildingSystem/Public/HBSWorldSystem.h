#pragma once

#include "CoreMinimal.h"
#include "SceneEntityProcessor/SceneEntityProcessorBase.h"
#include "HBSWorldSystem.generated.h"

class UHBSWallSceneEntity;
class UHBSSceneEntityBase;
class AHBSRenderEntityBase;
class UHBSSceneEntityOperatorBase;
class UHBSSceneEntityProcessorBase;
class UHBSRenderEntityProcessorBase;
class UHBSSceneEntityProcessorContext;
class UHBSRenderEntityProcessorContext;

enum class EHBSPlaceablePlaneType : uint8;

USTRUCT(BlueprintType, Blueprintable)
struct FHBSTraceWallResult
{
	GENERATED_BODY()

	UPROPERTY(BlueprintReadOnly, Category = "HBS|TraceWallResult")
	UHBSWallSceneEntity* WallEntity = nullptr;

	UPROPERTY(BlueprintReadOnly, Category = "HBS|TraceWallResult")
	FVector HitPosition;

	UPROPERTY(BlueprintReadOnly, Category = "HBS|TraceWallResult")
	FVector2D HitLocalPosition;

	UPROPERTY(BlueprintReadOnly, Category = "HBS|TraceWallResult")
	FVector HitNormal;

	UPROPERTY(BlueprintReadOnly, Category = "HBS|TraceWallResult")
	float Distance;
};

USTRUCT(BlueprintType, Blueprintable)
struct FHBSHitResult
{
	GENERATED_BODY()

	UPROPERTY(BlueprintReadOnly, Category = "HBS|HitResult")
	UHBSSceneEntityBase* HitEntity = nullptr;

	UPROPERTY(BlueprintReadOnly, Category = "HBS|HitResult")
	FVector HitPosition;

	UPROPERTY(BlueprintReadOnly, Category = "HBS|HitResult")
	FVector HitNormal;

	UPROPERTY(BlueprintReadOnly, Category = "HBS|HitResult")
	float HitDistance = 0.0f;
};

UENUM(BlueprintType)
enum class EHBSWallShowMode : uint8
{
	ShowAll,
	HideAll,
	ShowExterior
};

USTRUCT(BlueprintType, Blueprintable)
struct FHBSSetting
{
	GENERATED_BODY()

public:
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "HBS|Setting")
	bool bEnableSnap = true;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "HBS|Setting")
	float WallAngleGrid = 45.f;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "HBS|Setting")
	EHBSWallShowMode WallShowMode = EHBSWallShowMode::ShowExterior;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "HBS|Setting")
	int32 CurrentLevel = 0;
};

UCLASS(Blueprintable, BlueprintType)
class HOMESTEADBUILDINGSYSTEM_API UHBSWorldSystem : public UTickableWorldSubsystem
{
	GENERATED_BODY()

public:
	static UHBSWorldSystem* Get(const UObject* WorldContextObject)
	{
		UWorld* World = GEngine ? GEngine->GetWorldFromContextObject(WorldContextObject, EGetWorldErrorMode::LogAndReturnNull) : nullptr;
		return World ? World->GetSubsystem<UHBSWorldSystem>() : nullptr;
	}

	virtual bool ShouldCreateSubsystem(UObject* Outer) const override;

	virtual void Initialize(FSubsystemCollectionBase& Collection) override;
	virtual void Deinitialize() override;

	virtual void Tick(float DeltaTime) override;
	virtual TStatId GetStatId() const override { RETURN_QUICK_DECLARE_CYCLE_STAT(UHBSWorldSystem, STATGROUP_Tickables); }

public:
	// High Light and Selection
	UFUNCTION(BlueprintCallable, Category = "HBSWorldSystem")
	void SetHighLightedSceneEntity(UHBSSceneEntityBase* InSceneEntity);

	UFUNCTION(BlueprintCallable, Category = "HBSWorldSystem")
	void SetHighLightedSceneEntityList(TArray<UHBSSceneEntityBase*> InSceneEntityList);

	UFUNCTION(BlueprintCallable, Category = "HBSWorldSystem")
	void SetSelectedSceneEntity(UHBSSceneEntityBase* InSceneEntity);

	UFUNCTION(BlueprintCallable, Category = "HBSWorldSystem")
	void SetSelectedSceneEntityList(TArray<UHBSSceneEntityBase*> InSceneEntityList);
	// End of High Light and Selection

	UFUNCTION(BlueprintCallable, Category = "HBSWorldSystem")
	FHBSSetting GetSetting() const { return HBSSetting; }

	UFUNCTION(BlueprintCallable, Category = "HBSWorldSystem")
	void SetSetting(const FHBSSetting& InSetting) { HBSSetting = InSetting; }

	FVector2D SnapToGrid2D(FVector2D Position, FVector2D Direction = FVector2D(1, 0), FVector2D GridSize = FVector2D(0, 0));
	FVector SnapToGrid(FVector Position, FVector2D Direction = FVector2D(1, 0), FVector2D GridSize = FVector2D(0, 0));

	UFUNCTION(BlueprintCallable, Category = "HBSWorldSystem")
	int32 GetMaxLevel();

	UFUNCTION(BlueprintCallable, Category = "HBSWorldSystem")
	int32 CalculateNextLevel();

	UFUNCTION(BlueprintCallable, Category = "HBSWorldSystem")
	int32 CalculatePreviousLevel();

	UFUNCTION(BlueprintCallable, Category = "HBSWorldSystem")
	TArray<UHBSFloorSceneEntity*> GenerateCurrentFocusLevelList();

	// Scene Entity Access
	UFUNCTION(BlueprintCallable, Category = "HBSWorldSystem")
	UHBSSceneEntityBase* PlacementSceneEntity(TSubclassOf<UHBSSceneEntityBase> SceneEntityClass);
	UHBSSceneEntityBase* PlacementPreviewSceneEntity(TSubclassOf<UHBSSceneEntityBase> SceneEntityClass);

	void RemoveSceneEntity(UHBSSceneEntityBase* SceneEntity);

	const TArray<UHBSSceneEntityBase*>& GetAllSceneEntities() const;

	template<typename SEType>
	TArray<SEType*> GetTypedAllSceneEntityList(bool InbMustPlacementDone = true)
	{
		return SceneEntityProcessorContext->GetTypedSceneEntityList<SEType>(SE_All, InbMustPlacementDone);
	}

	template<typename SEType>
	TArray<SEType*> GetTypedAllSceneEntityList(UClass* InClass, bool InbMustPlacementDone = true)
	{
		return SceneEntityProcessorContext->GetTypedSceneEntityList<SEType>(SE_All, InbMustPlacementDone, InClass);
	}

	template<typename SEType, typename PredType>
	TArray<SEType*> GetTypedAllSceneEntityListByPred(PredType InPredFunc, bool InbMustPlacementDone = true)
	{
		return SceneEntityProcessorContext->GetTypedSceneEntityList<SEType>(SE_All, InPredFunc, InbMustPlacementDone);
	}

	template<typename SEType, typename PredType>
	TArray<SEType*> GetTypedAllSceneEntityListByPred(UClass* InClass, PredType InPredFunc, bool InbMustPlacementDone = true)
	{
		return SceneEntityProcessorContext->GetTypedSceneEntityList<SEType>(SE_All, InPredFunc, InbMustPlacementDone, InClass);
	}

	void MarkSceneEntityChanged(UHBSSceneEntityBase* SceneEntity);
	// End of Scene Entity Access

	// Operator Management
	void NotifyOperatorCreated(UHBSSceneEntityOperatorBase* Operator);
	void NotifyOperatorDestroyed(UHBSSceneEntityOperatorBase* Operator);
	// End of Operator Management

	// Line Trace and Projection
	UFUNCTION(BlueprintCallable, Category = "HBSWorldSystem")
	FHBSHitResult LineTraceSceneEntityFromMousePosition(FVector2D ScreenPositionOffset = FVector2D(0, 0));

	UFUNCTION(BlueprintCallable, Category = "HBSWorldSystem")
	FHBSHitResult LineTraceSceneEntityFromScreenPosition(FVector2D ScreenPosition);

	UFUNCTION(BlueprintCallable, Category = "HBSWorldSystem")
	FHBSHitResult LineTraceSceneEntity(const FVector& Start, const FVector& End);

	FVector2D GetMousePositionOnScreen();
	FVector2D GetCenterPositionOnScreen();

	UFUNCTION(BlueprintCallable, BlueprintPure, Category = "HBSWorldSystem")
	FVector2D ProjectWorldPositionToScreen(FVector WorldPosition);

	FVector ProjectMousePositionToWorld(TArray<EHBSPlaceablePlaneType> PlaceablePlaneTypeList, FVector2D ScreenPositionOffset = FVector2D(0, 0));
	FVector ProjectScreenPositionToWorld(TArray<EHBSPlaceablePlaneType> PlaceablePlaneTypeList, FVector2D ScreenPosition);

	FVector ProjectMousePositionToWorldWithSpecialHeight(int32 SpecialHeight, FVector2D ScreenPositionOffset = FVector2D(0, 0));
	FVector ProjectScreenPositionToWorldWithSpecialHeight(int32 SpecialHeight, FVector2D ScreenPosition);

	FHBSTraceWallResult ProjectMousePositionToSpecialWall(UHBSWallSceneEntity* SpecifiedWall, FVector2D ScreenPositionOffset = FVector2D(0, 0));
	FHBSTraceWallResult ProjectScreenPositionToSpecialWall(UHBSWallSceneEntity* SpecifiedWall, FVector2D ScreenPosition);

	FHBSTraceWallResult ProjectMousePositionToWall(FVector2D ScreenPositionOffset = FVector2D(0, 0));
	FHBSTraceWallResult ProjectScreenPositionToWall(FVector2D ScreenPosition);
	FHBSTraceWallResult LineTraceWall(const FVector& Start, const FVector& End);
	// End of Line Trace and Projection

	// Render Entity Access
	TArray<AHBSRenderEntityBase*> GetRelatedRenderEntityList(const UHBSSceneEntityBase* SceneEntity);
	AHBSRenderEntityBase* GetRelatedRenderEntity(const UHBSSceneEntityBase* SceneEntity);
	AHBSRenderEntityBase* GetRelatedRenderEntityByClass(const UHBSSceneEntityBase* SceneEntity, TSubclassOf<AHBSRenderEntityBase> RenderEntityClass);
	// End of Render Entity Access

protected:
	void UpdateSceneEntityList();
	void UpdateRenderEntityProcessorContext(UHBSSceneEntityProcessorContext* Context, bool IsPreview);
	void UpdateRenderEntityList();
	void UpdateHighLighted();

private:
	UPROPERTY()
	FHBSSetting HBSSetting;

	UPROPERTY()
	TArray<UHBSSceneEntityBase*> HighLightedSceneEntityList;

	UPROPERTY()
	TArray<UHBSSceneEntityBase*> SelectedSceneEntityList;

	UPROPERTY()
	UHBSSceneEntityProcessorContext* SceneEntityProcessorContext = nullptr;

	UPROPERTY()
	UHBSSceneEntityProcessorContext* PreviewSceneEntityProcessorContext = nullptr;

	UPROPERTY()
	UHBSRenderEntityProcessorContext* RenderEntityProcessorContext = nullptr;

	UPROPERTY()
	TArray<UHBSSceneEntityProcessorBase*> SceneEntityProcessors;

	UPROPERTY()
	TArray<UHBSRenderEntityProcessorBase*> RenderEntityProcessors;

	UPROPERTY()
	TArray<UHBSSceneEntityOperatorBase*> OperatorList;
};