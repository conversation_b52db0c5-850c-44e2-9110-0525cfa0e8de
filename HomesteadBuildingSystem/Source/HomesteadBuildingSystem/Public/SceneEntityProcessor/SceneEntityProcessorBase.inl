#define GENERATE_SCENE_ENTITY_LIST_ACCESS_FUNC_FOR_SCENE_ENTITY_PROCESSOR(InSceneEntityList)																												\
	template<typename SEType> TArray<SEType*> GetTyped##InSceneEntityList(bool InbMustPlacementDone = true)																		\
	{																																											\
		return GetTypedSceneEntityListFromSEList<SEType>(InSceneEntityList, InbMustPlacementDone);																				\
	}																																											\
																																												\
	template<typename SEType> TArray<SEType*> GetTyped##InSceneEntityList(UClass* InClass, bool InbMustPlacementDone = true)													\
	{																																											\
		return GetTypedSceneEntityListFromSEList<SEType>(InSceneEntityList, InClass, InbMustPlacementDone);																		\
	}																																											\
																																												\
	template<typename SEType, typename PredType> TArray<SEType*> GetTyped##InSceneEntityList##ByPred(PredType InPredFunc, bool InbMustPlacementDone = true)						\
	{																																											\
		return GetTypedSceneEntityListByPredFromSEList<SEType>(InSceneEntityList, InPredFunc, InbMustPlacementDone);															\
	}																																											\
																																												\
	template<typename SEType, typename PredType> TArray<SEType*> GetTyped##InSceneEntityList##ByPred(UClass* InClass, PredType InPredFunc, bool InbMustPlacementDone = true)	\
	{																																											\
		return GetTypedSceneEntityListByPredFromSEList<SEType>(InSceneEntityList, InClass, InPredFunc, InbMustPlacementDone);													\
	}																																											\
