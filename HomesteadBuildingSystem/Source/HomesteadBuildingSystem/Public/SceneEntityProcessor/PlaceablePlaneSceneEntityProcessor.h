#pragma once

#include "CoreMinimal.h"
#include "SceneEntityProcessorBase.h"

#include "PlaceablePlaneSceneEntityProcessor.generated.h"

UCLASS(Blueprintable, BlueprintType)
class HOMESTEADBUILDINGSYSTEM_API UHBSPlaceablePlaneSceneEntityProcessor : public UHBSSceneEntityProcessorBase
{
	GENERATED_BODY()

public:
	UHBSPlaceablePlaneSceneEntityProcessor()
	{
		Priority = 200;
	}

	void Process(UHBSSceneEntityProcessorContext* InContext) override;
};
