#pragma once

#include "CoreMinimal.h"
#include "SceneEntity/SceneEntityBase.h"
#include "SceneEntityProcessorBase.inl"
#include "SceneEntityProcessorBase.generated.h"

class UHBSSceneEntityBase;

enum ESceneEntityListType
{
	SE_New,
	SE_Modified,
	SE_Removed,
	SE_All
};

UCLASS(Blueprintable, BlueprintType)
class HOMESTEADBUILDINGSYSTEM_API UHBSSceneEntityProcessorContext : public UObject
{
	GENERATED_BODY()

public:
	UFUNCTION(BlueprintCallable, Category = "HBS|SceneEntityProcessorContext")
	bool HasAnyEntityChangedByClass(TSubclassOf<UHBSSceneEntityBase> EntityClass);

	UFUNCTION(BlueprintCallable, Category = "HBS|SceneEntityProcessorContext")
	const TArray<UHBSSceneEntityBase*>& GetNewSceneEntityList() { return NewSceneEntityList; }

	UFUNCTION(BlueprintCallable, Category = "HBS|SceneEntityProcessorContext")
	const TArray<UHBSSceneEntityBase*>& GetModifiedSceneEntityList() { return ModifiedSceneEntityList; }

	UFUNCTION(BlueprintCallable, Category = "HBS|SceneEntityProcessorContext")
	const TArray<UHBSSceneEntityBase*>& GetRemovedSceneEntityList() { return RemovedSceneEntityList; }

	UFUNCTION(BlueprintCallable, Category = "HBS|SceneEntityProcessorContext")
	const TArray<UHBSSceneEntityBase*>& GetAllSceneEntityList() { return AllSceneEntityList; }

	template<typename SEType>
	TArray<SEType*> GetTypedSceneEntityList(ESceneEntityListType Type, bool bMustPlacementDone = true, UClass* InClass = nullptr)
	{
		return GetTypedSceneEntityList<SEType>(Type, [](SEType* InEntity){ return true; }, bMustPlacementDone, InClass);
	}

	template<typename SEType, typename PredType>
	TArray<SEType*> GetTypedSceneEntityList(ESceneEntityListType Type, PredType InPredFunc, bool bMustPlacementDone = true, UClass* InClass = nullptr)
	{
		switch (Type)
		{
		case SE_New:
			return GetSceneEntityList<SEType, PredType>(NewSceneEntityList, bMustPlacementDone, InClass, InPredFunc);
		case SE_Modified:
			return GetSceneEntityList<SEType, PredType>(ModifiedSceneEntityList, bMustPlacementDone, InClass, InPredFunc);
		case SE_Removed:
			return GetSceneEntityList<SEType, PredType>(RemovedSceneEntityList, bMustPlacementDone, InClass, InPredFunc);
		case SE_All:
			return GetSceneEntityList<SEType, PredType>(AllSceneEntityList, bMustPlacementDone, InClass, InPredFunc);
		}
		return TArray<SEType*>();
	}

	template<typename SEType, typename PredType>
	TArray<SEType*> GetSceneEntityList(const TArray<UHBSSceneEntityBase*>& InSEList, bool bMustPlacementDone = true, UClass* InClass = nullptr, PredType InPredFunc = [](SEType* InEntity){ return true;})
	{
		TArray<SEType*> Result;
		for (UHBSSceneEntityBase* Entity : InSEList)
		{
			if (bMustPlacementDone)
			{
				if (Entity->GetIsPlacementDone() == false)
					continue;
			}

			if (InClass && (Entity->IsA(InClass) == false))
				continue;

			if (SEType* TypedEntity = Cast<SEType>(Entity))
			{
				if (::Invoke(InPredFunc, TypedEntity))
					Result.Add(TypedEntity);
			}
		}
		return Result;
	}

	UHBSSceneEntityBase* PlacementSceneEntityByClass(TSubclassOf<UHBSSceneEntityBase> SceneEntityClass, bool IsPlacementDone = true);

	template<typename SEType>
	SEType* PlacementSceneEntityByClass(bool IsPlacementDone = true)
	{
		return Cast<SEType>(PlacementSceneEntityByClass(SEType::StaticClass(), IsPlacementDone));
	}


	UFUNCTION(BlueprintCallable, Category = "HBS|SceneEntityProcessorContext")
	void MarkSceneEntityModify(UHBSSceneEntityBase* SceneEntity);

	UFUNCTION(BlueprintCallable, Category = "HBS|SceneEntityProcessorContext")
	void RemoveSceneEntity(UHBSSceneEntityBase* SceneEntity);

	// 由HBSSubsystem调用--------------
	bool NeedUpdateSceneEntity() const;

	void PreProcess();
	void PostProcess();
	//--------------
private:
	UPROPERTY(VisibleAnywhere, Category = "HBS|SceneEntityProcessorContext")
	TArray<UHBSSceneEntityBase*> NewSceneEntityList;

	UPROPERTY(VisibleAnywhere, Category = "HBS|SceneEntityProcessorContext")
	TArray<UHBSSceneEntityBase*> ModifiedSceneEntityList;

	UPROPERTY(VisibleAnywhere, Category = "HBS|SceneEntityProcessorContext")
	TArray<UHBSSceneEntityBase*> RemovedSceneEntityList;

	UPROPERTY(VisibleAnywhere, Category = "HBS|SceneEntityProcessorContext")
	TArray<UHBSSceneEntityBase*> AllSceneEntityList;
};

UCLASS(Abstract, Blueprintable, BlueprintType)
class HOMESTEADBUILDINGSYSTEM_API UHBSSceneEntityProcessorBase : public UObject
{
	GENERATED_BODY()

public:
	virtual void Process(UHBSSceneEntityProcessorContext* InContext) PURE_VIRTUAL(UHBSSceneEntityProcessorBase::Process, );

public:
	int32 GetPriority() const { return Priority; }

protected:
	UPROPERTY()
	int32 Priority = 0;
};
