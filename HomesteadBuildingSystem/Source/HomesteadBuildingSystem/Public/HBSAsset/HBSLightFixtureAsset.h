#pragma once

#include "CoreMinimal.h"
#include "HBSAssetBase.h"

#include "HBSLightFixtureAsset.generated.h"

class AHBSLightFixtureRenderEntity;

UENUM(BlueprintType)
enum class EHBSLightFixtureType : uint8
{
	WallMounted UMETA(DisplayName = "壁挂"),
	CeilingMounted UMETA(DisplayName = "顶棚灯"),
	FloorStanding UMETA(DisplayName = "落地灯"),
	TableLamp UMETA(DisplayName = "台灯"),
	Outdoor UMETA(DisplayName = "户外灯")
};

UCLASS(Blueprintable, BlueprintType)
class HOMESTEADBUILDINGSYSTEM_API UHBSLightFixtureAsset : public UHBSAssetBase
{
	GENERATED_BODY()

public:
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "HBS|LightFixtureAsset")
	TSubclassOf<AHBSLightFixtureRenderEntity> LightFixtureRenderEntityClass;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "HBS|LightFixtureAsset")
	UStaticMesh* PreviewStaticMesh = nullptr;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "HBS|LightFixtureAsset")
	EHBSLightFixtureType Type = EHBSLightFixtureType::FloorStanding;
};
