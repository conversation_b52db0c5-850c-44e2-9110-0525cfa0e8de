#pragma once
#include "HBSAssetBase.h"

#include "HBSStaticMeshAsset.generated.h"

USTRUCT(BlueprintType, Blueprintable)
struct FHBSBackfaceOrientationInfo
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "HBS|BackfaceOrientation")
	bool Enable = false;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "HBS|BackfaceOrientation")
	FVector Position;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "HBS|BackfaceOrientation")
	FVector2D Direction;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "HBS|BackfaceOrientation")
	float SnapDistance = 50.f;
};

USTRUCT(BlueprintType, Blueprintable)
struct FHBSPlaceablePlaneInfo
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "HBS|PlaceablePlane")
	TArray<FVector2D> Vertices;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "HBS|PlaceablePlane")
	float Height = 0.f;
};

UCLASS(BlueprintType, Blueprintable, DisplayName = "静态网格")
class HOMESTEADBUILDINGSYSTEM_API UHBSStaticMeshAsset : public UHBSAssetBase
{
	GENERATED_BODY()

public:
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "HBS|StaticMeshAsset")
	UStaticMesh* StaticMesh;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "HBS|StaticMeshAsset")
	FHBSBackfaceOrientationInfo BackfaceOrientation;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "HBS|StaticMeshAsset")
	TArray<FHBSPlaceablePlaneInfo> PlaceablePlaneList;
};
