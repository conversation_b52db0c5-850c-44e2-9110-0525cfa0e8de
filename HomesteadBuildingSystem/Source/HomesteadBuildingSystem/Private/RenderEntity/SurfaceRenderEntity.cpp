#include "RenderEntity/SurfaceRenderEntity.h"
#include "Components/DynamicMeshComponent.h"
#include "SceneEntity/FlooringSceneEntity.h"
#include "GeometryScript/MeshUVFunctions.h"
#include "GeometryScript/MeshPrimitiveFunctions.h"
#include "GeometryScript/MeshNormalsFunctions.h"
#include "GeometryScript/MeshMaterialFunctions.h"
#include "HBSConfig.h"
#include "SceneEntity/CeilingSceneEntity.h"
#include "RenderEntity/Library/RenderEntityFunctionLibrary.h"

AHBSSurfaceRenderEntity::AHBSSurfaceRenderEntity(const FObjectInitializer& ObjectInitializer)
{
	PrimaryActorTick.bCanEverTick = false;

	RootComponent = SurfaceMeshComponent = CreateDefaultSubobject<UDynamicMeshComponent>(TEXT("Surface Mesh"));

	URenderEntityFunctionLibrary::SetupPrimitiveComponentCollision(SurfaceMeshComponent);
}

void AHBSSurfaceRenderEntity::UpdateSurfaceMeshInfo(UHBSSurfaceSceneEntity* InSurfaceEntity)
{
	RelativeSceneEntityList = TArray<UHBSSceneEntityBase*>{ InSurfaceEntity };

	UDynamicMesh* DynamicMesh = SurfaceMeshComponent->GetDynamicMesh();
	DynamicMesh->Reset();

	UGeometryScriptLibrary_MeshMaterialFunctions::EnableMaterialIDs(DynamicMesh);
	UGeometryScriptLibrary_MeshUVFunctions::SetNumUVSets(DynamicMesh, 3);

	TArray<FVector2D> SurfaceVertices = InSurfaceEntity->GetVertices();
	float SurfaceHeight = InSurfaceEntity->GetSurfaceHeight();
	float SurfaceThickness = InSurfaceEntity->GetSurfaceThickness();
	SurfaceThickness = FMath::Max(SurfaceThickness, 1);

	if (UHBSCeilingSceneEntity* CeilingSceneEntity = Cast<UHBSCeilingSceneEntity>(InSurfaceEntity))
		SurfaceHeight -= (SurfaceThickness - 0.01);

	if (UHBSFlooringSceneEntity* FlooringSceneEntity = Cast<UHBSFlooringSceneEntity>(InSurfaceEntity))
		SurfaceHeight += 0.01;

	FTransform Transform = FTransform(FQuat::Identity, FVector(0.f, 0.f, SurfaceHeight));

	UGeometryScriptLibrary_MeshPrimitiveFunctions::AppendSimpleExtrudePolygon(DynamicMesh, FGeometryScriptPrimitiveOptions(), Transform, SurfaceVertices, SurfaceThickness);

	FVector2D Max = SurfaceVertices[0];
	FVector2D Min = SurfaceVertices[0];
	for (const FVector2D& Vertex : SurfaceVertices)
	{
		Max.X = FMath::Max(Max.X, Vertex.X);
		Max.Y = FMath::Max(Max.Y, Vertex.Y);
		Min.X = FMath::Min(Min.X, Vertex.X);
		Min.Y = FMath::Min(Min.Y, Vertex.Y);
	}

	float MaxXY = FMath::Max(Max.X - Min.X, Max.Y - Min.Y);

	FTransform UV0PlanarTransform = FTransform(FQuat::Identity, FVector(0, 0, 0), FVector(100, 100, 1));
	FTransform UV2PlanarTransform = FTransform(FQuat::Identity, FVector(Min.X, Min.Y, 0), FVector(MaxXY, MaxXY, 1));

	UGeometryScriptLibrary_MeshUVFunctions::SetNumUVSets(DynamicMesh, 3);
	UGeometryScriptLibrary_MeshUVFunctions::SetMeshUVsFromPlanarProjection(DynamicMesh, 0, UV0PlanarTransform, FGeometryScriptMeshSelection());
	UGeometryScriptLibrary_MeshUVFunctions::SetMeshUVsFromPlanarProjection(DynamicMesh, 2, UV2PlanarTransform, FGeometryScriptMeshSelection());

	UGeometryScriptLibrary_MeshMaterialFunctions::ClearMaterialIDs(DynamicMesh, 0);
	UGeometryScriptLibrary_MeshNormalsFunctions::SetPerFaceNormals(DynamicMesh);

	SurfaceMeshComponent->EnableComplexAsSimpleCollision();
	SurfaceMeshComponent->SetMaterial(0, UHBSConfig::Get()->GetDefaultFloorMaterial());
}
