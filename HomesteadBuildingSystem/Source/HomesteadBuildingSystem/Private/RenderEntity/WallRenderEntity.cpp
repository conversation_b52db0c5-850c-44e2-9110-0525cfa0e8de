#include "RenderEntity/WallRenderEntity.h"
#include "GeometryScript/MeshPrimitiveFunctions.h"
#include "HBSConfig.h"
#include "Kismet/KismetMathLibrary.h"
#include "SceneEntity/WallSceneEntity.h"
#include "SceneEntity/WindowSceneEntity.h"
#include "GeometryScript/MeshPolygroupFunctions.h"
#include "GeometryScript/MeshMaterialFunctions.h"
#include "GeometryScript/MeshUVFunctions.h"
#include "UGCProfile.h"
#include "UGCComponent.h"
#include "GeometryScript/MeshNormalsFunctions.h"
#include "HBSWorldSystem.h"
#include "Kismet/GameplayStatics.h"
#include "GeometryScript/MeshBasicEditFunctions.h"
#include "RenderEntity/Library/RenderEntityFunctionLibrary.h"

AHBSWallRenderEntity::AHBSWallRenderEntity(const FObjectInitializer& ObjectInitializer)
{
	PrimaryActorTick.bCanEverTick = false;

	// Create a Procedural Mesh Component
	RootComponent = WallMesh = CreateDefaultSubobject<UDynamicMeshComponent>(TEXT("WallMesh"));
	TempDynamicMesh = CreateDefaultSubobject<UDynamicMesh>(TEXT("Temp Dynamic Mesh"));

	URenderEntityFunctionLibrary::SetupPrimitiveComponentCollision(WallMesh);
}

void AHBSWallRenderEntity::UpdateWallInfo(const FHBSProcessedWallVertices& InWallVertices, UHBSWallSceneEntity* InWallSceneEntity, const TArray<UHBSWindowSceneEntity*>& InWindowSceneEntity)
{
	RelativeSceneEntityList = TArray<UHBSSceneEntityBase*>{ InWallSceneEntity };
	RelativeSceneEntityList.Append(InWindowSceneEntity);
	ProcessedWallVertices = InWallVertices;
	WallSceneEntity = InWallSceneEntity;
	WindowSceneEntityList = InWindowSceneEntity;
	CurrentVisibility = EHBSWallVisibility::Visibile;

	GenerateDynamicMesh();

	UMaterialInterface* DefaultWallMaterial = UHBSConfig::Get()->GetDefaultWallMaterial();
	UMaterialInterface* PreviewMaterial = UHBSConfig::Get()->GetPreviewMaterial();

	UMaterialInterface* Material = WallSceneEntity->GetIsPreview() ? PreviewMaterial : DefaultWallMaterial;

	WallMesh->SetMaterial(0, Material);
	WallMesh->SetMaterial(1, Material);
	WallMesh->SetMaterial(2, Material);

	if (WallSceneEntity->GetIsPreview() == false)
	{
		InitUGC(WallSceneEntity);
	}
}

void AHBSWallRenderEntity::SetWallVisibility(EHBSWallVisibility InVisibility)
{
	if (InVisibility != CurrentVisibility)
	{
		if (CurrentVisibility == EHBSWallVisibility::FootOnly || InVisibility == EHBSWallVisibility::FootOnly)
		{
			CurrentVisibility = InVisibility;
			GenerateDynamicMesh();
		}

		CurrentVisibility = InVisibility;

		bool IsHiddenInGame = CurrentVisibility == EHBSWallVisibility::Hidden;
		SetRenderEntityVisible(!IsHiddenInGame);
	}
}

void AHBSWallRenderEntity::GenerateDynamicMesh()
{
	UDynamicMesh* DynamicMesh = WallMesh->GetDynamicMesh();
	DynamicMesh->Reset();

	UGeometryScriptLibrary_MeshPolygroupFunctions::EnablePolygroups(DynamicMesh);
	UGeometryScriptLibrary_MeshMaterialFunctions::EnableMaterialIDs(DynamicMesh);
	UGeometryScriptLibrary_MeshMaterialFunctions::ClearMaterialIDs(DynamicMesh, 2);

	FVector2D Vertices[] = {
		ProcessedWallVertices.Vertex0,
		ProcessedWallVertices.Vertex1,
		ProcessedWallVertices.Vertex2,
		ProcessedWallVertices.Vertex3,
	};

	float StartHeight = WallSceneEntity->GetWallStartHeight();
	float WallHeight = (CurrentVisibility != EHBSWallVisibility::FootOnly) ? WallSceneEntity->WallHeight : 75.f;
	TArray<FVector> BottonVertices =
	{
		FVector(Vertices[0].X, Vertices[0].Y, StartHeight),
		FVector(Vertices[1].X, Vertices[1].Y, StartHeight),
		FVector(Vertices[2].X, Vertices[2].Y, StartHeight),
		FVector(Vertices[3].X, Vertices[3].Y, StartHeight),
	};

	TArray<FVector> TopVertices =
	{
		FVector(Vertices[0].X, Vertices[0].Y, StartHeight + WallHeight),
		FVector(Vertices[1].X, Vertices[1].Y, StartHeight + WallHeight),
		FVector(Vertices[2].X, Vertices[2].Y, StartHeight + WallHeight),
		FVector(Vertices[3].X, Vertices[3].Y, StartHeight + WallHeight),
	};

	FGeometryScriptPrimitiveOptions PrimitiveOptions;
	PrimitiveOptions.PolygroupMode = EGeometryScriptPrimitivePolygroupMode::PerQuad;

	TArray<FVector> SortedTopVertices =
	{
		TopVertices[3],
		TopVertices[2],
		TopVertices[1],
		TopVertices[0],
	};

	UGeometryScriptLibrary_MeshPrimitiveFunctions::AppendTriangulatedPolygon3D(DynamicMesh, PrimitiveOptions, FTransform::Identity, BottonVertices);
	UGeometryScriptLibrary_MeshPrimitiveFunctions::AppendTriangulatedPolygon3D(DynamicMesh, PrimitiveOptions, FTransform::Identity, SortedTopVertices);

	int32 OtherFaceIndexList[2] = {};
	int32 ImportantFaceIndexList[2] = {};

	for (int32 i = 0, j = 0; i < 4; ++i)
	{
		if (ProcessedWallVertices.InsideWallIndex == i)
			ImportantFaceIndexList[0] = i;
		else if (ProcessedWallVertices.OutsideWallIndex == i)
			ImportantFaceIndexList[1] = i;
		else
		{
			OtherFaceIndexList[j++] = i;
		}
	}

	for (int32 FaceIndex : OtherFaceIndexList)
	{
		TArray<FVector> Polygon =
		{
			TopVertices[FaceIndex],
			TopVertices[(FaceIndex + 1) % 4],
			BottonVertices[(FaceIndex + 1) % 4],
			BottonVertices[FaceIndex],
		};

		if (FVector::DotProduct(Polygon[2] - Polygon[3], Polygon[2] - Polygon[3]) < 1)
			continue;

		UGeometryScriptLibrary_MeshPrimitiveFunctions::AppendTriangulatedPolygon3D(DynamicMesh, PrimitiveOptions, FTransform::Identity, Polygon);
	}

	UGeometryScriptLibrary_MeshMaterialFunctions::ClearMaterialIDs(DynamicMesh, 2);

	for (int32 FaceIndex : ImportantFaceIndexList)
	{
		TArray<FVector> Polygon =
		{
			TopVertices[FaceIndex],
			TopVertices[(FaceIndex + 1) % 4],
			BottonVertices[(FaceIndex + 1) % 4],
			BottonVertices[FaceIndex],
		};

		if (FVector::DotProduct(Polygon[2] - Polygon[3], Polygon[2] - Polygon[3]) < 1)
			continue;

		TempDynamicMesh->Reset();

		UGeometryScriptLibrary_MeshPrimitiveFunctions::AppendTriangulatedPolygon3D(TempDynamicMesh, PrimitiveOptions, FTransform::Identity, Polygon);

		if (FaceIndex == ProcessedWallVertices.InsideWallIndex)
			UGeometryScriptLibrary_MeshMaterialFunctions::ClearMaterialIDs(TempDynamicMesh, 0);

		if (FaceIndex == ProcessedWallVertices.OutsideWallIndex)
			UGeometryScriptLibrary_MeshMaterialFunctions::ClearMaterialIDs(TempDynamicMesh, 1);

		FVector UpVector = FVector::UpVector;
		FVector WallNormal = WallSceneEntity->GetWallNormal3D();
		if (FaceIndex == ProcessedWallVertices.InsideWallIndex)
			WallNormal = -WallNormal;
		FVector WallDir = UpVector ^ WallNormal;

		FTransform UV0PlanarTransform = FTransform(UKismetMathLibrary::MakeRotFromXY(-WallDir, -UpVector), FVector::ZeroVector, FVector(100, 100, 1));

		float WallLength = FVector::Distance(Polygon[0], Polygon[1]);
		float MaxXY = FMath::Max(WallLength, WallSceneEntity->WallHeight);

		FVector Center = (Polygon[0] + Polygon[1] + Polygon[2] + Polygon[3]) * 0.25f;
		FVector Origin = Center + 0.5 * UpVector * MaxXY + 0.5 * WallDir * MaxXY;
		FTransform UV2PlanarTransform = FTransform(UKismetMathLibrary::MakeRotFromXY(-WallDir, -UpVector), Origin, FVector(MaxXY, MaxXY, 1));

		UGeometryScriptLibrary_MeshUVFunctions::SetNumUVSets(TempDynamicMesh, 3);
		UGeometryScriptLibrary_MeshUVFunctions::SetMeshUVsFromPlanarProjection(TempDynamicMesh, 0, UV0PlanarTransform, FGeometryScriptMeshSelection());
		UGeometryScriptLibrary_MeshUVFunctions::SetMeshUVsFromPlanarProjection(TempDynamicMesh, 2, UV2PlanarTransform, FGeometryScriptMeshSelection());

		UGeometryScriptLibrary_MeshBasicEditFunctions::AppendMesh(DynamicMesh, TempDynamicMesh, FTransform::Identity);
	}

	float WallWidth = UHBSConfig::Get()->WallWidth;
	for (UHBSWindowSceneEntity* WindowSceneEntity : WindowSceneEntityList)
	{
		if (WindowSceneEntity->GetRelativeWall() != WallSceneEntity)
			continue;

		FVector2D Start = WallSceneEntity->GetWallStart();
		FVector2D End = WallSceneEntity->GetWallEnd();

		if (WallSceneEntity->IsPointWall())
			continue;

		FVector XDir = WallSceneEntity->GetWallVector3D();
		FVector ZDir = FVector::UpVector;
		FVector YDir = WallSceneEntity->GetWallNormal3D();

		FVector2D WindowSize = WindowSceneEntity->GetWindowSizeWithScale();

		FTransform Transform = FTransform(UKismetMathLibrary::MakeRotFromXY(XDir, ZDir), WindowSceneEntity->CalcWindowWorldPosition() + YDir * WallWidth * 0.5);

		TempDynamicMesh->Reset();

		TArray<FVector2D> WindowVertices =
		{
			FVector2D(WindowSize.X * 0.5f, -WindowSize.Y * 0.5f),
			FVector2D(WindowSize.X * 0.5f, WindowSize.Y * 0.5f),
			FVector2D(-WindowSize.X * 0.5f, WindowSize.Y * 0.5f),
			FVector2D(-WindowSize.X * 0.5f, -WindowSize.Y * 0.5f),
		};

		UGeometryScriptLibrary_MeshPrimitiveFunctions::AppendSimpleExtrudePolygon(TempDynamicMesh, FGeometryScriptPrimitiveOptions(), Transform, WindowVertices, WallWidth);
		
		UGeometryScriptLibrary_MeshMaterialFunctions::ClearMaterialIDs(TempDynamicMesh, 2);

		UGeometryScriptLibrary_MeshBooleanFunctions::ApplyMeshBoolean(DynamicMesh, FTransform(), TempDynamicMesh, FTransform(), EGeometryScriptBooleanOperation::Subtract, FGeometryScriptMeshBooleanOptions());
	}

	UGeometryScriptLibrary_MeshNormalsFunctions::SetPerFaceNormals(DynamicMesh);

	WallMesh->EnableComplexAsSimpleCollision();
}

void AHBSWallRenderEntity::InitUGC(UHBSWallSceneEntity* InWallSceneEntity)
{
	if (InWallSceneEntity && InWallSceneEntity->WallUGCProfile)
	{
		if (!UGCComponent)
		{
			UGCComponent = Cast<UUGCComponent>(AddComponentByClass(UUGCComponent::StaticClass(), false, FTransform::Identity, false));
		}

		UGCComponent->SetUGCAsset(InWallSceneEntity->WallUGCProfile, false);
		UGCComponent->SetUGCMeshComponents(TArray<UMeshComponent*>{ WallMesh });
		UGCComponent->SetUGCUserData(InWallSceneEntity->GetOrCreateUGCUserData());
	}


	//for (int32 i = UGCComponentList.Num(); i < WallUGCCount; ++i)
	//{
	//	UUGCComponent* NewUGCComponent = Cast<UUGCComponent>(AddComponentByClass(UUGCComponent::StaticClass(), false, FTransform::Identity, false));

	//	int32 MaterialSlotIndex = i + 1;
	//	UUGCProfile* NewProfile = NewObject<UUGCProfile>(this, NAME_None);
	//	NewProfile->CreateDyeProfile()->AddMaterialMapping(0, FMaterialBindingInfo(NAME_None, MaterialSlotIndex), FMaterialParameterInfo(TEXT("")));
	//	NewProfile->CreatePatternProfile()->AddMaterialMapping(0, FMaterialBindingInfo(NAME_None, MaterialSlotIndex));
	//	NewProfile->CreateAppliqueProfile()->AddMaterialMapping(0, FMaterialBindingInfo(NAME_None, MaterialSlotIndex));
	//	//NewProfile->CreateFabricProfile()->AddMaterialMapping(0, FMaterialBindingInfo(NAME_None, MaterialSlotIndex));

	//	NewUGCComponent->SetUGCAsset(NewProfile);
	//	NewUGCComponent->SetUGCMeshComponents(TArray<UMeshComponent*>{ WallMesh });
	//	UGCComponentList.Add(NewUGCComponent);
	//	UGCProfileList.Add(NewProfile);
	//}

	//for (int32 i = 0; i != WallSceneEntityList.Num(); ++i)
	//{
	//	UUGCUserData* InsideUserData = WallSceneEntityList[i]->WallUserDataInside;
	//	UUGCUserData* OutsideUserData = WallSceneEntityList[i]->WallUserDataOutside;
	//	InsideUserData->Profile = UGCProfileList[i * 2];
	//	OutsideUserData->Profile = UGCProfileList[i * 2 + 1];
	//	InsideUserData->BroadCastUpdate();
	//	OutsideUserData->BroadCastUpdate();
	//	UGCComponentList[i * 2]->InitUGC(InsideUserData);
	//	UGCComponentList[i * 2 + 1]->InitUGC(OutsideUserData);
	//}
}
