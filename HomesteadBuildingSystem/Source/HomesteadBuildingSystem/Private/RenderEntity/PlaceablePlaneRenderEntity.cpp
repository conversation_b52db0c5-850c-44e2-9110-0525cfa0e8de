#include "RenderEntity/PlaceablePlaneRenderEntity.h"
#include "SceneEntity/PlaceablePlaneSceneEntity.h"
#include "GeometryScript/MeshPrimitiveFunctions.h"
#include "UDynamicMesh.h"

void AHBSPlaceablePlaneRenderEntity::UpdatePlaceablePlaneInfo(UHBSPlaceablePlaneSE* InPlaceablePlane)
{
	RelativeSceneEntityList = TArray<UHBSSceneEntityBase*>{ InPlaceablePlane };

	UDynamicMesh* DynamicMesh = PlaceablePlaneMeshComponent->GetDynamicMesh();
	DynamicMesh->Reset();

	FTransform Transform;
	
	if (InPlaceablePlane->GetPlaneType() == EHBSPlaceablePlaneType::Ceiling)
		Transform = FTransform(FVector(0, 0, InPlaceablePlane->GetPlaneHeight() - 1));
	else
		Transform = FTransform(FVector(0, 0, InPlaceablePlane->GetPlaneHeight() + 1));

	UGeometryScriptLibrary_MeshPrimitiveFunctions::AppendTriangulatedPolygon(DynamicMesh, FGeometryScriptPrimitiveOptions(), Transform, InPlaceablePlane->GetVertices());

	UMaterialInterface* Material = UHBSConfig::Get()->GetPlaceablePlaneMaterial();
	PlaceablePlaneMeshComponent->SetMaterial(0,  Material);
}
