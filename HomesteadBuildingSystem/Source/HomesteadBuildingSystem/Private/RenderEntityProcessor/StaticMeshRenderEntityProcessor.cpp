#include "RenderEntityProcessor/StaticMeshRenderEntityProcessor.h"
#include "SceneEntity/WindowSceneEntity.h"
#include "SceneEntity/StaticMeshSceneEntity.h"
#include "RenderEntity/StaticMeshRenderEntity.h"
#include "Kismet/KismetMathLibrary.h"
#include "SceneEntity/WallSceneEntity.h"

void UHBSStaticMeshRenderEntityProcessor::Process(UHBSRenderEntityProcessorContext* Context)
{
	for (int32 i = 0; i != 2; ++i)
	{
		bool IsPreview = i % 2 == 0;
		for (UHBSSceneEntityBase* Entity : Context->GetNewSceneEntityList(IsPreview))
		{
			if (UHBSStaticMeshSceneEntity* StaticMeshEntity = Cast<UHBSStaticMeshSceneEntity>(Entity))
			{
				AHBSStaticMeshRenderEntity* RenderEntity = Cast<AHBSStaticMeshRenderEntity>(Context->RequestRenderEntityByClass(AHBSStaticMeshRenderEntity::StaticClass()));

				RenderEntity->UpdateStaticMeshInfo(StaticMeshEntity->GetStaticMesh(), StaticMeshEntity->GetTransform(), StaticMeshEntity, IsPreview, StaticMeshEntity->IsErrorPreview);
			}

			if (UHBSWindowSceneEntity* WindowEntity = Cast<UHBSWindowSceneEntity>(Entity))
			{
				AHBSStaticMeshRenderEntity* RenderEntity = Cast<AHBSStaticMeshRenderEntity>(Context->RequestRenderEntityByClass(AHBSStaticMeshRenderEntity::StaticClass()));

				UHBSWallSceneEntity* RelativeWall = WindowEntity->GetRelativeWall();
				UStaticMesh* WindowMesh = WindowEntity->GetWindowMesh();
				FVector WindowPosition = WindowEntity->CalcWindowWorldPosition();
				FVector2D WindowScale = WindowEntity->GetWindowScale();
				FVector WallVector = RelativeWall->GetWallVector3D();
				FVector WallNormal = RelativeWall->GetWallNormal3D();

				FTransform Transform(UKismetMathLibrary::MakeRotFromXY(WallVector, WallNormal), WindowPosition, FVector(WindowScale.X, 1, WindowScale.Y));

				RenderEntity->UpdateStaticMeshInfo(WindowMesh, Transform, WindowEntity, false);
			}
		}

		for (UHBSSceneEntityBase* Entity : Context->GetModifiedSceneEntityList(IsPreview))
		{
			if (UHBSStaticMeshSceneEntity* StaticMeshEntity = Cast<UHBSStaticMeshSceneEntity>(Entity))
			{
				AHBSStaticMeshRenderEntity* RenderEntity = Cast<AHBSStaticMeshRenderEntity>(Context->GetRelatedRenderEntityByClass(StaticMeshEntity, AHBSStaticMeshRenderEntity::StaticClass()));

				RenderEntity->UpdateStaticMeshInfo(StaticMeshEntity->GetStaticMesh(), StaticMeshEntity->GetTransform(), StaticMeshEntity, IsPreview, StaticMeshEntity->IsErrorPreview);

				Context->MarkRenderEntityModified(RenderEntity);
			}

			if (IsPreview == false)
			{
				if (UHBSWindowSceneEntity* WindowEntity = Cast<UHBSWindowSceneEntity>(Entity))
				{
					AHBSStaticMeshRenderEntity* RenderEntity = Cast<AHBSStaticMeshRenderEntity>(Context->GetRelatedRenderEntityByClass(WindowEntity, AHBSStaticMeshRenderEntity::StaticClass()));

					UHBSWallSceneEntity* RelativeWall = WindowEntity->GetRelativeWall();
					UStaticMesh* WindowMesh = WindowEntity->GetWindowMesh();
					FVector WindowPosition = WindowEntity->CalcWindowWorldPosition();
					FVector2D WindowScale = WindowEntity->GetWindowScale();
					FVector WallVector = RelativeWall->GetWallVector3D();
					FVector WallNormal = RelativeWall->GetWallNormal3D();

					FTransform Transform(UKismetMathLibrary::MakeRotFromXY(WallVector, WallNormal), WindowPosition, FVector(WindowScale.X, 1, WindowScale.Y));

					RenderEntity->UpdateStaticMeshInfo(WindowMesh, Transform, WindowEntity, false);
					Context->MarkRenderEntityModified(RenderEntity);
				}
			}
		}
	}
}
