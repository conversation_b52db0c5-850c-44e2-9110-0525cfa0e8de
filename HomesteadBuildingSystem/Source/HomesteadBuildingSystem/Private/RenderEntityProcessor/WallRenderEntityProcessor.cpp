#include "RenderEntityProcessor/WallRenderEntityProcessor.h"
#include "SceneEntity/WallSceneEntity.h"
#include "RenderEntity/WallRenderEntity.h"
#include "SceneEntity/WindowSceneEntity.h"
#include "HBSMathLibrary.h"
#include "SceneEntity/SurfaceSceneEntity.h"

void UHBSWallRenderEntityProcessor::Process(UHBSRenderEntityProcessorContext* Context)
{
	if (Context->HasAnyEntityChangedByClass(UHBSWallSceneEntity::StaticClass()))
	{
		TArray<UHBSWallSceneEntity*> AllWallList = Context->GetTypedAllSceneEntityList<UHBSWallSceneEntity>();
		TArray<UHBSWindowSceneEntity*> WindowList = Context->GetTypedAllSceneEntityList<UHBSWindowSceneEntity>();

		TMap<int32, TArray<UHBSWallSceneEntity*>> StartHeight2WallMap;
		for (UHBSWallSceneEntity* Wall : AllWallList)
		{
			StartHeight2WallMap.FindOrAdd(Wall->GetWallStartHeight()).Add(Wall);
		}

		for (auto& It : StartHeight2WallMap)
		{
			TArray<UHBSWallSceneEntity*>& WallList = It.Value;

			TArray<FHBSWallEndPointInfo> WallEndPointInfoList;
			TArray<FHBSEndPointWallGroup> EndPointWallGroupList;
			GenerateEndPointWallGroup(WallList, WallEndPointInfoList, EndPointWallGroupList);

			TArray<FHBSProcessedWallVertices> ProcessedWallVerticesList;
			GenerateProcessedWallVerticesList(WallList, WallEndPointInfoList, EndPointWallGroupList, ProcessedWallVerticesList);

			TArray<TArray<UHBSWindowSceneEntity*> > RelativeWindowList;
			GenerateRelativeWindowList(WallList, WindowList, RelativeWindowList);

			for (int32 i = 0; i != WallList.Num(); ++i)
			{
				const FHBSProcessedWallVertices& ProcessedWallVertices = ProcessedWallVerticesList[i];
				const TArray<UHBSWindowSceneEntity*>& RelativeWindow = RelativeWindowList[i];

				AHBSWallRenderEntity* WallRenderEntity = Cast<AHBSWallRenderEntity>(Context->GetRelatedRenderEntity(ProcessedWallVertices.WallSceneEntity));

				if (WallRenderEntity == nullptr)
					WallRenderEntity = Cast<AHBSWallRenderEntity>(Context->RequestRenderEntityByClass(AHBSWallRenderEntity::StaticClass()));

				WallRenderEntity->UpdateWallInfo(ProcessedWallVertices, ProcessedWallVertices.WallSceneEntity, RelativeWindow);

				Context->MarkRenderEntityModified(WallRenderEntity);
			}
		}
	}

	for (UHBSSceneEntityBase* SceneEntity : Context->GetNewSceneEntityList(true))
	{
		if (UHBSWallSceneEntity* WallEntity = Cast<UHBSWallSceneEntity>(SceneEntity))
		{
			AHBSWallRenderEntity* WallRenderEntity = Cast<AHBSWallRenderEntity>(Context->RequestRenderEntityByClass(AHBSWallRenderEntity::StaticClass()));

			FHBSProcessedWallVertices WallVertices = GenerateProcessedWallVerticesForSingleWall(WallEntity);

			WallRenderEntity->UpdateWallInfo(WallVertices, WallEntity, TArray<UHBSWindowSceneEntity*>());
		}
	}

	for (UHBSSceneEntityBase* SceneEntity : Context->GetModifiedSceneEntityList(true))
	{
		if (UHBSWallSceneEntity* WallEntity = Cast<UHBSWallSceneEntity>(SceneEntity))
		{
			AHBSWallRenderEntity* WallRenderEntity = Cast<AHBSWallRenderEntity>(Context->GetRelatedRenderEntity(SceneEntity));

			FHBSProcessedWallVertices WallVertices = GenerateProcessedWallVerticesForSingleWall(WallEntity);

			WallRenderEntity->UpdateWallInfo(WallVertices, WallEntity, TArray<UHBSWindowSceneEntity*>());

			Context->MarkRenderEntityModified(WallRenderEntity);
		}
	}
}

void UHBSWallRenderEntityProcessor::GenerateEndPointWallGroup(const TArray<UHBSWallSceneEntity*>& WallList, TArray<FHBSWallEndPointInfo>& OutWallEndPointInfoList, TArray<FHBSEndPointWallGroup>& OutEndPointWallGroupList)
{
	for (int32 i = 0; i != WallList.Num(); ++i)
	{
		FVector2D WallStart = WallList[i]->WallStart;
		FVector2D WallEnd = WallList[i]->WallEnd;

		FHBSWallEndPointInfo WallEndPointInfo0;
		WallEndPointInfo0.EndPoint = WallStart;
		WallEndPointInfo0.AnotherEndPoint = WallEnd;
		WallEndPointInfo0.WallIndex = i;
		WallEndPointInfo0.IsWallStart = true;
		WallEndPointInfo0.WallVector = (WallEnd - WallStart).GetSafeNormal();
		WallEndPointInfo0.WallNormal = WallList[i]->GetWallNormal();

		FHBSWallEndPointInfo WallEndPointInfo1;
		WallEndPointInfo1.EndPoint = WallEnd;
		WallEndPointInfo1.AnotherEndPoint = WallStart;
		WallEndPointInfo1.WallIndex = i;
		WallEndPointInfo1.IsWallStart = false;
		WallEndPointInfo1.WallVector = (WallStart - WallEnd).GetSafeNormal();
		WallEndPointInfo1.WallNormal = WallList[i]->GetWallNormal();

		if (WallList[i]->IsPointWall())
		{
			WallEndPointInfo0.WallVector = FVector2D(1, 0);
			WallEndPointInfo1.WallVector = FVector2D(-1, 0);

			WallEndPointInfo0.WallNormal = FVector2D(0, 1);
			WallEndPointInfo1.WallNormal = FVector2D(0, 1);
		}

		OutWallEndPointInfoList.Add(WallEndPointInfo0);
		OutWallEndPointInfoList.Add(WallEndPointInfo1);
	}

	float WallWidth = UHBSConfig::Get()->WallWidth;
	TArray<FHBSWallMeetingPoint> WallMeetingPointList;
	for (int32 i = 0; i != OutWallEndPointInfoList.Num(); ++i)
	{
		FHBSWallEndPointInfo WallEndPointInfo0 = OutWallEndPointInfoList[i];

		if (WallEndPointInfo0.EndPoint == WallEndPointInfo0.AnotherEndPoint)
			continue;

		for (int32 j = 0; j != OutWallEndPointInfoList.Num(); ++j)
		{
			FHBSWallEndPointInfo WallEndPointInfo1 = OutWallEndPointInfoList[j];

			if (WallEndPointInfo1.EndPoint == WallEndPointInfo1.AnotherEndPoint)
				continue;

			if (WallEndPointInfo0.WallIndex == WallEndPointInfo1.WallIndex)
				continue;

			if (FVector2D::DistSquared(WallEndPointInfo0.EndPoint, WallEndPointInfo1.EndPoint) < WallWidth * WallWidth)
			{
				FHBSWallMeetingPoint MeetingPoint;
				MeetingPoint.EndPointIndex0 = i;
				MeetingPoint.EndPointIndex1 = j;
				MeetingPoint.AngleDot = FVector2D::DotProduct(WallEndPointInfo0.WallVector, WallEndPointInfo1.WallVector);

				if (UHBSMathLibrary::IsVectorParallel(WallEndPointInfo0.WallVector, WallEndPointInfo1.WallVector))
				{
					MeetingPoint.MeetingPoint = (WallEndPointInfo0.EndPoint + WallEndPointInfo1.EndPoint) * 0.5;
				}
				else
				{
					MeetingPoint.MeetingPoint = UHBSMathLibrary::CalcLineIntersection(WallEndPointInfo0.EndPoint, WallEndPointInfo0.WallVector, WallEndPointInfo1.EndPoint, WallEndPointInfo1.WallVector);
				}

				WallMeetingPointList.Add(MeetingPoint);
			}
		}
	}

	Algo::SortBy(WallMeetingPointList, [](const FHBSWallMeetingPoint& MeetingPoint) { return MeetingPoint.AngleDot; });

	TArray<bool> IsWallEndPointInGroup;
	IsWallEndPointInGroup.SetNum(OutWallEndPointInfoList.Num());
	for (int32 i = 0; i != WallMeetingPointList.Num(); ++i)
	{
		const FHBSWallMeetingPoint& MeetingPoint = WallMeetingPointList[i];

		if (IsWallEndPointInGroup[MeetingPoint.EndPointIndex0])
			continue;

		if (IsWallEndPointInGroup[MeetingPoint.EndPointIndex1])
			continue;

		FHBSEndPointWallGroup EndPointWallGroup;

		EndPointWallGroup.EndPointIndex.Add(MeetingPoint.EndPointIndex0);
		EndPointWallGroup.EndPointIndex.Add(MeetingPoint.EndPointIndex1);

		IsWallEndPointInGroup[MeetingPoint.EndPointIndex0] = true;
		IsWallEndPointInGroup[MeetingPoint.EndPointIndex1] = true;

		for (int32 j = 0; j != OutWallEndPointInfoList.Num(); ++j)
		{
			if (IsWallEndPointInGroup[j])
				continue;

			const FHBSWallEndPointInfo& EndPointInfo = OutWallEndPointInfoList[j];

			if (EndPointInfo.EndPoint == EndPointInfo.AnotherEndPoint)
				continue;

			if (FVector2D::Distance(EndPointInfo.EndPoint, MeetingPoint.MeetingPoint) < WallWidth)
			{
				IsWallEndPointInGroup[j] = true;
				EndPointWallGroup.EndPointIndex.Add(j);
			}
		}

		OutEndPointWallGroupList.Add(EndPointWallGroup);
	}

	for (int32 i = 0; i != OutWallEndPointInfoList.Num(); ++i)
	{
		if (IsWallEndPointInGroup[i])
			continue;

		FHBSEndPointWallGroup EndPointWallGroup;
		EndPointWallGroup.EndPointIndex.Add(i);

		OutEndPointWallGroupList.Add(EndPointWallGroup);
	}
}

void UHBSWallRenderEntityProcessor::GenerateProcessedWallVerticesList(const TArray<UHBSWallSceneEntity*>& InWallList, const TArray<FHBSWallEndPointInfo>& InWallEndPointInfoList, const TArray<FHBSEndPointWallGroup>& InEndPointWallGroupList, TArray<FHBSProcessedWallVertices>& OutProcessedWallVerticesList)
{
	OutProcessedWallVerticesList.SetNum(InWallList.Num());

	for (int32 i = 0; i != InWallList.Num(); ++i)
	{
		OutProcessedWallVerticesList[i].WallSceneEntity = InWallList[i];
	}

	float HalfWallWidth = UHBSConfig::Get()->WallWidth * 0.5;
	for (const FHBSEndPointWallGroup& EndPointWallGroup : InEndPointWallGroupList)
	{
		if (EndPointWallGroup.EndPointIndex.Num() == 1)
		{
			int32 WallEndPointIndex = EndPointWallGroup.EndPointIndex[0];
			const FHBSWallEndPointInfo& WallEndPointInfo = InWallEndPointInfoList[WallEndPointIndex];

			FVector2D WallEndPoint = WallEndPointInfo.EndPoint;
			FVector2D WallVector = WallEndPointInfo.WallVector;
			FVector2D WallNormal = WallEndPointInfo.WallNormal;

			FVector2D OutsideVertice = WallEndPoint - WallVector * HalfWallWidth + WallNormal * HalfWallWidth;
			FVector2D InsideVertice = WallEndPoint - WallVector * HalfWallWidth - WallNormal * HalfWallWidth;
			EmitProcessedWallVerticesList(OutProcessedWallVerticesList, WallEndPointInfo.IsWallStart, WallEndPointInfo.WallIndex, OutsideVertice, InsideVertice);
		}
		else
		{
			int32 MainEndPointIndex0 = EndPointWallGroup.EndPointIndex[0];
			int32 MainEndPointIndex1 = EndPointWallGroup.EndPointIndex[1];

			const FHBSWallEndPointInfo& MainEndPoint0 = InWallEndPointInfoList[MainEndPointIndex0];
			const FHBSWallEndPointInfo& MainEndPoint1 = InWallEndPointInfoList[MainEndPointIndex1];

			bool NeedSwap0 = false;
			bool NeedSwap1 = false;

			if (UHBSMathLibrary::IsVectorParallel(MainEndPoint0.WallVector, MainEndPoint1.WallVector))
			{
				if (FVector2D::DotProduct(MainEndPoint0.WallNormal, MainEndPoint1.WallNormal) < 0)
				{
					NeedSwap0 = false;
					NeedSwap1 = true;
				}
			}
			else
			{
				FVector2D WallCooperateVector = -(MainEndPoint0.WallVector + MainEndPoint1.WallVector).GetSafeNormal();

				NeedSwap0 = FVector2D::DotProduct(WallCooperateVector, MainEndPoint0.WallNormal) < 0;
				NeedSwap1 = FVector2D::DotProduct(WallCooperateVector, MainEndPoint1.WallNormal) < 0;
			}

			FVector2D MainWall0Normal = NeedSwap0 ? -MainEndPoint0.WallNormal : MainEndPoint0.WallNormal;
			FVector2D MainWall1Normal = NeedSwap1 ? -MainEndPoint1.WallNormal : MainEndPoint1.WallNormal;

			FVector2D MainWallOutsideStart0 = MainEndPoint0.EndPoint + MainWall0Normal * HalfWallWidth;
			FVector2D MainWallInsideStart0 = MainEndPoint0.EndPoint - MainWall0Normal * HalfWallWidth;

			FVector2D MainWallOutsideStart1 = MainEndPoint1.EndPoint + MainWall1Normal * HalfWallWidth;
			FVector2D MainWallInsideStart1 = MainEndPoint1.EndPoint - MainWall1Normal * HalfWallWidth;

			FVector2D MainWallOutsideIntersection;
			FVector2D MainWallInsideIntersection;
			bool IsParallel = false;
			FVector2D MainWallCenterIntersectionForParallel;
			if (UHBSMathLibrary::IsVectorParallel(MainEndPoint0.WallVector, MainEndPoint1.WallVector))
			{
				MainWallOutsideIntersection = (MainWallOutsideStart0 + MainWallOutsideStart1) * 0.5;
				MainWallInsideIntersection = (MainWallInsideStart0 + MainWallInsideStart1) * 0.5;

				IsParallel = true;
				MainWallCenterIntersectionForParallel = (MainEndPoint0.EndPoint + MainEndPoint1.EndPoint) * 0.5;
			}
			else
			{
				MainWallOutsideIntersection = UHBSMathLibrary::CalcLineIntersection(MainWallOutsideStart0, MainEndPoint0.WallVector, MainWallOutsideStart1, MainEndPoint1.WallVector);
				MainWallInsideIntersection = UHBSMathLibrary::CalcLineIntersection(MainWallInsideStart0, MainEndPoint0.WallVector, MainWallInsideStart1, MainEndPoint1.WallVector);
			}

			FVector2D MainWallInside0 = NeedSwap0 ? MainWallOutsideIntersection : MainWallInsideIntersection;
			FVector2D MainWallOutside0 = NeedSwap0 ? MainWallInsideIntersection : MainWallOutsideIntersection;

			FVector2D MainWallInside1 = NeedSwap1 ? MainWallOutsideIntersection : MainWallInsideIntersection;
			FVector2D MainWallOutside1 = NeedSwap1 ? MainWallInsideIntersection : MainWallOutsideIntersection;

			EmitProcessedWallVerticesList(OutProcessedWallVerticesList, MainEndPoint0.IsWallStart, MainEndPoint0.WallIndex, MainWallOutside0, MainWallInside0);
			EmitProcessedWallVerticesList(OutProcessedWallVerticesList, MainEndPoint1.IsWallStart, MainEndPoint1.WallIndex, MainWallOutside1, MainWallInside1);

			for (int32 i = 2; i != EndPointWallGroup.EndPointIndex.Num(); ++i)
			{
				int32 WallEndPointIndex = EndPointWallGroup.EndPointIndex[i];
				const FHBSWallEndPointInfo& WallEndPointInfo = InWallEndPointInfoList[WallEndPointIndex];

				FVector2D WallOutsideStart = WallEndPointInfo.AnotherEndPoint + WallEndPointInfo.WallNormal * HalfWallWidth;
				FVector2D WallInsideStart = WallEndPointInfo.AnotherEndPoint - WallEndPointInfo.WallNormal * HalfWallWidth;

				FVector2D OutsideIntersection(0, 0);
				FVector2D InsideIntersection(0, 0);

				float OutsideIntersectionDistance = -FLT_MAX;
				float InsideIntersectionDistance = -FLT_MAX;

				if (IsParallel)
				{
					CalcAndMergeIntersectionForRayIntersection(WallOutsideStart, -WallEndPointInfo.WallVector, MainWallCenterIntersectionForParallel, MainEndPoint0.WallVector, OutsideIntersection, OutsideIntersectionDistance);
					CalcAndMergeIntersectionForRayIntersection(WallOutsideStart, -WallEndPointInfo.WallVector, MainWallCenterIntersectionForParallel, MainEndPoint1.WallVector, OutsideIntersection, OutsideIntersectionDistance);

					CalcAndMergeIntersectionForRayIntersection(WallInsideStart, -WallEndPointInfo.WallVector, MainWallCenterIntersectionForParallel, MainEndPoint0.WallVector, InsideIntersection, InsideIntersectionDistance);
					CalcAndMergeIntersectionForRayIntersection(WallInsideStart, -WallEndPointInfo.WallVector, MainWallCenterIntersectionForParallel, MainEndPoint1.WallVector, InsideIntersection, InsideIntersectionDistance);
				}
				else
				{
					CalcAndMergeIntersectionForRayIntersection(WallOutsideStart, -WallEndPointInfo.WallVector, MainWallOutsideIntersection, MainEndPoint0.WallVector, OutsideIntersection, OutsideIntersectionDistance);
					CalcAndMergeIntersectionForRayIntersection(WallOutsideStart, -WallEndPointInfo.WallVector, MainWallOutsideIntersection, MainEndPoint1.WallVector, OutsideIntersection, OutsideIntersectionDistance);

					CalcAndMergeIntersectionForRayIntersection(WallInsideStart, -WallEndPointInfo.WallVector, MainWallOutsideIntersection, MainEndPoint0.WallVector, InsideIntersection, InsideIntersectionDistance);
					CalcAndMergeIntersectionForRayIntersection(WallInsideStart, -WallEndPointInfo.WallVector, MainWallOutsideIntersection, MainEndPoint1.WallVector, InsideIntersection, InsideIntersectionDistance);
				}

				EmitProcessedWallVerticesList(OutProcessedWallVerticesList, WallEndPointInfo.IsWallStart, WallEndPointInfo.WallIndex, OutsideIntersection, InsideIntersection);
			}
		}
	}

	for (FHBSProcessedWallVertices& ProcessedWallVertices : OutProcessedWallVerticesList)
		SortProcessedWallVertices(ProcessedWallVertices);
}

void UHBSWallRenderEntityProcessor::GenerateRelativeWindowList(const TArray<UHBSWallSceneEntity*>& InWallList, const TArray<UHBSWindowSceneEntity*>& InWindowList, TArray<TArray<UHBSWindowSceneEntity*>>& OutRelativeWindowList)
{
	OutRelativeWindowList.SetNum(InWallList.Num());

	for (UHBSWindowSceneEntity* Window : InWindowList)
	{
		int32 WallIndex = InWallList.Find(Window->GetRelativeWall());
		if (WallIndex != INDEX_NONE)
			OutRelativeWindowList[WallIndex].Add(Window);
	}
}

FHBSProcessedWallVertices UHBSWallRenderEntityProcessor::GenerateProcessedWallVerticesForSingleWall(UHBSWallSceneEntity* InWall)
{
	float HalfWallWidth = UHBSConfig::Get()->WallWidth * 0.5f;

	if (InWall->GetIsPreview())
	{
		HalfWallWidth *= 1.01f;
	}

	FVector2D WallStart = InWall->GetWallStart();
	FVector2D WallEnd = InWall->GetWallEnd();
	FVector2D WallVector = InWall->GetWallVector();
	FVector2D WallNormal = InWall->GetWallNormal();

	FHBSProcessedWallVertices ProcessedWallVertices;
	ProcessedWallVertices.OutsideStartVertex = WallStart - WallVector * HalfWallWidth + WallNormal * HalfWallWidth;
	ProcessedWallVertices.InsideStartVertex = WallStart - WallVector * HalfWallWidth - WallNormal * HalfWallWidth;
	ProcessedWallVertices.OutsideEndVertex = WallEnd + WallVector * HalfWallWidth + WallNormal * HalfWallWidth;
	ProcessedWallVertices.InsideEndVertex = WallEnd + WallVector * HalfWallWidth - WallNormal * HalfWallWidth;
	ProcessedWallVertices.WallSceneEntity = InWall;

	SortProcessedWallVertices(ProcessedWallVertices);

	return ProcessedWallVertices;
}

void UHBSWallRenderEntityProcessor::EmitProcessedWallVerticesList(TArray<FHBSProcessedWallVertices>& ProcessedWallVerticesList, bool IsStartPoint, int32 WallIndex, FVector2D OutsideVertice, FVector2D InsideVertice)
{
	if (IsStartPoint)
	{
		ProcessedWallVerticesList[WallIndex].OutsideStartVertex = OutsideVertice;
		ProcessedWallVerticesList[WallIndex].InsideStartVertex = InsideVertice;
	}
	else
	{
		ProcessedWallVerticesList[WallIndex].OutsideEndVertex = OutsideVertice;
		ProcessedWallVerticesList[WallIndex].InsideEndVertex = InsideVertice;
	}
}

void UHBSWallRenderEntityProcessor::SortProcessedWallVertices(FHBSProcessedWallVertices& InOutWallVertices)
{
	struct FWallVertex
	{
		FVector2D Vertex;
		int32 VertexIndex;
	};

	FWallVertex WallVertices[] = {
		{ InOutWallVertices.OutsideStartVertex, 0 },
		{ InOutWallVertices.InsideStartVertex, 1 },
		{ InOutWallVertices.OutsideEndVertex, 2 },
		{ InOutWallVertices.InsideEndVertex, 3 },
	};

	FVector2D Center(0, 0);
	for (const FWallVertex& WallVertex : WallVertices)
		Center += WallVertex.Vertex;
	Center = Center / 4.0f;

	Algo::SortBy(WallVertices, [&](const FWallVertex& WallVertex)
		{
			return FMath::Atan2(WallVertex.Vertex.Y - Center.Y, WallVertex.Vertex.X - Center.X);
		});

	InOutWallVertices.Vertex0 = WallVertices[0].Vertex;
	InOutWallVertices.Vertex1 = WallVertices[1].Vertex;
	InOutWallVertices.Vertex2 = WallVertices[2].Vertex;
	InOutWallVertices.Vertex3 = WallVertices[3].Vertex;

	for (int32 j = 0; j != 4; ++j)
	{
		int32 Index0 = WallVertices[j].VertexIndex;
		int32 Index1 = WallVertices[(j + 1) % 4].VertexIndex;

		if ((Index0 == 0 && Index1 == 2) || (Index0 == 2 && Index1 == 0))
			InOutWallVertices.OutsideWallIndex = j;

		if ((Index0 == 1 && Index1 == 3) || (Index0 == 3 && Index1 == 1))
			InOutWallVertices.InsideWallIndex = j;
	}
}

void UHBSWallRenderEntityProcessor::CalcAndMergeIntersectionForRayIntersection(FVector2D WallStart, FVector2D WallVector, FVector2D MainWallStart, FVector2D MainWallVector, FVector2D& OutIntersection, float& OutDistance)
{
	FVector2D TempIntersection(0, 0);
	if (UHBSMathLibrary::CalcRayIntersection(WallStart, WallVector, MainWallStart, MainWallVector, TempIntersection))
	{
		float Distance = FVector2D::DotProduct(TempIntersection - WallStart, WallVector);
		if (Distance > OutDistance)
		{
			OutDistance = Distance;
			OutIntersection = TempIntersection;
		}
	}
}

void UHBSWallRenderEntityProcessor::CalcAndMergeIntersectionForLineRayIntersection(FVector2D WallStart, FVector2D WallVector, FVector2D MainWallStart, FVector2D MainWallVector, FVector2D& OutIntersection, float& OutDistance)
{
	FVector2D TempIntersection(0, 0);
	if (UHBSMathLibrary::CalcLineRayIntersection(MainWallStart, MainWallVector, WallStart, WallVector, TempIntersection))
	{
		float Distance = FVector2D::DotProduct(TempIntersection - WallStart, WallVector);
		if (Distance < OutDistance)
		{
			OutDistance = Distance;
			OutIntersection = TempIntersection;
		}
	}
}
