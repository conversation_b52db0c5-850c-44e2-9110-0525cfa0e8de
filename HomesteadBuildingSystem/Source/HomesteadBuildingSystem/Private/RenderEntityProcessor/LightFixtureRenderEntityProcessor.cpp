#include "RenderEntityProcessor/LightFixtureRenderEntityProcessor.h"
#include "HBSAsset/HBSLightFixtureAsset.h"
#include "RenderEntity/LightFixtureRenderEntity.h"
#include "SceneEntity/LightFixtureSceneEntity.h"

void UHBSLightFixtureRenderEntityProcessor::Process(UHBSRenderEntityProcessorContext* Context)
{
	TArray<UHBSLightFixtureSceneEntity*> NewLightFixtureSEList = Context->GetTypedNewSceneEntityList<UHBSLightFixtureSceneEntity>();
	for (UHBSLightFixtureSceneEntity* LightFixtureSE : NewLightFixtureSEList)
	{
		UHBSLightFixtureAsset* LightFixtureAsset = LightFixtureSE->GetLightFixtureAsset();
		if (LightFixtureAsset->LightFixtureRenderEntityClass.Get() == nullptr)
			continue;

		AHBSLightFixtureRenderEntity* NewLightFixtureRE = Context->RequestRenderEntity<AHBSLightFixtureRenderEntity>(LightFixtureAsset->LightFixtureRenderEntityClass);

		NewLightFixtureRE->SetRelatedSceneEntity(LightFixtureSE);
		NewLightFixtureRE->SetLightState(LightFixtureSE->GetIsLightOn());
		NewLightFixtureRE->SetActorTransform(LightFixtureSE->GetTransform());
	}

	TArray<UHBSLightFixtureSceneEntity*> ModifiedLightFixtureSEList = Context->GetTypedModifiedSceneEntityList<UHBSLightFixtureSceneEntity>();
	for (UHBSLightFixtureSceneEntity* LightFixtureSE : ModifiedLightFixtureSEList)
	{
		AHBSLightFixtureRenderEntity* LinkedLightFixtureRE = Context->GetRelatedRenderEntity<AHBSLightFixtureRenderEntity>(LightFixtureSE);

		UHBSLightFixtureAsset* Asset = LightFixtureSE->GetLightFixtureAsset();

		if (LinkedLightFixtureRE->GetClass() != Asset->LightFixtureRenderEntityClass)
		{
			Context->RemoveRenderEntity(LinkedLightFixtureRE);
			LinkedLightFixtureRE = Context->RequestRenderEntity<AHBSLightFixtureRenderEntity>(Asset->LightFixtureRenderEntityClass);
		}

		LinkedLightFixtureRE->SetLightState(LightFixtureSE->GetIsLightOn());
		LinkedLightFixtureRE->SetActorTransform(LightFixtureSE->GetTransform());
	}
}
