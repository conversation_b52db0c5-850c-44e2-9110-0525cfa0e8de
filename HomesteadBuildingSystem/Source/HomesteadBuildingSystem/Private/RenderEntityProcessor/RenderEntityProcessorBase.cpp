#include "RenderEntityProcessor/RenderEntityProcessorBase.h"
#include "SceneEntity/SceneEntityBase.h"
#include "RenderEntity/RenderEntityBase.h"
#include "Misc/LowLevelTestAdapter.h"

bool UHBSRenderEntityProcessorContext::HasAnyEntityChangedByClass(TSubclassOf<UHBSSceneEntityBase> EntityClass, bool IsPreview)
{
	auto Predicate = [EntityClass](const UHBSSceneEntityBase* Entity)
		{
			return Entity->IsA(EntityClass);
		};

	bool HasNewEntity = GetNewSceneEntityList(IsPreview).ContainsByPredicate(Predicate);
	bool HasModifiedEntity = GetModifiedSceneEntityList(IsPreview).ContainsByPredicate(Predicate);
	bool HasRemovedEntity = GetRemovedSceneEntityList(IsPreview).ContainsByPredicate(Predicate);

	return HasNewEntity || HasModifiedEntity || HasRemovedEntity;
}

const TArray<UHBSSceneEntityBase*>& UHBSRenderEntityProcessorContext::GetNewSceneEntityList(bool IsPreview)
{
	return IsPreview ? NewPreviewSceneEntityList : NewSceneEntityList;
}

const TArray<UHBSSceneEntityBase*>& UHBSRenderEntityProcessorContext::GetModifiedSceneEntityList(bool IsPreview)
{
	return IsPreview ? ModifiedPreviewSceneEntityList : ModifiedSceneEntityList;
}

const TArray<UHBSSceneEntityBase*>& UHBSRenderEntityProcessorContext::GetRemovedSceneEntityList(bool IsPreview)
{
	return IsPreview ? RemovedPreviewSceneEntityList : RemovedSceneEntityList;
}

const TArray<UHBSSceneEntityBase*>& UHBSRenderEntityProcessorContext::GetAllSceneEntityList(bool IsPreview)
{
	return IsPreview ? AllPreviewSceneEntityList : AllSceneEntityList;
}

const TArray<AHBSRenderEntityBase*>& UHBSRenderEntityProcessorContext::GetAllRenderEntityList()
{
	return AllRenderEntityList;
}

TArray<AHBSRenderEntityBase*> UHBSRenderEntityProcessorContext::GetRelatedRenderEntityList(const UHBSSceneEntityBase* SceneEntity)
{
	if (FRelatedRenderEntityList* RenderEntityList = RelatedRenderEntityMap.Find(SceneEntity))
		return RenderEntityList->RenderEntityList;

	return TArray<AHBSRenderEntityBase*>();
}

AHBSRenderEntityBase* UHBSRenderEntityProcessorContext::GetRelatedRenderEntityByClass(const UHBSSceneEntityBase* SceneEntity,
	TSubclassOf<AHBSRenderEntityBase> RenderEntityClass)
{
	TArray<AHBSRenderEntityBase*> RenderEntityList = GetRelatedRenderEntityList(SceneEntity);
	for (AHBSRenderEntityBase* RenderEntity : RenderEntityList)
	{
		if (RenderEntity->IsA(RenderEntityClass))
			return RenderEntity;
	}

	return nullptr;
}

AHBSRenderEntityBase* UHBSRenderEntityProcessorContext::GetRelatedRenderEntity(const UHBSSceneEntityBase* SceneEntity)
{
	TArray<AHBSRenderEntityBase*> RenderEntityList = GetRelatedRenderEntityList(SceneEntity);
	if (RenderEntityList.Num() == 0)
		return nullptr;

	return RenderEntityList[0];
}

AHBSRenderEntityBase* UHBSRenderEntityProcessorContext::RequestRenderEntityByClass(TSubclassOf<AHBSRenderEntityBase> RenderEntityClass)
{
	AHBSRenderEntityBase* RequestRenderEntity = nullptr;
	for (AHBSRenderEntityBase* RenderEntity : OrphanedRenderEntityList)
	{
		if (RenderEntity->GetClass() == RenderEntityClass)
		{
			OrphanedRenderEntityList.Remove(RenderEntity);
			RequestRenderEntity = RenderEntity;
			break;
		}
	}

	if (!RequestRenderEntity)
	{
		RequestRenderEntity = GetWorld()->SpawnActor<AHBSRenderEntityBase>(RenderEntityClass);
	}

	AllRenderEntityList.Add(RequestRenderEntity);
	MarkRenderEntityModified(RequestRenderEntity);
	return RequestRenderEntity;
}

void UHBSRenderEntityProcessorContext::MarkRenderEntityModified(AHBSRenderEntityBase* RenderEntity)
{
	if (!RenderEntity)
	{
		return;
	}

	CHECK(AllRenderEntityList.Contains(RenderEntity));
	ModifiedRenderEntityList.AddUnique(RenderEntity);
}

void UHBSRenderEntityProcessorContext::RemoveRenderEntity(AHBSRenderEntityBase* RenderEntity)
{
	if (!RenderEntity)
	{
		return;
	}

	CHECK(AllRenderEntityList.Contains(RenderEntity));

	AllRenderEntityList.Remove(RenderEntity);
	ModifiedRenderEntityList.Remove(RenderEntity);
	OrphanedRenderEntityList.Add(RenderEntity);
}

bool UHBSRenderEntityProcessorContext::NeedUpdateRenderEntity() const
{
	bool Changed = false;
	Changed = Changed || !NewSceneEntityList.IsEmpty();
	Changed = Changed || !ModifiedSceneEntityList.IsEmpty();
	Changed = Changed || !RemovedSceneEntityList.IsEmpty();

	Changed = Changed || !NewPreviewSceneEntityList.IsEmpty();
	Changed = Changed || !ModifiedPreviewSceneEntityList.IsEmpty();
	Changed = Changed || !RemovedPreviewSceneEntityList.IsEmpty();

	return Changed;
}

void UHBSRenderEntityProcessorContext::AddSceneEntity_Internal(UHBSSceneEntityBase* SceneEntity, bool IsPreview)
{
	TArray<UHBSSceneEntityBase*>& NewSceneEntityListRef = IsPreview ? NewPreviewSceneEntityList : NewSceneEntityList;
	TArray<UHBSSceneEntityBase*>& AllSceneEntityListRef = IsPreview ? AllPreviewSceneEntityList : AllSceneEntityList;

	NewSceneEntityListRef.AddUnique(SceneEntity);
	AllSceneEntityListRef.AddUnique(SceneEntity);
}

void UHBSRenderEntityProcessorContext::ModifySceneEntity_Internal(UHBSSceneEntityBase* SceneEntity, bool IsPreview)
{
	TArray<UHBSSceneEntityBase*>& NewSceneEntityListRef = IsPreview ? NewPreviewSceneEntityList : NewSceneEntityList;
	TArray<UHBSSceneEntityBase*>& ModifiedSceneEntityListRef = IsPreview ? ModifiedPreviewSceneEntityList : ModifiedSceneEntityList;

	if (!NewSceneEntityListRef.Contains(SceneEntity))
		ModifiedSceneEntityListRef.AddUnique(SceneEntity);
}

void UHBSRenderEntityProcessorContext::RemoveSceneEntity_Internal(UHBSSceneEntityBase* SceneEntity, bool IsPreview)
{
	TArray<UHBSSceneEntityBase*>& NewSceneEntityListRef = IsPreview ? NewPreviewSceneEntityList : NewSceneEntityList;
	TArray<UHBSSceneEntityBase*>& ModifiedSceneEntityListRef = IsPreview ? ModifiedPreviewSceneEntityList : ModifiedSceneEntityList;
	TArray<UHBSSceneEntityBase*>& RemovedSceneEntityListRef = IsPreview ? RemovedPreviewSceneEntityList : RemovedSceneEntityList;
	TArray<UHBSSceneEntityBase*>& AllSceneEntityListRef = IsPreview ? AllPreviewSceneEntityList : AllSceneEntityList;

	NewSceneEntityListRef.Remove(SceneEntity);
	ModifiedSceneEntityListRef.Remove(SceneEntity);
	RemovedSceneEntityListRef.AddUnique(SceneEntity);

	AllSceneEntityListRef.Remove(SceneEntity);
}

void UHBSRenderEntityProcessorContext::PreProcess()
{
	for (UHBSSceneEntityBase* SceneEntity : NewSceneEntityList)
		RelatedRenderEntityMap.Add(SceneEntity, FRelatedRenderEntityList());

	for (UHBSSceneEntityBase* SceneEntity : NewPreviewSceneEntityList)
		RelatedRenderEntityMap.Add(SceneEntity, FRelatedRenderEntityList());

	for (UHBSSceneEntityBase* SceneEntity : RemovedSceneEntityList)
		RelatedRenderEntityMap.Remove(SceneEntity);

	for (UHBSSceneEntityBase* SceneEntity : RemovedPreviewSceneEntityList)
		RelatedRenderEntityMap.Remove(SceneEntity);

	TArray<AHBSRenderEntityBase*> RelatedRenderEntityList;
	for (auto& Pair : RelatedRenderEntityMap)
	{
		FRelatedRenderEntityList& RenderEntityList = Pair.Value;
		for (AHBSRenderEntityBase* RenderEntity : RenderEntityList.RenderEntityList)
			RelatedRenderEntityList.AddUnique(RenderEntity);
	}

	for (AHBSRenderEntityBase* RenderEntity : AllRenderEntityList)
	{
		if (RelatedRenderEntityList.Contains(RenderEntity) == false)
			OrphanedRenderEntityList.AddUnique(RenderEntity);
	}

	AllRenderEntityList = RelatedRenderEntityList;
}

void UHBSRenderEntityProcessorContext::RefreshRelatedRenderEntityMap_Internal()
{
	for (auto& Pair : RelatedRenderEntityMap)
	{
		TArray<AHBSRenderEntityBase*>& RenderEntityList = Pair.Value.RenderEntityList;

		for (AHBSRenderEntityBase* ModifiedRenderEntity : ModifiedRenderEntityList)
			RenderEntityList.Remove(ModifiedRenderEntity);

		for (AHBSRenderEntityBase* OrphanedRenderEntity : OrphanedRenderEntityList)
			RenderEntityList.Remove(OrphanedRenderEntity);
	}

	for (AHBSRenderEntityBase* ModifiedRenderEntity : ModifiedRenderEntityList)
	{
		TArray<UHBSSceneEntityBase*> RelatedSceneEntityList = ModifiedRenderEntity->GetRelativeSceneEntityList();
		for (UHBSSceneEntityBase* RelatedSceneEntity : RelatedSceneEntityList)
		{
			if (FRelatedRenderEntityList* RenderEntityList = RelatedRenderEntityMap.Find(RelatedSceneEntity))
			{
				RenderEntityList->RenderEntityList.Add(ModifiedRenderEntity);
			}
		}
	}

	ModifiedRenderEntityList.Empty();
}

void UHBSRenderEntityProcessorContext::PostProcess()
{
	for (AHBSRenderEntityBase* RenderEntity : OrphanedRenderEntityList)
		RenderEntity->Destroy();

	for (UHBSSceneEntityBase* SceneEntity : RemovedSceneEntityList)
		SceneEntity->Destroy();

	for (UHBSSceneEntityBase* SceneEntity : RemovedPreviewSceneEntityList)
		SceneEntity->Destroy();

	NewSceneEntityList.Empty();
	ModifiedSceneEntityList.Empty();
	RemovedSceneEntityList.Empty();

	NewPreviewSceneEntityList.Empty();
	ModifiedPreviewSceneEntityList.Empty();
	RemovedPreviewSceneEntityList.Empty();

	OrphanedRenderEntityList.Empty();
}
