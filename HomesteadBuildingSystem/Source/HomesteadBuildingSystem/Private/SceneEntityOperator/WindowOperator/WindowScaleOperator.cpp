#include "SceneEntityOperator/WindowOperator/WindowScaleOperator.h"
#include "SceneEntity/WindowSceneEntity.h"
#include "SceneEntity/WallSceneEntity.h"
#include "HBSWorldSystem.h"
#include "SceneEntity/StaticMeshSceneEntity.h"
#include "Kismet/KismetMathLibrary.h"

void UHBSWindowScaleOperator::Initialize(UHBSSceneEntityBase* InParentEntity)
{
	Super::Initialize(InParentEntity);
	
	UHBSWindowSceneEntity* ParentWindowEntity = Cast<UHBSWindowSceneEntity>(InParentEntity);
	UHBSWallSceneEntity* RelativeWall = ParentWindowEntity->GetRelativeWall();

	FVector2D CornerPositionOnWall = ParentWindowEntity->GetWindowLocalPosition() +
		ParentWindowEntity->GetWindowSizeWithScale() * FVector2D(TargetCorner.X, TargetCorner.Y) * 0.5;
	FVector CornerWorldPosition = RelativeWall->CalcWorldPositionOnWall(CornerPositionOnWall);

	FVector2D CurrentMousePosition = UHBSWorldSystem::Get(this)->GetMousePositionOnScreen();
	FVector2D CornerPosition = UHBSWorldSystem::Get(this)->ProjectWorldPositionToScreen(CornerWorldPosition);

	MousePositionOffset = CornerPosition - CurrentMousePosition;
}

void UHBSWindowScaleOperator::BeginScale()
{
	IsScaling = true;
}

void UHBSWindowScaleOperator::EndScale()
{
	UHBSWindowSceneEntity* ParentWindowEntity = GetTypedTargetEntity<UHBSWindowSceneEntity>();

	FBox2D ScaledWindowRect = CalcScaledWindowRect();

	ParentWindowEntity->SetPlacementInfo(ScaledWindowRect.GetCenter(), ScaledWindowRect.GetSize() / ParentWindowEntity->GetWindowSize(), ParentWindowEntity->GetRelativeWall());
	UHBSWorldSystem::Get(this)->MarkSceneEntityChanged(ParentWindowEntity);

	if (PreviewEntity)
	{
		UHBSWorldSystem::Get(this)->RemoveSceneEntity(PreviewEntity);
		PreviewEntity = nullptr;
	}

	IsScaling = false;
	DestroySelf();
}

void UHBSWindowScaleOperator::UpdatePreviewEntity()
{
	if (!PreviewEntity)
		return;

	UHBSWindowSceneEntity* ParentWindowEntity = GetTypedTargetEntity<UHBSWindowSceneEntity>();
	UHBSWallSceneEntity* RelativeWall = ParentWindowEntity->GetRelativeWall();

	FBox2D ScaledWindowRect = CalcScaledWindowRect();
	FVector2D ScaledWindowSize = ScaledWindowRect.GetSize();
	FVector2D ScaledWindowPosition = ScaledWindowRect.GetCenter();

	FVector WindowWorldPos = RelativeWall->CalcWorldPositionOnWall(ScaledWindowPosition);

	FVector X = RelativeWall->GetWallVector3D();
	FVector Y = RelativeWall->GetWallNormal3D();

	float WallWidth = UHBSConfig::Get()->WallWidth;
	FTransform Transform(UKismetMathLibrary::MakeRotFromXY(X, Y), WindowWorldPos, FVector(ScaledWindowSize.X * 0.01, WallWidth * 0.015, ScaledWindowSize.Y * 0.01));
	PreviewEntity->SetTransform(Transform);

	UHBSWorldSystem::Get(this)->MarkSceneEntityChanged(PreviewEntity);
}

FBox2D UHBSWindowScaleOperator::CalcScaledWindowRect() const
{
	FBox2D ScaledWindowRect;

	UHBSWindowSceneEntity* ParentWindowEntity = GetTypedTargetEntity<UHBSWindowSceneEntity>();
	UHBSWallSceneEntity* RelativeWall = ParentWindowEntity->GetRelativeWall();

	float WallLength = RelativeWall->GetWallLength();
	int32 WallHeight = RelativeWall->GetWallHeight();

	FVector2D WindowLocalPos = ParentWindowEntity->GetWindowLocalPosition();
	FVector2D WindowSize = ParentWindowEntity->GetWindowSizeWithScale();
	ScaledWindowRect.Min = WindowLocalPos - 0.5 * WindowSize;
	ScaledWindowRect.Max = WindowLocalPos + 0.5 * WindowSize;

	if (IsScaling)
	{
		FHBSTraceWallResult TraceWallResult = UHBSWorldSystem::Get(this)->ProjectMousePositionToSpecialWall(RelativeWall, MousePositionOffset);
		if (TraceWallResult.WallEntity != nullptr)
		{
			FVector2D MousePositionOnWall = TraceWallResult.HitLocalPosition;
			FVector2D WindowCornerOffset = MousePositionOnWall - WindowLocalPos;

			FVector2D ScaleVector = (FVector2D(TargetCorner.X, TargetCorner.Y) * WindowSize).GetSafeNormal();

			float MinDistance = 10 / (FMath::Max(FMath::Abs(ScaleVector.X), FMath::Abs(ScaleVector.Y))) - (WindowSize * FVector2D(TargetCorner.X, TargetCorner.Y)).Length() * 0.5;

			FVector2D XYScale = FVector2D(FLT_MAX, FLT_MAX);
			if (ScaleVector.X != 0.0f)
				XYScale.X = WindowCornerOffset.X / ScaleVector.X;
			if (ScaleVector.Y != 0.0f)
				XYScale.Y = WindowCornerOffset.Y / ScaleVector.Y;

			float ScaleDistance = FMath::Min(XYScale.X, XYScale.Y);
			ScaleDistance = FMath::Max(ScaleDistance, MinDistance);

			FVector2D ScaledTargetPosition = WindowLocalPos + ScaleDistance * ScaleVector;

			switch (TargetCorner.X)
			{
			case -1: ScaledWindowRect.Min.X = FMath::Max(ScaledTargetPosition.X, 0); break;
			case 1: ScaledWindowRect.Max.X = FMath::Min(ScaledTargetPosition.X, WallLength); break;
			}

			switch (TargetCorner.Y)
			{
			case -1: ScaledWindowRect.Min.Y = FMath::Max(ScaledTargetPosition.Y, 0); break;
			case 1: ScaledWindowRect.Max.Y = FMath::Min(ScaledTargetPosition.Y, WallHeight); break;
			}
		}
	}

	return ScaledWindowRect;
}
