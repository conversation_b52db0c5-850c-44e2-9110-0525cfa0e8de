#include "SceneEntityOperator/WindowOperator/WindowMoveOperator.h"
#include "HBSWorldSystem.h"
#include "SceneEntity/StaticMeshSceneEntity.h"
#include "SceneEntity/WindowSceneEntity.h"
#include "SceneEntity/WallSceneEntity.h"
#include "Kismet/KismetMathLibrary.h"
#include "HBSMathLibrary.h"

bool UHBSWindowMoveOperator::TryPlaceEntityAndDestroy()
{
	FHBSTraceWallResult TraceWallResult = UHBSWorldSystem::Get(this)->ProjectMousePositionToWall();

	if (TraceWallResult.WallEntity == nullptr)
		return false;

	TraceWallResult.HitLocalPosition = UHBSWorldSystem::Get(this)->SnapToGrid2D(TraceWallResult.HitLocalPosition);

	if (IsWindowPlacementValid(TraceWallResult) == false)
		return false;

	UHBSWindowSceneEntity* ParentWindowEntity = GetTypedTargetEntity<UHBSWindowSceneEntity>();
	ParentWindowEntity->SetPlacementInfo(TraceWallResult.HitLocalPosition, ParentWindowEntity->GetWindowScale(), TraceWallResult.WallEntity);

	UHBSWorldSystem::Get(this)->MarkSceneEntityChanged(ParentWindowEntity);
	UHBSWorldSystem::Get(this)->RemoveSceneEntity(PreviewEntity);
	PreviewEntity = nullptr;

	DestroySelf();
	return true;
}

FVector UHBSWindowMoveOperator::GetCurrentPosition()
{
	return PreviewEntity->GetTransform().GetLocation();
}

bool UHBSWindowMoveOperator::IsWindowPlacementValid(const FHBSTraceWallResult& TraceWallResult) const
{
	UHBSWorldSystem* HBSSystem = UHBSWorldSystem::Get(this);

	UHBSWindowSceneEntity* CurrentWindow = GetTypedTargetEntity<UHBSWindowSceneEntity>();

	for (UHBSSceneEntityBase* SceneEntity : HBSSystem->GetAllSceneEntities())
	{
		if (UHBSWindowSceneEntity* Window = Cast<UHBSWindowSceneEntity>(SceneEntity))
		{
			if (Window == CurrentWindow)
				continue;

			if (Window->GetRelativeWall() != TraceWallResult.WallEntity)
				continue;

			FVector2D WindowLocalPos = Window->GetWindowLocalPosition();
			FVector2D WindowSize = Window->GetWindowSizeWithScale();

			FVector2D TargetWindowLocalPos = TraceWallResult.HitLocalPosition;
			FVector2D TargetWindowSize = CurrentWindow->GetWindowSizeWithScale();

			if (UHBSMathLibrary::IsRectOverlaped(WindowLocalPos, WindowSize, TargetWindowLocalPos, TargetWindowSize))
			{
				return false;
			}
		}
	}

	return true;
}

void UHBSWindowMoveOperator::UpdatePreviewEntity()
{
	UHBSWorldSystem* HBSSystem = UHBSWorldSystem::Get(this);
	FHBSTraceWallResult TraceWallResult = HBSSystem->ProjectMousePositionToWall();

	if (TraceWallResult.WallEntity)
	{
		UHBSWindowSceneEntity* ParentWindowEntity = GetTypedTargetEntity<UHBSWindowSceneEntity>();
		UHBSWallSceneEntity* WallEntity = TraceWallResult.WallEntity;

		TraceWallResult.HitLocalPosition = HBSSystem->SnapToGrid2D(TraceWallResult.HitLocalPosition);
		FVector WindowPosition = WallEntity->CalcWorldPositionOnWall(TraceWallResult.HitLocalPosition);

		FVector X = WallEntity->GetWallVector3D();
		FVector Y = WallEntity->GetWallNormal3D();

		float WallWidth = UHBSConfig::Get()->WallWidth;

		FTransform Transform(UKismetMathLibrary::MakeRotFromXY(X, Y), WindowPosition, FVector(ParentWindowEntity->GetWindowSizeWithScale().X * 0.01, WallWidth * 0.015, ParentWindowEntity->GetWindowSizeWithScale().Y * 0.01));

		PreviewEntity->SetTransform(Transform);

		PreviewEntity->IsErrorPreview = !IsWindowPlacementValid(TraceWallResult);
	}
	else
	{
		PreviewEntity->SetTransform(FTransform(FRotator(), FVector(), FVector(0)));
	}

	UHBSWorldSystem::Get(this)->MarkSceneEntityChanged(PreviewEntity);
}
