#include "SceneEntityOperator/TransformOperator/WallMountedMoveOperator.h"
#include "HBSWorldSystem.h"
#include "HBSConfig.h"
#include "Kismet/KismetMathLibrary.h"
#include "SceneEntity/TransformSceneEntity.h"

void UHBSWallMountedMoveOperator::Initialize(UHBSSceneEntityBase* InTargetEntity)
{
	Super::Initialize(InTargetEntity);

	UHBSTransformSceneEntity* TransformTargetEntity = GetTypedTargetEntity<UHBSTransformSceneEntity>();
	MouseOffset = UHBSWorldSystem::Get(this)->ProjectWorldPositionToScreen(TransformTargetEntity->GetTransform().GetTranslation()) - UHBSWorldSystem::Get(this)->GetMousePositionOnScreen();

	TryUpdateSceneEntity(PreviewEntity);
}

void UHBSWallMountedMoveOperator::Tick(float DeltaTime)
{
	TryUpdateSceneEntity(PreviewEntity);
}

bool UHBSWallMountedMoveOperator::TryPlaceEntityAndDestroy()
{
	bool res = TryUpdateSceneEntity(PreviewEntity);
	if (res)
	{
		TryUpdateSceneEntity(GetTypedTargetEntity<UHBSTransformSceneEntity>());
		UHBSWorldSystem::Get(this)->MarkSceneEntityChanged(GetTargetEntity());
		UHBSWorldSystem::Get(this)->RemoveSceneEntity(PreviewEntity);
		PreviewEntity = nullptr;

		DestroySelf();
		return true;
	}

	UHBSWorldSystem::Get(this)->MarkSceneEntityChanged(PreviewEntity);
	return false;
}

void UHBSWallMountedMoveOperator::CancelOperator()
{
	UHBSWorldSystem::Get(this)->RemoveSceneEntity(PreviewEntity);

	PreviewEntity = nullptr;
	DestroySelf();
}

void UHBSWallMountedMoveOperator::AddRoll(float InDeltaRoll)
{
	CurrentRoll += InDeltaRoll;
}

void UHBSWallMountedMoveOperator::SetRoll(float InRoll)
{
	CurrentRoll = InRoll;
}

bool UHBSWallMountedMoveOperator::TryUpdateSceneEntity(UHBSTransformSceneEntity* InEntity)
{
	FHBSTraceWallResult TraceWallResult = UHBSWorldSystem::Get(this)->ProjectMousePositionToWall(MouseOffset);

	if (TraceWallResult.WallEntity)
	{
		UHBSWallSceneEntity* WallEntity = TraceWallResult.WallEntity;
		FRotator SnapToWallRotation = UKismetMathLibrary::MakeRotFromXZ(-TraceWallResult.HitNormal, FVector::UpVector);
		FRotator BackwardRotation = UKismetMathLibrary::MakeRotFromXZ(FVector::BackwardVector, FVector::UpVector);

		FTransform RightTransform = FTransform(BackwardRotation).Inverse() * FTransform(SnapToWallRotation);
		FTransform RotateTransform = FTransform(FRotator(0, 0, CurrentRoll));

		TargetTransform = RotateTransform * RightTransform;

		FVector Location = TraceWallResult.HitPosition + TraceWallResult.HitNormal * UHBSConfig::Get()->WallWidth * 0.5f;
		TargetTransform.SetTranslation(Location);

		InEntity->SetIsVisible(true);
		InEntity->SetTransform(TargetTransform);
		UHBSWorldSystem::Get(this)->MarkSceneEntityChanged(InEntity);

		return true;
	}

	InEntity->SetIsVisible(false);
	return false;
}
