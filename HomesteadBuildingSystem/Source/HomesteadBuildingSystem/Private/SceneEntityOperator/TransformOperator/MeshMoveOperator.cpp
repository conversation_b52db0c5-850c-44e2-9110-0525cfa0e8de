#include "SceneEntityOperator/TransformOperator/MeshMoveOperator.h"
#include "SceneEntity/StaticMeshSceneEntity.h"
#include "HBSWorldSystem.h"
#include "SceneEntity/UGCObjectSceneEntity.h"
#include "SceneEntity/AIMeshSceneEntity.h"
#include "SceneEntity/WallSceneEntity.h"
#include "Kismet/KismetMathLibrary.h"
#include "SceneEntity/FloorSceneEntity.h"
#include "Kismet/GameplayStatics.h"
#include "SceneEntity/PlaceablePlaneSceneEntity.h"
#include "SceneEntity/LightFixtureSceneEntity.h"

void UHBSMeshMoveOperator::Initialize(UHBSSceneEntityBase* InSceneEntity)
{
	Super::Initialize(InSceneEntity);

	if (UHBSLightFixtureSceneEntity* LightFixtureEntity = GetTypedTargetEntity<UHBSLightFixtureSceneEntity>())
	{
		switch (LightFixtureEntity->GetLightFixtureAsset()->Type)
		{
		case EHBSLightFixtureType::CeilingMounted: PlaceablePlaneTypes = { EHBSPlaceablePlaneType::Ceiling }; break;
		case EHBSLightFixtureType::FloorStanding: PlaceablePlaneTypes = { EHBSPlaceablePlaneType::Ground,EHBSPlaceablePlaneType::Foundation, EHBSPlaceablePlaneType::Floor }; break;
		case EHBSLightFixtureType::TableLamp: PlaceablePlaneTypes = { EHBSPlaceablePlaneType::Ground,EHBSPlaceablePlaneType::Foundation, EHBSPlaceablePlaneType::Floor, EHBSPlaceablePlaneType::Tabletop }; break;
		case EHBSLightFixtureType::Outdoor: PlaceablePlaneTypes = { EHBSPlaceablePlaneType::Ground,EHBSPlaceablePlaneType::Foundation }; break;
		}
	}

	TargetTransform = GetTypedTargetEntity<UHBSTransformSceneEntity>()->GetTransform();

	MouseOffset = UHBSWorldSystem::Get(this)->ProjectWorldPositionToScreen(TargetTransform.GetTranslation()) - UHBSWorldSystem::Get(this)->GetMousePositionOnScreen();

	FVector MousePosition = UHBSWorldSystem::Get(this)->ProjectMousePositionToWorld(PlaceablePlaneTypes, MouseOffset);
	MousePosition = UHBSWorldSystem::Get(this)->SnapToGrid(MousePosition);
	TargetTransform.SetTranslation(MousePosition);
	TargetTransform = AutoAlignedTransform(TargetTransform);
	PreviewEntity->SetTransform(TargetTransform);

	UHBSWorldSystem::Get(this)->MarkSceneEntityChanged(PreviewEntity);
}

void UHBSMeshMoveOperator::Tick(float DeltaTime)
{
	FVector MousePosition = TargetTransform.GetTranslation();
	if (MoveFlag != EHBSMeshMoveFlag::All)
	{
		MousePosition = UHBSWorldSystem::Get(this)->ProjectMousePositionToWorldWithSpecialHeight(MousePosition.Z, MouseOffset);
	}
	else
	{
		MousePosition = UHBSWorldSystem::Get(this)->ProjectMousePositionToWorld(PlaceablePlaneTypes, MouseOffset);
	}

	MousePosition = UHBSWorldSystem::Get(this)->SnapToGrid(MousePosition);

	FVector TargetPosition = TargetTransform.GetTranslation();
	if ((MoveFlag & EHBSMeshMoveFlag::MoveX) != EHBSMeshMoveFlag::None)
		TargetPosition.X = MousePosition.X;
	if ((MoveFlag & EHBSMeshMoveFlag::MoveY) != EHBSMeshMoveFlag::None)
		TargetPosition.Y = MousePosition.Y;
	TargetPosition.Z = MousePosition.Z;

	TargetTransform.SetTranslation(TargetPosition);

	if (MoveFlag == EHBSMeshMoveFlag::All)
		TargetTransform = AutoAlignedTransform(TargetTransform);

	PreviewEntity->SetTransform(TargetTransform);

	UHBSWorldSystem::Get(this)->MarkSceneEntityChanged(PreviewEntity);
}

void UHBSMeshMoveOperator::PlaceEntityAndDestroy()
{
	if (UHBSTransformSceneEntity* TransformEntity = GetTypedTargetEntity<UHBSTransformSceneEntity>())
	{
		TransformEntity->SetTransform(TargetTransform);
		UHBSWorldSystem::Get(this)->MarkSceneEntityChanged(TransformEntity);
	}

	UHBSWorldSystem::Get(this)->RemoveSceneEntity(PreviewEntity);

	PreviewEntity = nullptr;
	DestroySelf();
}

void UHBSMeshMoveOperator::AddYaw(float InYawDelta)
{
	if ((MoveFlag & EHBSMeshMoveFlag::Rotate) != EHBSMeshMoveFlag::None)
	{
		float Yaw = TargetTransform.Rotator().Yaw + InYawDelta;
		TargetTransform.SetRotation(FRotator(0, Yaw, 0).Quaternion());
		PreviewEntity->SetTransform(TargetTransform);
	}
}

void UHBSMeshMoveOperator::SetYaw(float InYaw)
{
	if ((MoveFlag & EHBSMeshMoveFlag::Rotate) != EHBSMeshMoveFlag::None)
	{
		TargetTransform.SetRotation(FRotator(0, InYaw, 0).Quaternion());
		PreviewEntity->SetTransform(TargetTransform);
	}
}

float UHBSMeshMoveOperator::GetYaw() const
{
	return TargetTransform.Rotator().Yaw;
}

void UHBSMeshMoveOperator::CancelOperator()
{
	UHBSWorldSystem::Get(this)->RemoveSceneEntity(PreviewEntity);

	PreviewEntity = nullptr;
	DestroySelf();
}

FTransform UHBSMeshMoveOperator::AutoAlignedTransform(const FTransform& InTransform)
{
	FHBSBackfaceOrientationInfo BackfaceOrientationInfo = GetTypedTargetEntity<UHBSTransformSceneEntity>()->GetBackfaceOrientationInfo();

	FTransform ResultTransform = InTransform;

	FVector2D WallNormal;
	UHBSWallSceneEntity* NearestWallSE = nullptr;
	if (BackfaceOrientationInfo.Enable)
	{
		NearestWallSE = FindNearestWall(InTransform, BackfaceOrientationInfo.SnapDistance, WallNormal);
		if (NearestWallSE)
		{
			FRotator SnapToWallRotation = UKismetMathLibrary::MakeRotFromXZ(FVector(-WallNormal, 0), FVector::UpVector);
			FRotator BackwardRotation = UKismetMathLibrary::MakeRotFromXZ(FVector(BackfaceOrientationInfo.Direction, 0), FVector::UpVector);

			FTransform RightTransform = FTransform(BackwardRotation).Inverse() * FTransform(SnapToWallRotation);
			ResultTransform.SetRotation(RightTransform.GetRotation());
		}
	}

	FVector RawPosition = ResultTransform.GetTranslation();
	FVector XAxis = ResultTransform.GetUnitAxis(EAxis::X);
	FVector SnapedPosition = UHBSWorldSystem::Get(this)->SnapToGrid(RawPosition, FVector2D(XAxis));
	ResultTransform.SetTranslation(SnapedPosition);

	if (NearestWallSE)
	{
		FVector BackwardPosition = ResultTransform.TransformPosition(BackfaceOrientationInfo.Position);
		float DistanceToWall = FVector::DotProduct(BackwardPosition - NearestWallSE->GetWallStart3D(), FVector(WallNormal, 0)) - UHBSConfig::Get()->WallWidth * 0.5f;
		FVector Offset = DistanceToWall * FVector(WallNormal, 0);

		ResultTransform.SetTranslation(SnapedPosition - Offset);
	}

	return ResultTransform;
}

UHBSWallSceneEntity* UHBSMeshMoveOperator::FindNearestWall(const FTransform& InTransform, float MaxSnapDistance, FVector2D& OutWallNormal)
{
	int32 RelatedFloorHeight = CurrentLevelEntity ? CurrentLevelEntity->GetLevelHeight() : 0;

	FVector CameraPosition;
	FVector CameraDirection;

	int32 ViewportWidth, ViewportHeight;
	UGameplayStatics::GetPlayerController(this, 0)->GetViewportSize(ViewportWidth, ViewportHeight);
	UGameplayStatics::GetPlayerController(this, 0)->DeprojectScreenPositionToWorld(ViewportWidth * 0.5, ViewportHeight * 0.5, CameraPosition, CameraDirection);

	TArray<UHBSWallSceneEntity*> WallEntities = UHBSWorldSystem::Get(this)->GetTypedAllSceneEntityListByPred<UHBSWallSceneEntity>([&](UHBSWallSceneEntity* Wall)
		{
			return (Wall->GetWallStartHeight() == RelatedFloorHeight) &&
				(Wall->GetWallStart() != Wall->GetWallEnd());
		});

	FVector2D CurrentPosition = FVector2D(InTransform.GetTranslation());

	float MinDistance = MaxSnapDistance;
	UHBSWallSceneEntity* NearestWallSE = nullptr;

	for (UHBSWallSceneEntity* WallSE : WallEntities)
	{
		FVector2D ToWallVector = WallSE->GetWallStart() - FVector2D(CameraPosition);

		bool UseOutwardSurface = FVector2D::DotProduct(WallSE->GetWallNormal(), ToWallVector) < 0;

		FVector2D StartPoint = WallSE->GetWallStart();
		FVector2D EndPoint = WallSE->GetWallEnd();
		FVector2D WallNormal = UseOutwardSurface ? WallSE->GetWallNormal() : -WallSE->GetWallNormal();
		FVector2D WallVector = WallSE->GetWallVector();

		float Project = FVector2D::DotProduct(CurrentPosition - StartPoint, WallVector);

		if (Project < 0 || Project > WallSE->GetWallLength())
			continue;

		float DistanceToWall = FVector2D::DotProduct(CurrentPosition - StartPoint, WallNormal);

		if (DistanceToWall < 0)
			continue;

		if (DistanceToWall < MinDistance)
		{
			MinDistance = DistanceToWall;

			NearestWallSE = WallSE;
			OutWallNormal = WallNormal;
		}
	}

	return NearestWallSE;
}
