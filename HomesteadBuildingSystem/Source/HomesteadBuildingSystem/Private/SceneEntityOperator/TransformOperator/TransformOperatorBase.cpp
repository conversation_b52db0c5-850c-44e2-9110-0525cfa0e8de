#include "SceneEntityOperator/TransformOperator/TransformOperatorBase.h"
#include "SceneEntity/UGCObjectSceneEntity.h"
#include "SceneEntity/LightFixtureSceneEntity.h"
#include "SceneEntity/StaticMeshSceneEntity.h"
#include "SceneEntity/AIMeshSceneEntity.h"
#include "HBSWorldSystem.h"

void UHBSTransformOperatorBase::Initialize(UHBSSceneEntityBase* InTargetEntity)
{
	Super::Initialize(InTargetEntity);

	UClass* PreviewEntityClass = GetTargetEntity()->GetClass();
	if (GetTypedTargetEntity<UHBSUGCObjectSceneEntity>() || GetTypedTargetEntity<UHBSLightFixtureSceneEntity>())
	{
		PreviewEntityClass = UHBSStaticMeshSceneEntity::StaticClass();
	}

	UHBSSceneEntityBase* NewEntity = UHBSWorldSystem::Get(this)->PlacementPreviewSceneEntity(PreviewEntityClass);
	PreviewEntity = Cast<UHBSTransformSceneEntity>(NewEntity);

	if (UHBSStaticMeshSceneEntity* StaticMeshEntity = GetTypedTargetEntity<UHBSStaticMeshSceneEntity>())
	{
		Cast<UHBSStaticMeshSceneEntity>(PreviewEntity)->SetupInitInfo(StaticMeshEntity->GetStaticMesh());
	}
	else if (UHBSUGCObjectSceneEntity* UGCObjectEntity = GetTypedTargetEntity<UHBSUGCObjectSceneEntity>())
	{
		Cast<UHBSStaticMeshSceneEntity>(PreviewEntity)->SetupInitInfo(UGCObjectEntity->GetStaticMesh());
	}
	else if (UHBSAIMeshSceneEntity* AIMeshEntity = GetTypedTargetEntity<UHBSAIMeshSceneEntity>())
	{
		// Fallback to a default static mesh if no specific mesh is set
		Cast<UHBSAIMeshSceneEntity>(PreviewEntity)->SetupInitInfo(AIMeshEntity->GetAIMeshTaskGuid());
	}
	else if (UHBSLightFixtureSceneEntity* LightFixtureEntity = GetTypedTargetEntity<UHBSLightFixtureSceneEntity>())
	{
		Cast<UHBSStaticMeshSceneEntity>(PreviewEntity)->SetupInitInfo(LightFixtureEntity->GetLightFixtureAsset()->PreviewStaticMesh);
	}
}
