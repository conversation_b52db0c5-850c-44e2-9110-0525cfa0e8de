#include "SceneEntityOperator/WallOperator/WallMoveOperator.h"
#include "HBSWorldSystem.h"
#include "SceneEntity/WallSceneEntity.h"
#include "SceneEntity/PlaceablePlaneSceneEntity.h"

void UHBSWallMoveOperator::Initialize(UHBSSceneEntityBase* InTargetEntity)
{
	Super::Initialize(InTargetEntity);

	UHBSWallSceneEntity* TargetWallEntity = GetTypedTargetEntity<UHBSWallSceneEntity>();

	UHBSSceneEntityBase* NewEntity = UHBSWorldSystem::Get(this)->PlacementPreviewSceneEntity(UHBSWallSceneEntity::StaticClass());
	PreviewWallEntity = Cast<UHBSWallSceneEntity>(NewEntity);

	PreviewWallEntity->SetupInitInfo(TargetWallEntity->GetWallHeight() + 1);
	PreviewWallEntity->WallStart = TargetWallEntity->WallStart;
	PreviewWallEntity->WallEnd = TargetWallEntity->WallEnd;

	FVector2D WallDir = TargetWallEntity->GetWallVector();
	Yaw = FMath::Atan2(WallDir.Y, WallDir.X) * 180.f / PI;
	WallLength = TargetWallEntity->GetWallLength();

	FVector WallCenter = (TargetWallEntity->GetWallStart3D() + TargetWallEntity->GetWallEnd3D()) * 0.5;
	FVector2D WallCenterOnScreen = UHBSWorldSystem::Get(this)->ProjectWorldPositionToScreen(WallCenter);
	FVector2D MousePos = UHBSWorldSystem::Get(this)->GetMousePositionOnScreen();

	MouseOffset = WallCenterOnScreen - MousePos;

	UpdateWallEntity(PreviewWallEntity);
}

void UHBSWallMoveOperator::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);

	UpdateWallEntity(PreviewWallEntity);
}

void UHBSWallMoveOperator::PlaceWallAndDestroy()
{
	if (PreviewWallEntity)
	{
		UHBSWorldSystem::Get(this)->RemoveSceneEntity(PreviewWallEntity);
		PreviewWallEntity = nullptr;
	}

	UpdateWallEntity(GetTypedTargetEntity<UHBSWallSceneEntity>());

	DestroySelf();
}

void UHBSWallMoveOperator::CancelOperator()
{
	if (PreviewWallEntity)
	{
		UHBSWorldSystem::Get(this)->RemoveSceneEntity(PreviewWallEntity);
		PreviewWallEntity = nullptr;
	}

	DestroySelf();
}

void UHBSWallMoveOperator::AddYaw(float InYawDelta)
{
	Yaw += InYawDelta;
}

void UHBSWallMoveOperator::SetYaw(float InYaw)
{
	Yaw = InYaw;
}

float UHBSWallMoveOperator::GetYaw() const
{
	return Yaw;
}

void UHBSWallMoveOperator::UpdateWallEntity(UHBSWallSceneEntity* InWall)
{
	FVector MousePos = UHBSWorldSystem::Get(this)->ProjectMousePositionToWorld({ EHBSPlaceablePlaneType::Foundation }, MouseOffset);
	MousePos = UHBSWorldSystem::Get(this)->SnapToGrid(MousePos);

	int32 WallStartHeight = FMath::RoundToInt(MousePos.Z);

	float RadianYaw = FMath::DegreesToRadians(Yaw);
	FVector2D WallVector = FVector2D(FMath::Cos(RadianYaw), FMath::Sin(RadianYaw));

	FVector2D WallStart = FVector2D(MousePos) - WallVector * WallLength * 0.5;
	FVector2D WallEnd = FVector2D(MousePos) + WallVector * WallLength * 0.5;

	TArray<UHBSWallSceneEntity*> WallSEList = UHBSWorldSystem::Get(this)->GetTypedAllSceneEntityListByPred<UHBSWallSceneEntity>([WallStartHeight, this](UHBSWallSceneEntity* InWall)
		{
			if (InWall == GetTargetEntity())
				return false;

			return InWall->GetWallStartHeight() == WallStartHeight;
		});

	float MinDisSqr = 50 * 50;
	bool SnapSuccess = false;
	FVector2D Offset = FVector2D(0, 0);
	auto TrySnapToPoint = [&](FVector2D WallEndPoint, FVector2D SnapPoint)
		{
			float DisSqr = FVector2D::DistSquared(WallEndPoint, SnapPoint);
			if (DisSqr < MinDisSqr)
			{
				MinDisSqr = DisSqr;
				Offset = SnapPoint - WallEndPoint;
				SnapSuccess = true;
			}
		};

	for (UHBSWallSceneEntity* WallSE : WallSEList)
	{
		TrySnapToPoint(WallStart, WallSE->WallStart);
		TrySnapToPoint(WallStart, WallSE->WallEnd);
		TrySnapToPoint(WallEnd, WallSE->WallStart);
		TrySnapToPoint(WallEnd, WallSE->WallEnd);
	}

	if (SnapSuccess)
	{
		WallStart = WallStart + Offset;
		WallEnd = WallEnd + Offset;
	}

	InWall->WallStart = WallStart;
	InWall->WallEnd = WallEnd;
	InWall->WallStartHeight = WallStartHeight;

	UHBSWorldSystem::Get(this)->MarkSceneEntityChanged(InWall);
}
