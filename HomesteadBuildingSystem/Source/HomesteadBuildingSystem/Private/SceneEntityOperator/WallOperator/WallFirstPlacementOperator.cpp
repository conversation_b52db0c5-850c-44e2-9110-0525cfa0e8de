#include "SceneEntityOperator/WallOperator/WallFirstPlacementOperator.h"
#include "HBSWorldSystem.h"
#include "HBSMathLibrary.h"
#include "Components/LineBatchComponent.h"
#include "SceneEntity/PlaceablePlaneSceneEntity.h"

void UHBSWallFirstPlacementOperator::Initialize(UHBSSceneEntityBase* InParentEntity)
{
	Super::Initialize(InParentEntity);

	UHBSSceneEntityBase* NewEntity = UHBSWorldSystem::Get(this)->PlacementPreviewSceneEntity(UHBSWallSceneEntity::StaticClass());
	PreviewWallEntity = Cast<UHBSWallSceneEntity>(NewEntity);

	PreviewWallEntity->SetupInitInfo(GetTypedTargetEntity<UHBSWallSceneEntity>()->WallHeight + 1);

	int32 Height;
	PreviewWallEntity->WallStart = PreviewWallEntity->WallEnd = GenerateSnapedPos(&Height);
	PreviewWallEntity->WallStartHeight = Height;

	UHBSWorldSystem::Get(this)->MarkSceneEntityChanged(PreviewWallEntity);
}

void UHBSWallFirstPlacementOperator::Tick(float DeltaTime)
{
	if (PreviewWallEntity)
	{
		int32 Height;
		PreviewWallEntity->WallEnd = GenerateSnapedPos(&Height);

		if (IsBeginOperator == false)
		{
			PreviewWallEntity->WallStart = PreviewWallEntity->WallEnd;
			PreviewWallEntity->WallStartHeight = Height;
		}

		UHBSWorldSystem::Get(this)->MarkSceneEntityChanged(PreviewWallEntity);
	}
}

void UHBSWallFirstPlacementOperator::BeginOperator()
{
	int32 Height;
	FVector2D Pos = GenerateSnapedPos(&Height);
	BeginOperatorWithStart(Pos, Height);
}

void UHBSWallFirstPlacementOperator::BeginOperatorWithStart(FVector2D InStartPos, int32 InWallStartHeight)
{
	WallStart = InStartPos;
	WallStartHeight = InWallStartHeight;

	PreviewWallEntity->WallStart = WallStart;
	PreviewWallEntity->WallStartHeight = WallStartHeight;
	UHBSWorldSystem::Get(this)->MarkSceneEntityChanged(PreviewWallEntity);
	IsBeginOperator = true;
}

void UHBSWallFirstPlacementOperator::EndOperatorAndDestroy(FVector2D& OutWallEnd, int32& OutWallStartHeight, bool& OutIsGenerateCloseSpace)
{
	OutWallEnd = WallEnd = GenerateSnapedPos();
	OutWallStartHeight = WallStartHeight;

	UHBSWallSceneEntity* Wall = GetTypedTargetEntity<UHBSWallSceneEntity>();
	Wall->WallStart = WallStart;
	Wall->WallEnd = WallEnd;
	Wall->WallStartHeight = WallStartHeight;
	Wall->MarkPlacementDone(); // Mark the wall as placed

	UHBSWorldSystem::Get(this)->MarkSceneEntityChanged(Wall);
	UHBSWorldSystem::Get(this)->RemoveSceneEntity(PreviewWallEntity);
	PreviewWallEntity = nullptr;

	OutIsGenerateCloseSpace = IsGenerateCloseSpace();

	DestroySelf();
}

void UHBSWallFirstPlacementOperator::CancelOperator()
{
	UHBSWorldSystem::Get(this)->RemoveSceneEntity(PreviewWallEntity);
	UHBSWorldSystem::Get(this)->RemoveSceneEntity(TargetEntity);

	PreviewWallEntity = nullptr;
	DestroySelf();
}

FVector2D UHBSWallFirstPlacementOperator::GenerateSnapedPos(int32* OutHeight)
{
	FVector MouseWorldPosition = FVector();
	if (IsBeginOperator)
		MouseWorldPosition = UHBSWorldSystem::Get(this)->ProjectMousePositionToWorldWithSpecialHeight(WallStartHeight);
	else
		MouseWorldPosition = UHBSWorldSystem::Get(this)->ProjectMousePositionToWorld({ EHBSPlaceablePlaneType::Foundation });

	MouseWorldPosition = UHBSWorldSystem::Get(this)->SnapToGrid(MouseWorldPosition);

	FVector2D CurrentPosition = FVector2D(MouseWorldPosition.X, MouseWorldPosition.Y);

	float WallAngleGrid = UHBSWorldSystem::Get(this)->GetSetting().WallAngleGrid;

	bool EnableAngleGrid = bEnableWallAngleGrid && IsBeginOperator && (WallAngleGrid != 0);

	FVector2D AngleSnapedDirection;
	float WallLength = 0.f;
	if (EnableAngleGrid)
	{
		FVector2D DeltaVector = CurrentPosition - WallStart;
		float WallAngle = FMath::RadiansToDegrees(FMath::Atan2(DeltaVector.Y, DeltaVector.X));
		float SnappedAngle = WallAngleGrid * FMath::RoundToInt(WallAngle / WallAngleGrid);
		SnappedAngle = FMath::DegreesToRadians(SnappedAngle);
		AngleSnapedDirection = FVector2D(FMath::Cos(SnappedAngle), FMath::Sin(SnappedAngle));
		WallLength = FVector2D::DotProduct(CurrentPosition - WallStart, AngleSnapedDirection);
		CurrentPosition = WallStart + WallLength * AngleSnapedDirection;
	}

	TArray<UHBSWallSceneEntity*> WallList = UHBSWorldSystem::Get(this)->GetTypedAllSceneEntityList<UHBSWallSceneEntity>(true);

	FVector2D SnappedPosition = CurrentPosition;
	bool IsSnappedToPoint = false;
	bool IsSnappedToWallEndPoint = false;
	FVector2D SnappedToPoint;
	int32 SnapedToPointRawHeight = 0;
	float MinDis = 50;
	float MinDisSquared = MinDis * MinDis;

	for (UHBSWallSceneEntity* WallEntity : WallList)
	{
		if (WallEntity == GetTargetEntity())
			continue;

		FVector2D OtherWallStart = WallEntity->WallStart;
		FVector2D OtherWallEnd = WallEntity->WallEnd;

		if (EnableAngleGrid)
		{
			auto TrySnapToPoint = [&](FVector2D RawPoint, FVector2D Point)
				{
					float TrySnapWallLength = FVector2D::DotProduct(Point - WallStart, AngleSnapedDirection);

					if (FMath::Abs(TrySnapWallLength) > 1)
					{
						float Dis = FMath::Abs(TrySnapWallLength - WallLength);
						if (Dis < MinDis)
						{
							MinDis = Dis;
							SnappedPosition = WallStart + TrySnapWallLength * AngleSnapedDirection;
							IsSnappedToPoint = true;
							SnappedToPoint = RawPoint;
							SnapedToPointRawHeight = WallEntity->GetWallStartHeight();
						}
					}
				};

			auto TrySnapToIntersection = [&](FVector2D Point, FVector2D Vector)
				{
					if (UHBSMathLibrary::IsVectorParallel(AngleSnapedDirection, Vector) == false)
					{
						FVector2D Intersection = UHBSMathLibrary::CalcLineIntersection(WallStart, AngleSnapedDirection, OtherWallStart, Vector);

						TrySnapToPoint(Point, Intersection);
					}
				};

			TrySnapToPoint(OtherWallStart, OtherWallStart);
			TrySnapToPoint(OtherWallEnd, OtherWallEnd);

			TrySnapToIntersection(OtherWallStart, OtherWallEnd - OtherWallStart);
			TrySnapToIntersection(OtherWallEnd, OtherWallEnd - OtherWallStart);

			TrySnapToIntersection(OtherWallStart, FVector2D(1, 0));
			TrySnapToIntersection(OtherWallEnd, FVector2D(1, 0));

			TrySnapToIntersection(OtherWallStart, FVector2D(0, 1));
			TrySnapToIntersection(OtherWallEnd, FVector2D(0, 1));
		}
		else
		{
			if ((OtherWallStart - WallStart).SizeSquared() > 1)
			{
				float DisSquared = (CurrentPosition - OtherWallStart).SizeSquared();
				if (DisSquared < MinDisSquared)
				{
					MinDisSquared = DisSquared;
					SnappedPosition = OtherWallStart;
					IsSnappedToWallEndPoint = true;
				}
			}

			if ((OtherWallEnd - WallStart).SizeSquared() > 1)
			{
				float DisSquared = (CurrentPosition - OtherWallEnd).SizeSquared();

				if (DisSquared < MinDisSquared)
				{
					MinDisSquared = DisSquared;
					SnappedPosition = OtherWallEnd;
					IsSnappedToWallEndPoint = true;
				}
			}
		}
	}

	if ((EnableAngleGrid || IsSnappedToWallEndPoint) == false)
	{
		for (UHBSWallSceneEntity* WallEntity : WallList)
		{
			if (WallEntity == GetTargetEntity())
				continue;

			FVector2D OtherWallStart = WallEntity->WallStart;
			FVector2D OtherWallEnd = WallEntity->WallEnd;
			FVector2D OtherWallNormal = WallEntity->GetWallNormal();

			FVector2D Intersection;
			if (UHBSMathLibrary::CalcLineSegmentIntersection(OtherWallStart, OtherWallEnd, CurrentPosition - 50 * OtherWallNormal, CurrentPosition + 50 * OtherWallNormal, Intersection))
			{
				float DisSquared = (CurrentPosition - Intersection).SizeSquared();
				if (DisSquared < MinDisSquared)
				{
					MinDisSquared = DisSquared;
					SnappedPosition = Intersection;
					IsSnappedToWallEndPoint = true;
				}
			}
		}
	}

	if (IsSnappedToPoint)
	{
		if (SnapedToPointRawHeight != WallStartHeight)
		{
			GetWorld()->LineBatcher->DrawLine(FVector(SnappedToPoint, WallStartHeight), FVector(SnappedToPoint, SnapedToPointRawHeight), FLinearColor::White, SDPG_Foreground, 4);
		}

		GetWorld()->LineBatcher->DrawLine(FVector(SnappedPosition, WallStartHeight), FVector(SnappedToPoint, WallStartHeight), FLinearColor::White, SDPG_Foreground, 4);
	}

	if (OutHeight)
		*OutHeight = FMath::RoundToInt32(MouseWorldPosition.Z);
	return SnappedPosition;
}

bool UHBSWallFirstPlacementOperator::IsGenerateCloseSpace() const
{
	float WallWidth = UHBSConfig::Get()->WallWidth;
	UHBSWallSceneEntity* TargetWall = GetTypedTargetEntity<UHBSWallSceneEntity>();

	TArray<UHBSWallSceneEntity*> WallList;
	for (UHBSSceneEntityBase* Entity : UHBSWorldSystem::Get(this)->GetAllSceneEntities())
	{
		if (UHBSWallSceneEntity* WallEntity = Cast<UHBSWallSceneEntity>(Entity))
		{
			if (WallEntity->GetIsPlacementDone() == false)
				continue;

			if (WallEntity == GetTargetEntity())
				continue;

			if (WallEntity->GetWallStartHeight() != WallStartHeight)
				continue;

			if (WallEntity->IsPointWall())
				continue;

			WallList.Add(WallEntity);
		}
	}

	TArray<FDirectedEdge> EdgeList;
	for (int32 i = 0; i != WallList.Num(); ++i)
	{
		FDirectedEdge ForwardEdge;
		FDirectedEdge InverseEdge;

		ForwardEdge.Start = WallList[i]->GetWallStart();
		ForwardEdge.End = WallList[i]->GetWallEnd();
		ForwardEdge.Direction = WallList[i]->GetWallVector();

		InverseEdge.Start = WallList[i]->GetWallEnd();
		InverseEdge.End = WallList[i]->GetWallStart();
		InverseEdge.Direction = -WallList[i]->GetWallVector();

		EdgeList.Add(ForwardEdge);
		EdgeList.Add(InverseEdge);
	}

	for (int32 i = 0; i != EdgeList.Num(); ++i)
	{
		for (int32 j = 0; j != EdgeList.Num(); ++j)
		{
			if (i / 2 == j / 2)
				continue;

			FDirectedEdge& EdgeA = EdgeList[i];
			FDirectedEdge& EdgeB = EdgeList[j];

			if (FVector2D::DistSquared(EdgeA.End, EdgeB.Start) < WallWidth * WallWidth)
			{
				FAdjacentEdge AdjacentEdge;
				AdjacentEdge.AngleDot = FVector2D::DotProduct(EdgeA.Direction, EdgeB.Direction);
				AdjacentEdge.EdgeIndex = j;
				EdgeA.AdjacentEdgeList.Add(AdjacentEdge);
			}
		}
	}

	for (FDirectedEdge& Edge : EdgeList)
	{
		Algo::SortBy(Edge.AdjacentEdgeList, [](FAdjacentEdge& AdjacentEdge) { return AdjacentEdge.AngleDot; });
	}

	TArray<int32> StartingEdgeList;
	TArray<int32> EndEdgeList;
	for (int32 i = 0; i != WallList.Num(); ++i)
	{
		FVector2D Intersection;
		if (IsWallIntersected(WallList[i], TargetWall, Intersection))
		{
			if (WallList[i]->GetWallLength() < WallWidth)
			{
				StartingEdgeList.Add(i * 2);
				StartingEdgeList.Add(i * 2 + 1);
				EndEdgeList.Add(i * 2);
				EndEdgeList.Add(i * 2 + 1);
			}
			else
			{
				if (FVector2D::DistSquared(Intersection, WallList[i]->GetWallEnd()) > WallWidth * WallWidth)
				{
					if (FVector2D::DistSquared(Intersection, WallEnd) > WallWidth * WallWidth)
						StartingEdgeList.Add(i * 2);

					if (FVector2D::DistSquared(Intersection, WallStart) > WallWidth * WallWidth)
						EndEdgeList.Add(i * 2 + 1);
				}

				if (FVector2D::DistSquared(Intersection, WallList[i]->GetWallStart()) > WallWidth * WallWidth)
				{
					if (FVector2D::DistSquared(Intersection, WallEnd) > WallWidth * WallWidth)
						StartingEdgeList.Add(i * 2 + 1);

					if (FVector2D::DistSquared(Intersection, WallStart) > WallWidth * WallWidth)
						EndEdgeList.Add(i * 2);
				}
			}
		}
	}

	if (StartingEdgeList.IsEmpty() || EndEdgeList.IsEmpty())
		return false;

	FVector2D WallVector = TargetWall->GetWallVector();
	Algo::SortBy(StartingEdgeList, [&](int32 EdgeIndex) { return FVector2D::DotProduct(EdgeList[EdgeIndex].Direction, WallVector); });

	for (int32 startIdx : StartingEdgeList)
	{
		TArray<bool> Visited;
		Visited.Init(false, EdgeList.Num());
		Visited[startIdx] = true;

		if (PerformBFS(
			startIdx,
			startIdx,
			EdgeList,
			EndEdgeList,
			Visited,
			false)
			)
		{
			return true;
		}
	}

	return false;
}

bool UHBSWallFirstPlacementOperator::IsWallIntersected(UHBSWallSceneEntity* WallA, UHBSWallSceneEntity* WallB, FVector2D& OutIntersection) const
{
	float WallWidth = UHBSConfig::Get()->WallWidth;
	FVector2D WallStartA = WallA->GetWallStart();
	FVector2D WallEndA = WallA->GetWallEnd();
	FVector2D WallVectorA = WallA->GetWallVector();
	FVector2D WallStartB = WallB->GetWallStart();
	FVector2D WallEndB = WallB->GetWallEnd();
	FVector2D WallVectorB = WallB->GetWallVector();

	return UHBSMathLibrary::CalcLineSegmentIntersection(
		WallStartA - WallWidth * WallVectorA, WallEndA + WallWidth * WallVectorA,
		WallStartB - WallWidth * WallVectorB, WallEndB + WallWidth * WallVectorB,
		OutIntersection
	);
}

bool UHBSWallFirstPlacementOperator::PerformBFS(int CurrentIndex, int StartIndex, const TArray<FDirectedEdge>& EdgeList, const TArray<int32>& EndEdges, TArray<bool>& Visited, bool HasNonParallel) const
{
	// BFS 核心数据结构：队列 + 节点状态
	struct FBFSNodeState
	{
		int32 Index;
		bool HasNonParallelFlag;
	};
	TQueue<FBFSNodeState> BFSQueue;

	// 初始化队列和访问标记
	if (CurrentIndex >= Visited.Num())
	{
		Visited.SetNum(CurrentIndex + 1, false); // 确保数组足够大
	}
	Visited[CurrentIndex] = true;
	BFSQueue.Enqueue(FBFSNodeState{ CurrentIndex, HasNonParallel });

	while (!BFSQueue.IsEmpty())
	{
		FBFSNodeState CurrentState;
		BFSQueue.Dequeue(CurrentState);
		const int32 CurrentIdx = CurrentState.Index;
		const bool CurrentHasNonParallel = CurrentState.HasNonParallelFlag;

		// 终止条件检查（与原始 DFS 完全一致）
		if (CurrentIdx / 2 != StartIndex / 2)
		{
			if (EndEdges.Contains(CurrentIdx))
			{
				return CurrentHasNonParallel;
			}
		}

		// 遍历邻接边
		for (const FAdjacentEdge& NeighborEdge : EdgeList[CurrentIdx].AdjacentEdgeList)
		{
			const int32 Neighbor = NeighborEdge.EdgeIndex;
			if (!Visited[Neighbor])
			{
				Visited[Neighbor] = true; // 立即标记为已访问

				// 计算新状态
				bool bIsParallel = UHBSMathLibrary::IsVectorParallel(
					EdgeList[CurrentIdx].Direction,
					EdgeList[Neighbor].Direction
				);
				const bool NewHasNonParallel = CurrentHasNonParallel || !bIsParallel;

				// 将邻居节点加入队列
				BFSQueue.Enqueue(FBFSNodeState{ Neighbor, NewHasNonParallel });
			}
		}
	}

	return false; // 未找到符合条件的节点
}
