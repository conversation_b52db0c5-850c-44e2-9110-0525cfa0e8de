#include "HBSWorldSystem.h"

#include "HBSConfig.h"
#include "Kismet/GameplayStatics.h"
#include "SceneEntity/WallSceneEntity.h"
#include "SceneEntity/SceneEntityBase.h"
#include "Blueprint/WidgetLayoutLibrary.h"
#include "RenderEntity/StaticMeshRenderEntity.h"
#include "SceneEntityProcessor/SceneEntityProcessorBase.h"
#include "RenderEntityProcessor/WallRenderEntityProcessor.h"
#include "RenderEntityProcessor/RenderEntityProcessorBase.h"
#include "RenderEntityProcessor/StaticMeshRenderEntityProcessor.h"
#include "RenderEntityProcessor/AIMeshRenderEntityProcessor.h"
#include "RenderEntity/AIMeshRenderEntity.h"
#include "RenderEntity/UGCObjectRenderEntity.h"
#include "RenderEntity/WallRenderEntity.h"
#include "RenderEntityProcessor/UGCObjectRenderEntityProcessor.h"
#include "SceneEntityProcessor/WallSceneEntityProcessor.h"
#include "SceneEntity/WindowSceneEntity.h"
#include "GameFramework/HUD.h"
#include "Components/LineBatchComponent.h"
#include "RenderEntityProcessor/SurfaceRenderEntityProcessor.h"
#include "SceneEntityProcessor/FlooringCeilingSceneEntityProcessor.h"
#include "RenderEntity/SurfaceRenderEntity.h"
#include "RenderEntityProcessor/PlaceablePlaneRenderEntityProcessor.h"
#include "SceneEntity/PlaceablePlaneSceneEntity.h"
#include "SceneEntityProcessor/PlaceablePlaneSceneEntityProcessor.h"
#include "SceneEntity/FloorSceneEntity.h"
#include "SceneEntityProcessor/BuildingAndLevelSceneEntityProcessor.h"
#include "RenderEntityProcessor/LightFixtureRenderEntityProcessor.h"

bool UHBSWorldSystem::ShouldCreateSubsystem(UObject* Outer) const
{
	// Todo: Check game mode to decide whether to create this subsystem
	if (const UWorld* World = Cast<UWorld>(Outer))
	{
		if (auto WorldSettings = World->GetWorldSettings())
		{
			return true;
		}
	}
	return true;
}

void UHBSWorldSystem::Initialize(FSubsystemCollectionBase& Collection)
{
	Super::Initialize(Collection);

	// Initialize your subsystem here
	SceneEntityProcessorContext = NewObject<UHBSSceneEntityProcessorContext>(this);
	PreviewSceneEntityProcessorContext = NewObject<UHBSSceneEntityProcessorContext>(this);

	RenderEntityProcessorContext = NewObject<UHBSRenderEntityProcessorContext>(this);

	SceneEntityProcessors.Add(NewObject<UHBSWallSceneEntityProcessor>(this));
	SceneEntityProcessors.Add(NewObject<UHBSFlooringCeilingSceneEntityProcessor>(this));
	SceneEntityProcessors.Add(NewObject<UHBSPlaceablePlaneSceneEntityProcessor>(this));
	SceneEntityProcessors.Add(NewObject<UHBSBuildingAndLevelSceneEntityProcessor>(this));

	RenderEntityProcessors.Add(NewObject<UHBSWallRenderEntityProcessor>(this));
	RenderEntityProcessors.Add(NewObject<UHBSStaticMeshRenderEntityProcessor>(this));
	RenderEntityProcessors.Add(NewObject<UHBSAIMeshRenderEntityProcessor>(this));
	RenderEntityProcessors.Add(NewObject<UHBSUGCObjectRenderEntityProcessor>(this));
	RenderEntityProcessors.Add(NewObject<UHBSSurfaceRenderEntityProcessor>(this));
	RenderEntityProcessors.Add(NewObject<UHBSPlaceablePlaneRenderEntityProcessor>(this));
	RenderEntityProcessors.Add(NewObject<UHBSLightFixtureRenderEntityProcessor>(this));

	Algo::SortBy(SceneEntityProcessors, [](const UHBSSceneEntityProcessorBase* Processor) { return Processor->GetPriority(); });
	Algo::SortBy(RenderEntityProcessors, [](const UHBSRenderEntityProcessorBase* Processor) { return Processor->GetPriority(); });
}

void UHBSWorldSystem::Deinitialize()
{
	// Clean up your subsystem here
	Super::Deinitialize();
}

void UHBSWorldSystem::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);

	for (UHBSSceneEntityOperatorBase* Operator : OperatorList)
	{
		if (Operator->IsTickable())
			Operator->Tick(DeltaTime);
	}

	UpdateSceneEntityList();
	UpdateRenderEntityList();
	UpdateHighLighted();

	for (UHBSSceneEntityBase* Entity : SceneEntityProcessorContext->GetAllSceneEntityList())
	{
		if (Entity->IsTickable())
			Entity->Tick(DeltaTime);
	}
}

void UHBSWorldSystem::SetHighLightedSceneEntity(UHBSSceneEntityBase* InSceneEntity)
{
	SetHighLightedSceneEntityList(TArray<UHBSSceneEntityBase*>{ InSceneEntity });
}

void UHBSWorldSystem::SetHighLightedSceneEntityList(TArray<UHBSSceneEntityBase*> InSceneEntityList)
{
	HighLightedSceneEntityList = InSceneEntityList;
}

void UHBSWorldSystem::SetSelectedSceneEntity(UHBSSceneEntityBase* InSceneEntity)
{
	SetSelectedSceneEntityList(TArray<UHBSSceneEntityBase*>{ InSceneEntity });
}

void UHBSWorldSystem::SetSelectedSceneEntityList(TArray<UHBSSceneEntityBase*> InSceneEntityList)
{
	SelectedSceneEntityList = InSceneEntityList;
}

FVector2D UHBSWorldSystem::SnapToGrid2D(FVector2D Position, FVector2D Direction, FVector2D GridSize)
{
	if (GridSize.X == 0.0f)
		GridSize.X = UHBSConfig::Get()->GridSize;
	if (GridSize.Y == 0.0f)
		GridSize.Y = UHBSConfig::Get()->GridSize;

	Direction = Direction.GetSafeNormal();
	FVector2D AnotherDirection = FVector2D(-Direction.Y, Direction.X);

	FVector2D ProjectedPosition = FVector2D(FVector2D::DotProduct(Position, Direction), FVector2D::DotProduct(Position, AnotherDirection));

	FVector2D SnapedPosition = FVector2D(
		FMath::RoundToInt(ProjectedPosition.X / GridSize.X) * GridSize.X,
		FMath::RoundToInt(ProjectedPosition.Y / GridSize.Y) * GridSize.Y
	);

	return SnapedPosition.X * Direction + SnapedPosition.Y * AnotherDirection;
}

FVector UHBSWorldSystem::SnapToGrid(FVector Position, FVector2D Direction, FVector2D GridSize)
{
	return FVector(SnapToGrid2D(FVector2D(Position), Direction, GridSize), Position.Z);
}

int32 UHBSWorldSystem::GetMaxLevel()
{
	TArray<UHBSFloorSceneEntity*> LevelSEList = GetTypedAllSceneEntityList<UHBSFloorSceneEntity>();

	int32 MaxLevel = 0;
	for (UHBSFloorSceneEntity* LevelSE : LevelSEList)
		MaxLevel = FMath::Max(MaxLevel, LevelSE->GetLevelIndex());

	return MaxLevel;
}

int32 UHBSWorldSystem::CalculateNextLevel()
{
	int32 CurrentLevel = UHBSWorldSystem::Get(this)->GetSetting().CurrentLevel;

	TArray<UHBSFloorSceneEntity*> FocusLevelList = GenerateCurrentFocusLevelList();

	UHBSFloorSceneEntity** NextLevelPtr = FocusLevelList.FindByPredicate([&](UHBSFloorSceneEntity* LevelSE)
		{
			return LevelSE->GetLevelIndex() > CurrentLevel;
		});

	if (NextLevelPtr)
		return (*NextLevelPtr)->GetLevelIndex();

	return FMath::Min(CurrentLevel + 1, GetMaxLevel());
}

int32 UHBSWorldSystem::CalculatePreviousLevel()
{
	int32 CurrentLevel = UHBSWorldSystem::Get(this)->GetSetting().CurrentLevel;

	TArray<UHBSFloorSceneEntity*> FocusLevelList = GenerateCurrentFocusLevelList();

	int32 PrevLevelIndex = FocusLevelList.FindLastByPredicate([&](UHBSFloorSceneEntity* LevelSE)
		{
			return LevelSE->GetLevelIndex() < CurrentLevel;
		});

	if (PrevLevelIndex != INDEX_NONE)
		return FocusLevelList[PrevLevelIndex]->GetLevelIndex();

	return FMath::Max(CurrentLevel - 1, 0);
}

TArray<UHBSFloorSceneEntity*> UHBSWorldSystem::GenerateCurrentFocusLevelList()
{
	TArray<UHBSPlaceablePlaneSE*> PlaceablePlaneSEList = GetTypedAllSceneEntityList<UHBSPlaceablePlaneSE>();

	FVector2D ViewportSize = UWidgetLayoutLibrary::GetViewportSize(this);
	FBox2D FocusBox = FBox2D(ViewportSize * FVector2D(0.1, 0.2), ViewportSize * FVector2D(0.9, 1));

	TArray<UHBSFloorSceneEntity*> CurrentFocusLevelList;
	for (UHBSPlaceablePlaneSE* PlaceablePlane : PlaceablePlaneSEList)
	{
		if (UHBSFloorSceneEntity* ParentLevel = PlaceablePlane->GetTypedParentEntity<UHBSFloorSceneEntity>())
		{
			if (CurrentFocusLevelList.Contains(ParentLevel))
				continue;

			FBox2D BoundingBox = PlaceablePlane->GetBoundingBox();
			if (BoundingBox.bIsValid == false)
				continue;

			float Height = PlaceablePlane->GetPlaneHeight();

			FVector2D ProjectedPos[] =
			{
				FVector2D(ProjectWorldPositionToScreen(FVector(BoundingBox.Min, Height))),
				FVector2D(ProjectWorldPositionToScreen(FVector(BoundingBox.Max, Height))),
				FVector2D(ProjectWorldPositionToScreen(FVector(BoundingBox.Min.X, BoundingBox.Max.Y, Height))),
				FVector2D(ProjectWorldPositionToScreen(FVector(BoundingBox.Max.X, BoundingBox.Min.Y, Height))),
			};

			FBox2D ProjectedBox = FBox2D(EForceInit::ForceInitToZero);
			for (const FVector2D& Pos : ProjectedPos)
				ProjectedBox += Pos;

			if (ProjectedBox.Intersect(FocusBox))
			{
				CurrentFocusLevelList.Add(ParentLevel);
			}
		}
	}

	Algo::SortBy(CurrentFocusLevelList, [](UHBSFloorSceneEntity* LevelSE) { return LevelSE->GetLevelHeight(); });

	return CurrentFocusLevelList;
}

UHBSSceneEntityBase* UHBSWorldSystem::PlacementSceneEntity(TSubclassOf<UHBSSceneEntityBase> SceneEntityClass)
{
	return SceneEntityProcessorContext->PlacementSceneEntityByClass(SceneEntityClass, false);
}

UHBSSceneEntityBase* UHBSWorldSystem::PlacementPreviewSceneEntity(TSubclassOf<UHBSSceneEntityBase> SceneEntityClass)
{
	if (UHBSSceneEntityBase* NewEntity = PreviewSceneEntityProcessorContext->PlacementSceneEntityByClass(SceneEntityClass, true))
	{
		NewEntity->SetIsPreview(true);
		return NewEntity;
	}

	return nullptr;
}

void UHBSWorldSystem::RemoveSceneEntity(UHBSSceneEntityBase* SceneEntity)
{
	if (SceneEntity == nullptr)
		return;

	UHBSSceneEntityProcessorContext* SEPContext = SceneEntity->GetIsPreview() ? PreviewSceneEntityProcessorContext : SceneEntityProcessorContext;

	SEPContext->RemoveSceneEntity(SceneEntity);
}

const TArray<UHBSSceneEntityBase*>& UHBSWorldSystem::GetAllSceneEntities() const
{
	return SceneEntityProcessorContext->GetAllSceneEntityList();
}

void UHBSWorldSystem::MarkSceneEntityChanged(UHBSSceneEntityBase* SceneEntity)
{
	if (SceneEntity == nullptr)
		return;

	bool IsPreview = SceneEntity->GetIsPreview();

	if (IsPreview == false)
	{
		if (SceneEntity->GetIsPlacementDone())
			SceneEntity->RefreshOperatorsMetaDataList();
		SceneEntityProcessorContext->MarkSceneEntityModify(SceneEntity);
	}

	if (IsPreview)
	{
		PreviewSceneEntityProcessorContext->MarkSceneEntityModify(SceneEntity);
	}
}

void UHBSWorldSystem::NotifyOperatorCreated(UHBSSceneEntityOperatorBase* Operator)
{
	OperatorList.AddUnique(Operator);
}

void UHBSWorldSystem::NotifyOperatorDestroyed(UHBSSceneEntityOperatorBase* Operator)
{
	OperatorList.Remove(Operator);
}

FHBSHitResult UHBSWorldSystem::LineTraceSceneEntityFromMousePosition(FVector2D ScreenPositionOffset)
{
	return LineTraceSceneEntityFromScreenPosition(GetMousePositionOnScreen() + ScreenPositionOffset);
}

FHBSHitResult UHBSWorldSystem::LineTraceSceneEntityFromScreenPosition(FVector2D ScreenPosition)
{
	FVector WorldPosition;
	FVector WorldDirection;

	UGameplayStatics::GetPlayerController(this, 0)->DeprojectScreenPositionToWorld(ScreenPosition.X, ScreenPosition.Y, WorldPosition, WorldDirection);

	return LineTraceSceneEntity(WorldPosition, WorldPosition + WorldDirection * 100000.0f);
}

FHBSHitResult UHBSWorldSystem::LineTraceSceneEntity(const FVector& Start, const FVector& End)
{
	FHitResult HitResult;
	FCollisionQueryParams CollisionParams = FCollisionQueryParams::DefaultQueryParam;
	GetWorld()->LineTraceSingleByChannel(HitResult, Start, End, ECollisionChannel::ECC_Camera);

	if (AHBSRenderEntityBase* RenderEntity = Cast<AHBSRenderEntityBase>(HitResult.GetActor()))
	{
		FHBSHitResult HBSHitResult;
		if (RenderEntity->GetRelativeSceneEntityList().Num() != 0)
		{
			HBSHitResult.HitEntity = RenderEntity->GetRelativeSceneEntityList()[0];
		}
		HBSHitResult.HitPosition = HitResult.Location;
		HBSHitResult.HitNormal = HitResult.Normal;
		HBSHitResult.HitDistance = HitResult.Distance;

		return HBSHitResult;
	}

	return FHBSHitResult();
}

FVector2D UHBSWorldSystem::GetMousePositionOnScreen()
{
	return UWidgetLayoutLibrary::GetMousePositionOnViewport(this) * UWidgetLayoutLibrary::GetViewportScale(this);
}

FVector2D UHBSWorldSystem::GetCenterPositionOnScreen()
{
	int32 ViewportWidth = 0, ViewportHeight = 0;
	UGameplayStatics::GetPlayerController(this, 0)->GetViewportSize(ViewportWidth, ViewportHeight);

	return FVector2D(ViewportWidth / 2.0f, ViewportHeight / 2.0f);
}

FVector2D UHBSWorldSystem::ProjectWorldPositionToScreen(FVector WorldPosition)
{
	FVector2D ScreenPosition;
	FVector WorldDirection;

	UGameplayStatics::GetPlayerController(this, 0)->ProjectWorldLocationToScreen(WorldPosition, ScreenPosition, false);

	return ScreenPosition;
}

FVector UHBSWorldSystem::ProjectMousePositionToWorld(TArray<EHBSPlaceablePlaneType> PlaceablePlaneTypeList, FVector2D ScreenPositionOffset)
{
	return ProjectScreenPositionToWorld(PlaceablePlaneTypeList, GetMousePositionOnScreen() + ScreenPositionOffset);
}

FVector UHBSWorldSystem::ProjectScreenPositionToWorld(TArray<EHBSPlaceablePlaneType> PlaceablePlaneTypeList, FVector2D ScreenPosition)
{
	FVector WorldPosition;
	FVector WorldDirection;
	UGameplayStatics::GetPlayerController(this, 0)->DeprojectScreenPositionToWorld(ScreenPosition.X, ScreenPosition.Y, WorldPosition, WorldDirection);

	TArray<UHBSPlaceablePlaneSE*> PlaceablePlaneList = GetTypedAllSceneEntityListByPred<UHBSPlaceablePlaneSE>([&](UHBSPlaceablePlaneSE* PlaceablePlane)
		{
			return PlaceablePlane->IsVisible() && Algo::Find(PlaceablePlaneTypeList, PlaceablePlane->GetPlaneType());
		});

	PlaceablePlaneList.Sort([&](const UHBSPlaceablePlaneSE& Left, const UHBSPlaceablePlaneSE& Right)
		{
			if (Left.GetPlaneHeight() != Right.GetPlaneHeight())
			{
				if (WorldDirection.Z < 0)
					return Left.GetPlaneHeight() > Right.GetPlaneHeight();
				else
					return Left.GetPlaneHeight() < Right.GetPlaneHeight();

			}

			return Left.GetPriority() < Right.GetPriority();
		});

	for (UHBSPlaceablePlaneSE* PlaceablePlane : PlaceablePlaneList)
	{
		int32 PlaneHeight = PlaceablePlane->GetPlaneHeight();

		float Distance = (PlaneHeight - WorldPosition.Z) / WorldDirection.Z;

		if (Distance < 0)
			continue;

		FVector ProjectedPosition = WorldPosition + WorldDirection * Distance;

		if (PlaceablePlane->IsInPlane(FVector2D(ProjectedPosition)))
			return ProjectedPosition;
	}

	float Distance = (0 - WorldPosition.Z) / WorldDirection.Z;
	if (Distance < 0)
		return FVector(WorldPosition.X, WorldPosition.Y, 0);

	return WorldPosition + WorldDirection * Distance;
}

FVector UHBSWorldSystem::ProjectMousePositionToWorldWithSpecialHeight(int32 SpecialHeight, FVector2D ScreenPositionOffset)
{
	return ProjectScreenPositionToWorldWithSpecialHeight(SpecialHeight, GetMousePositionOnScreen() + ScreenPositionOffset);
}

FVector UHBSWorldSystem::ProjectScreenPositionToWorldWithSpecialHeight(int32 SpecialHeight, FVector2D ScreenPosition)
{
	FVector WorldPosition;
	FVector WorldDirection;

	UGameplayStatics::GetPlayerController(this, 0)->DeprojectScreenPositionToWorld(ScreenPosition.X, ScreenPosition.Y, WorldPosition, WorldDirection);

	float Distance = (SpecialHeight - WorldPosition.Z) / WorldDirection.Z;
	if (Distance < 0)
		return FVector(WorldPosition.X, WorldPosition.Y, SpecialHeight);

	return WorldPosition + WorldDirection * Distance;
}

FHBSTraceWallResult UHBSWorldSystem::ProjectMousePositionToSpecialWall(UHBSWallSceneEntity* SpecifiedWall, FVector2D ScreenPositionOffset)
{
	return ProjectScreenPositionToSpecialWall(SpecifiedWall, GetMousePositionOnScreen() + ScreenPositionOffset);
}

FHBSTraceWallResult UHBSWorldSystem::ProjectScreenPositionToSpecialWall(UHBSWallSceneEntity* SpecifiedWall, FVector2D ScreenPosition)
{
	if (SpecifiedWall == nullptr)
		return FHBSTraceWallResult();

	FVector WorldPosition;
	FVector WorldDirection;
	UGameplayStatics::GetPlayerController(this, 0)->DeprojectScreenPositionToWorld(ScreenPosition.X, ScreenPosition.Y, WorldPosition, WorldDirection);

	FVector WallStart = FVector(SpecifiedWall->WallStart.X, SpecifiedWall->WallStart.Y, 0);
	FVector WallEnd = FVector(SpecifiedWall->WallEnd.X, SpecifiedWall->WallEnd.Y, 0);

	FVector WallVector = (WallEnd - WallStart).GetSafeNormal();
	FVector WallNormal = FVector(-WallVector.Y, WallVector.X, 0);

	float Dot = FVector::DotProduct(WorldDirection, WallNormal);
	if (FMath::IsNearlyZero(Dot))
		return FHBSTraceWallResult();

	float Distance = FVector::DotProduct(WallStart - WorldPosition, WallNormal) / Dot;

	FVector ProjectedPosition = WorldPosition + WorldDirection * Distance;

	if (Distance <= 1)
		return FHBSTraceWallResult();

	FVector ProjectedPositionOnFloor = FVector(ProjectedPosition.X, ProjectedPosition.Y, 0);

	FVector2D ToWallVector = SpecifiedWall->GetWallStart() - FVector2D(WorldPosition);
	bool UseOutwardSurface = FVector2D::DotProduct(SpecifiedWall->GetWallNormal(), ToWallVector) < 0;

	FHBSTraceWallResult TraceWallResult;
	TraceWallResult.WallEntity = SpecifiedWall;
	TraceWallResult.HitPosition = ProjectedPosition;
	TraceWallResult.HitNormal = UseOutwardSurface ? SpecifiedWall->GetWallNormal3D() : -SpecifiedWall->GetWallNormal3D();
	TraceWallResult.HitLocalPosition = FVector2D(FVector::DotProduct(ProjectedPosition - WallStart, WallVector), ProjectedPosition.Z - SpecifiedWall->GetWallStartHeight());

	return TraceWallResult;
}

FHBSTraceWallResult UHBSWorldSystem::ProjectMousePositionToWall(FVector2D ScreenPositionOffset)
{
	return ProjectScreenPositionToWall(GetMousePositionOnScreen() + ScreenPositionOffset);
}

FHBSTraceWallResult UHBSWorldSystem::ProjectScreenPositionToWall(FVector2D ScreenPosition)
{
	FVector WorldPosition;
	FVector WorldDirection;

	UGameplayStatics::GetPlayerController(this, 0)->DeprojectScreenPositionToWorld(ScreenPosition.X, ScreenPosition.Y, WorldPosition, WorldDirection);

	return LineTraceWall(WorldPosition, WorldPosition + WorldDirection * 100000.0f);
}

FHBSTraceWallResult UHBSWorldSystem::LineTraceWall(const FVector& Start, const FVector& End)
{
	FHBSTraceWallResult TraceWallResult;

	FVector WorldPosition = Start;
	FVector WorldDirection = (End - Start).GetSafeNormal();

	float MinDistance = FLT_MAX;
	TArray<UHBSSceneEntityBase*> SceneEntityList = GetAllSceneEntities();
	for (UHBSSceneEntityBase* SceneEntity : SceneEntityList)
	{
		if (UHBSWallSceneEntity* WallEntity = Cast<UHBSWallSceneEntity>(SceneEntity))
		{
			if (WallEntity->IsWallVisible() == false)
				continue;

			FVector WallStart = FVector(WallEntity->WallStart.X, WallEntity->WallStart.Y, 0);
			FVector WallEnd = FVector(WallEntity->WallEnd.X, WallEntity->WallEnd.Y, 0);

			FVector WallVector = WallEntity->GetWallVector3D();
			FVector WallNormal = WallEntity->GetWallNormal3D();

			if (WallEntity->IsPointWall())
			{
				WallStart -= WallVector * 0.5f * UHBSConfig::Get()->WallWidth;
				WallEnd += WallVector * 0.5f * UHBSConfig::Get()->WallWidth;
			}

			float Dot = FVector::DotProduct(WorldDirection, WallNormal);
			if (FMath::IsNearlyZero(Dot))
				continue;

			float Distance = FVector::DotProduct(WallStart - WorldPosition, WallNormal) / Dot;

			FVector ProjectedPosition = WorldPosition + WorldDirection * Distance;

			if (Distance <= 1)
				continue;

			int32 TopHeight = WallEntity->GetWallStartHeight() + WallEntity->GetWallHeight();
			int32 BottomHeight = WallEntity->GetWallStartHeight();
			if (ProjectedPosition.Z > TopHeight || ProjectedPosition.Z < BottomHeight)
				continue;

			FVector ProjectedPositionOnFloor = FVector(ProjectedPosition.X, ProjectedPosition.Y, 0);

			if (FVector::DotProduct(WallStart - ProjectedPositionOnFloor, WallEnd - ProjectedPositionOnFloor) > 0)
				continue;

			FVector2D ToWallVector = WallEntity->GetWallStart() - FVector2D(Start);
			bool UseOutwardSurface = FVector2D::DotProduct(WallEntity->GetWallNormal(), ToWallVector) < 0;

			if (Distance < MinDistance)
			{
				MinDistance = Distance;

				TraceWallResult.WallEntity = WallEntity;
				TraceWallResult.HitPosition = ProjectedPosition;
				TraceWallResult.HitLocalPosition = FVector2D(FVector::DotProduct(ProjectedPosition - WallStart, WallVector), ProjectedPosition.Z);
				TraceWallResult.HitNormal = UseOutwardSurface ? WallEntity->GetWallNormal3D() : -WallEntity->GetWallNormal3D();
				TraceWallResult.Distance = Distance;
			}
		}
	}

	return TraceWallResult;
}

TArray<AHBSRenderEntityBase*> UHBSWorldSystem::GetRelatedRenderEntityList(const UHBSSceneEntityBase* SceneEntity)
{
	return RenderEntityProcessorContext->GetRelatedRenderEntityList(SceneEntity);
}

AHBSRenderEntityBase* UHBSWorldSystem::GetRelatedRenderEntity(const UHBSSceneEntityBase* SceneEntity)
{
	if (SceneEntity == nullptr)
		return nullptr;

	return RenderEntityProcessorContext->GetRelatedRenderEntity(SceneEntity);
}

AHBSRenderEntityBase* UHBSWorldSystem::GetRelatedRenderEntityByClass(const UHBSSceneEntityBase* SceneEntity, TSubclassOf<AHBSRenderEntityBase> RenderEntityClass)
{
	if (SceneEntity == nullptr || RenderEntityClass == nullptr)
		return nullptr;

	return RenderEntityProcessorContext->GetRelatedRenderEntityByClass(SceneEntity, RenderEntityClass);
}

void UHBSWorldSystem::UpdateSceneEntityList()
{
	if (SceneEntityProcessorContext->NeedUpdateSceneEntity())
	{
		SceneEntityProcessorContext->PreProcess();
		for (UHBSSceneEntityProcessorBase* Processor : SceneEntityProcessors)
			Processor->Process(SceneEntityProcessorContext);

		UpdateRenderEntityProcessorContext(SceneEntityProcessorContext, false);
		SceneEntityProcessorContext->PostProcess();
	}

	if (PreviewSceneEntityProcessorContext->NeedUpdateSceneEntity())
	{
		PreviewSceneEntityProcessorContext->PreProcess();
		UpdateRenderEntityProcessorContext(PreviewSceneEntityProcessorContext, true);
		PreviewSceneEntityProcessorContext->PostProcess();
	}
}

void UHBSWorldSystem::UpdateRenderEntityProcessorContext(UHBSSceneEntityProcessorContext* Context, bool IsPreview)
{
	for (UHBSSceneEntityBase* SceneEntity : Context->GetNewSceneEntityList())
	{
		if (SceneEntity->GetIsPlacementDone())
			RenderEntityProcessorContext->AddSceneEntity_Internal(SceneEntity, IsPreview);
	}

	for (UHBSSceneEntityBase* SceneEntity : Context->GetModifiedSceneEntityList())
		RenderEntityProcessorContext->ModifySceneEntity_Internal(SceneEntity, IsPreview);

	for (UHBSSceneEntityBase* SceneEntity : Context->GetRemovedSceneEntityList())
		RenderEntityProcessorContext->RemoveSceneEntity_Internal(SceneEntity, IsPreview);
}

void UHBSWorldSystem::UpdateRenderEntityList()
{
	if (RenderEntityProcessorContext->NeedUpdateRenderEntity())
	{
		RenderEntityProcessorContext->PreProcess();
		for (UHBSRenderEntityProcessorBase* Processor : RenderEntityProcessors)
		{
			Processor->Process(RenderEntityProcessorContext);
			RenderEntityProcessorContext->RefreshRelatedRenderEntityMap_Internal();
		}
		RenderEntityProcessorContext->PostProcess();
	}
}

void UHBSWorldSystem::UpdateHighLighted()
{
	for (AHBSRenderEntityBase* RenderEntity : RenderEntityProcessorContext->GetAllRenderEntityList())
	{
		RenderEntity->SetStencilValue(0);
	}

	TArray<UHBSSceneEntityBase*>* HightLightedAndSelectedList[] =
	{
		&HighLightedSceneEntityList,
		&SelectedSceneEntityList
	};

	int32 StencilValue[] = { 255, 254 };
	FColor Color[] = { FColor::White, FLinearColor(1.f, 0.63137f, 0.f).ToFColor(true) };

	for (int32 i = 0; i != 2; ++i)
	{
		for (UHBSSceneEntityBase* SceneEntity : *HightLightedAndSelectedList[i])
		{
			if (UHBSWindowSceneEntity* WindowEntity = Cast<UHBSWindowSceneEntity>(SceneEntity))
			{
				if (AHBSStaticMeshRenderEntity* StaticMeshEntity = Cast<AHBSStaticMeshRenderEntity>(RenderEntityProcessorContext->GetRelatedRenderEntityByClass(SceneEntity, AHBSStaticMeshRenderEntity::StaticClass())))
				{
					StaticMeshEntity->SetStencilValue(StencilValue[i]);
				}
			}
			else if (AHBSRenderEntityBase* RenderEntity = RenderEntityProcessorContext->GetRelatedRenderEntity(SceneEntity))
			{
				RenderEntity->SetStencilValue(StencilValue[i]);
			}
		}
	}
}
