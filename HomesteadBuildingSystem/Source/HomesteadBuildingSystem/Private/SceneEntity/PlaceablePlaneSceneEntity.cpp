#include "SceneEntity/PlaceablePlaneSceneEntity.h"
#include "SceneEntity/FloorSceneEntity.h"
#include "HBSWorldSystem.h"
#include "RenderEntity/RenderEntityBase.h"

void UHBSPlaceablePlaneSE::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);

	if (UHBSFloorSceneEntity* ParentLevel = GetTypedParentEntity<UHBSFloorSceneEntity>())
	{
		int32 Level = ParentLevel->GetLevelIndex();
		int32 CurrentLevel = UHBSWorldSystem::Get(this)->GetSetting().CurrentLevel;

		if (Type == EHBSPlaceablePlaneType::Ceiling)
			IsVisibility = (Level - 1) <= CurrentLevel;
		else
			IsVisibility = Level <= CurrentLevel;

		if (AHBSRenderEntityBase* RenderEntity = UHBSWorldSystem::Get(this)->GetRelatedRenderEntity(this))
		{
			RenderEntity->SetRenderEntityVisible(IsVisibility);
		}
	}
	else
	{
		IsVisibility = true;
	}
}
