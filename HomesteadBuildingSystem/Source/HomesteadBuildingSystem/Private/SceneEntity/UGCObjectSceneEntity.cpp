// Fill out your copyright notice in the Description page of Project Settings.


#include "SceneEntity/UGCObjectSceneEntity.h"

#include "Data/UGCUserData.h"
#include "SceneEntityOperator/UGCOperator/UGCOperatorBase.h"
#include "SceneEntityOperator/UGCOperator/UGCOperatorLibrary.h"

void UHBSUGCObjectSceneEntity::SetUGCProfile(UUGCProfile* InProfile)
{
	Profile = InProfile;
	UserData = nullptr;
}

void UHBSUGCObjectSceneEntity::SetUGCUserData(UUGCUserData* InUserData)
{
	if (Profile && InUserData && InUserData->Profile != Profile)
	{
		return;
	}

	UserData = InUserData;
	if (UserData)
	{
		Profile = Cast<UUGCProfile>(UserData->Profile);
	}
}

void UHBSUGCObjectSceneEntity::Reset()
{
	Profile = nullptr;
	UserData = nullptr;
}

UUGCProfile* UHBSUGCObjectSceneEntity::GetUGCProfile()
{
	return Profile;
}

UStaticMesh* UHBSUGCObjectSceneEntity::GetStaticMesh() const
{
	if (Profile)
		return Profile->UGCBaseInfo.UGCStaticMesh;

	return nullptr;
}

UUGCUserData* UHBSUGCObjectSceneEntity::GetOrCreateUGCUserData()
{
	if (!UserData && Profile)
	{
		UserData = NewObject<UUGCUserData>(this, "UGCUserData");
		UserData->Initialize(Profile);
	}

	return UserData;
}

void UHBSUGCObjectSceneEntity::RefreshOperatorsMetaDataList()
{
	Super::RefreshOperatorsMetaDataList();

	if (Profile)
	{
		FUGCOperatorLibrary::UpdateOperatorsMetaDataList(OperatorsMetaDataList, Profile, 0);
	}
}

void UHBSUGCObjectSceneEntity::PreInitializeOperator(UHBSSceneEntityOperatorBase* NewOperator, TSubclassOf<UHBSSceneEntityOperatorBase> OperatorClass,
	int32 Identify)
{
	if (UHBSUGCOperatorBase* UGCOperator = Cast<UHBSUGCOperatorBase>(NewOperator))
	{
		UGCOperator->SetProfileAndUserData(GetUGCProfile(), GetOrCreateUGCUserData());
	}
}
