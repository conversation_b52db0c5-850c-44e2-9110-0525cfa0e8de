#include "SceneEntity/WindowSceneEntity.h"
#include "SceneEntityOperator/WindowOperator/WindowMoveOperator.h"
#include "SceneEntityOperator/WindowOperator/WindowDragFirstPlacementOperator.h"
#include "SceneEntity/WallSceneEntity.h"
#include "HBSConfig.h"
#include "SceneEntityOperator/WindowOperator/WindowScaleOperator.h"
#include "RenderEntity/StaticMeshRenderEntity.h"

UHBSWindowSceneEntity::UHBSWindowSceneEntity(const FObjectInitializer& ObjectInitializer)
	: Super(ObjectInitializer)
{
	FHBSSceneEntityOperatorMetaData WindowMoveOperatorMetaData;
	WindowMoveOperatorMetaData.OperatorClass = UHBSWindowMoveOperator::StaticClass();

	OperatorsMetaDataList.Add(WindowMoveOperatorMetaData);

	for (int32 i = 0; i != 8; ++i)
	{
		FHBSSceneEntityOperatorMetaData WindowScaleOperatorMetaData;
		WindowScaleOperatorMetaData.OperatorClass = UHBSWindowTransformOperatorBase::StaticClass();
		WindowScaleOperatorMetaData.Identify = i;
		OperatorsMetaDataList.Add(WindowScaleOperatorMetaData);
	}
}

FBox UHBSWindowSceneEntity::CalcEntityBound() const
{
	if (AHBSRenderEntityBase* MeshRenderEntity = UHBSWorldSystem::Get(this)->GetRelatedRenderEntityByClass(this, AHBSStaticMeshRenderEntity::StaticClass()))
	{
		FVector Origin;
		FVector Extent;
		MeshRenderEntity->GetActorBounds(false, Origin, Extent);
		return FBox(Origin - Extent, Origin + Extent);
	}
	else
		return FBox(ForceInitToZero);
}

FVector UHBSWindowSceneEntity::CalcWindowWorldPosition() const
{
	if (RelativeWallSceneEntity == nullptr)
		return FVector::ZeroVector;

	return RelativeWallSceneEntity->CalcWorldPositionOnWall(WindowPosition);
}

void UHBSWindowSceneEntity::SetPlacementInfo(FVector2D InWindowPosition, FVector2D InWindowScale, UHBSWallSceneEntity* InRelativeWall)
{
	WindowPosition = InWindowPosition;
	WindowScale = InWindowScale;
	RelativeWallSceneEntity = InRelativeWall;
	SetParentEntity(RelativeWallSceneEntity);
}

UHBSSceneEntityOperatorBase* UHBSWindowSceneEntity::CreateFirstPlacementOperator()
{
	UHBSWindowDragFirstPlacementOperator* FirstPlacementOperator = NewObject<UHBSWindowDragFirstPlacementOperator>(this, UHBSWindowDragFirstPlacementOperator::StaticClass());
	FirstPlacementOperator->Initialize(this);
	return FirstPlacementOperator;
}

void UHBSWindowSceneEntity::PreInitializeOperator(UHBSSceneEntityOperatorBase* NewOperator, TSubclassOf<UHBSSceneEntityOperatorBase> OperatorClass, int32 Identify)
{
	Super::PreInitializeOperator(NewOperator, OperatorClass, Identify);

	if (UHBSWindowScaleOperator* WindowScaleOperator = Cast<UHBSWindowScaleOperator>(NewOperator))
	{
		FIntVector2 WindowCorner[] =
		{
			FIntVector2(0, 1),
			FIntVector2(1, 1),
			FIntVector2(1, 0),
			FIntVector2(1, -1),
			FIntVector2(0, -1),
			FIntVector2(-1, -1),
			FIntVector2(-1, 0),
			FIntVector2(-1, 1)
		};

		WindowScaleOperator->SetTargetCorner(WindowCorner[Identify]);
	}
}

void UHBSWindowSceneEntity::RefreshOperatorsMetaDataList()
{
	Super::RefreshOperatorsMetaDataList();

	FHBSSceneEntityOperatorMetaData WindowMoveOperatorMetaData;
	WindowMoveOperatorMetaData.OperatorClass = UHBSWindowMoveOperator::StaticClass();
	WindowMoveOperatorMetaData.Position = CalcWindowWorldPosition();
	OperatorsMetaDataList.Add(WindowMoveOperatorMetaData);

	if (RelativeWallSceneEntity)
	{
		FIntVector2 WindowCorner[] =
		{
			FIntVector2(0, 1),
			FIntVector2(1, 1),
			FIntVector2(1, 0),
			FIntVector2(1, -1),
			FIntVector2(0, -1),
			FIntVector2(-1, -1),
			FIntVector2(-1, 0),
			FIntVector2(-1, 1)
		};

		for (int32 i = 0; i != 8; ++i)
		{
			FHBSSceneEntityOperatorMetaData ScaleMetaData;
			ScaleMetaData.OperatorClass = UHBSWindowScaleOperator::StaticClass();
			ScaleMetaData.Identify = i;
			ScaleMetaData.Position = RelativeWallSceneEntity->CalcWorldPositionOnWall(GetWindowLocalPosition() + GetWindowSizeWithScale() * FVector2D(WindowCorner[i].X, WindowCorner[i].Y) * 0.5);
			ScaleMetaData.Direction = (WindowCorner[i].X * GetWindowSizeWithScale().X * RelativeWallSceneEntity->GetWallVector3D() + WindowCorner[i].Y * GetWindowSizeWithScale().Y * FVector::UpVector).GetSafeNormal();
			OperatorsMetaDataList.Add(ScaleMetaData);
		}
	}
}

void UHBSWindowSceneEntity::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);

	if (RelativeWallSceneEntity)
	{
		if (AHBSRenderEntityBase* RenderEntity = UHBSWorldSystem::Get(this)->GetRelatedRenderEntityByClass(this, AHBSStaticMeshRenderEntity::StaticClass()))
		{
			RenderEntity->SetRenderEntityVisible(RelativeWallSceneEntity->IsWallVisible());
		}
	}
}
