// Fill out your copyright notice in the Description page of Project Settings.


#include "SceneEntity/TransformSceneEntity.h"
#include "SceneEntityOperator/DragFirstPlacementOperator.h"
#include "RenderEntity/RenderEntityBase.h"

FBox UHBSTransformSceneEntity::CalcEntityBound() const
{
	if (AHBSRenderEntityBase* RenderEntity = UHBSWorldSystem::Get(this)->GetRelatedRenderEntity(this))
	{
		FVector Origin;
		FVector Extent;
		RenderEntity->GetActorBounds(false, Origin, Extent);
		return FBox(Origin - Extent, Origin + Extent);
	}

	return FBox(ForceInitToZero);
}

void UHBSTransformSceneEntity::SetIsVisible(bool InIsVisible)
{
	if (AHBSRenderEntityBase* RenderEntity = UHBSWorldSystem::Get(this)->GetRelatedRenderEntity(this))
	{
		RenderEntity->SetRenderEntityVisible(InIsVisible);
	}
}

UHBSSceneEntityOperatorBase* UHBSTransformSceneEntity::CreateFirstPlacementOperator()
{
	UHBSDragFirstPlacementOperator* FirstPlacementOperator = NewObject<UHBSDragFirstPlacementOperator>(this,
		UHBSDragFirstPlacementOperator::StaticClass());
	FirstPlacementOperator->Initialize(this);
	return FirstPlacementOperator;
}

void UHBSTransformSceneEntity::RefreshOperatorsMetaDataList()
{
	Super::RefreshOperatorsMetaDataList();

	FHBSSceneEntityOperatorMetaData MeshMoveOperatorMetaData;
	MeshMoveOperatorMetaData.OperatorClass = UHBSMeshMoveOperator::StaticClass();
	MeshMoveOperatorMetaData.Position = GetTransform().GetLocation();
	OperatorsMetaDataList.Add(MeshMoveOperatorMetaData);
}
