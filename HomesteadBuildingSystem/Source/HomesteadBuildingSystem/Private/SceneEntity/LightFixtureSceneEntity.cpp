#include "SceneEntity/LightFixtureSceneEntity.h"
#include "SceneEntityOperator/TransformOperator/WallMountedMoveFirstPlacementOperator.h"

UHBSSceneEntityOperatorBase* UHBSLightFixtureSceneEntity::CreateFirstPlacementOperator()
{
	if (LightFixtureAsset->Type != EHBSLightFixtureType::WallMounted)
		return Super::CreateFirstPlacementOperator();

	UHBSWallMountedMoveFirstPlacementOperator* FirstPlacementOperator = NewObject<UHBSWallMountedMoveFirstPlacementOperator>(this);
	FirstPlacementOperator->Initialize(this);
	return FirstPlacementOperator;
}

void UHBSLightFixtureSceneEntity::RefreshOperatorsMetaDataList()
{
	if (LightFixtureAsset->Type != EHBSLightFixtureType::WallMounted)
	{
		Super::RefreshOperatorsMetaDataList();
		return;
	}

	UHBSSceneEntityBase::RefreshOperatorsMetaDataList();

	FHBSSceneEntityOperatorMetaData WallMountedMoveOperatorMetaData;
	WallMountedMoveOperatorMetaData.OperatorClass = UHBSWallMountedMoveOperator::StaticClass();
	WallMountedMoveOperatorMetaData.Position = GetTransform().GetLocation();
	OperatorsMetaDataList.Add(WallMountedMoveOperatorMetaData);
}
