#include "SceneEntity/SceneEntityBase.h"
#include "HBSWorldSystem.h"
#include "SceneEntityOperator/DeleteOperator.h"
#include "Misc/LowLevelTestAdapter.h"

void UHBSSceneEntityOperatorBase::Initialize(UHBSSceneEntityBase* InTargetEntity)
{
	TargetEntity = InTargetEntity;
	UHBSWorldSystem::Get(this)->NotifyOperatorCreated(this);
}

void UHBSSceneEntityOperatorBase::DestroySelf()
{
	bIsValid = false;
	UHBSWorldSystem::Get(this)->NotifyOperatorDestroyed(this);
}

UHBSSceneEntityOperatorBase* UHBSSceneEntityBase::InitializeAndGenerateFirstPlacementOperator()
{
	CHECK(IsPlacementDone == false);

	OnInitialize();
	RefreshOperatorsMetaDataList();
	if (UHBSSceneEntityOperatorBase* FirstPlacementOperator = CreateFirstPlacementOperator())
	{
		IsPlacementDone = false;
		return FirstPlacementOperator;
	}
	else
	{
		IsPlacementDone = true;
		return nullptr;
	}
}

void UHBSSceneEntityBase::InitializeImmediate()
{
	CHECK(IsPlacementDone == false);

	IsPlacementDone = true;
	OnInitialize();
	RefreshOperatorsMetaDataList();
}

void UHBSSceneEntityBase::Destroy()
{
	TArray<UHBSSceneEntityBase*> ChildrenSE = ChildrenSEList;
	for (UHBSSceneEntityBase* Child : ChildrenSE)
		Child->SetParentEntity(GetParentEntity());

	SetParentEntity(nullptr);

	ConditionalBeginDestroy();
}

bool UHBSSceneEntityBase::CanCreateOperator(TSubclassOf<UHBSSceneEntityOperatorBase> OperatorClass, int32 Identify)
{
	if (OperatorClass.Get() == nullptr)
		return false;

	return OperatorsMetaDataList.ContainsByPredicate(
		[&](const FHBSSceneEntityOperatorMetaData& MetaData) {
			return MetaData.OperatorClass == OperatorClass && MetaData.Identify == Identify;
		});
}

UHBSSceneEntityOperatorBase* UHBSSceneEntityBase::CreateOperatorByClass(TSubclassOf<UHBSSceneEntityOperatorBase> OperatorClass, int32 Identify, FHBSPreOperatorInitCallback PreInitCallback)
{
	if (CanCreateOperator(OperatorClass, Identify) == false)
		return nullptr;

	if (UHBSSceneEntityOperatorBase* NewOperator = NewObject<UHBSSceneEntityOperatorBase>(this, OperatorClass))
	{
		PreInitializeOperator(NewOperator, OperatorClass, Identify);
		PreInitCallback.ExecuteIfBound(NewOperator, OperatorClass, Identify);
		NewOperator->Initialize(this);
		return NewOperator;
	}

	return nullptr;
}

void UHBSSceneEntityBase::RefreshOperatorsMetaDataList()
{
	OperatorsMetaDataList.Empty();

	FHBSSceneEntityOperatorMetaData DeleteMetaData;
	DeleteMetaData.OperatorClass = UHBSDeleteOperator::StaticClass();
	OperatorsMetaDataList.Add(DeleteMetaData);
}

void UHBSSceneEntityBase::SetParentEntity(UHBSSceneEntityBase* InParentSE)
{
	if (InParentSE == ParentSE)
		return;

	if (InParentSE)
	{
		CHECK(InParentSE->IsChildOf(this) == false);
		CHECK(InParentSE != this);
	}

	if (ParentSE)
		ParentSE->ChildrenSEList.Remove(this);

	if (InParentSE)
		InParentSE->ChildrenSEList.AddUnique(this);

	ParentSE = InParentSE;
}

bool UHBSSceneEntityBase::IsChildOf(UHBSSceneEntityBase* OtherSE)
{
	for (UHBSSceneEntityBase* SE = GetParentEntity(); SE != nullptr; SE = SE->GetParentEntity())
	{
		if (SE == OtherSE)
			return true;
	}
	return false;
}

