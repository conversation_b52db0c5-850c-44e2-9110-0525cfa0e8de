#include "SceneEntity/WallSceneEntity.h"
#include "HBSConfig.h"
#include "UGCProfile.h"
#include "Data/UGCUserData.h"
#include "SceneEntityOperator/UGCOperator/UGCOperatorBase.h"
#include "SceneEntityOperator/UGCOperator/UGCOperatorLibrary.h"
#include "RenderEntity/WallRenderEntity.h"
#include "HBSWorldSystem.h"
#include "Kismet/GameplayStatics.h"
#include "SceneEntity/FloorSceneEntity.h"
#include "SceneEntityOperator/WallOperator/WallFirstPlacementOperator.h"
#include "SceneEntityOperator/WallOperator/WallMoveOperator.h"

void UHBSWallSceneEntity::SetupInitInfo(int32 InWallHeight, UUGCProfile* InWallUGCProfile)
{
	WallHeight = InWallHeight;
	WallUGCProfile = InWallUGCProfile;
	if (WallUGCProfile)
	{
		if (WallUserData)
		{
			if (WallUserData->Profile != WallUGCProfile)
			{
				WallUserData = nullptr;
			}
		}

		OnInitialize();
	}
	else
	{
		WallUserData = nullptr;
	}
}

void UHBSWallSceneEntity::SetUGCUserData(UUGCUserData* InUserData)
{
	if (!WallUGCProfile || (InUserData && InUserData->Profile != WallUGCProfile))
	{
		return;
	}

	WallUserData = InUserData;
}

UUGCUserData* UHBSWallSceneEntity::GetOrCreateUGCUserData()
{
	if (!WallUserData && WallUGCProfile)
	{
		WallUserData = NewObject<UUGCUserData>(this, "WallUGCUserData");
		WallUserData->Initialize(WallUGCProfile);
	}

	return WallUserData;
}

void UHBSWallSceneEntity::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);

	CurrentVisibility = CalcWallVisible();

	if (AHBSRenderEntityBase* RenderEntity = UHBSWorldSystem::Get(this)->GetRelatedRenderEntityByClass(this, AHBSWallRenderEntity::StaticClass()))
	{
		AHBSWallRenderEntity* WallRenderEntity = Cast<AHBSWallRenderEntity>(RenderEntity);
		WallRenderEntity->SetWallVisibility(CurrentVisibility);
	}
}

void UHBSWallSceneEntity::OnPreDestroy()
{
	TArray<UHBSSceneEntityBase*> Children = GetChildrenEntityList();
	for (UHBSSceneEntityBase* Child : Children)
	{
		UHBSWorldSystem::Get(this)->RemoveSceneEntity(Child);
	}
}

void UHBSWallSceneEntity::CopyUGCData(UUGCUserData* InSourceUGCData)
{
	if (InSourceUGCData && WallUserData)
	{
		WallUserData->DefaultDyeCustomDataIndex = InSourceUGCData->DefaultDyeCustomDataIndex;
		WallUserData->DyeCustomDatas = InSourceUGCData->DyeCustomDatas;
		WallUserData->PatternCustomDatas = InSourceUGCData->PatternCustomDatas;
		WallUserData->AppliqueCustomDatas = InSourceUGCData->AppliqueCustomDatas;
		WallUserData->CutCustomDatas = InSourceUGCData->CutCustomDatas;
		WallUserData->FabricCustomDatas = InSourceUGCData->FabricCustomDatas;
	}
}

FBox UHBSWallSceneEntity::CalcEntityBound() const
{
	FVector2D Start = WallStart - GetWallVector() * 0.5 * UHBSConfig::Get()->WallWidth;
	FVector2D End = WallEnd + GetWallVector() * 0.5 * UHBSConfig::Get()->WallWidth;
	FVector2D Min = FVector2D(FMath::Min(Start.X, End.X), FMath::Min(Start.Y, End.Y));
	FVector2D Max = FVector2D(FMath::Max(Start.X, End.X), FMath::Max(Start.Y, End.Y));

	return FBox(FVector(Min.X, Min.Y, 0), FVector(Max.X, Max.Y, WallHeight));
}

FVector UHBSWallSceneEntity::CalcWorldPositionOnWall(FVector2D InLocalPosition)
{
	return GetWallStart3D() + GetWallVector3D() * InLocalPosition.X + FVector::UpVector * InLocalPosition.Y;
}

UHBSSceneEntityOperatorBase* UHBSWallSceneEntity::CreateFirstPlacementOperator()
{
	UHBSWallFirstPlacementOperator* NewOperator = NewObject<UHBSWallFirstPlacementOperator>(this, UHBSWallFirstPlacementOperator::StaticClass());
	NewOperator->Initialize(this);
	return NewOperator;
}

void UHBSWallSceneEntity::RefreshOperatorsMetaDataList()
{
	Super::RefreshOperatorsMetaDataList();

	if (WallUGCProfile)
	{
		FUGCOperatorLibrary::UpdateOperatorsMetaDataList(OperatorsMetaDataList, WallUGCProfile, 0);
	}

	FHBSSceneEntityOperatorMetaData WallMoveMetaData;
	WallMoveMetaData.OperatorClass = UHBSWallMoveOperator::StaticClass();
	WallMoveMetaData.Position = (GetWallStart3D() + GetWallEnd3D()) * 0.5 + FVector(0, 0, WallHeight * 0.5);
	OperatorsMetaDataList.Add(WallMoveMetaData);
}

void UHBSWallSceneEntity::OnInitialize()
{
	// WallInsideUGCProfile = NewObject<UUGCProfile>(this, NAME_None);
	// WallInsideUGCProfile->CreateDyeProfile()->AddMaterialMapping(0, FMaterialBindingInfo(NAME_None, 0), FMaterialParameterInfo(TEXT("")));
	// WallInsideUGCProfile->CreatePatternProfile()->AddMaterialMapping(0, FMaterialBindingInfo(NAME_None, 0));
	// WallInsideUGCProfile->CreateAppliqueProfile()->AddMaterialMapping(0, FMaterialBindingInfo(NAME_None, 0));
	// //WallUGCProfile->CreateFabricProfile()->AddMaterialMapping(0, FMaterialBindingInfo(NAME_None, 0));
	//
	if (WallUGCProfile && !WallUserData)
	{
		WallUserData = NewObject<UUGCUserData>(this, TEXT("WallUserData"));
		WallUserData->Initialize(WallUGCProfile);
	}
}

void UHBSWallSceneEntity::PreInitializeOperator(UHBSSceneEntityOperatorBase* NewOperator, TSubclassOf<UHBSSceneEntityOperatorBase> OperatorClass,
	int32 Identify)
{
	if (UHBSUGCOperatorBase* UGCOperator = Cast<UHBSUGCOperatorBase>(NewOperator))
	{
		UGCOperator->SetProfileAndUserData(WallUGCProfile, WallUserData);
	}
}

EHBSWallVisibility UHBSWallSceneEntity::CalcWallVisible()
{
	if (UHBSFloorSceneEntity* ParentLevel = GetTypedParentEntity<UHBSFloorSceneEntity>())
	{
		int32 WallLevel = ParentLevel->GetLevelIndex();
		int32 CurrentLevel = UHBSWorldSystem::Get(this)->GetSetting().CurrentLevel;

		if (WallLevel < CurrentLevel)
			return EHBSWallVisibility::Visibile;
		else if (WallLevel > CurrentLevel)
			return EHBSWallVisibility::Hidden;
	}

	EHBSWallShowMode WallShowMode = UHBSWorldSystem::Get(this)->GetSetting().WallShowMode;

	if (WallShowMode == EHBSWallShowMode::ShowAll)
		return EHBSWallVisibility::Visibile;

	if (WallShowMode == EHBSWallShowMode::HideAll)
		return EHBSWallVisibility::FootOnly;

	if (WallType == EHBSWallType::NonEnclosingWall)
		return EHBSWallVisibility::Visibile;

	if (WallType == EHBSWallType::FullyEnclosedWall)
		return EHBSWallVisibility::FootOnly;

	FVector WallNormal = FVector(0, 0, 0);
	switch (WallType)
	{
	case EHBSWallType::OuterFaceOutward: WallNormal = GetWallNormal3D(); break;
	case EHBSWallType::InsideFaceOutward: WallNormal = -GetWallNormal3D(); break;
	}

	FVector WorldPosition;
	FVector WorldDirection;

	int32 ViewportWidth, ViewportHeight;
	UGameplayStatics::GetPlayerController(this, 0)->GetViewportSize(ViewportWidth, ViewportHeight);

	UGameplayStatics::GetPlayerController(this, 0)->DeprojectScreenPositionToWorld(ViewportWidth * 0.5, ViewportHeight * 0.5, WorldPosition, WorldDirection);

	FVector ToWallDirection = GetWallStart3D() - WorldPosition;
	ToWallDirection = FVector(ToWallDirection.X, ToWallDirection.Y, 0).GetSafeNormal();

	bool Visibility = FVector::DotProduct(ToWallDirection, WallNormal) > 0;
	return Visibility ? EHBSWallVisibility::Visibile : EHBSWallVisibility::FootOnly;
}
