#include "SceneEntityProcessor/WallSceneEntityProcessor.h"
#include "SceneEntity/WallSceneEntity.h"
#include "HBSMathLibrary.h"
#include "HBSWorldSystem.h"
#include "Misc/LowLevelTestAdapter.h"
#include "SceneEntity/WindowSceneEntity.h"

struct FMergedWallInfoForSceneEntity
{
	TArray<UHBSWallSceneEntity*> WallEntityList;
	TArray<bool> WallUsedList;
	TArray<FFloatRange> WallRange;
	FVector2D Start;
	FVector2D End;
	FVector2D Vector;
	TArray<float> SplitPoints;
};

void UHBSWallSceneEntityProcessor::Process(UHBSSceneEntityProcessorContext* InContext)
{
	bool NeedProcess = false;
	NeedProcess = NeedProcess || InContext->HasAnyEntityChangedByClass(UHBSWallSceneEntity::StaticClass());
	NeedProcess = NeedProcess || InContext->HasAnyEntityChangedByClass(UHBSWindowSceneEntity::StaticClass());

	if (NeedProcess == false)
		return;

	TArray<UHBSWallSceneEntity*> AllNeedProcessWallSEList = InContext->GetTypedSceneEntityList<UHBSWallSceneEntity>(SE_All,
		[](UHBSWallSceneEntity* Wall)
		{
			return Wall->WallStart != Wall->WallEnd;
		});

	TMap<int32, TArray<UHBSWallSceneEntity*>> StartHeight2WallMap;
	for (UHBSWallSceneEntity* Wall : AllNeedProcessWallSEList)
	{
		StartHeight2WallMap.FindOrAdd(Wall->GetWallStartHeight()).Add(Wall);
	}

	for (auto& It : StartHeight2WallMap)
	{
		TArray<UHBSWallSceneEntity*>& NeedProcessWallSceneEntityList = It.Value;

		TArray<TArray<UHBSWallSceneEntity*>> MergedWallArrayList;
		for (UHBSWallSceneEntity* WallEntity : NeedProcessWallSceneEntityList)
		{
			bool bFindMergedWall = false;
			for (TArray<UHBSWallSceneEntity*>& MergedWallArray : MergedWallArrayList)
			{
				if (CouldMergedIntoWallList(WallEntity, MergedWallArray))
				{
					MergedWallArray.Add(WallEntity);
					bFindMergedWall = true;
					break;
				}
			}

			if (bFindMergedWall == false)
				MergedWallArrayList.Add(TArray<UHBSWallSceneEntity*>{ WallEntity });
		}

		float WallWidth = UHBSConfig::Get()->WallWidth;
		TArray<FMergedWallInfoForSceneEntity> MergedWallInfoList;
		for (TArray<UHBSWallSceneEntity*>& MergedWallArray : MergedWallArrayList)
		{
			FVector2D MergedWallDirection = CalcMergedWallDirection(MergedWallArray);
			FVector2D WallStart = MergedWallArray[0]->WallStart;

			Algo::SortBy(MergedWallArray, [&](UHBSWallSceneEntity* WallEntity)
			{
				float Start = FVector2D::DotProduct(WallEntity->WallStart - WallStart, MergedWallDirection);
				float End = FVector2D::DotProduct(WallEntity->WallEnd - WallStart, MergedWallDirection);
				return FMath::Min(Start, End);
			});

			TArray<FFloatRange> MergedWallRange;
			for (UHBSWallSceneEntity* WallEntity : MergedWallArray)
			{
				float Start = FVector2D::DotProduct(WallEntity->WallStart - WallStart, MergedWallDirection);
				float End = FVector2D::DotProduct(WallEntity->WallEnd - WallStart, MergedWallDirection);

				MergedWallRange.Add(FFloatRange(FMath::Min(Start, End), FMath::Max(Start, End)));
			}

			float End = -FLT_MAX;
			for (int32 i = 0; i != MergedWallArray.Num(); ++i)
			{
				FFloatRange Range = MergedWallRange[i];
				UHBSWallSceneEntity* WallEntity = MergedWallArray[i];

				if (Range.GetLowerBoundValue() - WallWidth > End)
				{
					FMergedWallInfoForSceneEntity MergedWallInfo;
					MergedWallInfo.WallEntityList.Add(WallEntity);
					MergedWallInfo.WallUsedList.Add(false);
					MergedWallInfoList.Add(MergedWallInfo);
				}
				else
				{
					FMergedWallInfoForSceneEntity& MergedWallInfo = MergedWallInfoList.Last();
					MergedWallInfo.WallEntityList.Add(WallEntity);
					MergedWallInfo.WallUsedList.Add(false);
				}

				End = Range.GetUpperBoundValue();
			}
		}

		for (FMergedWallInfoForSceneEntity& MergedWallInfo : MergedWallInfoList)
		{
			FVector2D WallDirection = CalcMergedWallDirection(MergedWallInfo.WallEntityList);

			FVector2D Origin = MergedWallInfo.WallEntityList[0]->WallStart;

			float MinDot = FLT_MAX;
			float MaxDot = -FLT_MAX;
			FVector2D Start;
			FVector2D End;
			for (UHBSWallSceneEntity* Wall : MergedWallInfo.WallEntityList)
			{
				FVector2D Point[2] = { Wall->WallStart, Wall->WallEnd };
				for (int32 j = 0; j != 2; ++j)
				{
					FVector2D PointDirection = Point[j] - Origin;
					float DotProduct = FVector2D::DotProduct(PointDirection, WallDirection);
					if (DotProduct > MaxDot)
					{
						MaxDot = DotProduct;
						End = Point[j];
					}
					if (DotProduct < MinDot)
					{
						MinDot = DotProduct;
						Start = Point[j];
					}
				}
			}

			MergedWallInfo.Start = Start;
			MergedWallInfo.End = End;
			MergedWallInfo.Vector = (End - Start).GetSafeNormal();
		}

		for (FMergedWallInfoForSceneEntity& MergedWallInfo : MergedWallInfoList)
		{
			for (UHBSWallSceneEntity* Wall : MergedWallInfo.WallEntityList)
			{
				float WallStart = FVector2D::DotProduct(Wall->WallStart - MergedWallInfo.Start, MergedWallInfo.Vector);
				float WallEnd = FVector2D::DotProduct(Wall->WallEnd - MergedWallInfo.Start, MergedWallInfo.Vector);

				float Start = FMath::Min(WallStart, WallEnd);
				float End = FMath::Max(WallStart, WallEnd);

				MergedWallInfo.WallRange.Add(FFloatRange(Start, End));
			}
		}

		for (int32 i = 0; i != MergedWallInfoList.Num(); ++i)
		{
			FMergedWallInfoForSceneEntity& MergedWallInfo = MergedWallInfoList[i];

			TArray<FVector2D> IntersectionPoints;
			for (int32 j = 0; j != MergedWallInfoList.Num(); ++j)
			{
				if (i == j)
					continue;

				const FMergedWallInfoForSceneEntity& OtherMergedWallInfo = MergedWallInfoList[j];

				FVector2D IntersectionPoint;
				if (UHBSMathLibrary::CalcLineSegmentIntersection(
						MergedWallInfo.Start, MergedWallInfo.End,
						OtherMergedWallInfo.Start - OtherMergedWallInfo.Vector * WallWidth,
						OtherMergedWallInfo.End + OtherMergedWallInfo.Vector * WallWidth,
						IntersectionPoint)
				)
				{
					IntersectionPoints.Add(IntersectionPoint);
				}
			}

			float WallLength = FVector2D::Distance(MergedWallInfo.Start, MergedWallInfo.End);
			for (FVector2D IntersectionPoint : IntersectionPoints)
			{
				float SplitPoint = FVector2D::DotProduct(IntersectionPoint - MergedWallInfo.Start, MergedWallInfo.Vector);

				MergedWallInfo.SplitPoints.Add(SplitPoint);
			}
			MergedWallInfo.SplitPoints.Sort();
		}

		for (FMergedWallInfoForSceneEntity& MergedWallInfo : MergedWallInfoList)
		{
			TArray<FFloatRange> WallStride;
			float CurrentWallSplitPoint = 0;
			for (float SplitPoint : MergedWallInfo.SplitPoints)
			{
				if (SplitPoint - CurrentWallSplitPoint > WallWidth)
				{
					WallStride.Add(FFloatRange(CurrentWallSplitPoint, SplitPoint));
					CurrentWallSplitPoint = SplitPoint;
				}
			}

			float WallLength = FVector2D::Distance(MergedWallInfo.Start, MergedWallInfo.End);

			if (WallStride.IsEmpty() == false)
			{
				if (WallLength - CurrentWallSplitPoint > WallWidth)
					WallStride.Add(FFloatRange(CurrentWallSplitPoint, WallLength));
				else
					WallStride.Last().SetUpperBoundValue(WallLength);
			}
			else
			{
				WallStride.Add(FFloatRange(0, WallLength));
			}

			for (int32 i = 0; i < WallStride.Num(); ++i)
			{
				UHBSWallSceneEntity* WallSceneEntity = nullptr;
				int32 SuitedWallIndex = INDEX_NONE;
				float MaxOverlapLength = -FLT_MAX;
				for (int32 j = 0; j < MergedWallInfo.WallEntityList.Num(); ++j)
				{
					const FFloatRange& WallRange = MergedWallInfo.WallRange[j];
					float OverlapLength = FFloatRange::Intersection(WallRange, WallStride[i]).Size<float>();

					if (OverlapLength > MaxOverlapLength)
					{
						MaxOverlapLength = OverlapLength;
						SuitedWallIndex = j;
					}
				}

				if (SuitedWallIndex == INDEX_NONE || MergedWallInfo.WallUsedList[SuitedWallIndex])
				{
					WallSceneEntity = Cast<UHBSWallSceneEntity>(InContext->PlacementSceneEntityByClass(UHBSWallSceneEntity::StaticClass()));
				}
				else
				{
					WallSceneEntity = MergedWallInfo.WallEntityList[SuitedWallIndex];
					MergedWallInfo.WallUsedList[SuitedWallIndex] = true;
				}

				float WallStart = WallStride[i].GetLowerBoundValue();
				float WallEnd = WallStride[i].GetUpperBoundValue();

				CHECK(SuitedWallIndex != INDEX_NONE);

				UHBSWallSceneEntity* SuitedWallEntity = MergedWallInfo.WallEntityList[SuitedWallIndex];

				if (FVector2D::DotProduct(MergedWallInfo.Vector, SuitedWallEntity->GetWallVector()) < 0)
				{
					WallStart = WallStride[i].GetUpperBoundValue();
					WallEnd = WallStride[i].GetLowerBoundValue();
				}

				WallSceneEntity->WallStart = MergedWallInfo.Start + MergedWallInfo.Vector * WallStart;
				WallSceneEntity->WallEnd = MergedWallInfo.Start + MergedWallInfo.Vector * WallEnd;
				WallSceneEntity->WallStartHeight = SuitedWallEntity->GetWallStartHeight();

				WallSceneEntity->SetupInitInfo(SuitedWallEntity->WallHeight, SuitedWallEntity->WallUGCProfile);
				WallSceneEntity->CopyUGCData(SuitedWallEntity->WallUserData);

				InContext->MarkSceneEntityModify(WallSceneEntity);
			}

			for (int32 i = 0; i < MergedWallInfo.WallEntityList.Num(); ++i)
			{
				if (MergedWallInfo.WallUsedList[i] == false)
					InContext->RemoveSceneEntity(MergedWallInfo.WallEntityList[i]);
			}
		}
	}
}

bool UHBSWallSceneEntityProcessor::CouldMergedIntoWallList(const UHBSWallSceneEntity* InWallEntity,
	const TArray<UHBSWallSceneEntity*>& InWallEntitylist, float DotThreshold) const
{
	float WallWidth = UHBSConfig::Get()->WallWidth;

	FVector2D WallDirection = InWallEntity->GetWallVector();
	for (UHBSWallSceneEntity* OtherWall : InWallEntitylist)
	{
		FVector2D OtherWallDirection = OtherWall->GetWallVector();
		float DotProduct = FVector2D::DotProduct(WallDirection, OtherWallDirection);
		if (FMath::Abs(DotProduct) < DotThreshold)
			return false;

		float StartNormalDistance = FVector2D::DotProduct(InWallEntity->WallStart - OtherWall->WallStart, OtherWall->GetWallNormal());
		if (FMath::Abs(StartNormalDistance) > WallWidth)
			return false;

		float EndNormalDistance = FVector2D::DotProduct(InWallEntity->WallEnd - OtherWall->WallStart, OtherWall->GetWallNormal());
		if (FMath::Abs(EndNormalDistance) > WallWidth)
			return false;
	}

	return true;
}

FVector2D UHBSWallSceneEntityProcessor::CalcMergedWallDirection(const TArray<UHBSWallSceneEntity*>& InWallEntitylist) const
{
	if (InWallEntitylist.IsEmpty())
		return FVector2D();

	FVector2D MergedWallDirection = InWallEntitylist[0]->GetWallVector();
	for (int32 i = 1; i != InWallEntitylist.Num(); ++i)
	{
		FVector2D WallDirection = InWallEntitylist[i]->GetWallVector();
		if (FVector2D::DotProduct(MergedWallDirection, WallDirection) < 0)
			WallDirection *= -1;

		MergedWallDirection += WallDirection;
	}

	return MergedWallDirection.GetSafeNormal();
}
