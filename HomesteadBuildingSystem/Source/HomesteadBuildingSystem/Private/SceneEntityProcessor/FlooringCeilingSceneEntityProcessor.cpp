#include "SceneEntityProcessor/FlooringCeilingSceneEntityProcessor.h"
#include "SceneEntity/WallSceneEntity.h"
#include "SceneEntity/SurfaceSceneEntity.h"
#include "SceneEntity/FlooringSceneEntity.h"
#include "SceneEntity/CeilingSceneEntity.h"
#include "HBSMathLibrary.h"

struct FAdjacentWallInfo
{
	int32 WallIndex;
	float SignAngleDot;
};

struct FDirectionWall
{
	UHBSWallSceneEntity* WallEntity;
	FVector2D Start;
	FVector2D End;
	FVector2D Direction;
	FVector2D Normal;
	bool IsInversed = false;

	FAdjacentWallInfo AngleNearestWallInfo = { -1, FLT_MAX };
	TArray<FAdjacentWallInfo> AdjacentWallList;
	bool IsVisited = false;
};

struct FWallPath
{
	TArray<int32> WallList;
	bool Closed = false;

	void AddWall(int32 WallIndex)
	{
		Closed = WallList.Contains(WallIndex / 2 * 2) || WallList.Contains(WallIndex / 2 * 2 + 1);
		WallList.Add(WallIndex);
	}

	bool IsClosed() const { return Closed; }

	FWallPath GenerateClosedSpace()
	{
		if (Closed == false)
			return FWallPath();

		if (WallList.Num() < 3)
			return FWallPath();

		int32 LastWallIndex = WallList.Last();
		WallList.RemoveAt(WallList.Num() - 1);
		if (WallList.Contains(LastWallIndex))
		{
			int32 CircleStartIndex = WallList.Find(LastWallIndex);

			FWallPath ClosedSpace;
			for (int32 i = CircleStartIndex; i < WallList.Num(); ++i)
			{
				ClosedSpace.WallList.Add(WallList[i]);
			}
			ClosedSpace.Closed = true;
			return ClosedSpace;
		}
		else
		{
			int32 CircleStartIndex = WallList.Find(LastWallIndex / 2 * 2);
			if (CircleStartIndex == INDEX_NONE)
				CircleStartIndex = WallList.Find(LastWallIndex / 2 * 2 + 1);

			if (CircleStartIndex == INDEX_NONE)
				return FWallPath();

			FWallPath ClosedSpace;
			for (int32 i = CircleStartIndex + 1; i < WallList.Num(); ++i)
			{
				ClosedSpace.WallList.Add(WallList[i]);
			}
			ClosedSpace.Closed = true;
			return ClosedSpace;
		}
	}
};

void UHBSFlooringCeilingSceneEntityProcessor::Process(UHBSSceneEntityProcessorContext* InContext)
{
	if (InContext->HasAnyEntityChangedByClass(UHBSWallSceneEntity::StaticClass()) == false)
		return;

	TArray<UHBSWallSceneEntity*> AllNeedProcessWallSEList = InContext->GetTypedSceneEntityList<UHBSWallSceneEntity>(SE_All,
		[](UHBSWallSceneEntity* Wall)
		{
			return Wall->WallStart != Wall->WallEnd;
		});

	TMap<int32, TArray<UHBSWallSceneEntity*>> StartHeight2WallMap;
	for (UHBSWallSceneEntity* Wall : AllNeedProcessWallSEList)
	{
		StartHeight2WallMap.FindOrAdd(Wall->GetWallStartHeight()).Add(Wall);
	}

	for (auto& It : StartHeight2WallMap)
	{
		int32 WallStartHeight = It.Key;
		TArray<UHBSWallSceneEntity*>& WallEntityList = It.Value;

		TArray<FDirectionWall> DirectionWallList;
		for (UHBSWallSceneEntity* Wall : WallEntityList)
		{
			FDirectionWall ForwardWall;
			FDirectionWall InverseWall;

			ForwardWall.WallEntity = Wall;
			InverseWall.WallEntity = Wall;

			ForwardWall.Start = Wall->WallStart;
			ForwardWall.End = Wall->WallEnd;
			ForwardWall.Direction = Wall->GetWallVector();
			ForwardWall.Normal = Wall->GetWallNormal();
			ForwardWall.IsInversed = false;

			InverseWall.Start = Wall->WallEnd;
			InverseWall.End = Wall->WallStart;
			InverseWall.Direction = -ForwardWall.Direction;
			InverseWall.Normal = -ForwardWall.Normal;
			InverseWall.IsInversed = true;

			DirectionWallList.Add(ForwardWall);
			DirectionWallList.Add(InverseWall);
		}

		float WallWidth = UHBSConfig::Get()->WallWidth;
		for (int32 i = 0; i != DirectionWallList.Num(); ++i)
		{
			for (int32 j = 0; j != DirectionWallList.Num(); ++j)
			{
				FDirectionWall& DirectionWallA = DirectionWallList[i];
				FDirectionWall& DirectionWallB = DirectionWallList[j];

				if (DirectionWallA.WallEntity == DirectionWallB.WallEntity)
					continue;

				if (FVector2D::DistSquared(DirectionWallA.End, DirectionWallB.Start) < WallWidth * WallWidth)
				{
					float AngleDot = FVector2D::DotProduct(DirectionWallA.Direction, DirectionWallB.Direction);
					AngleDot = AngleDot - 1;
					if (FVector2D::DotProduct(DirectionWallA.Normal, DirectionWallB.Direction) < 0)
						AngleDot = -AngleDot;

					FAdjacentWallInfo AdjacentWallInfo;
					AdjacentWallInfo.WallIndex = j;
					AdjacentWallInfo.SignAngleDot = AngleDot;

					DirectionWallA.AdjacentWallList.Add(AdjacentWallInfo);
				}
			}
		}

		TArray<bool> IsIsolated;
		IsIsolated.Init(false, DirectionWallList.Num());
		while (true)
		{
			TArray<int32> LeafWall;
			for (int32 i = 0; i != DirectionWallList.Num(); ++i)
			{
				if (IsIsolated[i])
					continue;

				if (DirectionWallList[i].AdjacentWallList.IsEmpty())
				{
					LeafWall.Add(i);
					IsIsolated[i] = true;
				}
			}

			for (int32 i = 0; i != DirectionWallList.Num(); ++i)
			{
				DirectionWallList[i].AdjacentWallList.RemoveAll([&](FAdjacentWallInfo& WallInfo) { return IsIsolated[WallInfo.WallIndex]; });
			}

			if (LeafWall.IsEmpty())
				break;
		}

		for (int32 i = 0; i != DirectionWallList.Num(); ++i)
		{
			if (DirectionWallList[i].AdjacentWallList.Num() != 0)
			{
				Algo::SortBy(DirectionWallList[i].AdjacentWallList, [](FAdjacentWallInfo& WallInfo) { return WallInfo.SignAngleDot; });
				DirectionWallList[i].AngleNearestWallInfo.WallIndex = DirectionWallList[i].AdjacentWallList[0].WallIndex;
				DirectionWallList[i].AngleNearestWallInfo.SignAngleDot = DirectionWallList[i].AdjacentWallList[0].SignAngleDot;
			}
		}

		TArray<FWallPath> CloseSpaceList;
		for (int32 i = 0; i != DirectionWallList.Num(); ++i)
		{
			if (DirectionWallList[i].IsVisited)
				continue;

			int32 CurrentWallIndex = i;

			FWallPath WallPath;
			do
			{
				if (DirectionWallList[CurrentWallIndex].IsVisited)
					break;

				WallPath.AddWall(CurrentWallIndex);

				if (DirectionWallList[CurrentWallIndex].AngleNearestWallInfo.WallIndex == -1)
					break;

				CurrentWallIndex = DirectionWallList[CurrentWallIndex].AngleNearestWallInfo.WallIndex;
			}
			while (WallPath.IsClosed() == false);

			if (WallPath.IsClosed())
			{
				FWallPath ClosedSpace = WallPath.GenerateClosedSpace();

				float Area = 0.f;
				for (int32 j = 0; j != ClosedSpace.WallList.Num(); ++j)
				{
					int32 WallIndex0 = ClosedSpace.WallList[j];
					int32 WallIndex1 = ClosedSpace.WallList[(j + 1) % ClosedSpace.WallList.Num()];

					FVector2D Vertex0 = DirectionWallList[WallIndex0].Start;
					FVector2D Vertex1 = DirectionWallList[WallIndex1].Start;
					Area += Vertex0.X * Vertex1.Y - Vertex1.X * Vertex0.Y;
				}

				if (Area <= 0)
					continue;

				CloseSpaceList.Add(ClosedSpace);
				for (int32 WallIndex : ClosedSpace.WallList)
				{
					DirectionWallList[WallIndex].IsVisited = true;
				}
			}
		}

		TArray<UHBSSurfaceSceneEntity*> FloorSEList = InContext->GetTypedSceneEntityList<UHBSSurfaceSceneEntity>(SE_All,
			[&](UHBSSurfaceSceneEntity* FloorSE)
			{
				return Cast<UHBSFlooringSceneEntity>(FloorSE)->GetSurfaceHeight() == WallStartHeight;
			}, true, UHBSFlooringSceneEntity::StaticClass());

		TArray<UHBSSurfaceSceneEntity*> CeilingSEList = InContext->GetTypedSceneEntityList<UHBSSurfaceSceneEntity>(SE_All,
			[&](UHBSSurfaceSceneEntity* CeilingSE)
			{
				return Cast<UHBSCeilingSceneEntity>(CeilingSE)->GetFloorHeight() == WallStartHeight;
			}, true, UHBSCeilingSceneEntity::StaticClass());


		TArray<bool> FloorSEUsedList;
		TArray<bool> CeilingSEUsedList;
		FloorSEUsedList.Init(false, FloorSEList.Num());
		CeilingSEUsedList.Init(false, CeilingSEList.Num());

		TArray<UHBSSurfaceSceneEntity*>* SurfaceEntityListArray[2] = { &FloorSEList, &CeilingSEList };
		TArray<bool>* SurfaceEntityUsedArray[2] = { &FloorSEUsedList, &CeilingSEUsedList };
		UClass* SurfaceEntityClassArray[2] = { UHBSFlooringSceneEntity::StaticClass(), UHBSCeilingSceneEntity::StaticClass() };

		for (int32 i = 0; i != CloseSpaceList.Num(); ++i)
		{
			TArray<FWallEdge> WallEdgeList;
			TArray<FVector2D> Vertices;
			for (int32 WallIndex : CloseSpaceList[i].WallList)
			{
				FDirectionWall& DirectionWall = DirectionWallList[WallIndex];
				Vertices.Add(DirectionWall.Start);

				FWallEdge WallEdge;
				WallEdge.WallEntity = DirectionWall.WallEntity;
				WallEdge.IsInversed = DirectionWall.IsInversed;
				WallEdgeList.Add(WallEdge);
			}

			for (int32 j = 0; j != 2; ++j)
			{
				TArray<UHBSSurfaceSceneEntity*>& SurfaceEntityList = *SurfaceEntityListArray[j];
				TArray<bool>& SurfaceEntityUsed = *SurfaceEntityUsedArray[j];
				UClass* SurfaceEntityClass = SurfaceEntityClassArray[j];

				int32 SuitedIndex = INDEX_NONE;
				float MaxOverlapArea = 0.f;
				for (int32 k = 0; k != SurfaceEntityList.Num(); ++k)
				{
					float OverlapArea = CalcOverlapAreaApprox(SurfaceEntityList[k]->GetVertices(), Vertices, 10.f);

					if (OverlapArea > MaxOverlapArea)
					{
						MaxOverlapArea = OverlapArea;
						SuitedIndex = k;
					}
				}

				UHBSSurfaceSceneEntity* SurfaceEntity = nullptr;
				if (SuitedIndex == INDEX_NONE || SurfaceEntityUsed[SuitedIndex])
				{
					SurfaceEntity = Cast<UHBSSurfaceSceneEntity>(InContext->PlacementSceneEntityByClass(SurfaceEntityClass));
				}
				else
				{
					SurfaceEntity = SurfaceEntityList[SuitedIndex];
					SurfaceEntityUsed[SuitedIndex] = true;
				}

				if (SuitedIndex != INDEX_NONE)
				{
					// 拷贝UGC信息与材质，等阿灿那边确认之后再弄
				}

				if (j == 0)
					Cast<UHBSFlooringSceneEntity>(SurfaceEntity)->SetupInitInfo(Vertices, WallEdgeList, WallStartHeight, 5);
				else
				{
					int32 MinWallHeight = INT_MAX;
					for (FWallEdge& Wall : WallEdgeList)
						MinWallHeight = FMath::Min(MinWallHeight, Wall.WallEntity->GetWallHeight());

					Cast<UHBSCeilingSceneEntity>(SurfaceEntity)->SetupInitInfo(Vertices, WallEdgeList, WallStartHeight + MinWallHeight,
						WallStartHeight, 5);
				}
				InContext->MarkSceneEntityModify(SurfaceEntity);
			}
		}

		for (int32 i = 0; i != 2; ++i)
		{
			TArray<UHBSSurfaceSceneEntity*>& SurfaceEntityList = *SurfaceEntityListArray[i];
			TArray<bool>& SurfaceEntityUsed = *SurfaceEntityUsedArray[i];
			for (int32 j = 0; j != SurfaceEntityList.Num(); ++j)
			{
				if (SurfaceEntityUsed[j] == false)
				{
					InContext->RemoveSceneEntity(SurfaceEntityList[j]);
				}
			}
		}

		TArray<EHBSWallType> WallTypeList;
		WallTypeList.SetNum(WallEntityList.Num());
		for (int32 i = 0; i != CloseSpaceList.Num(); ++i)
		{
			for (int32 DirectionWallIndex : CloseSpaceList[i].WallList)
			{
				int32 WallIndex = DirectionWallIndex / 2;
				EHBSWallType WallType = (DirectionWallIndex % 2) ? EHBSWallType::OuterFaceOutward : EHBSWallType::InsideFaceOutward;

				WallTypeList[WallIndex] = WallTypeList[WallIndex] | WallType;
			}
		}

		for (int32 i = 0; i != WallEntityList.Num(); ++i)
		{
			WallEntityList[i]->SetWallType(WallTypeList[i]);
		}
	}

	TArray<UHBSFlooringSceneEntity*> FloorSEList = InContext->GetTypedSceneEntityList<UHBSFlooringSceneEntity>(SE_All,
		[&](UHBSFlooringSceneEntity* FloorSE)
		{
			return StartHeight2WallMap.Contains(FloorSE->GetSurfaceHeight()) == false;
		});

	TArray<UHBSCeilingSceneEntity*> CeilingSEList = InContext->GetTypedSceneEntityList<UHBSCeilingSceneEntity>(SE_All,
		[&](UHBSCeilingSceneEntity* CeilingSE)
		{
			return StartHeight2WallMap.Contains(CeilingSE->GetFloorHeight()) == false;
		});

	for (UHBSFlooringSceneEntity* FloorSE : FloorSEList)
		InContext->RemoveSceneEntity(FloorSE);

	for (UHBSCeilingSceneEntity* CeilingSE : CeilingSEList)
		InContext->RemoveSceneEntity(CeilingSE);
}

float UHBSFlooringCeilingSceneEntityProcessor::CalcOverlapAreaApprox(const TArray<FVector2D>& PolyA, const TArray<FVector2D>& PolyB, float StepSize)
{
	FVector2D MinA = PolyA[0];
	FVector2D MaxA = PolyA[0];
	for (const FVector2D& Point : PolyA)
	{
		MinA.X = FMath::Min(MinA.X, Point.X);
		MinA.Y = FMath::Min(MinA.Y, Point.Y);
		MaxA.X = FMath::Max(MaxA.X, Point.X);
		MaxA.Y = FMath::Max(MaxA.Y, Point.Y);
	}

	FVector2D MinB = PolyB[0];
	FVector2D MaxB = PolyB[0];
	for (const FVector2D& Point : PolyB)
	{
		MinB.X = FMath::Min(MinB.X, Point.X);
		MinB.Y = FMath::Min(MinB.Y, Point.Y);
		MaxB.X = FMath::Max(MaxB.X, Point.X);
		MaxB.Y = FMath::Max(MaxB.Y, Point.Y);
	}

	FVector2D Min = FVector2D::Max(MinA, MinB);
	FVector2D Max = FVector2D::Min(MaxA, MaxB);

	float XStepSize = FMath::Max(StepSize, (Max.X - Min.X) / 100);
	float YStepSize = FMath::Max(StepSize, (Max.Y - Min.Y) / 100);

	float Area = 0.f;
	for (float X = Min.X; X < Max.X; X += XStepSize)
	{
		for (float Y = Min.Y; Y < Max.Y; Y += YStepSize)
		{
			FVector2D Point(X, Y);
			if (UHBSMathLibrary::IsPointInPolygon(Point, PolyA) && UHBSMathLibrary::IsPointInPolygon(Point, PolyB))
			{
				Area += XStepSize * YStepSize;
			}
		}
	}

	return Area;
}
