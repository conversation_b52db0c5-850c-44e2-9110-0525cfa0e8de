#include "SceneEntityProcessor/BuildingAndLevelSceneEntityProcessor.h"
#include "SceneEntity/WallSceneEntity.h"
#include "SceneEntity/FloorSceneEntity.h"
#include "SceneEntity/SurfaceSceneEntity.h"
#include "SceneEntity/CeilingSceneEntity.h"
#include "SceneEntity/FlooringSceneEntity.h"
#include "SceneEntity/BuildingSceneEntity.h"

void UHBSBuildingAndLevelSceneEntityProcessor::Process(UHBSSceneEntityProcessorContext* InContext)
{
	if (InContext->HasAnyEntityChangedByClass(UHBSWallSceneEntity::StaticClass()) == false)
		return;

	TArray<UHBSBuildingSceneEntity*> BuildingSEList = InContext->GetTypedSceneEntityList<UHBSBuildingSceneEntity>(SE_All);
	for (UHBSBuildingSceneEntity* BuildingSE : BuildingSEList)
		InContext->RemoveSceneEntity(BuildingSE);

	TArray<UHBSWallSceneEntity*> WallEntities = InContext->GetTypedSceneEntityList<UHBSWallSceneEntity>(SE_All);
	TArray<TArray<UHBSWallSceneEntity*>> MergedWallList = GetMergedWallEntityList(WallEntities);

	for (TArray<UHBSWallSceneEntity*>& WallListForBuilding : MergedWallList)
	{
		UHBSBuildingSceneEntity* NewBuildingSE = InContext->PlacementSceneEntityByClass<UHBSBuildingSceneEntity>();

		FBox2D BuildingBox(EForceInit::ForceInitToZero);
		for (UHBSWallSceneEntity* WallEntity : WallListForBuilding)
		{
			BuildingBox += WallEntity->GetWallStart();
			BuildingBox += WallEntity->GetWallEnd();
		}

		TArray<int32> LevelHeightList;
		for (UHBSWallSceneEntity* WallEntity : WallListForBuilding)
		{
			int32 WallStartHeight = WallEntity->GetWallStartHeight();
			if (LevelHeightList.Contains(WallStartHeight) == false)
				LevelHeightList.Add(WallStartHeight);
		}
		LevelHeightList.Sort();
		LevelHeightList.Add(LevelHeightList.Last() + 100);

		TArray<UHBSFloorSceneEntity*> LeveSEList;
		for (int32 LevelIndex = 0; LevelIndex != LevelHeightList.Num(); ++LevelIndex)
		{
			UHBSFloorSceneEntity* NewLevel = InContext->PlacementSceneEntityByClass<UHBSFloorSceneEntity>();
			NewLevel->SetupInitInfo(LevelIndex, LevelHeightList[LevelIndex]);
			NewLevel->SetParentEntity(NewBuildingSE);
			LeveSEList.Add(NewLevel);
		}

		auto FindLevelSEByHeight = [&](int32 InHeight) -> UHBSFloorSceneEntity*
		{
			for (UHBSFloorSceneEntity* LevelSE : LeveSEList)
			{
				if (LevelSE->GetLevelHeight() >= InHeight)
					return LevelSE;
			}

			return nullptr;
		};

		for (UHBSWallSceneEntity* Wall : WallListForBuilding)
		{
			int32 Height = Wall->GetWallStartHeight();
			Wall->SetParentEntity(FindLevelSEByHeight(Height));
		}


		TArray<UHBSSurfaceSceneEntity*> SurfaceSEList = InContext->GetTypedSceneEntityList<UHBSSurfaceSceneEntity>(SE_All,
			[&](UHBSSurfaceSceneEntity* SurfaceSE)
			{
				return SurfaceSE->GetBoundingBox2D().Intersect(BuildingBox);
			});
		for (UHBSSurfaceSceneEntity* SurfaceSE : SurfaceSEList)
		{
			if (UHBSCeilingSceneEntity* Ceiling = Cast<UHBSCeilingSceneEntity>(SurfaceSE))
			{
				int32 Height = Ceiling->GetFloorHeight() + 1;
				Ceiling->SetParentEntity(FindLevelSEByHeight(Height));
			}

			if (UHBSFlooringSceneEntity* FloorSE = Cast<UHBSFlooringSceneEntity>(SurfaceSE))
			{
				int32 Height = FloorSE->GetSurfaceHeight();
				FloorSE->SetParentEntity(FindLevelSEByHeight(Height));
			}
		}
	}
}

TArray<TArray<UHBSWallSceneEntity*>> UHBSBuildingAndLevelSceneEntityProcessor::GetMergedWallEntityList(
	const TArray<UHBSWallSceneEntity*>& InWallEntityList) const
{
	TArray<int32> DSU;
	DSU.Init(-1, InWallEntityList.Num());

	TArray<FBox2D> AABBList;
	for (int32 i = 0; i < InWallEntityList.Num(); ++i)
	{
		const UHBSWallSceneEntity* WallEntity = InWallEntityList[i];

		FVector2D Min = FVector2D::Min(WallEntity->GetWallStart(), WallEntity->GetWallEnd());
		FVector2D Max = FVector2D::Max(WallEntity->GetWallStart(), WallEntity->GetWallEnd());

		Min.X = Min.X - 50;
		Min.Y = Min.Y - 50;
		Max.X = Max.X + 50;
		Max.Y = Max.Y + 50;

		AABBList.Add(FBox2D(Min, Max));
	}

	for (int32 i = 0; i < InWallEntityList.Num(); ++i)
	{
		for (int32 j = i + 1; j < InWallEntityList.Num(); ++j)
		{
			if (AABBList[i].Intersect(AABBList[j]))
			{
				int32 RootI = i;
				while (DSU[RootI] != -1)
					RootI = DSU[RootI];

				int32 RootJ = j;
				while (DSU[RootJ] != -1)
					RootJ = DSU[RootJ];

				if (RootI != RootJ)
				{
					DSU[RootJ] = RootI; // Union
				}
			}
		}
	}

	TArray<TArray<UHBSWallSceneEntity*>> MergedWallEntityList;
	TArray<bool> VisitedArray;
	VisitedArray.Init(false, InWallEntityList.Num());
	for (int32 i = 0; i < InWallEntityList.Num(); ++i)
	{
		if (VisitedArray[i])
			continue;

		VisitedArray[i] = true; // Mark as visited

		TArray<UHBSWallSceneEntity*> MergedWalls;
		MergedWalls.Add(InWallEntityList[i]);

		for (int32 j = i + 1; j != InWallEntityList.Num(); ++j)
		{
			if (VisitedArray[j] == true)
				continue;

			int32 RootJ = j;
			while (DSU[RootJ] != -1)
				RootJ = DSU[RootJ];

			if (RootJ == i)
			{
				MergedWalls.Add(InWallEntityList[j]);
				VisitedArray[j] = true; // Mark as visited
			}
		}

		MergedWallEntityList.Add(MergedWalls);
	}

	return MergedWallEntityList;
}
