#include "SceneEntityProcessor/PlaceablePlaneSceneEntityProcessor.h"
#include "SceneEntity/WallSceneEntity.h"
#include "SceneEntity/PlaceablePlaneSceneEntity.h"
#include "SceneEntity/FlooringSceneEntity.h"
#include "SceneEntity/CeilingSceneEntity.h"


void UHBSPlaceablePlaneSceneEntityProcessor::Process(UHBSSceneEntityProcessorContext* InContext)
{
	TArray<UHBSFlooringSceneEntity*> NewFloorSceneEntityList = InContext->GetTypedSceneEntityList<UHBSFlooringSceneEntity>(SE_New);
	for (UHBSFlooringSceneEntity* Floor : NewFloorSceneEntityList)
	{
		UHBSPlaceablePlaneSE* PlaceablePlane = InContext->PlacementSceneEntityByClass<UHBSPlaceablePlaneSE>();
		PlaceablePlane->SetupInitInfo(Floor->GetVertices(), Floor->GetSurfaceHeight() + Floor->GetSurfaceThickness(), EHBSPlaceablePlaneType::Floor,
			0);
		PlaceablePlane->SetParentEntity(Floor);
	}

	TArray<UHBSFlooringSceneEntity*> ModifiedFloorSceneEntityList = InContext->GetTypedSceneEntityList<UHBSFlooringSceneEntity>(SE_Modified);
	for (UHBSFlooringSceneEntity* Floor : ModifiedFloorSceneEntityList)
	{
		UHBSPlaceablePlaneSE* PlaceablePlane = Floor->GetTypedChild<UHBSPlaceablePlaneSE>();
		PlaceablePlane->SetupInitInfo(Floor->GetVertices(), Floor->GetSurfaceHeight() + Floor->GetSurfaceThickness(), EHBSPlaceablePlaneType::Floor,
			0);
		InContext->MarkSceneEntityModify(PlaceablePlane);
	}

	TArray<UHBSCeilingSceneEntity*> NewCeilingSEList = InContext->GetTypedSceneEntityList<UHBSCeilingSceneEntity>(SE_New);
	for (UHBSCeilingSceneEntity* Ceiling : NewCeilingSEList)
	{
		UHBSPlaceablePlaneSE* FoundationPlaceablePlane = InContext->PlacementSceneEntityByClass<UHBSPlaceablePlaneSE>();
		FoundationPlaceablePlane->SetupInitInfo(Ceiling->GetVertices(), Ceiling->GetSurfaceHeight(), EHBSPlaceablePlaneType::Foundation, 0);
		FoundationPlaceablePlane->SetParentEntity(Ceiling);

		UHBSPlaceablePlaneSE* CeilingPlaceablePlane = InContext->PlacementSceneEntityByClass<UHBSPlaceablePlaneSE>();
		CeilingPlaceablePlane->SetupInitInfo(Ceiling->GetVertices(), Ceiling->GetSurfaceHeight() - Ceiling->GetSurfaceThickness(),
			EHBSPlaceablePlaneType::Ceiling, 0);
		CeilingPlaceablePlane->SetParentEntity(Ceiling);
	}

	TArray<UHBSCeilingSceneEntity*> ModifiedCeilingSEList = InContext->GetTypedSceneEntityList<UHBSCeilingSceneEntity>(SE_Modified);
	for (UHBSCeilingSceneEntity* Ceiling : ModifiedCeilingSEList)
	{
		TArray<UHBSPlaceablePlaneSE*> PlaceablePlaneList = Ceiling->GetTypedChildrenList<UHBSPlaceablePlaneSE>();
		for (UHBSPlaceablePlaneSE* PlaceablePlane : PlaceablePlaneList)
		{
			if (PlaceablePlane->GetPlaneType() == EHBSPlaceablePlaneType::Foundation)
				PlaceablePlane->SetupInitInfo(Ceiling->GetVertices(), Ceiling->GetSurfaceHeight(), EHBSPlaceablePlaneType::Foundation, 0);
			else if (PlaceablePlane->GetPlaneType() == EHBSPlaceablePlaneType::Ceiling)
				PlaceablePlane->SetupInitInfo(Ceiling->GetVertices(), Ceiling->GetSurfaceHeight() - Ceiling->GetSurfaceThickness(),
					EHBSPlaceablePlaneType::Ceiling, 0);

			InContext->MarkSceneEntityModify(PlaceablePlane);
		}
	}
}
