#include "SceneEntityProcessor/SceneEntityProcessorBase.h"
#include "Misc/LowLevelTestAdapter.h"
#include "SceneEntity/SceneEntityBase.h"

bool UHBSSceneEntityProcessorContext::HasAnyEntityChangedByClass(TSubclassOf<UHBSSceneEntityBase> EntityClass)
{
	auto Predicate = [EntityClass](const UHBSSceneEntityBase* Entity)
	{
		return Entity->IsA(EntityClass);
	};

	bool HasNewEntity = NewSceneEntityList.ContainsByPredicate(Predicate);
	bool HasModifiedEntity = ModifiedSceneEntityList.ContainsByPredicate(Predicate);
	bool HasRemovedEntity = RemovedSceneEntityList.ContainsByPredicate(Predicate);

	return HasNewEntity || HasModifiedEntity || HasRemovedEntity;
}

UHBSSceneEntityBase* UHBSSceneEntityProcessorContext::PlacementSceneEntityByClass(TSubclassOf<UHBSSceneEntityBase> SceneEntityClass,
	bool IsPlacementDone)
{
	if (!SceneEntityClass)
		return nullptr;

	if (UHBSSceneEntityBase* NewEntity = NewObject<UHBSSceneEntityBase>(this, SceneEntityClass))
	{
		NewSceneEntityList.Add(NewEntity);
		AllSceneEntityList.Add(NewEntity);
		if (IsPlacementDone)
			NewEntity->InitializeImmediate();
		return NewEntity;
	}

	return nullptr;
}

void UHBSSceneEntityProcessorContext::MarkSceneEntityModify(UHBSSceneEntityBase* SceneEntity)
{
	if (SceneEntity == nullptr)
		return;

	CHECK(AllSceneEntityList.Contains(SceneEntity));
	if (NewSceneEntityList.Contains(SceneEntity) == false)
		ModifiedSceneEntityList.AddUnique(SceneEntity);
}

void UHBSSceneEntityProcessorContext::RemoveSceneEntity(UHBSSceneEntityBase* SceneEntity)
{
	if (SceneEntity == nullptr)
		return;

	SceneEntity->OnPreDestroy();

	CHECK(AllSceneEntityList.Contains(SceneEntity));
	NewSceneEntityList.Remove(SceneEntity);
	ModifiedSceneEntityList.Remove(SceneEntity);
	RemovedSceneEntityList.AddUnique(SceneEntity);
	AllSceneEntityList.Remove(SceneEntity);
}

bool UHBSSceneEntityProcessorContext::NeedUpdateSceneEntity() const
{
	bool HasNewPlacementDoneEntity = NewSceneEntityList.ContainsByPredicate([](const UHBSSceneEntityBase* SceneEntity)
	{
		return SceneEntity->GetIsPlacementDone();
	});
	return HasNewPlacementDoneEntity
		|| ModifiedSceneEntityList.IsEmpty() == false
		|| RemovedSceneEntityList.IsEmpty() == false;
}

void UHBSSceneEntityProcessorContext::PreProcess()
{
}

void UHBSSceneEntityProcessorContext::PostProcess()
{
	NewSceneEntityList.RemoveAll([](UHBSSceneEntityBase* SceneEntity)
	{
		return SceneEntity->GetIsPlacementDone();
	});

	ModifiedSceneEntityList.Empty();
	RemovedSceneEntityList.Empty();
}
