#pragma once

#include "CoreMinimal.h"
#include "AssetDefinitionDefault.h"
#include "Factories/Factory.h"
#include "HBSAsset/HBSAssetBase.h"
#include "HBSAssetEditorToolkit.h"
#include "HBSAsset/HBSStaticMeshAsset.h"

#include "HBSAssetDefinitionBase.generated.h"

#define LOCTEXT_NAMESPACE "HBSAssetDefinition"

UCLASS(Abstract)
class HBSEDITOR_API UHBSAssetFactoryBase : public UFactory
{
	GENERATED_BODY()

public:
	UHBSAssetFactoryBase(const FObjectInitializer& ObjectInitializer)
		: Super(ObjectInitializer)
	{
		bCreateNew = true;
		bEditAfterNew = true;
		SupportedClass = UHBSAssetBase::StaticClass();
	}

	UObject* FactoryCreateNew(UClass* Class, UObject* InParent, FName Name, EObjectFlags Flags, UObject* Context, FFeedbackContext* Warn) override
	{
		return NewObject<UHBSAssetBase>(InParent, Class, Name, Flags);
	}
};

UCLASS(Abstract)
class HBSEDITOR_API UHBSAssetDefinitionBase : public UAssetDefinitionDefault
{
	GENERATED_BODY()

public:
	FText GetAssetDisplayName() const override { return LOCTEXT("HBSAssetDisplayName", "HBS 资产"); }
	TSoftClassPtr<UObject> GetAssetClass() const override { return UHBSAssetBase::StaticClass(); }
	FLinearColor GetAssetColor() const override { return FColor::Turquoise; }
	FText GetAssetDescription(const FAssetData& AssetData) const override { return GetAssetDisplayName(); }
	EAssetCommandResult OpenAssets(const FAssetOpenArgs& OpenArgs) const override
	{
		for (UHBSAssetBase* Asset : OpenArgs.LoadObjects<UHBSAssetBase>())
			MakeShared<FHBSAssetEditorToolkit>()->InitHBSAssetEditor(OpenArgs.GetToolkitMode(), OpenArgs.ToolkitHost, Asset);
		return EAssetCommandResult::Handled;
	}

	TConstArrayView<FAssetCategoryPath> GetAssetCategories() const override
	{
		static TArray<FAssetCategoryPath> Categories = { FAssetCategoryPath(LOCTEXT("HBS", "HBS")) };
		return Categories;
	}
};

#undef LOCTEXT_NAMESPACE