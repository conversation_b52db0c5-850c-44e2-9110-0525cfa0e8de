#include "Misc/DelayedAutoRegister.h"
#include "PropertyEditorModule.h"
#include "PropertyEditorDelegates.h"

#define REGISTER_HBS_ASSET_CUSTOMIZATIONS(ClassName, CustomizationType)																		\
static FDelayedAutoRegisterHelper CustomizationType##Register(EDelayedRegisterRunPhase::EndOfEngineInit, []() {								\
	FPropertyEditorModule& PropertyModule = FModuleManager::LoadModuleChecked<FPropertyEditorModule>("PropertyEditor");						\
	PropertyModule.RegisterCustomClassLayout(ClassName, FOnGetDetailCustomizationInstance::CreateStatic(&CustomizationType::MakeInstance));	\
	});
