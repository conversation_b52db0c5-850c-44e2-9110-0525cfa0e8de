#pragma once

#include "CoreMinimal.h"
#include "HBSAssetDefinitionBase.h"

#include "StaticMeshAssetDefinition.generated.h"

#define LOCTEXT_NAMESPACE "HBSStaticMeshAssetDefinition"

UCLASS()
class HBSEDITOR_API UHBSStaticMeshAssetFactory : public UHBSAssetFactoryBase
{
	GENERATED_BODY()

public:
	UHBSStaticMeshAssetFactory(const FObjectInitializer& ObjectInitializer)
		: Super(ObjectInitializer)
	{
		SupportedClass = UHBSStaticMeshAsset::StaticClass();
	}

	FText GetDisplayName() const override
	{
		return LOCTEXT("HBSStaticMeshAssetFactoryDisplayName", "HBS 静态网格物体");
	}
};

UCLASS()
class HBSEDITOR_API UHBSStaticMeshAssetDefinition : public UHBSAssetDefinitionBase
{
	GENERATED_BODY()

public:
	FText GetAssetDisplayName() const override { return LOCTEXT("HBSAssetDisplayName", "HBS 静态网格物体"); }
	TSoftClassPtr<UObject> GetAssetClass() const override { return UHBSStaticMeshAsset::StaticClass(); }
};

#undef LOCTEXT_NAMESPACE
