#pragma once

#include "CoreMinimal.h"
#include "HBSAssetDefinitionBase.h"
#include "HBSAsset/HBSLightFixtureAsset.h"

#include "LightFixtureAssetDefinition.generated.h"

#define LOCTEXT_NAMESPACE "HBSLightFixtureAssetDefinition"

UCLASS()
class HBSEDITOR_API UHBSLightFixtureAssetFactory : public UHBSAssetFactoryBase
{
	GENERATED_BODY()

public:
	UHBSLightFixtureAssetFactory(const FObjectInitializer& ObjectInitializer)
		: Super(ObjectInitializer)
	{
		SupportedClass = UHBSLightFixtureAsset::StaticClass();
	}

	FText GetDisplayName() const override
	{
		return LOCTEXT("HBSLightFixtureAssetFactoryDisplayName", "HBS 灯具");
	}
};

UCLASS()
class HBSEDITOR_API UHBSLightFixtureAssetDefinition : public UHBSAssetDefinitionBase
{
	GENERATED_BODY()

public:
	FText GetAssetDisplayName() const override { return LOCTEXT("HBSAssetDisplayName", "HBS 灯具"); }
	TSoftClassPtr<UObject> GetAssetClass() const override { return UHBSLightFixtureAsset::StaticClass(); }
};

#undef LOCTEXT_NAMESPACE
