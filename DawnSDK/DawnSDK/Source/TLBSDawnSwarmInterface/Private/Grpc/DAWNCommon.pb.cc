#ifdef _MSC_VER
#pragma warning(disable : 4005 4125 4582 4583 4800 4946 4996)
#endif
// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: DAWNCommon.proto

#include "DAWNCommon.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
extern PROTOBUF_INTERNAL_EXPORT_DAWNCommon_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_CalculationSchemeInfo_DAWNCommon_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_DAWNCommon_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_JobBasicDesc_DAWNCommon_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_DAWNCommon_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_MissionStatisticMsg_DAWNCommon_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_DAWNCommon_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_PartitionDesc_DAWNCommon_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_DAWNCommon_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_ReportMsg_DAWNCommon_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_DAWNCommon_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_StringValue_DAWNCommon_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_DAWNCommon_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_SubTaskDesc_DAWNCommon_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_DAWNCommon_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_TaskDesc_DAWNCommon_2eproto;
extern PROTOBUF_INTERNAL_EXPORT_DAWNCommon_2eproto ::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_UserInfo_DAWNCommon_2eproto;
namespace DAWNProto {
class StringValueDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<StringValue> _instance;
} _StringValue_default_instance_;
class GeneralAckDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<GeneralAck> _instance;
} _GeneralAck_default_instance_;
class KeyValuePairDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<KeyValuePair> _instance;
} _KeyValuePair_default_instance_;
class UserInfoDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<UserInfo> _instance;
} _UserInfo_default_instance_;
class CalculationSchemeInfoDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<CalculationSchemeInfo> _instance;
} _CalculationSchemeInfo_default_instance_;
class JobBasicDescDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<JobBasicDesc> _instance;
} _JobBasicDesc_default_instance_;
class JobFullDescDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<JobFullDesc> _instance;
} _JobFullDesc_default_instance_;
class PartitionDescDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<PartitionDesc> _instance;
} _PartitionDesc_default_instance_;
class TaskDescDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<TaskDesc> _instance;
} _TaskDesc_default_instance_;
class SubTaskDescDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<SubTaskDesc> _instance;
} _SubTaskDesc_default_instance_;
class BeginMissionReqDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<BeginMissionReq> _instance;
} _BeginMissionReq_default_instance_;
class EndMissionReqDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<EndMissionReq> _instance;
} _EndMissionReq_default_instance_;
class AbortMissionReqDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<AbortMissionReq> _instance;
} _AbortMissionReq_default_instance_;
class TaskResultDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<TaskResult> _instance;
} _TaskResult_default_instance_;
class ReportMsgDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<ReportMsg> _instance;
} _ReportMsg_default_instance_;
class MissionStatisticMsgDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<MissionStatisticMsg> _instance;
} _MissionStatisticMsg_default_instance_;
class MissionStatisticMsgArrayDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<MissionStatisticMsgArray> _instance;
} _MissionStatisticMsgArray_default_instance_;
class MissionStatisticMsgReqDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<MissionStatisticMsgReq> _instance;
} _MissionStatisticMsgReq_default_instance_;
class PerfStatsMsgArrayDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<PerfStatsMsgArray> _instance;
} _PerfStatsMsgArray_default_instance_;
class PerfStatsMsgReqDefaultTypeInternal {
 public:
  ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<PerfStatsMsgReq> _instance;
} _PerfStatsMsgReq_default_instance_;
}  // namespace DAWNProto
static void InitDefaultsscc_info_AbortMissionReq_DAWNCommon_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::DAWNProto::_AbortMissionReq_default_instance_;
    new (ptr) ::DAWNProto::AbortMissionReq();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::DAWNProto::AbortMissionReq::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_AbortMissionReq_DAWNCommon_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_AbortMissionReq_DAWNCommon_2eproto}, {}};

static void InitDefaultsscc_info_BeginMissionReq_DAWNCommon_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::DAWNProto::_BeginMissionReq_default_instance_;
    new (ptr) ::DAWNProto::BeginMissionReq();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::DAWNProto::BeginMissionReq::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<2> scc_info_BeginMissionReq_DAWNCommon_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 2, 0, InitDefaultsscc_info_BeginMissionReq_DAWNCommon_2eproto}, {
      &scc_info_UserInfo_DAWNCommon_2eproto.base,
      &scc_info_CalculationSchemeInfo_DAWNCommon_2eproto.base,}};

static void InitDefaultsscc_info_CalculationSchemeInfo_DAWNCommon_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::DAWNProto::_CalculationSchemeInfo_default_instance_;
    new (ptr) ::DAWNProto::CalculationSchemeInfo();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::DAWNProto::CalculationSchemeInfo::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_CalculationSchemeInfo_DAWNCommon_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_CalculationSchemeInfo_DAWNCommon_2eproto}, {}};

static void InitDefaultsscc_info_EndMissionReq_DAWNCommon_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::DAWNProto::_EndMissionReq_default_instance_;
    new (ptr) ::DAWNProto::EndMissionReq();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::DAWNProto::EndMissionReq::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_EndMissionReq_DAWNCommon_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, 0, InitDefaultsscc_info_EndMissionReq_DAWNCommon_2eproto}, {
      &scc_info_ReportMsg_DAWNCommon_2eproto.base,}};

static void InitDefaultsscc_info_GeneralAck_DAWNCommon_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::DAWNProto::_GeneralAck_default_instance_;
    new (ptr) ::DAWNProto::GeneralAck();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::DAWNProto::GeneralAck::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_GeneralAck_DAWNCommon_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_GeneralAck_DAWNCommon_2eproto}, {}};

static void InitDefaultsscc_info_JobBasicDesc_DAWNCommon_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::DAWNProto::_JobBasicDesc_default_instance_;
    new (ptr) ::DAWNProto::JobBasicDesc();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::DAWNProto::JobBasicDesc::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_JobBasicDesc_DAWNCommon_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, 0, InitDefaultsscc_info_JobBasicDesc_DAWNCommon_2eproto}, {
      &scc_info_PartitionDesc_DAWNCommon_2eproto.base,}};

static void InitDefaultsscc_info_JobFullDesc_DAWNCommon_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::DAWNProto::_JobFullDesc_default_instance_;
    new (ptr) ::DAWNProto::JobFullDesc();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::DAWNProto::JobFullDesc::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<2> scc_info_JobFullDesc_DAWNCommon_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 2, 0, InitDefaultsscc_info_JobFullDesc_DAWNCommon_2eproto}, {
      &scc_info_JobBasicDesc_DAWNCommon_2eproto.base,
      &scc_info_TaskDesc_DAWNCommon_2eproto.base,}};

static void InitDefaultsscc_info_KeyValuePair_DAWNCommon_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::DAWNProto::_KeyValuePair_default_instance_;
    new (ptr) ::DAWNProto::KeyValuePair();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::DAWNProto::KeyValuePair::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_KeyValuePair_DAWNCommon_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_KeyValuePair_DAWNCommon_2eproto}, {}};

static void InitDefaultsscc_info_MissionStatisticMsg_DAWNCommon_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::DAWNProto::_MissionStatisticMsg_default_instance_;
    new (ptr) ::DAWNProto::MissionStatisticMsg();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::DAWNProto::MissionStatisticMsg::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_MissionStatisticMsg_DAWNCommon_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_MissionStatisticMsg_DAWNCommon_2eproto}, {}};

static void InitDefaultsscc_info_MissionStatisticMsgArray_DAWNCommon_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::DAWNProto::_MissionStatisticMsgArray_default_instance_;
    new (ptr) ::DAWNProto::MissionStatisticMsgArray();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::DAWNProto::MissionStatisticMsgArray::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_MissionStatisticMsgArray_DAWNCommon_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, 0, InitDefaultsscc_info_MissionStatisticMsgArray_DAWNCommon_2eproto}, {
      &scc_info_MissionStatisticMsg_DAWNCommon_2eproto.base,}};

static void InitDefaultsscc_info_MissionStatisticMsgReq_DAWNCommon_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::DAWNProto::_MissionStatisticMsgReq_default_instance_;
    new (ptr) ::DAWNProto::MissionStatisticMsgReq();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::DAWNProto::MissionStatisticMsgReq::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_MissionStatisticMsgReq_DAWNCommon_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_MissionStatisticMsgReq_DAWNCommon_2eproto}, {}};

static void InitDefaultsscc_info_PartitionDesc_DAWNCommon_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::DAWNProto::_PartitionDesc_default_instance_;
    new (ptr) ::DAWNProto::PartitionDesc();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::DAWNProto::PartitionDesc::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_PartitionDesc_DAWNCommon_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_PartitionDesc_DAWNCommon_2eproto}, {}};

static void InitDefaultsscc_info_PerfStatsMsgArray_DAWNCommon_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::DAWNProto::_PerfStatsMsgArray_default_instance_;
    new (ptr) ::DAWNProto::PerfStatsMsgArray();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::DAWNProto::PerfStatsMsgArray::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_PerfStatsMsgArray_DAWNCommon_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_PerfStatsMsgArray_DAWNCommon_2eproto}, {}};

static void InitDefaultsscc_info_PerfStatsMsgReq_DAWNCommon_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::DAWNProto::_PerfStatsMsgReq_default_instance_;
    new (ptr) ::DAWNProto::PerfStatsMsgReq();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::DAWNProto::PerfStatsMsgReq::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_PerfStatsMsgReq_DAWNCommon_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_PerfStatsMsgReq_DAWNCommon_2eproto}, {}};

static void InitDefaultsscc_info_ReportMsg_DAWNCommon_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::DAWNProto::_ReportMsg_default_instance_;
    new (ptr) ::DAWNProto::ReportMsg();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::DAWNProto::ReportMsg::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_ReportMsg_DAWNCommon_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_ReportMsg_DAWNCommon_2eproto}, {}};

static void InitDefaultsscc_info_StringValue_DAWNCommon_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::DAWNProto::_StringValue_default_instance_;
    new (ptr) ::DAWNProto::StringValue();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::DAWNProto::StringValue::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_StringValue_DAWNCommon_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_StringValue_DAWNCommon_2eproto}, {}};

static void InitDefaultsscc_info_SubTaskDesc_DAWNCommon_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::DAWNProto::_SubTaskDesc_default_instance_;
    new (ptr) ::DAWNProto::SubTaskDesc();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::DAWNProto::SubTaskDesc::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_SubTaskDesc_DAWNCommon_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_SubTaskDesc_DAWNCommon_2eproto}, {}};

static void InitDefaultsscc_info_TaskDesc_DAWNCommon_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::DAWNProto::_TaskDesc_default_instance_;
    new (ptr) ::DAWNProto::TaskDesc();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::DAWNProto::TaskDesc::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<1> scc_info_TaskDesc_DAWNCommon_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 1, 0, InitDefaultsscc_info_TaskDesc_DAWNCommon_2eproto}, {
      &scc_info_SubTaskDesc_DAWNCommon_2eproto.base,}};

static void InitDefaultsscc_info_TaskResult_DAWNCommon_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::DAWNProto::_TaskResult_default_instance_;
    new (ptr) ::DAWNProto::TaskResult();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::DAWNProto::TaskResult::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<2> scc_info_TaskResult_DAWNCommon_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 2, 0, InitDefaultsscc_info_TaskResult_DAWNCommon_2eproto}, {
      &scc_info_StringValue_DAWNCommon_2eproto.base,
      &scc_info_MissionStatisticMsg_DAWNCommon_2eproto.base,}};

static void InitDefaultsscc_info_UserInfo_DAWNCommon_2eproto() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

  {
    void* ptr = &::DAWNProto::_UserInfo_default_instance_;
    new (ptr) ::DAWNProto::UserInfo();
    ::PROTOBUF_NAMESPACE_ID::internal::OnShutdownDestroyMessage(ptr);
  }
  ::DAWNProto::UserInfo::InitAsDefaultInstance();
}

::PROTOBUF_NAMESPACE_ID::internal::SCCInfo<0> scc_info_UserInfo_DAWNCommon_2eproto =
    {{ATOMIC_VAR_INIT(::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase::kUninitialized), 0, 0, InitDefaultsscc_info_UserInfo_DAWNCommon_2eproto}, {}};

static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_DAWNCommon_2eproto[20];
static const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* file_level_enum_descriptors_DAWNCommon_2eproto[5];
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_DAWNCommon_2eproto = nullptr;

const ::PROTOBUF_NAMESPACE_ID::uint32 TableStruct_DAWNCommon_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::DAWNProto::StringValue, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::DAWNProto::StringValue, value_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::DAWNProto::GeneralAck, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::DAWNProto::GeneralAck, code_),
  PROTOBUF_FIELD_OFFSET(::DAWNProto::GeneralAck, message_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::DAWNProto::KeyValuePair, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::DAWNProto::KeyValuePair, key_),
  PROTOBUF_FIELD_OFFSET(::DAWNProto::KeyValuePair, value_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::DAWNProto::UserInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::DAWNProto::UserInfo, project_id_),
  PROTOBUF_FIELD_OFFSET(::DAWNProto::UserInfo, user_id_),
  PROTOBUF_FIELD_OFFSET(::DAWNProto::UserInfo, user_token_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::DAWNProto::CalculationSchemeInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::DAWNProto::CalculationSchemeInfo, scheme_),
  PROTOBUF_FIELD_OFFSET(::DAWNProto::CalculationSchemeInfo, localagentname_),
  PROTOBUF_FIELD_OFFSET(::DAWNProto::CalculationSchemeInfo, localexecutablepath_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::DAWNProto::JobBasicDesc, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::DAWNProto::JobBasicDesc, categoryid_),
  PROTOBUF_FIELD_OFFSET(::DAWNProto::JobBasicDesc, jobid_),
  PROTOBUF_FIELD_OFFSET(::DAWNProto::JobBasicDesc, jobdir_),
  PROTOBUF_FIELD_OFFSET(::DAWNProto::JobBasicDesc, jobfiles_),
  PROTOBUF_FIELD_OFFSET(::DAWNProto::JobBasicDesc, exefiles_),
  PROTOBUF_FIELD_OFFSET(::DAWNProto::JobBasicDesc, exeversionmain_),
  PROTOBUF_FIELD_OFFSET(::DAWNProto::JobBasicDesc, exeversionsub_),
  PROTOBUF_FIELD_OFFSET(::DAWNProto::JobBasicDesc, exetag_),
  PROTOBUF_FIELD_OFFSET(::DAWNProto::JobBasicDesc, parameters_),
  PROTOBUF_FIELD_OFFSET(::DAWNProto::JobBasicDesc, totaltaskcost_),
  PROTOBUF_FIELD_OFFSET(::DAWNProto::JobBasicDesc, exedir_),
  PROTOBUF_FIELD_OFFSET(::DAWNProto::JobBasicDesc, filecachedir_),
  PROTOBUF_FIELD_OFFSET(::DAWNProto::JobBasicDesc, partitionlist_),
  PROTOBUF_FIELD_OFFSET(::DAWNProto::JobBasicDesc, customizedbakerpath_),
  PROTOBUF_FIELD_OFFSET(::DAWNProto::JobBasicDesc, ismanualdebug_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::DAWNProto::JobFullDesc, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::DAWNProto::JobFullDesc, basicdesc_),
  PROTOBUF_FIELD_OFFSET(::DAWNProto::JobFullDesc, taskdesclist_),
  PROTOBUF_FIELD_OFFSET(::DAWNProto::JobFullDesc, isinteractivejob_),
  PROTOBUF_FIELD_OFFSET(::DAWNProto::JobFullDesc, iscustomization_),
  PROTOBUF_FIELD_OFFSET(::DAWNProto::JobFullDesc, isrequireproxy_),
  PROTOBUF_FIELD_OFFSET(::DAWNProto::JobFullDesc, proxyaddress_),
  PROTOBUF_FIELD_OFFSET(::DAWNProto::JobFullDesc, interagentresultfiles_),
  PROTOBUF_FIELD_OFFSET(::DAWNProto::JobFullDesc, debugtaskid_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::DAWNProto::PartitionDesc, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::DAWNProto::PartitionDesc, partitionid_),
  PROTOBUF_FIELD_OFFSET(::DAWNProto::PartitionDesc, sceneinfo_),
  PROTOBUF_FIELD_OFFSET(::DAWNProto::PartitionDesc, jobinputs_),
  PROTOBUF_FIELD_OFFSET(::DAWNProto::PartitionDesc, datafiles_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::DAWNProto::TaskDesc, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::DAWNProto::TaskDesc, taskid_),
  PROTOBUF_FIELD_OFFSET(::DAWNProto::TaskDesc, parameters_),
  PROTOBUF_FIELD_OFFSET(::DAWNProto::TaskDesc, cost_),
  PROTOBUF_FIELD_OFFSET(::DAWNProto::TaskDesc, resultfilesmust_),
  PROTOBUF_FIELD_OFFSET(::DAWNProto::TaskDesc, resultfilesoptional_),
  PROTOBUF_FIELD_OFFSET(::DAWNProto::TaskDesc, partitionid_),
  PROTOBUF_FIELD_OFFSET(::DAWNProto::TaskDesc, jobinputsrange_),
  PROTOBUF_FIELD_OFFSET(::DAWNProto::TaskDesc, subtasks_),
  PROTOBUF_FIELD_OFFSET(::DAWNProto::TaskDesc, optionalonflyfiles_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::DAWNProto::SubTaskDesc, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::DAWNProto::SubTaskDesc, taskid_),
  PROTOBUF_FIELD_OFFSET(::DAWNProto::SubTaskDesc, resultfilesmust_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::DAWNProto::BeginMissionReq, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::DAWNProto::BeginMissionReq, missionid_),
  PROTOBUF_FIELD_OFFSET(::DAWNProto::BeginMissionReq, categoryid_),
  PROTOBUF_FIELD_OFFSET(::DAWNProto::BeginMissionReq, programid_),
  PROTOBUF_FIELD_OFFSET(::DAWNProto::BeginMissionReq, custominfo_),
  PROTOBUF_FIELD_OFFSET(::DAWNProto::BeginMissionReq, userinfo_),
  PROTOBUF_FIELD_OFFSET(::DAWNProto::BeginMissionReq, gpucount_),
  PROTOBUF_FIELD_OFFSET(::DAWNProto::BeginMissionReq, calculationschemeinfo_),
  PROTOBUF_FIELD_OFFSET(::DAWNProto::BeginMissionReq, ispreview_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::DAWNProto::EndMissionReq, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::DAWNProto::EndMissionReq, missionid_),
  PROTOBUF_FIELD_OFFSET(::DAWNProto::EndMissionReq, reportmsgs_),
  PROTOBUF_FIELD_OFFSET(::DAWNProto::EndMissionReq, finalreportmsg_),
  PROTOBUF_FIELD_OFFSET(::DAWNProto::EndMissionReq, datareportmsgs_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::DAWNProto::AbortMissionReq, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::DAWNProto::TaskResult, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::DAWNProto::TaskResult, taskid_),
  PROTOBUF_FIELD_OFFSET(::DAWNProto::TaskResult, errmsg_),
  PROTOBUF_FIELD_OFFSET(::DAWNProto::TaskResult, resultfiles_),
  PROTOBUF_FIELD_OFFSET(::DAWNProto::TaskResult, parenttaskid_),
  PROTOBUF_FIELD_OFFSET(::DAWNProto::TaskResult, statisticmsg_),
  PROTOBUF_FIELD_OFFSET(::DAWNProto::TaskResult, type_),
  PROTOBUF_FIELD_OFFSET(::DAWNProto::TaskResult, progress_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::DAWNProto::ReportMsg, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::DAWNProto::ReportMsg, type_),
  PROTOBUF_FIELD_OFFSET(::DAWNProto::ReportMsg, msg_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::DAWNProto::MissionStatisticMsg, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::DAWNProto::MissionStatisticMsg, utctime_),
  PROTOBUF_FIELD_OFFSET(::DAWNProto::MissionStatisticMsg, type_),
  PROTOBUF_FIELD_OFFSET(::DAWNProto::MissionStatisticMsg, identifiernumber_),
  PROTOBUF_FIELD_OFFSET(::DAWNProto::MissionStatisticMsg, identifiernumber2_),
  PROTOBUF_FIELD_OFFSET(::DAWNProto::MissionStatisticMsg, agentid_),
  PROTOBUF_FIELD_OFFSET(::DAWNProto::MissionStatisticMsg, starttime_),
  PROTOBUF_FIELD_OFFSET(::DAWNProto::MissionStatisticMsg, costtime_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::DAWNProto::MissionStatisticMsgArray, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::DAWNProto::MissionStatisticMsgArray, msgarray_),
  PROTOBUF_FIELD_OFFSET(::DAWNProto::MissionStatisticMsgArray, localutctime_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::DAWNProto::MissionStatisticMsgReq, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::DAWNProto::PerfStatsMsgArray, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  PROTOBUF_FIELD_OFFSET(::DAWNProto::PerfStatsMsgArray, perfstatsmsgs_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::DAWNProto::PerfStatsMsgReq, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::DAWNProto::StringValue)},
  { 6, -1, sizeof(::DAWNProto::GeneralAck)},
  { 13, -1, sizeof(::DAWNProto::KeyValuePair)},
  { 20, -1, sizeof(::DAWNProto::UserInfo)},
  { 28, -1, sizeof(::DAWNProto::CalculationSchemeInfo)},
  { 36, -1, sizeof(::DAWNProto::JobBasicDesc)},
  { 56, -1, sizeof(::DAWNProto::JobFullDesc)},
  { 69, -1, sizeof(::DAWNProto::PartitionDesc)},
  { 78, -1, sizeof(::DAWNProto::TaskDesc)},
  { 92, -1, sizeof(::DAWNProto::SubTaskDesc)},
  { 99, -1, sizeof(::DAWNProto::BeginMissionReq)},
  { 112, -1, sizeof(::DAWNProto::EndMissionReq)},
  { 121, -1, sizeof(::DAWNProto::AbortMissionReq)},
  { 126, -1, sizeof(::DAWNProto::TaskResult)},
  { 138, -1, sizeof(::DAWNProto::ReportMsg)},
  { 145, -1, sizeof(::DAWNProto::MissionStatisticMsg)},
  { 157, -1, sizeof(::DAWNProto::MissionStatisticMsgArray)},
  { 164, -1, sizeof(::DAWNProto::MissionStatisticMsgReq)},
  { 169, -1, sizeof(::DAWNProto::PerfStatsMsgArray)},
  { 175, -1, sizeof(::DAWNProto::PerfStatsMsgReq)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::DAWNProto::_StringValue_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::DAWNProto::_GeneralAck_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::DAWNProto::_KeyValuePair_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::DAWNProto::_UserInfo_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::DAWNProto::_CalculationSchemeInfo_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::DAWNProto::_JobBasicDesc_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::DAWNProto::_JobFullDesc_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::DAWNProto::_PartitionDesc_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::DAWNProto::_TaskDesc_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::DAWNProto::_SubTaskDesc_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::DAWNProto::_BeginMissionReq_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::DAWNProto::_EndMissionReq_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::DAWNProto::_AbortMissionReq_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::DAWNProto::_TaskResult_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::DAWNProto::_ReportMsg_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::DAWNProto::_MissionStatisticMsg_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::DAWNProto::_MissionStatisticMsgArray_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::DAWNProto::_MissionStatisticMsgReq_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::DAWNProto::_PerfStatsMsgArray_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::DAWNProto::_PerfStatsMsgReq_default_instance_),
};

const char descriptor_table_protodef_DAWNCommon_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\020DAWNCommon.proto\022\tDAWNProto\"\034\n\013StringV"
  "alue\022\r\n\005value\030\001 \001(\t\"\?\n\nGeneralAck\022 \n\004cod"
  "e\030\001 \001(\0162\022.DAWNProto.AckCode\022\017\n\007message\030\002"
  " \001(\t\"*\n\014KeyValuePair\022\013\n\003key\030\001 \001(\t\022\r\n\005val"
  "ue\030\002 \001(\t\"C\n\010UserInfo\022\022\n\nproject_id\030\001 \001(\t"
  "\022\017\n\007user_id\030\002 \001(\t\022\022\n\nuser_token\030\003 \001(\t\"\315\001"
  "\n\025CalculationSchemeInfo\022B\n\006scheme\030\001 \001(\0162"
  "2.DAWNProto.CalculationSchemeInfo.Calcul"
  "ationScheme\022\026\n\016localAgentName\030\002 \001(\t\022\033\n\023l"
  "ocalExecutablePath\030\003 \001(\t\";\n\021CalculationS"
  "cheme\022\016\n\nOnlyRemote\020\000\022\007\n\003Mix\020\001\022\r\n\tOnlyLo"
  "cal\020\002\"\332\002\n\014JobBasicDesc\022\022\n\ncategoryID\030\013 \001"
  "(\t\022\r\n\005jobID\030\001 \001(\t\022\016\n\006jobDir\030\002 \001(\t\022\020\n\010job"
  "Files\030\003 \003(\t\022\020\n\010exeFiles\030\004 \003(\t\022\026\n\016exeVers"
  "ionMain\030\005 \001(\005\022\025\n\rexeVersionSub\030\006 \001(\005\022\016\n\006"
  "exeTag\030\014 \001(\t\022\022\n\nparameters\030\007 \001(\t\022\025\n\rtota"
  "lTaskCost\030\010 \001(\003\022\016\n\006exeDir\030\t \001(\t\022\024\n\014fileC"
  "acheDir\030\n \001(\t\022/\n\rpartitionList\030\r \003(\0132\030.D"
  "AWNProto.PartitionDesc\022\033\n\023customizedBake"
  "rPath\030\016 \001(\t\022\025\n\risManualDebug\030\017 \001(\010\"\371\001\n\013J"
  "obFullDesc\022*\n\tbasicDesc\030\001 \001(\0132\027.DAWNProt"
  "o.JobBasicDesc\022)\n\014taskDescList\030\002 \003(\0132\023.D"
  "AWNProto.TaskDesc\022\030\n\020isInteractiveJob\030\003 "
  "\001(\010\022\027\n\017isCustomization\030\004 \001(\010\022\026\n\016isRequir"
  "eProxy\030\005 \001(\010\022\024\n\014ProxyAddress\030\006 \001(\t\022\035\n\025In"
  "terAgentResultFiles\030\007 \003(\t\022\023\n\013debugTaskID"
  "\030\010 \001(\t\"]\n\rPartitionDesc\022\023\n\013partitionID\030\001"
  " \001(\t\022\021\n\tsceneInfo\030\002 \001(\014\022\021\n\tjobInputs\030\003 \001"
  "(\014\022\021\n\tdataFiles\030\004 \003(\t\"\345\001\n\010TaskDesc\022\016\n\006ta"
  "skID\030\001 \001(\t\022\022\n\nparameters\030\002 \001(\t\022\014\n\004cost\030\003"
  " \001(\005\022\027\n\017resultFilesMust\030\004 \003(\t\022\033\n\023resultF"
  "ilesOptional\030\005 \003(\t\022\023\n\013partitionID\030\006 \001(\t\022"
  "\026\n\016jobInputsRange\030\007 \001(\014\022(\n\010subTasks\030\010 \003("
  "\0132\026.DAWNProto.SubTaskDesc\022\032\n\022optionalOnF"
  "lyFiles\030\t \003(\t\"6\n\013SubTaskDesc\022\016\n\006taskID\030\001"
  " \001(\t\022\027\n\017resultFilesMust\030\002 \003(\t\"\354\001\n\017BeginM"
  "issionReq\022\021\n\tmissionID\030\001 \001(\t\022\022\n\ncategory"
  "ID\030\002 \001(\t\022\021\n\tProgramID\030\010 \001(\t\022\022\n\ncustomInf"
  "o\030\003 \001(\t\022%\n\010userInfo\030\004 \001(\0132\023.DAWNProto.Us"
  "erInfo\022\020\n\010gpuCount\030\005 \001(\005\022\?\n\025calculationS"
  "chemeInfo\030\006 \001(\0132 .DAWNProto.CalculationS"
  "chemeInfo\022\021\n\tIsPreview\030\007 \001(\010\"\250\001\n\rEndMiss"
  "ionReq\022\021\n\tmissionID\030\001 \001(\t\022(\n\nreportMsgs\030"
  "\002 \003(\0132\024.DAWNProto.ReportMsg\022,\n\016finalRepo"
  "rtMsg\030\003 \001(\0132\024.DAWNProto.ReportMsg\022,\n\016dat"
  "aReportMsgs\030\004 \003(\0132\024.DAWNProto.ReportMsg\""
  "\021\n\017AbortMissionReq\"\242\002\n\nTaskResult\022\016\n\006tas"
  "kID\030\001 \001(\t\022&\n\006errMsg\030\002 \001(\0132\026.DAWNProto.St"
  "ringValue\022\023\n\013resultFiles\030\003 \003(\t\022\024\n\014parent"
  "TaskID\030\004 \001(\t\0224\n\014statisticMsg\030\005 \003(\0132\036.DAW"
  "NProto.MissionStatisticMsg\0222\n\004type\030\006 \001(\016"
  "2$.DAWNProto.TaskResult.TaskResultType\022\020"
  "\n\010progress\030\007 \001(\001\"5\n\016TaskResultType\022\n\n\006Re"
  "sult\020\000\022\t\n\005Start\020\001\022\014\n\010Progress\020\002\"\245\001\n\tRepo"
  "rtMsg\0220\n\004type\030\001 \001(\0162\".DAWNProto.ReportMs"
  "g.ReportMsgType\022\013\n\003msg\030\002 \001(\t\"Y\n\rReportMs"
  "gType\022\014\n\010Progress\020\000\022\t\n\005Error\020\001\022\013\n\007Summar"
  "y\020\002\022\016\n\nDataReport\020\003\022\022\n\016DataReportList\020\004\""
  "\350\003\n\023MissionStatisticMsg\022\017\n\007UTCTime\030\001 \001(\003"
  "\0226\n\004Type\030\002 \001(\0162(.DAWNProto.MissionStatis"
  "ticMsg.EventType\022\030\n\020IdentifierNumber\030\003 \001"
  "(\003\022\031\n\021IdentifierNumber2\030\004 \001(\003\022\017\n\007AgentID"
  "\030\005 \001(\005\022\021\n\tStartTime\030\006 \001(\001\022\020\n\010CostTime\030\007 "
  "\001(\001\"\234\002\n\tEventType\022\020\n\014MissionStart\020\000\022\021\n\rM"
  "issionFinish\020\001\022\017\n\013ExportStart\020\002\022\020\n\014Expor"
  "tFinish\020\003\022\014\n\010JobStart\020\004\022\r\n\tJobFinish\020\005\022\025"
  "\n\021UploadGlobalStart\020\006\022\026\n\022UploadGlobalFin"
  "ish\020\007\022\030\n\024UploadPartitionStart\020\010\022\031\n\025Uploa"
  "dPartitionFinish\020\t\022\r\n\tTaskStart\020\n\022\016\n\nTas"
  "kFinish\020\013\022\022\n\016JobBakingStart\020\014\022\023\n\017JobBaki"
  "ngFinish\020\r\"b\n\030MissionStatisticMsgArray\0220"
  "\n\010MsgArray\030\001 \003(\0132\036.DAWNProto.MissionStat"
  "isticMsg\022\024\n\014LocalUTCTime\030\002 \001(\003\"\030\n\026Missio"
  "nStatisticMsgReq\"*\n\021PerfStatsMsgArray\022\025\n"
  "\rperfStatsMsgs\030\001 \003(\t\"\021\n\017PerfStatsMsgReq*"
  "[\n\007AckCode\022\006\n\002OK\020\000\022\024\n\007RPCFail\020\377\377\377\377\377\377\377\377\377\001"
  "\022\010\n\004Fail\020\001\022\016\n\nReRegister\020\002\022\016\n\nStateError"
  "\020\003\022\010\n\004Wait\020\004B\014\252\002\tDAWNProtob\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_DAWNCommon_2eproto_deps[1] = {
};
static ::PROTOBUF_NAMESPACE_ID::internal::SCCInfoBase*const descriptor_table_DAWNCommon_2eproto_sccs[20] = {
  &scc_info_AbortMissionReq_DAWNCommon_2eproto.base,
  &scc_info_BeginMissionReq_DAWNCommon_2eproto.base,
  &scc_info_CalculationSchemeInfo_DAWNCommon_2eproto.base,
  &scc_info_EndMissionReq_DAWNCommon_2eproto.base,
  &scc_info_GeneralAck_DAWNCommon_2eproto.base,
  &scc_info_JobBasicDesc_DAWNCommon_2eproto.base,
  &scc_info_JobFullDesc_DAWNCommon_2eproto.base,
  &scc_info_KeyValuePair_DAWNCommon_2eproto.base,
  &scc_info_MissionStatisticMsg_DAWNCommon_2eproto.base,
  &scc_info_MissionStatisticMsgArray_DAWNCommon_2eproto.base,
  &scc_info_MissionStatisticMsgReq_DAWNCommon_2eproto.base,
  &scc_info_PartitionDesc_DAWNCommon_2eproto.base,
  &scc_info_PerfStatsMsgArray_DAWNCommon_2eproto.base,
  &scc_info_PerfStatsMsgReq_DAWNCommon_2eproto.base,
  &scc_info_ReportMsg_DAWNCommon_2eproto.base,
  &scc_info_StringValue_DAWNCommon_2eproto.base,
  &scc_info_SubTaskDesc_DAWNCommon_2eproto.base,
  &scc_info_TaskDesc_DAWNCommon_2eproto.base,
  &scc_info_TaskResult_DAWNCommon_2eproto.base,
  &scc_info_UserInfo_DAWNCommon_2eproto.base,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_DAWNCommon_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_DAWNCommon_2eproto = {
  false, false, descriptor_table_protodef_DAWNCommon_2eproto, "DAWNCommon.proto", 3114,
  &descriptor_table_DAWNCommon_2eproto_once, descriptor_table_DAWNCommon_2eproto_sccs, descriptor_table_DAWNCommon_2eproto_deps, 20, 0,
  schemas, file_default_instances, TableStruct_DAWNCommon_2eproto::offsets,
  file_level_metadata_DAWNCommon_2eproto, 20, file_level_enum_descriptors_DAWNCommon_2eproto, file_level_service_descriptors_DAWNCommon_2eproto,
};

// Force running AddDescriptors() at dynamic initialization time.
static bool dynamic_init_dummy_DAWNCommon_2eproto = (static_cast<void>(::PROTOBUF_NAMESPACE_ID::internal::AddDescriptors(&descriptor_table_DAWNCommon_2eproto)), true);
namespace DAWNProto {
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* CalculationSchemeInfo_CalculationScheme_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_DAWNCommon_2eproto);
  return file_level_enum_descriptors_DAWNCommon_2eproto[0];
}
bool CalculationSchemeInfo_CalculationScheme_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
      return true;
    default:
      return false;
  }
}

#if (__cplusplus < 201703) && (!defined(_MSC_VER) || _MSC_VER >= 1900)
constexpr CalculationSchemeInfo_CalculationScheme CalculationSchemeInfo::OnlyRemote;
constexpr CalculationSchemeInfo_CalculationScheme CalculationSchemeInfo::Mix;
constexpr CalculationSchemeInfo_CalculationScheme CalculationSchemeInfo::OnlyLocal;
constexpr CalculationSchemeInfo_CalculationScheme CalculationSchemeInfo::CalculationScheme_MIN;
constexpr CalculationSchemeInfo_CalculationScheme CalculationSchemeInfo::CalculationScheme_MAX;
constexpr int CalculationSchemeInfo::CalculationScheme_ARRAYSIZE;
#endif  // (__cplusplus < 201703) && (!defined(_MSC_VER) || _MSC_VER >= 1900)
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* TaskResult_TaskResultType_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_DAWNCommon_2eproto);
  return file_level_enum_descriptors_DAWNCommon_2eproto[1];
}
bool TaskResult_TaskResultType_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
      return true;
    default:
      return false;
  }
}

#if (__cplusplus < 201703) && (!defined(_MSC_VER) || _MSC_VER >= 1900)
constexpr TaskResult_TaskResultType TaskResult::Result;
constexpr TaskResult_TaskResultType TaskResult::Start;
constexpr TaskResult_TaskResultType TaskResult::Progress;
constexpr TaskResult_TaskResultType TaskResult::TaskResultType_MIN;
constexpr TaskResult_TaskResultType TaskResult::TaskResultType_MAX;
constexpr int TaskResult::TaskResultType_ARRAYSIZE;
#endif  // (__cplusplus < 201703) && (!defined(_MSC_VER) || _MSC_VER >= 1900)
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* ReportMsg_ReportMsgType_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_DAWNCommon_2eproto);
  return file_level_enum_descriptors_DAWNCommon_2eproto[2];
}
bool ReportMsg_ReportMsgType_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
      return true;
    default:
      return false;
  }
}

#if (__cplusplus < 201703) && (!defined(_MSC_VER) || _MSC_VER >= 1900)
constexpr ReportMsg_ReportMsgType ReportMsg::Progress;
constexpr ReportMsg_ReportMsgType ReportMsg::Error;
constexpr ReportMsg_ReportMsgType ReportMsg::Summary;
constexpr ReportMsg_ReportMsgType ReportMsg::DataReport;
constexpr ReportMsg_ReportMsgType ReportMsg::DataReportList;
constexpr ReportMsg_ReportMsgType ReportMsg::ReportMsgType_MIN;
constexpr ReportMsg_ReportMsgType ReportMsg::ReportMsgType_MAX;
constexpr int ReportMsg::ReportMsgType_ARRAYSIZE;
#endif  // (__cplusplus < 201703) && (!defined(_MSC_VER) || _MSC_VER >= 1900)
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* MissionStatisticMsg_EventType_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_DAWNCommon_2eproto);
  return file_level_enum_descriptors_DAWNCommon_2eproto[3];
}
bool MissionStatisticMsg_EventType_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
    case 6:
    case 7:
    case 8:
    case 9:
    case 10:
    case 11:
    case 12:
    case 13:
      return true;
    default:
      return false;
  }
}

#if (__cplusplus < 201703) && (!defined(_MSC_VER) || _MSC_VER >= 1900)
constexpr MissionStatisticMsg_EventType MissionStatisticMsg::MissionStart;
constexpr MissionStatisticMsg_EventType MissionStatisticMsg::MissionFinish;
constexpr MissionStatisticMsg_EventType MissionStatisticMsg::ExportStart;
constexpr MissionStatisticMsg_EventType MissionStatisticMsg::ExportFinish;
constexpr MissionStatisticMsg_EventType MissionStatisticMsg::JobStart;
constexpr MissionStatisticMsg_EventType MissionStatisticMsg::JobFinish;
constexpr MissionStatisticMsg_EventType MissionStatisticMsg::UploadGlobalStart;
constexpr MissionStatisticMsg_EventType MissionStatisticMsg::UploadGlobalFinish;
constexpr MissionStatisticMsg_EventType MissionStatisticMsg::UploadPartitionStart;
constexpr MissionStatisticMsg_EventType MissionStatisticMsg::UploadPartitionFinish;
constexpr MissionStatisticMsg_EventType MissionStatisticMsg::TaskStart;
constexpr MissionStatisticMsg_EventType MissionStatisticMsg::TaskFinish;
constexpr MissionStatisticMsg_EventType MissionStatisticMsg::JobBakingStart;
constexpr MissionStatisticMsg_EventType MissionStatisticMsg::JobBakingFinish;
constexpr MissionStatisticMsg_EventType MissionStatisticMsg::EventType_MIN;
constexpr MissionStatisticMsg_EventType MissionStatisticMsg::EventType_MAX;
constexpr int MissionStatisticMsg::EventType_ARRAYSIZE;
#endif  // (__cplusplus < 201703) && (!defined(_MSC_VER) || _MSC_VER >= 1900)
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* AckCode_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_DAWNCommon_2eproto);
  return file_level_enum_descriptors_DAWNCommon_2eproto[4];
}
bool AckCode_IsValid(int value) {
  switch (value) {
    case -1:
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
      return true;
    default:
      return false;
  }
}


// ===================================================================

void StringValue::InitAsDefaultInstance() {
}
class StringValue::_Internal {
 public:
};

StringValue::StringValue(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:DAWNProto.StringValue)
}
StringValue::StringValue(const StringValue& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  value_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_value().empty()) {
    value_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from._internal_value(),
      GetArena());
  }
  // @@protoc_insertion_point(copy_constructor:DAWNProto.StringValue)
}

void StringValue::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_StringValue_DAWNCommon_2eproto.base);
  value_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

StringValue::~StringValue() {
  // @@protoc_insertion_point(destructor:DAWNProto.StringValue)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void StringValue::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  value_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void StringValue::ArenaDtor(void* object) {
  StringValue* _this = reinterpret_cast< StringValue* >(object);
  (void)_this;
}
void StringValue::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void StringValue::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const StringValue& StringValue::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_StringValue_DAWNCommon_2eproto.base);
  return *internal_default_instance();
}


void StringValue::Clear() {
// @@protoc_insertion_point(message_clear_start:DAWNProto.StringValue)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  value_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* StringValue::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  ::PROTOBUF_NAMESPACE_ID::Arena* arena = GetArena(); (void)arena;
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // string value = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          auto str = _internal_mutable_value();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "DAWNProto.StringValue.value"));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* StringValue::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:DAWNProto.StringValue)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string value = 1;
  if (this->value().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_value().data(), static_cast<int>(this->_internal_value().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "DAWNProto.StringValue.value");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_value(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:DAWNProto.StringValue)
  return target;
}

size_t StringValue::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:DAWNProto.StringValue)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string value = 1;
  if (this->value().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_value());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void StringValue::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:DAWNProto.StringValue)
  GOOGLE_DCHECK_NE(&from, this);
  const StringValue* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<StringValue>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:DAWNProto.StringValue)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:DAWNProto.StringValue)
    MergeFrom(*source);
  }
}

void StringValue::MergeFrom(const StringValue& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:DAWNProto.StringValue)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.value().size() > 0) {
    _internal_set_value(from._internal_value());
  }
}

void StringValue::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:DAWNProto.StringValue)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void StringValue::CopyFrom(const StringValue& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:DAWNProto.StringValue)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool StringValue::IsInitialized() const {
  return true;
}

void StringValue::InternalSwap(StringValue* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  value_.Swap(&other->value_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
}

::PROTOBUF_NAMESPACE_ID::Metadata StringValue::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

void GeneralAck::InitAsDefaultInstance() {
}
class GeneralAck::_Internal {
 public:
};

GeneralAck::GeneralAck(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:DAWNProto.GeneralAck)
}
GeneralAck::GeneralAck(const GeneralAck& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  message_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_message().empty()) {
    message_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from._internal_message(),
      GetArena());
  }
  code_ = from.code_;
  // @@protoc_insertion_point(copy_constructor:DAWNProto.GeneralAck)
}

void GeneralAck::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_GeneralAck_DAWNCommon_2eproto.base);
  message_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  code_ = 0;
}

GeneralAck::~GeneralAck() {
  // @@protoc_insertion_point(destructor:DAWNProto.GeneralAck)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void GeneralAck::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  message_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void GeneralAck::ArenaDtor(void* object) {
  GeneralAck* _this = reinterpret_cast< GeneralAck* >(object);
  (void)_this;
}
void GeneralAck::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GeneralAck::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const GeneralAck& GeneralAck::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_GeneralAck_DAWNCommon_2eproto.base);
  return *internal_default_instance();
}


void GeneralAck::Clear() {
// @@protoc_insertion_point(message_clear_start:DAWNProto.GeneralAck)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  message_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  code_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GeneralAck::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  ::PROTOBUF_NAMESPACE_ID::Arena* arena = GetArena(); (void)arena;
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // .DAWNProto.AckCode code = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_code(static_cast<::DAWNProto::AckCode>(val));
        } else goto handle_unusual;
        continue;
      // string message = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          auto str = _internal_mutable_message();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "DAWNProto.GeneralAck.message"));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* GeneralAck::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:DAWNProto.GeneralAck)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .DAWNProto.AckCode code = 1;
  if (this->code() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      1, this->_internal_code(), target);
  }

  // string message = 2;
  if (this->message().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_message().data(), static_cast<int>(this->_internal_message().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "DAWNProto.GeneralAck.message");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_message(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:DAWNProto.GeneralAck)
  return target;
}

size_t GeneralAck::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:DAWNProto.GeneralAck)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string message = 2;
  if (this->message().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_message());
  }

  // .DAWNProto.AckCode code = 1;
  if (this->code() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_code());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void GeneralAck::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:DAWNProto.GeneralAck)
  GOOGLE_DCHECK_NE(&from, this);
  const GeneralAck* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<GeneralAck>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:DAWNProto.GeneralAck)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:DAWNProto.GeneralAck)
    MergeFrom(*source);
  }
}

void GeneralAck::MergeFrom(const GeneralAck& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:DAWNProto.GeneralAck)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.message().size() > 0) {
    _internal_set_message(from._internal_message());
  }
  if (from.code() != 0) {
    _internal_set_code(from._internal_code());
  }
}

void GeneralAck::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:DAWNProto.GeneralAck)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GeneralAck::CopyFrom(const GeneralAck& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:DAWNProto.GeneralAck)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GeneralAck::IsInitialized() const {
  return true;
}

void GeneralAck::InternalSwap(GeneralAck* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  message_.Swap(&other->message_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  swap(code_, other->code_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GeneralAck::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

void KeyValuePair::InitAsDefaultInstance() {
}
class KeyValuePair::_Internal {
 public:
};

KeyValuePair::KeyValuePair(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:DAWNProto.KeyValuePair)
}
KeyValuePair::KeyValuePair(const KeyValuePair& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  key_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_key().empty()) {
    key_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from._internal_key(),
      GetArena());
  }
  value_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_value().empty()) {
    value_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from._internal_value(),
      GetArena());
  }
  // @@protoc_insertion_point(copy_constructor:DAWNProto.KeyValuePair)
}

void KeyValuePair::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_KeyValuePair_DAWNCommon_2eproto.base);
  key_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  value_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

KeyValuePair::~KeyValuePair() {
  // @@protoc_insertion_point(destructor:DAWNProto.KeyValuePair)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void KeyValuePair::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  key_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  value_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void KeyValuePair::ArenaDtor(void* object) {
  KeyValuePair* _this = reinterpret_cast< KeyValuePair* >(object);
  (void)_this;
}
void KeyValuePair::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void KeyValuePair::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const KeyValuePair& KeyValuePair::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_KeyValuePair_DAWNCommon_2eproto.base);
  return *internal_default_instance();
}


void KeyValuePair::Clear() {
// @@protoc_insertion_point(message_clear_start:DAWNProto.KeyValuePair)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  key_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  value_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* KeyValuePair::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  ::PROTOBUF_NAMESPACE_ID::Arena* arena = GetArena(); (void)arena;
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // string key = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          auto str = _internal_mutable_key();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "DAWNProto.KeyValuePair.key"));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // string value = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          auto str = _internal_mutable_value();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "DAWNProto.KeyValuePair.value"));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* KeyValuePair::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:DAWNProto.KeyValuePair)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string key = 1;
  if (this->key().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_key().data(), static_cast<int>(this->_internal_key().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "DAWNProto.KeyValuePair.key");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_key(), target);
  }

  // string value = 2;
  if (this->value().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_value().data(), static_cast<int>(this->_internal_value().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "DAWNProto.KeyValuePair.value");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_value(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:DAWNProto.KeyValuePair)
  return target;
}

size_t KeyValuePair::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:DAWNProto.KeyValuePair)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string key = 1;
  if (this->key().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_key());
  }

  // string value = 2;
  if (this->value().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_value());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void KeyValuePair::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:DAWNProto.KeyValuePair)
  GOOGLE_DCHECK_NE(&from, this);
  const KeyValuePair* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<KeyValuePair>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:DAWNProto.KeyValuePair)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:DAWNProto.KeyValuePair)
    MergeFrom(*source);
  }
}

void KeyValuePair::MergeFrom(const KeyValuePair& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:DAWNProto.KeyValuePair)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.key().size() > 0) {
    _internal_set_key(from._internal_key());
  }
  if (from.value().size() > 0) {
    _internal_set_value(from._internal_value());
  }
}

void KeyValuePair::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:DAWNProto.KeyValuePair)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void KeyValuePair::CopyFrom(const KeyValuePair& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:DAWNProto.KeyValuePair)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool KeyValuePair::IsInitialized() const {
  return true;
}

void KeyValuePair::InternalSwap(KeyValuePair* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  key_.Swap(&other->key_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  value_.Swap(&other->value_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
}

::PROTOBUF_NAMESPACE_ID::Metadata KeyValuePair::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

void UserInfo::InitAsDefaultInstance() {
}
class UserInfo::_Internal {
 public:
};

UserInfo::UserInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:DAWNProto.UserInfo)
}
UserInfo::UserInfo(const UserInfo& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  project_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_project_id().empty()) {
    project_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from._internal_project_id(),
      GetArena());
  }
  user_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_user_id().empty()) {
    user_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from._internal_user_id(),
      GetArena());
  }
  user_token_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_user_token().empty()) {
    user_token_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from._internal_user_token(),
      GetArena());
  }
  // @@protoc_insertion_point(copy_constructor:DAWNProto.UserInfo)
}

void UserInfo::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_UserInfo_DAWNCommon_2eproto.base);
  project_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  user_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  user_token_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

UserInfo::~UserInfo() {
  // @@protoc_insertion_point(destructor:DAWNProto.UserInfo)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void UserInfo::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  project_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  user_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  user_token_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void UserInfo::ArenaDtor(void* object) {
  UserInfo* _this = reinterpret_cast< UserInfo* >(object);
  (void)_this;
}
void UserInfo::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void UserInfo::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const UserInfo& UserInfo::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_UserInfo_DAWNCommon_2eproto.base);
  return *internal_default_instance();
}


void UserInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:DAWNProto.UserInfo)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  project_id_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  user_id_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  user_token_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* UserInfo::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  ::PROTOBUF_NAMESPACE_ID::Arena* arena = GetArena(); (void)arena;
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // string project_id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          auto str = _internal_mutable_project_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "DAWNProto.UserInfo.project_id"));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // string user_id = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          auto str = _internal_mutable_user_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "DAWNProto.UserInfo.user_id"));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // string user_token = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          auto str = _internal_mutable_user_token();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "DAWNProto.UserInfo.user_token"));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* UserInfo::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:DAWNProto.UserInfo)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string project_id = 1;
  if (this->project_id().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_project_id().data(), static_cast<int>(this->_internal_project_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "DAWNProto.UserInfo.project_id");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_project_id(), target);
  }

  // string user_id = 2;
  if (this->user_id().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_user_id().data(), static_cast<int>(this->_internal_user_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "DAWNProto.UserInfo.user_id");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_user_id(), target);
  }

  // string user_token = 3;
  if (this->user_token().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_user_token().data(), static_cast<int>(this->_internal_user_token().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "DAWNProto.UserInfo.user_token");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_user_token(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:DAWNProto.UserInfo)
  return target;
}

size_t UserInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:DAWNProto.UserInfo)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string project_id = 1;
  if (this->project_id().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_project_id());
  }

  // string user_id = 2;
  if (this->user_id().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_user_id());
  }

  // string user_token = 3;
  if (this->user_token().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_user_token());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void UserInfo::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:DAWNProto.UserInfo)
  GOOGLE_DCHECK_NE(&from, this);
  const UserInfo* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<UserInfo>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:DAWNProto.UserInfo)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:DAWNProto.UserInfo)
    MergeFrom(*source);
  }
}

void UserInfo::MergeFrom(const UserInfo& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:DAWNProto.UserInfo)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.project_id().size() > 0) {
    _internal_set_project_id(from._internal_project_id());
  }
  if (from.user_id().size() > 0) {
    _internal_set_user_id(from._internal_user_id());
  }
  if (from.user_token().size() > 0) {
    _internal_set_user_token(from._internal_user_token());
  }
}

void UserInfo::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:DAWNProto.UserInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void UserInfo::CopyFrom(const UserInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:DAWNProto.UserInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool UserInfo::IsInitialized() const {
  return true;
}

void UserInfo::InternalSwap(UserInfo* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  project_id_.Swap(&other->project_id_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  user_id_.Swap(&other->user_id_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  user_token_.Swap(&other->user_token_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
}

::PROTOBUF_NAMESPACE_ID::Metadata UserInfo::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

void CalculationSchemeInfo::InitAsDefaultInstance() {
}
class CalculationSchemeInfo::_Internal {
 public:
};

CalculationSchemeInfo::CalculationSchemeInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:DAWNProto.CalculationSchemeInfo)
}
CalculationSchemeInfo::CalculationSchemeInfo(const CalculationSchemeInfo& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  localagentname_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_localagentname().empty()) {
    localagentname_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from._internal_localagentname(),
      GetArena());
  }
  localexecutablepath_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_localexecutablepath().empty()) {
    localexecutablepath_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from._internal_localexecutablepath(),
      GetArena());
  }
  scheme_ = from.scheme_;
  // @@protoc_insertion_point(copy_constructor:DAWNProto.CalculationSchemeInfo)
}

void CalculationSchemeInfo::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_CalculationSchemeInfo_DAWNCommon_2eproto.base);
  localagentname_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  localexecutablepath_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  scheme_ = 0;
}

CalculationSchemeInfo::~CalculationSchemeInfo() {
  // @@protoc_insertion_point(destructor:DAWNProto.CalculationSchemeInfo)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void CalculationSchemeInfo::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  localagentname_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  localexecutablepath_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void CalculationSchemeInfo::ArenaDtor(void* object) {
  CalculationSchemeInfo* _this = reinterpret_cast< CalculationSchemeInfo* >(object);
  (void)_this;
}
void CalculationSchemeInfo::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void CalculationSchemeInfo::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const CalculationSchemeInfo& CalculationSchemeInfo::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_CalculationSchemeInfo_DAWNCommon_2eproto.base);
  return *internal_default_instance();
}


void CalculationSchemeInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:DAWNProto.CalculationSchemeInfo)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  localagentname_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  localexecutablepath_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  scheme_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* CalculationSchemeInfo::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  ::PROTOBUF_NAMESPACE_ID::Arena* arena = GetArena(); (void)arena;
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // .DAWNProto.CalculationSchemeInfo.CalculationScheme scheme = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_scheme(static_cast<::DAWNProto::CalculationSchemeInfo_CalculationScheme>(val));
        } else goto handle_unusual;
        continue;
      // string localAgentName = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          auto str = _internal_mutable_localagentname();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "DAWNProto.CalculationSchemeInfo.localAgentName"));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // string localExecutablePath = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          auto str = _internal_mutable_localexecutablepath();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "DAWNProto.CalculationSchemeInfo.localExecutablePath"));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* CalculationSchemeInfo::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:DAWNProto.CalculationSchemeInfo)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .DAWNProto.CalculationSchemeInfo.CalculationScheme scheme = 1;
  if (this->scheme() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      1, this->_internal_scheme(), target);
  }

  // string localAgentName = 2;
  if (this->localagentname().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_localagentname().data(), static_cast<int>(this->_internal_localagentname().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "DAWNProto.CalculationSchemeInfo.localAgentName");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_localagentname(), target);
  }

  // string localExecutablePath = 3;
  if (this->localexecutablepath().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_localexecutablepath().data(), static_cast<int>(this->_internal_localexecutablepath().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "DAWNProto.CalculationSchemeInfo.localExecutablePath");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_localexecutablepath(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:DAWNProto.CalculationSchemeInfo)
  return target;
}

size_t CalculationSchemeInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:DAWNProto.CalculationSchemeInfo)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string localAgentName = 2;
  if (this->localagentname().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_localagentname());
  }

  // string localExecutablePath = 3;
  if (this->localexecutablepath().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_localexecutablepath());
  }

  // .DAWNProto.CalculationSchemeInfo.CalculationScheme scheme = 1;
  if (this->scheme() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_scheme());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void CalculationSchemeInfo::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:DAWNProto.CalculationSchemeInfo)
  GOOGLE_DCHECK_NE(&from, this);
  const CalculationSchemeInfo* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<CalculationSchemeInfo>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:DAWNProto.CalculationSchemeInfo)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:DAWNProto.CalculationSchemeInfo)
    MergeFrom(*source);
  }
}

void CalculationSchemeInfo::MergeFrom(const CalculationSchemeInfo& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:DAWNProto.CalculationSchemeInfo)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.localagentname().size() > 0) {
    _internal_set_localagentname(from._internal_localagentname());
  }
  if (from.localexecutablepath().size() > 0) {
    _internal_set_localexecutablepath(from._internal_localexecutablepath());
  }
  if (from.scheme() != 0) {
    _internal_set_scheme(from._internal_scheme());
  }
}

void CalculationSchemeInfo::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:DAWNProto.CalculationSchemeInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void CalculationSchemeInfo::CopyFrom(const CalculationSchemeInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:DAWNProto.CalculationSchemeInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CalculationSchemeInfo::IsInitialized() const {
  return true;
}

void CalculationSchemeInfo::InternalSwap(CalculationSchemeInfo* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  localagentname_.Swap(&other->localagentname_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  localexecutablepath_.Swap(&other->localexecutablepath_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  swap(scheme_, other->scheme_);
}

::PROTOBUF_NAMESPACE_ID::Metadata CalculationSchemeInfo::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

void JobBasicDesc::InitAsDefaultInstance() {
}
class JobBasicDesc::_Internal {
 public:
};

JobBasicDesc::JobBasicDesc(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena),
  jobfiles_(arena),
  exefiles_(arena),
  partitionlist_(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:DAWNProto.JobBasicDesc)
}
JobBasicDesc::JobBasicDesc(const JobBasicDesc& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      jobfiles_(from.jobfiles_),
      exefiles_(from.exefiles_),
      partitionlist_(from.partitionlist_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  jobid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_jobid().empty()) {
    jobid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from._internal_jobid(),
      GetArena());
  }
  jobdir_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_jobdir().empty()) {
    jobdir_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from._internal_jobdir(),
      GetArena());
  }
  parameters_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_parameters().empty()) {
    parameters_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from._internal_parameters(),
      GetArena());
  }
  exedir_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_exedir().empty()) {
    exedir_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from._internal_exedir(),
      GetArena());
  }
  filecachedir_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_filecachedir().empty()) {
    filecachedir_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from._internal_filecachedir(),
      GetArena());
  }
  categoryid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_categoryid().empty()) {
    categoryid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from._internal_categoryid(),
      GetArena());
  }
  exetag_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_exetag().empty()) {
    exetag_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from._internal_exetag(),
      GetArena());
  }
  customizedbakerpath_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_customizedbakerpath().empty()) {
    customizedbakerpath_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from._internal_customizedbakerpath(),
      GetArena());
  }
  ::memcpy(&exeversionmain_, &from.exeversionmain_,
    static_cast<size_t>(reinterpret_cast<char*>(&ismanualdebug_) -
    reinterpret_cast<char*>(&exeversionmain_)) + sizeof(ismanualdebug_));
  // @@protoc_insertion_point(copy_constructor:DAWNProto.JobBasicDesc)
}

void JobBasicDesc::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_JobBasicDesc_DAWNCommon_2eproto.base);
  jobid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  jobdir_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  parameters_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  exedir_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  filecachedir_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  categoryid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  exetag_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  customizedbakerpath_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  ::memset(&exeversionmain_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&ismanualdebug_) -
      reinterpret_cast<char*>(&exeversionmain_)) + sizeof(ismanualdebug_));
}

JobBasicDesc::~JobBasicDesc() {
  // @@protoc_insertion_point(destructor:DAWNProto.JobBasicDesc)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void JobBasicDesc::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  jobid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  jobdir_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  parameters_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  exedir_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  filecachedir_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  categoryid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  exetag_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  customizedbakerpath_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void JobBasicDesc::ArenaDtor(void* object) {
  JobBasicDesc* _this = reinterpret_cast< JobBasicDesc* >(object);
  (void)_this;
}
void JobBasicDesc::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void JobBasicDesc::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const JobBasicDesc& JobBasicDesc::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_JobBasicDesc_DAWNCommon_2eproto.base);
  return *internal_default_instance();
}


void JobBasicDesc::Clear() {
// @@protoc_insertion_point(message_clear_start:DAWNProto.JobBasicDesc)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  jobfiles_.Clear();
  exefiles_.Clear();
  partitionlist_.Clear();
  jobid_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  jobdir_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  parameters_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  exedir_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  filecachedir_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  categoryid_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  exetag_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  customizedbakerpath_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  ::memset(&exeversionmain_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&ismanualdebug_) -
      reinterpret_cast<char*>(&exeversionmain_)) + sizeof(ismanualdebug_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* JobBasicDesc::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  ::PROTOBUF_NAMESPACE_ID::Arena* arena = GetArena(); (void)arena;
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // string jobID = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          auto str = _internal_mutable_jobid();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "DAWNProto.JobBasicDesc.jobID"));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // string jobDir = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          auto str = _internal_mutable_jobdir();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "DAWNProto.JobBasicDesc.jobDir"));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated string jobFiles = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          ptr -= 1;
          do {
            ptr += 1;
            auto str = _internal_add_jobfiles();
            ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
            CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "DAWNProto.JobBasicDesc.jobFiles"));
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<26>(ptr));
        } else goto handle_unusual;
        continue;
      // repeated string exeFiles = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          ptr -= 1;
          do {
            ptr += 1;
            auto str = _internal_add_exefiles();
            ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
            CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "DAWNProto.JobBasicDesc.exeFiles"));
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<34>(ptr));
        } else goto handle_unusual;
        continue;
      // int32 exeVersionMain = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 40)) {
          exeversionmain_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // int32 exeVersionSub = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 48)) {
          exeversionsub_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // string parameters = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 58)) {
          auto str = _internal_mutable_parameters();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "DAWNProto.JobBasicDesc.parameters"));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // int64 totalTaskCost = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 64)) {
          totaltaskcost_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // string exeDir = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 74)) {
          auto str = _internal_mutable_exedir();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "DAWNProto.JobBasicDesc.exeDir"));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // string fileCacheDir = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 82)) {
          auto str = _internal_mutable_filecachedir();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "DAWNProto.JobBasicDesc.fileCacheDir"));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // string categoryID = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 90)) {
          auto str = _internal_mutable_categoryid();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "DAWNProto.JobBasicDesc.categoryID"));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // string exeTag = 12;
      case 12:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 98)) {
          auto str = _internal_mutable_exetag();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "DAWNProto.JobBasicDesc.exeTag"));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated .DAWNProto.PartitionDesc partitionList = 13;
      case 13:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 106)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_partitionlist(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<106>(ptr));
        } else goto handle_unusual;
        continue;
      // string customizedBakerPath = 14;
      case 14:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 114)) {
          auto str = _internal_mutable_customizedbakerpath();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "DAWNProto.JobBasicDesc.customizedBakerPath"));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // bool isManualDebug = 15;
      case 15:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 120)) {
          ismanualdebug_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* JobBasicDesc::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:DAWNProto.JobBasicDesc)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string jobID = 1;
  if (this->jobid().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_jobid().data(), static_cast<int>(this->_internal_jobid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "DAWNProto.JobBasicDesc.jobID");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_jobid(), target);
  }

  // string jobDir = 2;
  if (this->jobdir().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_jobdir().data(), static_cast<int>(this->_internal_jobdir().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "DAWNProto.JobBasicDesc.jobDir");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_jobdir(), target);
  }

  // repeated string jobFiles = 3;
  for (int i = 0, n = this->_internal_jobfiles_size(); i < n; i++) {
    const auto& s = this->_internal_jobfiles(i);
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      s.data(), static_cast<int>(s.length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "DAWNProto.JobBasicDesc.jobFiles");
    target = stream->WriteString(3, s, target);
  }

  // repeated string exeFiles = 4;
  for (int i = 0, n = this->_internal_exefiles_size(); i < n; i++) {
    const auto& s = this->_internal_exefiles(i);
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      s.data(), static_cast<int>(s.length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "DAWNProto.JobBasicDesc.exeFiles");
    target = stream->WriteString(4, s, target);
  }

  // int32 exeVersionMain = 5;
  if (this->exeversionmain() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(5, this->_internal_exeversionmain(), target);
  }

  // int32 exeVersionSub = 6;
  if (this->exeversionsub() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(6, this->_internal_exeversionsub(), target);
  }

  // string parameters = 7;
  if (this->parameters().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_parameters().data(), static_cast<int>(this->_internal_parameters().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "DAWNProto.JobBasicDesc.parameters");
    target = stream->WriteStringMaybeAliased(
        7, this->_internal_parameters(), target);
  }

  // int64 totalTaskCost = 8;
  if (this->totaltaskcost() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(8, this->_internal_totaltaskcost(), target);
  }

  // string exeDir = 9;
  if (this->exedir().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_exedir().data(), static_cast<int>(this->_internal_exedir().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "DAWNProto.JobBasicDesc.exeDir");
    target = stream->WriteStringMaybeAliased(
        9, this->_internal_exedir(), target);
  }

  // string fileCacheDir = 10;
  if (this->filecachedir().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_filecachedir().data(), static_cast<int>(this->_internal_filecachedir().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "DAWNProto.JobBasicDesc.fileCacheDir");
    target = stream->WriteStringMaybeAliased(
        10, this->_internal_filecachedir(), target);
  }

  // string categoryID = 11;
  if (this->categoryid().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_categoryid().data(), static_cast<int>(this->_internal_categoryid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "DAWNProto.JobBasicDesc.categoryID");
    target = stream->WriteStringMaybeAliased(
        11, this->_internal_categoryid(), target);
  }

  // string exeTag = 12;
  if (this->exetag().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_exetag().data(), static_cast<int>(this->_internal_exetag().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "DAWNProto.JobBasicDesc.exeTag");
    target = stream->WriteStringMaybeAliased(
        12, this->_internal_exetag(), target);
  }

  // repeated .DAWNProto.PartitionDesc partitionList = 13;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_partitionlist_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(13, this->_internal_partitionlist(i), target, stream);
  }

  // string customizedBakerPath = 14;
  if (this->customizedbakerpath().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_customizedbakerpath().data(), static_cast<int>(this->_internal_customizedbakerpath().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "DAWNProto.JobBasicDesc.customizedBakerPath");
    target = stream->WriteStringMaybeAliased(
        14, this->_internal_customizedbakerpath(), target);
  }

  // bool isManualDebug = 15;
  if (this->ismanualdebug() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(15, this->_internal_ismanualdebug(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:DAWNProto.JobBasicDesc)
  return target;
}

size_t JobBasicDesc::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:DAWNProto.JobBasicDesc)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated string jobFiles = 3;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(jobfiles_.size());
  for (int i = 0, n = jobfiles_.size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
      jobfiles_.Get(i));
  }

  // repeated string exeFiles = 4;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(exefiles_.size());
  for (int i = 0, n = exefiles_.size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
      exefiles_.Get(i));
  }

  // repeated .DAWNProto.PartitionDesc partitionList = 13;
  total_size += 1UL * this->_internal_partitionlist_size();
  for (const auto& msg : this->partitionlist_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // string jobID = 1;
  if (this->jobid().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_jobid());
  }

  // string jobDir = 2;
  if (this->jobdir().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_jobdir());
  }

  // string parameters = 7;
  if (this->parameters().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_parameters());
  }

  // string exeDir = 9;
  if (this->exedir().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_exedir());
  }

  // string fileCacheDir = 10;
  if (this->filecachedir().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_filecachedir());
  }

  // string categoryID = 11;
  if (this->categoryid().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_categoryid());
  }

  // string exeTag = 12;
  if (this->exetag().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_exetag());
  }

  // string customizedBakerPath = 14;
  if (this->customizedbakerpath().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_customizedbakerpath());
  }

  // int32 exeVersionMain = 5;
  if (this->exeversionmain() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
        this->_internal_exeversionmain());
  }

  // int32 exeVersionSub = 6;
  if (this->exeversionsub() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
        this->_internal_exeversionsub());
  }

  // int64 totalTaskCost = 8;
  if (this->totaltaskcost() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64Size(
        this->_internal_totaltaskcost());
  }

  // bool isManualDebug = 15;
  if (this->ismanualdebug() != 0) {
    total_size += 1 + 1;
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void JobBasicDesc::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:DAWNProto.JobBasicDesc)
  GOOGLE_DCHECK_NE(&from, this);
  const JobBasicDesc* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<JobBasicDesc>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:DAWNProto.JobBasicDesc)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:DAWNProto.JobBasicDesc)
    MergeFrom(*source);
  }
}

void JobBasicDesc::MergeFrom(const JobBasicDesc& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:DAWNProto.JobBasicDesc)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  jobfiles_.MergeFrom(from.jobfiles_);
  exefiles_.MergeFrom(from.exefiles_);
  partitionlist_.MergeFrom(from.partitionlist_);
  if (from.jobid().size() > 0) {
    _internal_set_jobid(from._internal_jobid());
  }
  if (from.jobdir().size() > 0) {
    _internal_set_jobdir(from._internal_jobdir());
  }
  if (from.parameters().size() > 0) {
    _internal_set_parameters(from._internal_parameters());
  }
  if (from.exedir().size() > 0) {
    _internal_set_exedir(from._internal_exedir());
  }
  if (from.filecachedir().size() > 0) {
    _internal_set_filecachedir(from._internal_filecachedir());
  }
  if (from.categoryid().size() > 0) {
    _internal_set_categoryid(from._internal_categoryid());
  }
  if (from.exetag().size() > 0) {
    _internal_set_exetag(from._internal_exetag());
  }
  if (from.customizedbakerpath().size() > 0) {
    _internal_set_customizedbakerpath(from._internal_customizedbakerpath());
  }
  if (from.exeversionmain() != 0) {
    _internal_set_exeversionmain(from._internal_exeversionmain());
  }
  if (from.exeversionsub() != 0) {
    _internal_set_exeversionsub(from._internal_exeversionsub());
  }
  if (from.totaltaskcost() != 0) {
    _internal_set_totaltaskcost(from._internal_totaltaskcost());
  }
  if (from.ismanualdebug() != 0) {
    _internal_set_ismanualdebug(from._internal_ismanualdebug());
  }
}

void JobBasicDesc::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:DAWNProto.JobBasicDesc)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void JobBasicDesc::CopyFrom(const JobBasicDesc& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:DAWNProto.JobBasicDesc)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool JobBasicDesc::IsInitialized() const {
  return true;
}

void JobBasicDesc::InternalSwap(JobBasicDesc* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  jobfiles_.InternalSwap(&other->jobfiles_);
  exefiles_.InternalSwap(&other->exefiles_);
  partitionlist_.InternalSwap(&other->partitionlist_);
  jobid_.Swap(&other->jobid_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  jobdir_.Swap(&other->jobdir_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  parameters_.Swap(&other->parameters_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  exedir_.Swap(&other->exedir_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  filecachedir_.Swap(&other->filecachedir_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  categoryid_.Swap(&other->categoryid_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  exetag_.Swap(&other->exetag_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  customizedbakerpath_.Swap(&other->customizedbakerpath_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(JobBasicDesc, ismanualdebug_)
      + sizeof(JobBasicDesc::ismanualdebug_)
      - PROTOBUF_FIELD_OFFSET(JobBasicDesc, exeversionmain_)>(
          reinterpret_cast<char*>(&exeversionmain_),
          reinterpret_cast<char*>(&other->exeversionmain_));
}

::PROTOBUF_NAMESPACE_ID::Metadata JobBasicDesc::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

void JobFullDesc::InitAsDefaultInstance() {
  ::DAWNProto::_JobFullDesc_default_instance_._instance.get_mutable()->basicdesc_ = const_cast< ::DAWNProto::JobBasicDesc*>(
      ::DAWNProto::JobBasicDesc::internal_default_instance());
}
class JobFullDesc::_Internal {
 public:
  static const ::DAWNProto::JobBasicDesc& basicdesc(const JobFullDesc* msg);
};

const ::DAWNProto::JobBasicDesc&
JobFullDesc::_Internal::basicdesc(const JobFullDesc* msg) {
  return *msg->basicdesc_;
}
JobFullDesc::JobFullDesc(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena),
  taskdesclist_(arena),
  interagentresultfiles_(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:DAWNProto.JobFullDesc)
}
JobFullDesc::JobFullDesc(const JobFullDesc& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      taskdesclist_(from.taskdesclist_),
      interagentresultfiles_(from.interagentresultfiles_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  proxyaddress_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_proxyaddress().empty()) {
    proxyaddress_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from._internal_proxyaddress(),
      GetArena());
  }
  debugtaskid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_debugtaskid().empty()) {
    debugtaskid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from._internal_debugtaskid(),
      GetArena());
  }
  if (from._internal_has_basicdesc()) {
    basicdesc_ = new ::DAWNProto::JobBasicDesc(*from.basicdesc_);
  } else {
    basicdesc_ = nullptr;
  }
  ::memcpy(&isinteractivejob_, &from.isinteractivejob_,
    static_cast<size_t>(reinterpret_cast<char*>(&isrequireproxy_) -
    reinterpret_cast<char*>(&isinteractivejob_)) + sizeof(isrequireproxy_));
  // @@protoc_insertion_point(copy_constructor:DAWNProto.JobFullDesc)
}

void JobFullDesc::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_JobFullDesc_DAWNCommon_2eproto.base);
  proxyaddress_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  debugtaskid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  ::memset(&basicdesc_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&isrequireproxy_) -
      reinterpret_cast<char*>(&basicdesc_)) + sizeof(isrequireproxy_));
}

JobFullDesc::~JobFullDesc() {
  // @@protoc_insertion_point(destructor:DAWNProto.JobFullDesc)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void JobFullDesc::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  proxyaddress_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  debugtaskid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete basicdesc_;
}

void JobFullDesc::ArenaDtor(void* object) {
  JobFullDesc* _this = reinterpret_cast< JobFullDesc* >(object);
  (void)_this;
}
void JobFullDesc::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void JobFullDesc::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const JobFullDesc& JobFullDesc::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_JobFullDesc_DAWNCommon_2eproto.base);
  return *internal_default_instance();
}


void JobFullDesc::Clear() {
// @@protoc_insertion_point(message_clear_start:DAWNProto.JobFullDesc)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  taskdesclist_.Clear();
  interagentresultfiles_.Clear();
  proxyaddress_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  debugtaskid_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  if (GetArena() == nullptr && basicdesc_ != nullptr) {
    delete basicdesc_;
  }
  basicdesc_ = nullptr;
  ::memset(&isinteractivejob_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&isrequireproxy_) -
      reinterpret_cast<char*>(&isinteractivejob_)) + sizeof(isrequireproxy_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* JobFullDesc::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  ::PROTOBUF_NAMESPACE_ID::Arena* arena = GetArena(); (void)arena;
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // .DAWNProto.JobBasicDesc basicDesc = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_basicdesc(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated .DAWNProto.TaskDesc taskDescList = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_taskdesclist(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else goto handle_unusual;
        continue;
      // bool isInteractiveJob = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          isinteractivejob_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // bool isCustomization = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          iscustomization_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // bool isRequireProxy = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 40)) {
          isrequireproxy_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // string ProxyAddress = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 50)) {
          auto str = _internal_mutable_proxyaddress();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "DAWNProto.JobFullDesc.ProxyAddress"));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated string InterAgentResultFiles = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 58)) {
          ptr -= 1;
          do {
            ptr += 1;
            auto str = _internal_add_interagentresultfiles();
            ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
            CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "DAWNProto.JobFullDesc.InterAgentResultFiles"));
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<58>(ptr));
        } else goto handle_unusual;
        continue;
      // string debugTaskID = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 66)) {
          auto str = _internal_mutable_debugtaskid();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "DAWNProto.JobFullDesc.debugTaskID"));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* JobFullDesc::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:DAWNProto.JobFullDesc)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .DAWNProto.JobBasicDesc basicDesc = 1;
  if (this->has_basicdesc()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::basicdesc(this), target, stream);
  }

  // repeated .DAWNProto.TaskDesc taskDescList = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_taskdesclist_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, this->_internal_taskdesclist(i), target, stream);
  }

  // bool isInteractiveJob = 3;
  if (this->isinteractivejob() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(3, this->_internal_isinteractivejob(), target);
  }

  // bool isCustomization = 4;
  if (this->iscustomization() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(4, this->_internal_iscustomization(), target);
  }

  // bool isRequireProxy = 5;
  if (this->isrequireproxy() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(5, this->_internal_isrequireproxy(), target);
  }

  // string ProxyAddress = 6;
  if (this->proxyaddress().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_proxyaddress().data(), static_cast<int>(this->_internal_proxyaddress().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "DAWNProto.JobFullDesc.ProxyAddress");
    target = stream->WriteStringMaybeAliased(
        6, this->_internal_proxyaddress(), target);
  }

  // repeated string InterAgentResultFiles = 7;
  for (int i = 0, n = this->_internal_interagentresultfiles_size(); i < n; i++) {
    const auto& s = this->_internal_interagentresultfiles(i);
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      s.data(), static_cast<int>(s.length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "DAWNProto.JobFullDesc.InterAgentResultFiles");
    target = stream->WriteString(7, s, target);
  }

  // string debugTaskID = 8;
  if (this->debugtaskid().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_debugtaskid().data(), static_cast<int>(this->_internal_debugtaskid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "DAWNProto.JobFullDesc.debugTaskID");
    target = stream->WriteStringMaybeAliased(
        8, this->_internal_debugtaskid(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:DAWNProto.JobFullDesc)
  return target;
}

size_t JobFullDesc::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:DAWNProto.JobFullDesc)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .DAWNProto.TaskDesc taskDescList = 2;
  total_size += 1UL * this->_internal_taskdesclist_size();
  for (const auto& msg : this->taskdesclist_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated string InterAgentResultFiles = 7;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(interagentresultfiles_.size());
  for (int i = 0, n = interagentresultfiles_.size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
      interagentresultfiles_.Get(i));
  }

  // string ProxyAddress = 6;
  if (this->proxyaddress().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_proxyaddress());
  }

  // string debugTaskID = 8;
  if (this->debugtaskid().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_debugtaskid());
  }

  // .DAWNProto.JobBasicDesc basicDesc = 1;
  if (this->has_basicdesc()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *basicdesc_);
  }

  // bool isInteractiveJob = 3;
  if (this->isinteractivejob() != 0) {
    total_size += 1 + 1;
  }

  // bool isCustomization = 4;
  if (this->iscustomization() != 0) {
    total_size += 1 + 1;
  }

  // bool isRequireProxy = 5;
  if (this->isrequireproxy() != 0) {
    total_size += 1 + 1;
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void JobFullDesc::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:DAWNProto.JobFullDesc)
  GOOGLE_DCHECK_NE(&from, this);
  const JobFullDesc* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<JobFullDesc>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:DAWNProto.JobFullDesc)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:DAWNProto.JobFullDesc)
    MergeFrom(*source);
  }
}

void JobFullDesc::MergeFrom(const JobFullDesc& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:DAWNProto.JobFullDesc)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  taskdesclist_.MergeFrom(from.taskdesclist_);
  interagentresultfiles_.MergeFrom(from.interagentresultfiles_);
  if (from.proxyaddress().size() > 0) {
    _internal_set_proxyaddress(from._internal_proxyaddress());
  }
  if (from.debugtaskid().size() > 0) {
    _internal_set_debugtaskid(from._internal_debugtaskid());
  }
  if (from.has_basicdesc()) {
    _internal_mutable_basicdesc()->::DAWNProto::JobBasicDesc::MergeFrom(from._internal_basicdesc());
  }
  if (from.isinteractivejob() != 0) {
    _internal_set_isinteractivejob(from._internal_isinteractivejob());
  }
  if (from.iscustomization() != 0) {
    _internal_set_iscustomization(from._internal_iscustomization());
  }
  if (from.isrequireproxy() != 0) {
    _internal_set_isrequireproxy(from._internal_isrequireproxy());
  }
}

void JobFullDesc::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:DAWNProto.JobFullDesc)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void JobFullDesc::CopyFrom(const JobFullDesc& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:DAWNProto.JobFullDesc)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool JobFullDesc::IsInitialized() const {
  return true;
}

void JobFullDesc::InternalSwap(JobFullDesc* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  taskdesclist_.InternalSwap(&other->taskdesclist_);
  interagentresultfiles_.InternalSwap(&other->interagentresultfiles_);
  proxyaddress_.Swap(&other->proxyaddress_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  debugtaskid_.Swap(&other->debugtaskid_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(JobFullDesc, isrequireproxy_)
      + sizeof(JobFullDesc::isrequireproxy_)
      - PROTOBUF_FIELD_OFFSET(JobFullDesc, basicdesc_)>(
          reinterpret_cast<char*>(&basicdesc_),
          reinterpret_cast<char*>(&other->basicdesc_));
}

::PROTOBUF_NAMESPACE_ID::Metadata JobFullDesc::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

void PartitionDesc::InitAsDefaultInstance() {
}
class PartitionDesc::_Internal {
 public:
};

PartitionDesc::PartitionDesc(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena),
  datafiles_(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:DAWNProto.PartitionDesc)
}
PartitionDesc::PartitionDesc(const PartitionDesc& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      datafiles_(from.datafiles_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  partitionid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_partitionid().empty()) {
    partitionid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from._internal_partitionid(),
      GetArena());
  }
  sceneinfo_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_sceneinfo().empty()) {
    sceneinfo_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from._internal_sceneinfo(),
      GetArena());
  }
  jobinputs_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_jobinputs().empty()) {
    jobinputs_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from._internal_jobinputs(),
      GetArena());
  }
  // @@protoc_insertion_point(copy_constructor:DAWNProto.PartitionDesc)
}

void PartitionDesc::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_PartitionDesc_DAWNCommon_2eproto.base);
  partitionid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  sceneinfo_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  jobinputs_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

PartitionDesc::~PartitionDesc() {
  // @@protoc_insertion_point(destructor:DAWNProto.PartitionDesc)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void PartitionDesc::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  partitionid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  sceneinfo_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  jobinputs_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void PartitionDesc::ArenaDtor(void* object) {
  PartitionDesc* _this = reinterpret_cast< PartitionDesc* >(object);
  (void)_this;
}
void PartitionDesc::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void PartitionDesc::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const PartitionDesc& PartitionDesc::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_PartitionDesc_DAWNCommon_2eproto.base);
  return *internal_default_instance();
}


void PartitionDesc::Clear() {
// @@protoc_insertion_point(message_clear_start:DAWNProto.PartitionDesc)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  datafiles_.Clear();
  partitionid_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  sceneinfo_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  jobinputs_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* PartitionDesc::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  ::PROTOBUF_NAMESPACE_ID::Arena* arena = GetArena(); (void)arena;
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // string partitionID = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          auto str = _internal_mutable_partitionid();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "DAWNProto.PartitionDesc.partitionID"));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // bytes sceneInfo = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          auto str = _internal_mutable_sceneinfo();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // bytes jobInputs = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          auto str = _internal_mutable_jobinputs();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated string dataFiles = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          ptr -= 1;
          do {
            ptr += 1;
            auto str = _internal_add_datafiles();
            ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
            CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "DAWNProto.PartitionDesc.dataFiles"));
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<34>(ptr));
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* PartitionDesc::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:DAWNProto.PartitionDesc)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string partitionID = 1;
  if (this->partitionid().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_partitionid().data(), static_cast<int>(this->_internal_partitionid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "DAWNProto.PartitionDesc.partitionID");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_partitionid(), target);
  }

  // bytes sceneInfo = 2;
  if (this->sceneinfo().size() > 0) {
    target = stream->WriteBytesMaybeAliased(
        2, this->_internal_sceneinfo(), target);
  }

  // bytes jobInputs = 3;
  if (this->jobinputs().size() > 0) {
    target = stream->WriteBytesMaybeAliased(
        3, this->_internal_jobinputs(), target);
  }

  // repeated string dataFiles = 4;
  for (int i = 0, n = this->_internal_datafiles_size(); i < n; i++) {
    const auto& s = this->_internal_datafiles(i);
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      s.data(), static_cast<int>(s.length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "DAWNProto.PartitionDesc.dataFiles");
    target = stream->WriteString(4, s, target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:DAWNProto.PartitionDesc)
  return target;
}

size_t PartitionDesc::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:DAWNProto.PartitionDesc)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated string dataFiles = 4;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(datafiles_.size());
  for (int i = 0, n = datafiles_.size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
      datafiles_.Get(i));
  }

  // string partitionID = 1;
  if (this->partitionid().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_partitionid());
  }

  // bytes sceneInfo = 2;
  if (this->sceneinfo().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_sceneinfo());
  }

  // bytes jobInputs = 3;
  if (this->jobinputs().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_jobinputs());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void PartitionDesc::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:DAWNProto.PartitionDesc)
  GOOGLE_DCHECK_NE(&from, this);
  const PartitionDesc* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<PartitionDesc>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:DAWNProto.PartitionDesc)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:DAWNProto.PartitionDesc)
    MergeFrom(*source);
  }
}

void PartitionDesc::MergeFrom(const PartitionDesc& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:DAWNProto.PartitionDesc)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  datafiles_.MergeFrom(from.datafiles_);
  if (from.partitionid().size() > 0) {
    _internal_set_partitionid(from._internal_partitionid());
  }
  if (from.sceneinfo().size() > 0) {
    _internal_set_sceneinfo(from._internal_sceneinfo());
  }
  if (from.jobinputs().size() > 0) {
    _internal_set_jobinputs(from._internal_jobinputs());
  }
}

void PartitionDesc::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:DAWNProto.PartitionDesc)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void PartitionDesc::CopyFrom(const PartitionDesc& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:DAWNProto.PartitionDesc)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool PartitionDesc::IsInitialized() const {
  return true;
}

void PartitionDesc::InternalSwap(PartitionDesc* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  datafiles_.InternalSwap(&other->datafiles_);
  partitionid_.Swap(&other->partitionid_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  sceneinfo_.Swap(&other->sceneinfo_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  jobinputs_.Swap(&other->jobinputs_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
}

::PROTOBUF_NAMESPACE_ID::Metadata PartitionDesc::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

void TaskDesc::InitAsDefaultInstance() {
}
class TaskDesc::_Internal {
 public:
};

TaskDesc::TaskDesc(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena),
  resultfilesmust_(arena),
  resultfilesoptional_(arena),
  subtasks_(arena),
  optionalonflyfiles_(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:DAWNProto.TaskDesc)
}
TaskDesc::TaskDesc(const TaskDesc& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      resultfilesmust_(from.resultfilesmust_),
      resultfilesoptional_(from.resultfilesoptional_),
      subtasks_(from.subtasks_),
      optionalonflyfiles_(from.optionalonflyfiles_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  taskid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_taskid().empty()) {
    taskid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from._internal_taskid(),
      GetArena());
  }
  parameters_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_parameters().empty()) {
    parameters_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from._internal_parameters(),
      GetArena());
  }
  partitionid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_partitionid().empty()) {
    partitionid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from._internal_partitionid(),
      GetArena());
  }
  jobinputsrange_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_jobinputsrange().empty()) {
    jobinputsrange_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from._internal_jobinputsrange(),
      GetArena());
  }
  cost_ = from.cost_;
  // @@protoc_insertion_point(copy_constructor:DAWNProto.TaskDesc)
}

void TaskDesc::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_TaskDesc_DAWNCommon_2eproto.base);
  taskid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  parameters_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  partitionid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  jobinputsrange_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  cost_ = 0;
}

TaskDesc::~TaskDesc() {
  // @@protoc_insertion_point(destructor:DAWNProto.TaskDesc)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void TaskDesc::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  taskid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  parameters_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  partitionid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  jobinputsrange_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void TaskDesc::ArenaDtor(void* object) {
  TaskDesc* _this = reinterpret_cast< TaskDesc* >(object);
  (void)_this;
}
void TaskDesc::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void TaskDesc::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const TaskDesc& TaskDesc::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_TaskDesc_DAWNCommon_2eproto.base);
  return *internal_default_instance();
}


void TaskDesc::Clear() {
// @@protoc_insertion_point(message_clear_start:DAWNProto.TaskDesc)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  resultfilesmust_.Clear();
  resultfilesoptional_.Clear();
  subtasks_.Clear();
  optionalonflyfiles_.Clear();
  taskid_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  parameters_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  partitionid_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  jobinputsrange_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  cost_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* TaskDesc::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  ::PROTOBUF_NAMESPACE_ID::Arena* arena = GetArena(); (void)arena;
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // string taskID = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          auto str = _internal_mutable_taskid();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "DAWNProto.TaskDesc.taskID"));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // string parameters = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          auto str = _internal_mutable_parameters();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "DAWNProto.TaskDesc.parameters"));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // int32 cost = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          cost_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated string resultFilesMust = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          ptr -= 1;
          do {
            ptr += 1;
            auto str = _internal_add_resultfilesmust();
            ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
            CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "DAWNProto.TaskDesc.resultFilesMust"));
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<34>(ptr));
        } else goto handle_unusual;
        continue;
      // repeated string resultFilesOptional = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 42)) {
          ptr -= 1;
          do {
            ptr += 1;
            auto str = _internal_add_resultfilesoptional();
            ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
            CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "DAWNProto.TaskDesc.resultFilesOptional"));
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<42>(ptr));
        } else goto handle_unusual;
        continue;
      // string partitionID = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 50)) {
          auto str = _internal_mutable_partitionid();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "DAWNProto.TaskDesc.partitionID"));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // bytes jobInputsRange = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 58)) {
          auto str = _internal_mutable_jobinputsrange();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated .DAWNProto.SubTaskDesc subTasks = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 66)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_subtasks(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<66>(ptr));
        } else goto handle_unusual;
        continue;
      // repeated string optionalOnFlyFiles = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 74)) {
          ptr -= 1;
          do {
            ptr += 1;
            auto str = _internal_add_optionalonflyfiles();
            ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
            CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "DAWNProto.TaskDesc.optionalOnFlyFiles"));
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<74>(ptr));
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* TaskDesc::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:DAWNProto.TaskDesc)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string taskID = 1;
  if (this->taskid().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_taskid().data(), static_cast<int>(this->_internal_taskid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "DAWNProto.TaskDesc.taskID");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_taskid(), target);
  }

  // string parameters = 2;
  if (this->parameters().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_parameters().data(), static_cast<int>(this->_internal_parameters().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "DAWNProto.TaskDesc.parameters");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_parameters(), target);
  }

  // int32 cost = 3;
  if (this->cost() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(3, this->_internal_cost(), target);
  }

  // repeated string resultFilesMust = 4;
  for (int i = 0, n = this->_internal_resultfilesmust_size(); i < n; i++) {
    const auto& s = this->_internal_resultfilesmust(i);
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      s.data(), static_cast<int>(s.length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "DAWNProto.TaskDesc.resultFilesMust");
    target = stream->WriteString(4, s, target);
  }

  // repeated string resultFilesOptional = 5;
  for (int i = 0, n = this->_internal_resultfilesoptional_size(); i < n; i++) {
    const auto& s = this->_internal_resultfilesoptional(i);
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      s.data(), static_cast<int>(s.length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "DAWNProto.TaskDesc.resultFilesOptional");
    target = stream->WriteString(5, s, target);
  }

  // string partitionID = 6;
  if (this->partitionid().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_partitionid().data(), static_cast<int>(this->_internal_partitionid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "DAWNProto.TaskDesc.partitionID");
    target = stream->WriteStringMaybeAliased(
        6, this->_internal_partitionid(), target);
  }

  // bytes jobInputsRange = 7;
  if (this->jobinputsrange().size() > 0) {
    target = stream->WriteBytesMaybeAliased(
        7, this->_internal_jobinputsrange(), target);
  }

  // repeated .DAWNProto.SubTaskDesc subTasks = 8;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_subtasks_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(8, this->_internal_subtasks(i), target, stream);
  }

  // repeated string optionalOnFlyFiles = 9;
  for (int i = 0, n = this->_internal_optionalonflyfiles_size(); i < n; i++) {
    const auto& s = this->_internal_optionalonflyfiles(i);
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      s.data(), static_cast<int>(s.length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "DAWNProto.TaskDesc.optionalOnFlyFiles");
    target = stream->WriteString(9, s, target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:DAWNProto.TaskDesc)
  return target;
}

size_t TaskDesc::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:DAWNProto.TaskDesc)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated string resultFilesMust = 4;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(resultfilesmust_.size());
  for (int i = 0, n = resultfilesmust_.size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
      resultfilesmust_.Get(i));
  }

  // repeated string resultFilesOptional = 5;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(resultfilesoptional_.size());
  for (int i = 0, n = resultfilesoptional_.size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
      resultfilesoptional_.Get(i));
  }

  // repeated .DAWNProto.SubTaskDesc subTasks = 8;
  total_size += 1UL * this->_internal_subtasks_size();
  for (const auto& msg : this->subtasks_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated string optionalOnFlyFiles = 9;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(optionalonflyfiles_.size());
  for (int i = 0, n = optionalonflyfiles_.size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
      optionalonflyfiles_.Get(i));
  }

  // string taskID = 1;
  if (this->taskid().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_taskid());
  }

  // string parameters = 2;
  if (this->parameters().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_parameters());
  }

  // string partitionID = 6;
  if (this->partitionid().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_partitionid());
  }

  // bytes jobInputsRange = 7;
  if (this->jobinputsrange().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_jobinputsrange());
  }

  // int32 cost = 3;
  if (this->cost() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
        this->_internal_cost());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void TaskDesc::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:DAWNProto.TaskDesc)
  GOOGLE_DCHECK_NE(&from, this);
  const TaskDesc* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<TaskDesc>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:DAWNProto.TaskDesc)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:DAWNProto.TaskDesc)
    MergeFrom(*source);
  }
}

void TaskDesc::MergeFrom(const TaskDesc& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:DAWNProto.TaskDesc)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  resultfilesmust_.MergeFrom(from.resultfilesmust_);
  resultfilesoptional_.MergeFrom(from.resultfilesoptional_);
  subtasks_.MergeFrom(from.subtasks_);
  optionalonflyfiles_.MergeFrom(from.optionalonflyfiles_);
  if (from.taskid().size() > 0) {
    _internal_set_taskid(from._internal_taskid());
  }
  if (from.parameters().size() > 0) {
    _internal_set_parameters(from._internal_parameters());
  }
  if (from.partitionid().size() > 0) {
    _internal_set_partitionid(from._internal_partitionid());
  }
  if (from.jobinputsrange().size() > 0) {
    _internal_set_jobinputsrange(from._internal_jobinputsrange());
  }
  if (from.cost() != 0) {
    _internal_set_cost(from._internal_cost());
  }
}

void TaskDesc::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:DAWNProto.TaskDesc)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TaskDesc::CopyFrom(const TaskDesc& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:DAWNProto.TaskDesc)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TaskDesc::IsInitialized() const {
  return true;
}

void TaskDesc::InternalSwap(TaskDesc* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  resultfilesmust_.InternalSwap(&other->resultfilesmust_);
  resultfilesoptional_.InternalSwap(&other->resultfilesoptional_);
  subtasks_.InternalSwap(&other->subtasks_);
  optionalonflyfiles_.InternalSwap(&other->optionalonflyfiles_);
  taskid_.Swap(&other->taskid_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  parameters_.Swap(&other->parameters_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  partitionid_.Swap(&other->partitionid_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  jobinputsrange_.Swap(&other->jobinputsrange_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  swap(cost_, other->cost_);
}

::PROTOBUF_NAMESPACE_ID::Metadata TaskDesc::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

void SubTaskDesc::InitAsDefaultInstance() {
}
class SubTaskDesc::_Internal {
 public:
};

SubTaskDesc::SubTaskDesc(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena),
  resultfilesmust_(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:DAWNProto.SubTaskDesc)
}
SubTaskDesc::SubTaskDesc(const SubTaskDesc& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      resultfilesmust_(from.resultfilesmust_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  taskid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_taskid().empty()) {
    taskid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from._internal_taskid(),
      GetArena());
  }
  // @@protoc_insertion_point(copy_constructor:DAWNProto.SubTaskDesc)
}

void SubTaskDesc::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_SubTaskDesc_DAWNCommon_2eproto.base);
  taskid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

SubTaskDesc::~SubTaskDesc() {
  // @@protoc_insertion_point(destructor:DAWNProto.SubTaskDesc)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void SubTaskDesc::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  taskid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void SubTaskDesc::ArenaDtor(void* object) {
  SubTaskDesc* _this = reinterpret_cast< SubTaskDesc* >(object);
  (void)_this;
}
void SubTaskDesc::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SubTaskDesc::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const SubTaskDesc& SubTaskDesc::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_SubTaskDesc_DAWNCommon_2eproto.base);
  return *internal_default_instance();
}


void SubTaskDesc::Clear() {
// @@protoc_insertion_point(message_clear_start:DAWNProto.SubTaskDesc)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  resultfilesmust_.Clear();
  taskid_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SubTaskDesc::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  ::PROTOBUF_NAMESPACE_ID::Arena* arena = GetArena(); (void)arena;
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // string taskID = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          auto str = _internal_mutable_taskid();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "DAWNProto.SubTaskDesc.taskID"));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated string resultFilesMust = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            auto str = _internal_add_resultfilesmust();
            ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
            CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "DAWNProto.SubTaskDesc.resultFilesMust"));
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* SubTaskDesc::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:DAWNProto.SubTaskDesc)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string taskID = 1;
  if (this->taskid().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_taskid().data(), static_cast<int>(this->_internal_taskid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "DAWNProto.SubTaskDesc.taskID");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_taskid(), target);
  }

  // repeated string resultFilesMust = 2;
  for (int i = 0, n = this->_internal_resultfilesmust_size(); i < n; i++) {
    const auto& s = this->_internal_resultfilesmust(i);
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      s.data(), static_cast<int>(s.length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "DAWNProto.SubTaskDesc.resultFilesMust");
    target = stream->WriteString(2, s, target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:DAWNProto.SubTaskDesc)
  return target;
}

size_t SubTaskDesc::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:DAWNProto.SubTaskDesc)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated string resultFilesMust = 2;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(resultfilesmust_.size());
  for (int i = 0, n = resultfilesmust_.size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
      resultfilesmust_.Get(i));
  }

  // string taskID = 1;
  if (this->taskid().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_taskid());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void SubTaskDesc::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:DAWNProto.SubTaskDesc)
  GOOGLE_DCHECK_NE(&from, this);
  const SubTaskDesc* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<SubTaskDesc>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:DAWNProto.SubTaskDesc)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:DAWNProto.SubTaskDesc)
    MergeFrom(*source);
  }
}

void SubTaskDesc::MergeFrom(const SubTaskDesc& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:DAWNProto.SubTaskDesc)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  resultfilesmust_.MergeFrom(from.resultfilesmust_);
  if (from.taskid().size() > 0) {
    _internal_set_taskid(from._internal_taskid());
  }
}

void SubTaskDesc::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:DAWNProto.SubTaskDesc)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void SubTaskDesc::CopyFrom(const SubTaskDesc& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:DAWNProto.SubTaskDesc)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SubTaskDesc::IsInitialized() const {
  return true;
}

void SubTaskDesc::InternalSwap(SubTaskDesc* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  resultfilesmust_.InternalSwap(&other->resultfilesmust_);
  taskid_.Swap(&other->taskid_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
}

::PROTOBUF_NAMESPACE_ID::Metadata SubTaskDesc::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

void BeginMissionReq::InitAsDefaultInstance() {
  ::DAWNProto::_BeginMissionReq_default_instance_._instance.get_mutable()->userinfo_ = const_cast< ::DAWNProto::UserInfo*>(
      ::DAWNProto::UserInfo::internal_default_instance());
  ::DAWNProto::_BeginMissionReq_default_instance_._instance.get_mutable()->calculationschemeinfo_ = const_cast< ::DAWNProto::CalculationSchemeInfo*>(
      ::DAWNProto::CalculationSchemeInfo::internal_default_instance());
}
class BeginMissionReq::_Internal {
 public:
  static const ::DAWNProto::UserInfo& userinfo(const BeginMissionReq* msg);
  static const ::DAWNProto::CalculationSchemeInfo& calculationschemeinfo(const BeginMissionReq* msg);
};

const ::DAWNProto::UserInfo&
BeginMissionReq::_Internal::userinfo(const BeginMissionReq* msg) {
  return *msg->userinfo_;
}
const ::DAWNProto::CalculationSchemeInfo&
BeginMissionReq::_Internal::calculationschemeinfo(const BeginMissionReq* msg) {
  return *msg->calculationschemeinfo_;
}
BeginMissionReq::BeginMissionReq(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:DAWNProto.BeginMissionReq)
}
BeginMissionReq::BeginMissionReq(const BeginMissionReq& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  missionid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_missionid().empty()) {
    missionid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from._internal_missionid(),
      GetArena());
  }
  categoryid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_categoryid().empty()) {
    categoryid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from._internal_categoryid(),
      GetArena());
  }
  custominfo_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_custominfo().empty()) {
    custominfo_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from._internal_custominfo(),
      GetArena());
  }
  programid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_programid().empty()) {
    programid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from._internal_programid(),
      GetArena());
  }
  if (from._internal_has_userinfo()) {
    userinfo_ = new ::DAWNProto::UserInfo(*from.userinfo_);
  } else {
    userinfo_ = nullptr;
  }
  if (from._internal_has_calculationschemeinfo()) {
    calculationschemeinfo_ = new ::DAWNProto::CalculationSchemeInfo(*from.calculationschemeinfo_);
  } else {
    calculationschemeinfo_ = nullptr;
  }
  ::memcpy(&gpucount_, &from.gpucount_,
    static_cast<size_t>(reinterpret_cast<char*>(&ispreview_) -
    reinterpret_cast<char*>(&gpucount_)) + sizeof(ispreview_));
  // @@protoc_insertion_point(copy_constructor:DAWNProto.BeginMissionReq)
}

void BeginMissionReq::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_BeginMissionReq_DAWNCommon_2eproto.base);
  missionid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  categoryid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  custominfo_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  programid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  ::memset(&userinfo_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&ispreview_) -
      reinterpret_cast<char*>(&userinfo_)) + sizeof(ispreview_));
}

BeginMissionReq::~BeginMissionReq() {
  // @@protoc_insertion_point(destructor:DAWNProto.BeginMissionReq)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void BeginMissionReq::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  missionid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  categoryid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  custominfo_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  programid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete userinfo_;
  if (this != internal_default_instance()) delete calculationschemeinfo_;
}

void BeginMissionReq::ArenaDtor(void* object) {
  BeginMissionReq* _this = reinterpret_cast< BeginMissionReq* >(object);
  (void)_this;
}
void BeginMissionReq::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void BeginMissionReq::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const BeginMissionReq& BeginMissionReq::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_BeginMissionReq_DAWNCommon_2eproto.base);
  return *internal_default_instance();
}


void BeginMissionReq::Clear() {
// @@protoc_insertion_point(message_clear_start:DAWNProto.BeginMissionReq)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  missionid_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  categoryid_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  custominfo_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  programid_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  if (GetArena() == nullptr && userinfo_ != nullptr) {
    delete userinfo_;
  }
  userinfo_ = nullptr;
  if (GetArena() == nullptr && calculationschemeinfo_ != nullptr) {
    delete calculationschemeinfo_;
  }
  calculationschemeinfo_ = nullptr;
  ::memset(&gpucount_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&ispreview_) -
      reinterpret_cast<char*>(&gpucount_)) + sizeof(ispreview_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* BeginMissionReq::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  ::PROTOBUF_NAMESPACE_ID::Arena* arena = GetArena(); (void)arena;
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // string missionID = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          auto str = _internal_mutable_missionid();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "DAWNProto.BeginMissionReq.missionID"));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // string categoryID = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          auto str = _internal_mutable_categoryid();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "DAWNProto.BeginMissionReq.categoryID"));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // string customInfo = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          auto str = _internal_mutable_custominfo();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "DAWNProto.BeginMissionReq.customInfo"));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .DAWNProto.UserInfo userInfo = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          ptr = ctx->ParseMessage(_internal_mutable_userinfo(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // int32 gpuCount = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 40)) {
          gpucount_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .DAWNProto.CalculationSchemeInfo calculationSchemeInfo = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 50)) {
          ptr = ctx->ParseMessage(_internal_mutable_calculationschemeinfo(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // bool IsPreview = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 56)) {
          ispreview_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // string ProgramID = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 66)) {
          auto str = _internal_mutable_programid();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "DAWNProto.BeginMissionReq.ProgramID"));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* BeginMissionReq::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:DAWNProto.BeginMissionReq)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string missionID = 1;
  if (this->missionid().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_missionid().data(), static_cast<int>(this->_internal_missionid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "DAWNProto.BeginMissionReq.missionID");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_missionid(), target);
  }

  // string categoryID = 2;
  if (this->categoryid().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_categoryid().data(), static_cast<int>(this->_internal_categoryid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "DAWNProto.BeginMissionReq.categoryID");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_categoryid(), target);
  }

  // string customInfo = 3;
  if (this->custominfo().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_custominfo().data(), static_cast<int>(this->_internal_custominfo().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "DAWNProto.BeginMissionReq.customInfo");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_custominfo(), target);
  }

  // .DAWNProto.UserInfo userInfo = 4;
  if (this->has_userinfo()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        4, _Internal::userinfo(this), target, stream);
  }

  // int32 gpuCount = 5;
  if (this->gpucount() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(5, this->_internal_gpucount(), target);
  }

  // .DAWNProto.CalculationSchemeInfo calculationSchemeInfo = 6;
  if (this->has_calculationschemeinfo()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        6, _Internal::calculationschemeinfo(this), target, stream);
  }

  // bool IsPreview = 7;
  if (this->ispreview() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(7, this->_internal_ispreview(), target);
  }

  // string ProgramID = 8;
  if (this->programid().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_programid().data(), static_cast<int>(this->_internal_programid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "DAWNProto.BeginMissionReq.ProgramID");
    target = stream->WriteStringMaybeAliased(
        8, this->_internal_programid(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:DAWNProto.BeginMissionReq)
  return target;
}

size_t BeginMissionReq::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:DAWNProto.BeginMissionReq)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string missionID = 1;
  if (this->missionid().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_missionid());
  }

  // string categoryID = 2;
  if (this->categoryid().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_categoryid());
  }

  // string customInfo = 3;
  if (this->custominfo().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_custominfo());
  }

  // string ProgramID = 8;
  if (this->programid().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_programid());
  }

  // .DAWNProto.UserInfo userInfo = 4;
  if (this->has_userinfo()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *userinfo_);
  }

  // .DAWNProto.CalculationSchemeInfo calculationSchemeInfo = 6;
  if (this->has_calculationschemeinfo()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *calculationschemeinfo_);
  }

  // int32 gpuCount = 5;
  if (this->gpucount() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
        this->_internal_gpucount());
  }

  // bool IsPreview = 7;
  if (this->ispreview() != 0) {
    total_size += 1 + 1;
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void BeginMissionReq::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:DAWNProto.BeginMissionReq)
  GOOGLE_DCHECK_NE(&from, this);
  const BeginMissionReq* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<BeginMissionReq>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:DAWNProto.BeginMissionReq)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:DAWNProto.BeginMissionReq)
    MergeFrom(*source);
  }
}

void BeginMissionReq::MergeFrom(const BeginMissionReq& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:DAWNProto.BeginMissionReq)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.missionid().size() > 0) {
    _internal_set_missionid(from._internal_missionid());
  }
  if (from.categoryid().size() > 0) {
    _internal_set_categoryid(from._internal_categoryid());
  }
  if (from.custominfo().size() > 0) {
    _internal_set_custominfo(from._internal_custominfo());
  }
  if (from.programid().size() > 0) {
    _internal_set_programid(from._internal_programid());
  }
  if (from.has_userinfo()) {
    _internal_mutable_userinfo()->::DAWNProto::UserInfo::MergeFrom(from._internal_userinfo());
  }
  if (from.has_calculationschemeinfo()) {
    _internal_mutable_calculationschemeinfo()->::DAWNProto::CalculationSchemeInfo::MergeFrom(from._internal_calculationschemeinfo());
  }
  if (from.gpucount() != 0) {
    _internal_set_gpucount(from._internal_gpucount());
  }
  if (from.ispreview() != 0) {
    _internal_set_ispreview(from._internal_ispreview());
  }
}

void BeginMissionReq::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:DAWNProto.BeginMissionReq)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void BeginMissionReq::CopyFrom(const BeginMissionReq& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:DAWNProto.BeginMissionReq)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool BeginMissionReq::IsInitialized() const {
  return true;
}

void BeginMissionReq::InternalSwap(BeginMissionReq* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  missionid_.Swap(&other->missionid_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  categoryid_.Swap(&other->categoryid_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  custominfo_.Swap(&other->custominfo_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  programid_.Swap(&other->programid_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(BeginMissionReq, ispreview_)
      + sizeof(BeginMissionReq::ispreview_)
      - PROTOBUF_FIELD_OFFSET(BeginMissionReq, userinfo_)>(
          reinterpret_cast<char*>(&userinfo_),
          reinterpret_cast<char*>(&other->userinfo_));
}

::PROTOBUF_NAMESPACE_ID::Metadata BeginMissionReq::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

void EndMissionReq::InitAsDefaultInstance() {
  ::DAWNProto::_EndMissionReq_default_instance_._instance.get_mutable()->finalreportmsg_ = const_cast< ::DAWNProto::ReportMsg*>(
      ::DAWNProto::ReportMsg::internal_default_instance());
}
class EndMissionReq::_Internal {
 public:
  static const ::DAWNProto::ReportMsg& finalreportmsg(const EndMissionReq* msg);
};

const ::DAWNProto::ReportMsg&
EndMissionReq::_Internal::finalreportmsg(const EndMissionReq* msg) {
  return *msg->finalreportmsg_;
}
EndMissionReq::EndMissionReq(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena),
  reportmsgs_(arena),
  datareportmsgs_(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:DAWNProto.EndMissionReq)
}
EndMissionReq::EndMissionReq(const EndMissionReq& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      reportmsgs_(from.reportmsgs_),
      datareportmsgs_(from.datareportmsgs_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  missionid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_missionid().empty()) {
    missionid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from._internal_missionid(),
      GetArena());
  }
  if (from._internal_has_finalreportmsg()) {
    finalreportmsg_ = new ::DAWNProto::ReportMsg(*from.finalreportmsg_);
  } else {
    finalreportmsg_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:DAWNProto.EndMissionReq)
}

void EndMissionReq::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_EndMissionReq_DAWNCommon_2eproto.base);
  missionid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  finalreportmsg_ = nullptr;
}

EndMissionReq::~EndMissionReq() {
  // @@protoc_insertion_point(destructor:DAWNProto.EndMissionReq)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void EndMissionReq::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  missionid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete finalreportmsg_;
}

void EndMissionReq::ArenaDtor(void* object) {
  EndMissionReq* _this = reinterpret_cast< EndMissionReq* >(object);
  (void)_this;
}
void EndMissionReq::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void EndMissionReq::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const EndMissionReq& EndMissionReq::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_EndMissionReq_DAWNCommon_2eproto.base);
  return *internal_default_instance();
}


void EndMissionReq::Clear() {
// @@protoc_insertion_point(message_clear_start:DAWNProto.EndMissionReq)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  reportmsgs_.Clear();
  datareportmsgs_.Clear();
  missionid_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  if (GetArena() == nullptr && finalreportmsg_ != nullptr) {
    delete finalreportmsg_;
  }
  finalreportmsg_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* EndMissionReq::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  ::PROTOBUF_NAMESPACE_ID::Arena* arena = GetArena(); (void)arena;
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // string missionID = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          auto str = _internal_mutable_missionid();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "DAWNProto.EndMissionReq.missionID"));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated .DAWNProto.ReportMsg reportMsgs = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_reportmsgs(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else goto handle_unusual;
        continue;
      // .DAWNProto.ReportMsg finalReportMsg = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_finalreportmsg(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated .DAWNProto.ReportMsg dataReportMsgs = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_datareportmsgs(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<34>(ptr));
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* EndMissionReq::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:DAWNProto.EndMissionReq)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string missionID = 1;
  if (this->missionid().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_missionid().data(), static_cast<int>(this->_internal_missionid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "DAWNProto.EndMissionReq.missionID");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_missionid(), target);
  }

  // repeated .DAWNProto.ReportMsg reportMsgs = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_reportmsgs_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, this->_internal_reportmsgs(i), target, stream);
  }

  // .DAWNProto.ReportMsg finalReportMsg = 3;
  if (this->has_finalreportmsg()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        3, _Internal::finalreportmsg(this), target, stream);
  }

  // repeated .DAWNProto.ReportMsg dataReportMsgs = 4;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_datareportmsgs_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(4, this->_internal_datareportmsgs(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:DAWNProto.EndMissionReq)
  return target;
}

size_t EndMissionReq::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:DAWNProto.EndMissionReq)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .DAWNProto.ReportMsg reportMsgs = 2;
  total_size += 1UL * this->_internal_reportmsgs_size();
  for (const auto& msg : this->reportmsgs_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .DAWNProto.ReportMsg dataReportMsgs = 4;
  total_size += 1UL * this->_internal_datareportmsgs_size();
  for (const auto& msg : this->datareportmsgs_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // string missionID = 1;
  if (this->missionid().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_missionid());
  }

  // .DAWNProto.ReportMsg finalReportMsg = 3;
  if (this->has_finalreportmsg()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *finalreportmsg_);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void EndMissionReq::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:DAWNProto.EndMissionReq)
  GOOGLE_DCHECK_NE(&from, this);
  const EndMissionReq* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<EndMissionReq>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:DAWNProto.EndMissionReq)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:DAWNProto.EndMissionReq)
    MergeFrom(*source);
  }
}

void EndMissionReq::MergeFrom(const EndMissionReq& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:DAWNProto.EndMissionReq)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  reportmsgs_.MergeFrom(from.reportmsgs_);
  datareportmsgs_.MergeFrom(from.datareportmsgs_);
  if (from.missionid().size() > 0) {
    _internal_set_missionid(from._internal_missionid());
  }
  if (from.has_finalreportmsg()) {
    _internal_mutable_finalreportmsg()->::DAWNProto::ReportMsg::MergeFrom(from._internal_finalreportmsg());
  }
}

void EndMissionReq::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:DAWNProto.EndMissionReq)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void EndMissionReq::CopyFrom(const EndMissionReq& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:DAWNProto.EndMissionReq)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool EndMissionReq::IsInitialized() const {
  return true;
}

void EndMissionReq::InternalSwap(EndMissionReq* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  reportmsgs_.InternalSwap(&other->reportmsgs_);
  datareportmsgs_.InternalSwap(&other->datareportmsgs_);
  missionid_.Swap(&other->missionid_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  swap(finalreportmsg_, other->finalreportmsg_);
}

::PROTOBUF_NAMESPACE_ID::Metadata EndMissionReq::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

void AbortMissionReq::InitAsDefaultInstance() {
}
class AbortMissionReq::_Internal {
 public:
};

AbortMissionReq::AbortMissionReq(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:DAWNProto.AbortMissionReq)
}
AbortMissionReq::AbortMissionReq(const AbortMissionReq& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:DAWNProto.AbortMissionReq)
}

void AbortMissionReq::SharedCtor() {
}

AbortMissionReq::~AbortMissionReq() {
  // @@protoc_insertion_point(destructor:DAWNProto.AbortMissionReq)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void AbortMissionReq::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
}

void AbortMissionReq::ArenaDtor(void* object) {
  AbortMissionReq* _this = reinterpret_cast< AbortMissionReq* >(object);
  (void)_this;
}
void AbortMissionReq::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void AbortMissionReq::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const AbortMissionReq& AbortMissionReq::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_AbortMissionReq_DAWNCommon_2eproto.base);
  return *internal_default_instance();
}


void AbortMissionReq::Clear() {
// @@protoc_insertion_point(message_clear_start:DAWNProto.AbortMissionReq)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* AbortMissionReq::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  ::PROTOBUF_NAMESPACE_ID::Arena* arena = GetArena(); (void)arena;
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* AbortMissionReq::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:DAWNProto.AbortMissionReq)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:DAWNProto.AbortMissionReq)
  return target;
}

size_t AbortMissionReq::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:DAWNProto.AbortMissionReq)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void AbortMissionReq::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:DAWNProto.AbortMissionReq)
  GOOGLE_DCHECK_NE(&from, this);
  const AbortMissionReq* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<AbortMissionReq>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:DAWNProto.AbortMissionReq)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:DAWNProto.AbortMissionReq)
    MergeFrom(*source);
  }
}

void AbortMissionReq::MergeFrom(const AbortMissionReq& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:DAWNProto.AbortMissionReq)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

}

void AbortMissionReq::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:DAWNProto.AbortMissionReq)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void AbortMissionReq::CopyFrom(const AbortMissionReq& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:DAWNProto.AbortMissionReq)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool AbortMissionReq::IsInitialized() const {
  return true;
}

void AbortMissionReq::InternalSwap(AbortMissionReq* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
}

::PROTOBUF_NAMESPACE_ID::Metadata AbortMissionReq::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

void TaskResult::InitAsDefaultInstance() {
  ::DAWNProto::_TaskResult_default_instance_._instance.get_mutable()->errmsg_ = const_cast< ::DAWNProto::StringValue*>(
      ::DAWNProto::StringValue::internal_default_instance());
}
class TaskResult::_Internal {
 public:
  static const ::DAWNProto::StringValue& errmsg(const TaskResult* msg);
};

const ::DAWNProto::StringValue&
TaskResult::_Internal::errmsg(const TaskResult* msg) {
  return *msg->errmsg_;
}
TaskResult::TaskResult(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena),
  resultfiles_(arena),
  statisticmsg_(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:DAWNProto.TaskResult)
}
TaskResult::TaskResult(const TaskResult& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      resultfiles_(from.resultfiles_),
      statisticmsg_(from.statisticmsg_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  taskid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_taskid().empty()) {
    taskid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from._internal_taskid(),
      GetArena());
  }
  parenttaskid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_parenttaskid().empty()) {
    parenttaskid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from._internal_parenttaskid(),
      GetArena());
  }
  if (from._internal_has_errmsg()) {
    errmsg_ = new ::DAWNProto::StringValue(*from.errmsg_);
  } else {
    errmsg_ = nullptr;
  }
  ::memcpy(&progress_, &from.progress_,
    static_cast<size_t>(reinterpret_cast<char*>(&type_) -
    reinterpret_cast<char*>(&progress_)) + sizeof(type_));
  // @@protoc_insertion_point(copy_constructor:DAWNProto.TaskResult)
}

void TaskResult::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_TaskResult_DAWNCommon_2eproto.base);
  taskid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  parenttaskid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  ::memset(&errmsg_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&type_) -
      reinterpret_cast<char*>(&errmsg_)) + sizeof(type_));
}

TaskResult::~TaskResult() {
  // @@protoc_insertion_point(destructor:DAWNProto.TaskResult)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void TaskResult::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  taskid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  parenttaskid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete errmsg_;
}

void TaskResult::ArenaDtor(void* object) {
  TaskResult* _this = reinterpret_cast< TaskResult* >(object);
  (void)_this;
}
void TaskResult::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void TaskResult::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const TaskResult& TaskResult::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_TaskResult_DAWNCommon_2eproto.base);
  return *internal_default_instance();
}


void TaskResult::Clear() {
// @@protoc_insertion_point(message_clear_start:DAWNProto.TaskResult)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  resultfiles_.Clear();
  statisticmsg_.Clear();
  taskid_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  parenttaskid_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  if (GetArena() == nullptr && errmsg_ != nullptr) {
    delete errmsg_;
  }
  errmsg_ = nullptr;
  ::memset(&progress_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&type_) -
      reinterpret_cast<char*>(&progress_)) + sizeof(type_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* TaskResult::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  ::PROTOBUF_NAMESPACE_ID::Arena* arena = GetArena(); (void)arena;
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // string taskID = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          auto str = _internal_mutable_taskid();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "DAWNProto.TaskResult.taskID"));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .DAWNProto.StringValue errMsg = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_errmsg(), ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated string resultFiles = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 26)) {
          ptr -= 1;
          do {
            ptr += 1;
            auto str = _internal_add_resultfiles();
            ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
            CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "DAWNProto.TaskResult.resultFiles"));
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<26>(ptr));
        } else goto handle_unusual;
        continue;
      // string parentTaskID = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 34)) {
          auto str = _internal_mutable_parenttaskid();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "DAWNProto.TaskResult.parentTaskID"));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // repeated .DAWNProto.MissionStatisticMsg statisticMsg = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 42)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_statisticmsg(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<42>(ptr));
        } else goto handle_unusual;
        continue;
      // .DAWNProto.TaskResult.TaskResultType type = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 48)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_type(static_cast<::DAWNProto::TaskResult_TaskResultType>(val));
        } else goto handle_unusual;
        continue;
      // double progress = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 57)) {
          progress_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* TaskResult::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:DAWNProto.TaskResult)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string taskID = 1;
  if (this->taskid().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_taskid().data(), static_cast<int>(this->_internal_taskid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "DAWNProto.TaskResult.taskID");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_taskid(), target);
  }

  // .DAWNProto.StringValue errMsg = 2;
  if (this->has_errmsg()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::errmsg(this), target, stream);
  }

  // repeated string resultFiles = 3;
  for (int i = 0, n = this->_internal_resultfiles_size(); i < n; i++) {
    const auto& s = this->_internal_resultfiles(i);
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      s.data(), static_cast<int>(s.length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "DAWNProto.TaskResult.resultFiles");
    target = stream->WriteString(3, s, target);
  }

  // string parentTaskID = 4;
  if (this->parenttaskid().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_parenttaskid().data(), static_cast<int>(this->_internal_parenttaskid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "DAWNProto.TaskResult.parentTaskID");
    target = stream->WriteStringMaybeAliased(
        4, this->_internal_parenttaskid(), target);
  }

  // repeated .DAWNProto.MissionStatisticMsg statisticMsg = 5;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_statisticmsg_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(5, this->_internal_statisticmsg(i), target, stream);
  }

  // .DAWNProto.TaskResult.TaskResultType type = 6;
  if (this->type() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      6, this->_internal_type(), target);
  }

  // double progress = 7;
  if (!(this->progress() <= 0 && this->progress() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(7, this->_internal_progress(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:DAWNProto.TaskResult)
  return target;
}

size_t TaskResult::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:DAWNProto.TaskResult)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated string resultFiles = 3;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(resultfiles_.size());
  for (int i = 0, n = resultfiles_.size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
      resultfiles_.Get(i));
  }

  // repeated .DAWNProto.MissionStatisticMsg statisticMsg = 5;
  total_size += 1UL * this->_internal_statisticmsg_size();
  for (const auto& msg : this->statisticmsg_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // string taskID = 1;
  if (this->taskid().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_taskid());
  }

  // string parentTaskID = 4;
  if (this->parenttaskid().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_parenttaskid());
  }

  // .DAWNProto.StringValue errMsg = 2;
  if (this->has_errmsg()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *errmsg_);
  }

  // double progress = 7;
  if (!(this->progress() <= 0 && this->progress() >= 0)) {
    total_size += 1 + 8;
  }

  // .DAWNProto.TaskResult.TaskResultType type = 6;
  if (this->type() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_type());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void TaskResult::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:DAWNProto.TaskResult)
  GOOGLE_DCHECK_NE(&from, this);
  const TaskResult* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<TaskResult>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:DAWNProto.TaskResult)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:DAWNProto.TaskResult)
    MergeFrom(*source);
  }
}

void TaskResult::MergeFrom(const TaskResult& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:DAWNProto.TaskResult)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  resultfiles_.MergeFrom(from.resultfiles_);
  statisticmsg_.MergeFrom(from.statisticmsg_);
  if (from.taskid().size() > 0) {
    _internal_set_taskid(from._internal_taskid());
  }
  if (from.parenttaskid().size() > 0) {
    _internal_set_parenttaskid(from._internal_parenttaskid());
  }
  if (from.has_errmsg()) {
    _internal_mutable_errmsg()->::DAWNProto::StringValue::MergeFrom(from._internal_errmsg());
  }
  if (!(from.progress() <= 0 && from.progress() >= 0)) {
    _internal_set_progress(from._internal_progress());
  }
  if (from.type() != 0) {
    _internal_set_type(from._internal_type());
  }
}

void TaskResult::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:DAWNProto.TaskResult)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void TaskResult::CopyFrom(const TaskResult& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:DAWNProto.TaskResult)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TaskResult::IsInitialized() const {
  return true;
}

void TaskResult::InternalSwap(TaskResult* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  resultfiles_.InternalSwap(&other->resultfiles_);
  statisticmsg_.InternalSwap(&other->statisticmsg_);
  taskid_.Swap(&other->taskid_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  parenttaskid_.Swap(&other->parenttaskid_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(TaskResult, type_)
      + sizeof(TaskResult::type_)
      - PROTOBUF_FIELD_OFFSET(TaskResult, errmsg_)>(
          reinterpret_cast<char*>(&errmsg_),
          reinterpret_cast<char*>(&other->errmsg_));
}

::PROTOBUF_NAMESPACE_ID::Metadata TaskResult::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

void ReportMsg::InitAsDefaultInstance() {
}
class ReportMsg::_Internal {
 public:
};

ReportMsg::ReportMsg(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:DAWNProto.ReportMsg)
}
ReportMsg::ReportMsg(const ReportMsg& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  msg_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (!from._internal_msg().empty()) {
    msg_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from._internal_msg(),
      GetArena());
  }
  type_ = from.type_;
  // @@protoc_insertion_point(copy_constructor:DAWNProto.ReportMsg)
}

void ReportMsg::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_ReportMsg_DAWNCommon_2eproto.base);
  msg_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  type_ = 0;
}

ReportMsg::~ReportMsg() {
  // @@protoc_insertion_point(destructor:DAWNProto.ReportMsg)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void ReportMsg::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
  msg_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void ReportMsg::ArenaDtor(void* object) {
  ReportMsg* _this = reinterpret_cast< ReportMsg* >(object);
  (void)_this;
}
void ReportMsg::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ReportMsg::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const ReportMsg& ReportMsg::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_ReportMsg_DAWNCommon_2eproto.base);
  return *internal_default_instance();
}


void ReportMsg::Clear() {
// @@protoc_insertion_point(message_clear_start:DAWNProto.ReportMsg)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  msg_.ClearToEmpty(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  type_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ReportMsg::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  ::PROTOBUF_NAMESPACE_ID::Arena* arena = GetArena(); (void)arena;
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // .DAWNProto.ReportMsg.ReportMsgType type = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_type(static_cast<::DAWNProto::ReportMsg_ReportMsgType>(val));
        } else goto handle_unusual;
        continue;
      // string msg = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 18)) {
          auto str = _internal_mutable_msg();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "DAWNProto.ReportMsg.msg"));
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* ReportMsg::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:DAWNProto.ReportMsg)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .DAWNProto.ReportMsg.ReportMsgType type = 1;
  if (this->type() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      1, this->_internal_type(), target);
  }

  // string msg = 2;
  if (this->msg().size() > 0) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_msg().data(), static_cast<int>(this->_internal_msg().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "DAWNProto.ReportMsg.msg");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_msg(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:DAWNProto.ReportMsg)
  return target;
}

size_t ReportMsg::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:DAWNProto.ReportMsg)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string msg = 2;
  if (this->msg().size() > 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_msg());
  }

  // .DAWNProto.ReportMsg.ReportMsgType type = 1;
  if (this->type() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_type());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void ReportMsg::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:DAWNProto.ReportMsg)
  GOOGLE_DCHECK_NE(&from, this);
  const ReportMsg* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<ReportMsg>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:DAWNProto.ReportMsg)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:DAWNProto.ReportMsg)
    MergeFrom(*source);
  }
}

void ReportMsg::MergeFrom(const ReportMsg& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:DAWNProto.ReportMsg)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.msg().size() > 0) {
    _internal_set_msg(from._internal_msg());
  }
  if (from.type() != 0) {
    _internal_set_type(from._internal_type());
  }
}

void ReportMsg::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:DAWNProto.ReportMsg)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ReportMsg::CopyFrom(const ReportMsg& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:DAWNProto.ReportMsg)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ReportMsg::IsInitialized() const {
  return true;
}

void ReportMsg::InternalSwap(ReportMsg* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  msg_.Swap(&other->msg_, &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArena());
  swap(type_, other->type_);
}

::PROTOBUF_NAMESPACE_ID::Metadata ReportMsg::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

void MissionStatisticMsg::InitAsDefaultInstance() {
}
class MissionStatisticMsg::_Internal {
 public:
};

MissionStatisticMsg::MissionStatisticMsg(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:DAWNProto.MissionStatisticMsg)
}
MissionStatisticMsg::MissionStatisticMsg(const MissionStatisticMsg& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&utctime_, &from.utctime_,
    static_cast<size_t>(reinterpret_cast<char*>(&costtime_) -
    reinterpret_cast<char*>(&utctime_)) + sizeof(costtime_));
  // @@protoc_insertion_point(copy_constructor:DAWNProto.MissionStatisticMsg)
}

void MissionStatisticMsg::SharedCtor() {
  ::memset(&utctime_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&costtime_) -
      reinterpret_cast<char*>(&utctime_)) + sizeof(costtime_));
}

MissionStatisticMsg::~MissionStatisticMsg() {
  // @@protoc_insertion_point(destructor:DAWNProto.MissionStatisticMsg)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void MissionStatisticMsg::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
}

void MissionStatisticMsg::ArenaDtor(void* object) {
  MissionStatisticMsg* _this = reinterpret_cast< MissionStatisticMsg* >(object);
  (void)_this;
}
void MissionStatisticMsg::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void MissionStatisticMsg::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const MissionStatisticMsg& MissionStatisticMsg::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_MissionStatisticMsg_DAWNCommon_2eproto.base);
  return *internal_default_instance();
}


void MissionStatisticMsg::Clear() {
// @@protoc_insertion_point(message_clear_start:DAWNProto.MissionStatisticMsg)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&utctime_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&costtime_) -
      reinterpret_cast<char*>(&utctime_)) + sizeof(costtime_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* MissionStatisticMsg::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  ::PROTOBUF_NAMESPACE_ID::Arena* arena = GetArena(); (void)arena;
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // int64 UTCTime = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 8)) {
          utctime_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // .DAWNProto.MissionStatisticMsg.EventType Type = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          ::PROTOBUF_NAMESPACE_ID::uint64 val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_type(static_cast<::DAWNProto::MissionStatisticMsg_EventType>(val));
        } else goto handle_unusual;
        continue;
      // int64 IdentifierNumber = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 24)) {
          identifiernumber_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // int64 IdentifierNumber2 = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 32)) {
          identifiernumber2_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // int32 AgentID = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 40)) {
          agentid_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      // double StartTime = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 49)) {
          starttime_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else goto handle_unusual;
        continue;
      // double CostTime = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 57)) {
          costtime_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* MissionStatisticMsg::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:DAWNProto.MissionStatisticMsg)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 UTCTime = 1;
  if (this->utctime() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(1, this->_internal_utctime(), target);
  }

  // .DAWNProto.MissionStatisticMsg.EventType Type = 2;
  if (this->type() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      2, this->_internal_type(), target);
  }

  // int64 IdentifierNumber = 3;
  if (this->identifiernumber() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(3, this->_internal_identifiernumber(), target);
  }

  // int64 IdentifierNumber2 = 4;
  if (this->identifiernumber2() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(4, this->_internal_identifiernumber2(), target);
  }

  // int32 AgentID = 5;
  if (this->agentid() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(5, this->_internal_agentid(), target);
  }

  // double StartTime = 6;
  if (!(this->starttime() <= 0 && this->starttime() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(6, this->_internal_starttime(), target);
  }

  // double CostTime = 7;
  if (!(this->costtime() <= 0 && this->costtime() >= 0)) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(7, this->_internal_costtime(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:DAWNProto.MissionStatisticMsg)
  return target;
}

size_t MissionStatisticMsg::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:DAWNProto.MissionStatisticMsg)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // int64 UTCTime = 1;
  if (this->utctime() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64Size(
        this->_internal_utctime());
  }

  // int64 IdentifierNumber = 3;
  if (this->identifiernumber() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64Size(
        this->_internal_identifiernumber());
  }

  // .DAWNProto.MissionStatisticMsg.EventType Type = 2;
  if (this->type() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_type());
  }

  // int32 AgentID = 5;
  if (this->agentid() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
        this->_internal_agentid());
  }

  // int64 IdentifierNumber2 = 4;
  if (this->identifiernumber2() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64Size(
        this->_internal_identifiernumber2());
  }

  // double StartTime = 6;
  if (!(this->starttime() <= 0 && this->starttime() >= 0)) {
    total_size += 1 + 8;
  }

  // double CostTime = 7;
  if (!(this->costtime() <= 0 && this->costtime() >= 0)) {
    total_size += 1 + 8;
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void MissionStatisticMsg::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:DAWNProto.MissionStatisticMsg)
  GOOGLE_DCHECK_NE(&from, this);
  const MissionStatisticMsg* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<MissionStatisticMsg>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:DAWNProto.MissionStatisticMsg)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:DAWNProto.MissionStatisticMsg)
    MergeFrom(*source);
  }
}

void MissionStatisticMsg::MergeFrom(const MissionStatisticMsg& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:DAWNProto.MissionStatisticMsg)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.utctime() != 0) {
    _internal_set_utctime(from._internal_utctime());
  }
  if (from.identifiernumber() != 0) {
    _internal_set_identifiernumber(from._internal_identifiernumber());
  }
  if (from.type() != 0) {
    _internal_set_type(from._internal_type());
  }
  if (from.agentid() != 0) {
    _internal_set_agentid(from._internal_agentid());
  }
  if (from.identifiernumber2() != 0) {
    _internal_set_identifiernumber2(from._internal_identifiernumber2());
  }
  if (!(from.starttime() <= 0 && from.starttime() >= 0)) {
    _internal_set_starttime(from._internal_starttime());
  }
  if (!(from.costtime() <= 0 && from.costtime() >= 0)) {
    _internal_set_costtime(from._internal_costtime());
  }
}

void MissionStatisticMsg::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:DAWNProto.MissionStatisticMsg)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MissionStatisticMsg::CopyFrom(const MissionStatisticMsg& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:DAWNProto.MissionStatisticMsg)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool MissionStatisticMsg::IsInitialized() const {
  return true;
}

void MissionStatisticMsg::InternalSwap(MissionStatisticMsg* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(MissionStatisticMsg, costtime_)
      + sizeof(MissionStatisticMsg::costtime_)
      - PROTOBUF_FIELD_OFFSET(MissionStatisticMsg, utctime_)>(
          reinterpret_cast<char*>(&utctime_),
          reinterpret_cast<char*>(&other->utctime_));
}

::PROTOBUF_NAMESPACE_ID::Metadata MissionStatisticMsg::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

void MissionStatisticMsgArray::InitAsDefaultInstance() {
}
class MissionStatisticMsgArray::_Internal {
 public:
};

MissionStatisticMsgArray::MissionStatisticMsgArray(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena),
  msgarray_(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:DAWNProto.MissionStatisticMsgArray)
}
MissionStatisticMsgArray::MissionStatisticMsgArray(const MissionStatisticMsgArray& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      msgarray_(from.msgarray_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  localutctime_ = from.localutctime_;
  // @@protoc_insertion_point(copy_constructor:DAWNProto.MissionStatisticMsgArray)
}

void MissionStatisticMsgArray::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_MissionStatisticMsgArray_DAWNCommon_2eproto.base);
  localutctime_ = PROTOBUF_LONGLONG(0);
}

MissionStatisticMsgArray::~MissionStatisticMsgArray() {
  // @@protoc_insertion_point(destructor:DAWNProto.MissionStatisticMsgArray)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void MissionStatisticMsgArray::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
}

void MissionStatisticMsgArray::ArenaDtor(void* object) {
  MissionStatisticMsgArray* _this = reinterpret_cast< MissionStatisticMsgArray* >(object);
  (void)_this;
}
void MissionStatisticMsgArray::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void MissionStatisticMsgArray::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const MissionStatisticMsgArray& MissionStatisticMsgArray::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_MissionStatisticMsgArray_DAWNCommon_2eproto.base);
  return *internal_default_instance();
}


void MissionStatisticMsgArray::Clear() {
// @@protoc_insertion_point(message_clear_start:DAWNProto.MissionStatisticMsgArray)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  msgarray_.Clear();
  localutctime_ = PROTOBUF_LONGLONG(0);
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* MissionStatisticMsgArray::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  ::PROTOBUF_NAMESPACE_ID::Arena* arena = GetArena(); (void)arena;
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // repeated .DAWNProto.MissionStatisticMsg MsgArray = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_msgarray(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else goto handle_unusual;
        continue;
      // int64 LocalUTCTime = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 16)) {
          localutctime_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* MissionStatisticMsgArray::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:DAWNProto.MissionStatisticMsgArray)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .DAWNProto.MissionStatisticMsg MsgArray = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_msgarray_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, this->_internal_msgarray(i), target, stream);
  }

  // int64 LocalUTCTime = 2;
  if (this->localutctime() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(2, this->_internal_localutctime(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:DAWNProto.MissionStatisticMsgArray)
  return target;
}

size_t MissionStatisticMsgArray::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:DAWNProto.MissionStatisticMsgArray)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .DAWNProto.MissionStatisticMsg MsgArray = 1;
  total_size += 1UL * this->_internal_msgarray_size();
  for (const auto& msg : this->msgarray_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // int64 LocalUTCTime = 2;
  if (this->localutctime() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64Size(
        this->_internal_localutctime());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void MissionStatisticMsgArray::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:DAWNProto.MissionStatisticMsgArray)
  GOOGLE_DCHECK_NE(&from, this);
  const MissionStatisticMsgArray* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<MissionStatisticMsgArray>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:DAWNProto.MissionStatisticMsgArray)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:DAWNProto.MissionStatisticMsgArray)
    MergeFrom(*source);
  }
}

void MissionStatisticMsgArray::MergeFrom(const MissionStatisticMsgArray& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:DAWNProto.MissionStatisticMsgArray)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  msgarray_.MergeFrom(from.msgarray_);
  if (from.localutctime() != 0) {
    _internal_set_localutctime(from._internal_localutctime());
  }
}

void MissionStatisticMsgArray::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:DAWNProto.MissionStatisticMsgArray)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MissionStatisticMsgArray::CopyFrom(const MissionStatisticMsgArray& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:DAWNProto.MissionStatisticMsgArray)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool MissionStatisticMsgArray::IsInitialized() const {
  return true;
}

void MissionStatisticMsgArray::InternalSwap(MissionStatisticMsgArray* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  msgarray_.InternalSwap(&other->msgarray_);
  swap(localutctime_, other->localutctime_);
}

::PROTOBUF_NAMESPACE_ID::Metadata MissionStatisticMsgArray::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

void MissionStatisticMsgReq::InitAsDefaultInstance() {
}
class MissionStatisticMsgReq::_Internal {
 public:
};

MissionStatisticMsgReq::MissionStatisticMsgReq(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:DAWNProto.MissionStatisticMsgReq)
}
MissionStatisticMsgReq::MissionStatisticMsgReq(const MissionStatisticMsgReq& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:DAWNProto.MissionStatisticMsgReq)
}

void MissionStatisticMsgReq::SharedCtor() {
}

MissionStatisticMsgReq::~MissionStatisticMsgReq() {
  // @@protoc_insertion_point(destructor:DAWNProto.MissionStatisticMsgReq)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void MissionStatisticMsgReq::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
}

void MissionStatisticMsgReq::ArenaDtor(void* object) {
  MissionStatisticMsgReq* _this = reinterpret_cast< MissionStatisticMsgReq* >(object);
  (void)_this;
}
void MissionStatisticMsgReq::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void MissionStatisticMsgReq::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const MissionStatisticMsgReq& MissionStatisticMsgReq::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_MissionStatisticMsgReq_DAWNCommon_2eproto.base);
  return *internal_default_instance();
}


void MissionStatisticMsgReq::Clear() {
// @@protoc_insertion_point(message_clear_start:DAWNProto.MissionStatisticMsgReq)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* MissionStatisticMsgReq::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  ::PROTOBUF_NAMESPACE_ID::Arena* arena = GetArena(); (void)arena;
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* MissionStatisticMsgReq::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:DAWNProto.MissionStatisticMsgReq)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:DAWNProto.MissionStatisticMsgReq)
  return target;
}

size_t MissionStatisticMsgReq::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:DAWNProto.MissionStatisticMsgReq)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void MissionStatisticMsgReq::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:DAWNProto.MissionStatisticMsgReq)
  GOOGLE_DCHECK_NE(&from, this);
  const MissionStatisticMsgReq* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<MissionStatisticMsgReq>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:DAWNProto.MissionStatisticMsgReq)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:DAWNProto.MissionStatisticMsgReq)
    MergeFrom(*source);
  }
}

void MissionStatisticMsgReq::MergeFrom(const MissionStatisticMsgReq& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:DAWNProto.MissionStatisticMsgReq)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

}

void MissionStatisticMsgReq::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:DAWNProto.MissionStatisticMsgReq)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void MissionStatisticMsgReq::CopyFrom(const MissionStatisticMsgReq& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:DAWNProto.MissionStatisticMsgReq)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool MissionStatisticMsgReq::IsInitialized() const {
  return true;
}

void MissionStatisticMsgReq::InternalSwap(MissionStatisticMsgReq* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
}

::PROTOBUF_NAMESPACE_ID::Metadata MissionStatisticMsgReq::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

void PerfStatsMsgArray::InitAsDefaultInstance() {
}
class PerfStatsMsgArray::_Internal {
 public:
};

PerfStatsMsgArray::PerfStatsMsgArray(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena),
  perfstatsmsgs_(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:DAWNProto.PerfStatsMsgArray)
}
PerfStatsMsgArray::PerfStatsMsgArray(const PerfStatsMsgArray& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      perfstatsmsgs_(from.perfstatsmsgs_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:DAWNProto.PerfStatsMsgArray)
}

void PerfStatsMsgArray::SharedCtor() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&scc_info_PerfStatsMsgArray_DAWNCommon_2eproto.base);
}

PerfStatsMsgArray::~PerfStatsMsgArray() {
  // @@protoc_insertion_point(destructor:DAWNProto.PerfStatsMsgArray)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void PerfStatsMsgArray::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
}

void PerfStatsMsgArray::ArenaDtor(void* object) {
  PerfStatsMsgArray* _this = reinterpret_cast< PerfStatsMsgArray* >(object);
  (void)_this;
}
void PerfStatsMsgArray::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void PerfStatsMsgArray::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const PerfStatsMsgArray& PerfStatsMsgArray::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_PerfStatsMsgArray_DAWNCommon_2eproto.base);
  return *internal_default_instance();
}


void PerfStatsMsgArray::Clear() {
// @@protoc_insertion_point(message_clear_start:DAWNProto.PerfStatsMsgArray)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  perfstatsmsgs_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* PerfStatsMsgArray::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  ::PROTOBUF_NAMESPACE_ID::Arena* arena = GetArena(); (void)arena;
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
    switch (tag >> 3) {
      // repeated string perfStatsMsgs = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<::PROTOBUF_NAMESPACE_ID::uint8>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            auto str = _internal_add_perfstatsmsgs();
            ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
            CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "DAWNProto.PerfStatsMsgArray.perfStatsMsgs"));
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else goto handle_unusual;
        continue;
      default: {
      handle_unusual:
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
      }
    }  // switch
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* PerfStatsMsgArray::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:DAWNProto.PerfStatsMsgArray)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated string perfStatsMsgs = 1;
  for (int i = 0, n = this->_internal_perfstatsmsgs_size(); i < n; i++) {
    const auto& s = this->_internal_perfstatsmsgs(i);
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      s.data(), static_cast<int>(s.length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "DAWNProto.PerfStatsMsgArray.perfStatsMsgs");
    target = stream->WriteString(1, s, target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:DAWNProto.PerfStatsMsgArray)
  return target;
}

size_t PerfStatsMsgArray::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:DAWNProto.PerfStatsMsgArray)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated string perfStatsMsgs = 1;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(perfstatsmsgs_.size());
  for (int i = 0, n = perfstatsmsgs_.size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
      perfstatsmsgs_.Get(i));
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void PerfStatsMsgArray::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:DAWNProto.PerfStatsMsgArray)
  GOOGLE_DCHECK_NE(&from, this);
  const PerfStatsMsgArray* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<PerfStatsMsgArray>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:DAWNProto.PerfStatsMsgArray)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:DAWNProto.PerfStatsMsgArray)
    MergeFrom(*source);
  }
}

void PerfStatsMsgArray::MergeFrom(const PerfStatsMsgArray& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:DAWNProto.PerfStatsMsgArray)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  perfstatsmsgs_.MergeFrom(from.perfstatsmsgs_);
}

void PerfStatsMsgArray::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:DAWNProto.PerfStatsMsgArray)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void PerfStatsMsgArray::CopyFrom(const PerfStatsMsgArray& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:DAWNProto.PerfStatsMsgArray)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool PerfStatsMsgArray::IsInitialized() const {
  return true;
}

void PerfStatsMsgArray::InternalSwap(PerfStatsMsgArray* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
  perfstatsmsgs_.InternalSwap(&other->perfstatsmsgs_);
}

::PROTOBUF_NAMESPACE_ID::Metadata PerfStatsMsgArray::GetMetadata() const {
  return GetMetadataStatic();
}


// ===================================================================

void PerfStatsMsgReq::InitAsDefaultInstance() {
}
class PerfStatsMsgReq::_Internal {
 public:
};

PerfStatsMsgReq::PerfStatsMsgReq(::PROTOBUF_NAMESPACE_ID::Arena* arena)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena) {
  SharedCtor();
  RegisterArenaDtor(arena);
  // @@protoc_insertion_point(arena_constructor:DAWNProto.PerfStatsMsgReq)
}
PerfStatsMsgReq::PerfStatsMsgReq(const PerfStatsMsgReq& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:DAWNProto.PerfStatsMsgReq)
}

void PerfStatsMsgReq::SharedCtor() {
}

PerfStatsMsgReq::~PerfStatsMsgReq() {
  // @@protoc_insertion_point(destructor:DAWNProto.PerfStatsMsgReq)
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

void PerfStatsMsgReq::SharedDtor() {
  GOOGLE_DCHECK(GetArena() == nullptr);
}

void PerfStatsMsgReq::ArenaDtor(void* object) {
  PerfStatsMsgReq* _this = reinterpret_cast< PerfStatsMsgReq* >(object);
  (void)_this;
}
void PerfStatsMsgReq::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void PerfStatsMsgReq::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}
const PerfStatsMsgReq& PerfStatsMsgReq::default_instance() {
  ::PROTOBUF_NAMESPACE_ID::internal::InitSCC(&::scc_info_PerfStatsMsgReq_DAWNCommon_2eproto.base);
  return *internal_default_instance();
}


void PerfStatsMsgReq::Clear() {
// @@protoc_insertion_point(message_clear_start:DAWNProto.PerfStatsMsgReq)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* PerfStatsMsgReq::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  ::PROTOBUF_NAMESPACE_ID::Arena* arena = GetArena(); (void)arena;
  while (!ctx->Done(&ptr)) {
    ::PROTOBUF_NAMESPACE_ID::uint32 tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    CHK_(ptr);
        if ((tag & 7) == 4 || tag == 0) {
          ctx->SetLastTag(tag);
          goto success;
        }
        ptr = UnknownFieldParse(tag,
            _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
            ptr, ctx);
        CHK_(ptr != nullptr);
        continue;
  }  // while
success:
  return ptr;
failure:
  ptr = nullptr;
  goto success;
#undef CHK_
}

::PROTOBUF_NAMESPACE_ID::uint8* PerfStatsMsgReq::_InternalSerialize(
    ::PROTOBUF_NAMESPACE_ID::uint8* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:DAWNProto.PerfStatsMsgReq)
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:DAWNProto.PerfStatsMsgReq)
  return target;
}

size_t PerfStatsMsgReq::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:DAWNProto.PerfStatsMsgReq)
  size_t total_size = 0;

  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    return ::PROTOBUF_NAMESPACE_ID::internal::ComputeUnknownFieldsSize(
        _internal_metadata_, total_size, &_cached_size_);
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void PerfStatsMsgReq::MergeFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:DAWNProto.PerfStatsMsgReq)
  GOOGLE_DCHECK_NE(&from, this);
  const PerfStatsMsgReq* source =
      ::PROTOBUF_NAMESPACE_ID::DynamicCastToGenerated<PerfStatsMsgReq>(
          &from);
  if (source == nullptr) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:DAWNProto.PerfStatsMsgReq)
    ::PROTOBUF_NAMESPACE_ID::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:DAWNProto.PerfStatsMsgReq)
    MergeFrom(*source);
  }
}

void PerfStatsMsgReq::MergeFrom(const PerfStatsMsgReq& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:DAWNProto.PerfStatsMsgReq)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

}

void PerfStatsMsgReq::CopyFrom(const ::PROTOBUF_NAMESPACE_ID::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:DAWNProto.PerfStatsMsgReq)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void PerfStatsMsgReq::CopyFrom(const PerfStatsMsgReq& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:DAWNProto.PerfStatsMsgReq)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool PerfStatsMsgReq::IsInitialized() const {
  return true;
}

void PerfStatsMsgReq::InternalSwap(PerfStatsMsgReq* other) {
  using std::swap;
  _internal_metadata_.Swap<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(&other->_internal_metadata_);
}

::PROTOBUF_NAMESPACE_ID::Metadata PerfStatsMsgReq::GetMetadata() const {
  return GetMetadataStatic();
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace DAWNProto
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::DAWNProto::StringValue* Arena::CreateMaybeMessage< ::DAWNProto::StringValue >(Arena* arena) {
  return Arena::CreateMessageInternal< ::DAWNProto::StringValue >(arena);
}
template<> PROTOBUF_NOINLINE ::DAWNProto::GeneralAck* Arena::CreateMaybeMessage< ::DAWNProto::GeneralAck >(Arena* arena) {
  return Arena::CreateMessageInternal< ::DAWNProto::GeneralAck >(arena);
}
template<> PROTOBUF_NOINLINE ::DAWNProto::KeyValuePair* Arena::CreateMaybeMessage< ::DAWNProto::KeyValuePair >(Arena* arena) {
  return Arena::CreateMessageInternal< ::DAWNProto::KeyValuePair >(arena);
}
template<> PROTOBUF_NOINLINE ::DAWNProto::UserInfo* Arena::CreateMaybeMessage< ::DAWNProto::UserInfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::DAWNProto::UserInfo >(arena);
}
template<> PROTOBUF_NOINLINE ::DAWNProto::CalculationSchemeInfo* Arena::CreateMaybeMessage< ::DAWNProto::CalculationSchemeInfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::DAWNProto::CalculationSchemeInfo >(arena);
}
template<> PROTOBUF_NOINLINE ::DAWNProto::JobBasicDesc* Arena::CreateMaybeMessage< ::DAWNProto::JobBasicDesc >(Arena* arena) {
  return Arena::CreateMessageInternal< ::DAWNProto::JobBasicDesc >(arena);
}
template<> PROTOBUF_NOINLINE ::DAWNProto::JobFullDesc* Arena::CreateMaybeMessage< ::DAWNProto::JobFullDesc >(Arena* arena) {
  return Arena::CreateMessageInternal< ::DAWNProto::JobFullDesc >(arena);
}
template<> PROTOBUF_NOINLINE ::DAWNProto::PartitionDesc* Arena::CreateMaybeMessage< ::DAWNProto::PartitionDesc >(Arena* arena) {
  return Arena::CreateMessageInternal< ::DAWNProto::PartitionDesc >(arena);
}
template<> PROTOBUF_NOINLINE ::DAWNProto::TaskDesc* Arena::CreateMaybeMessage< ::DAWNProto::TaskDesc >(Arena* arena) {
  return Arena::CreateMessageInternal< ::DAWNProto::TaskDesc >(arena);
}
template<> PROTOBUF_NOINLINE ::DAWNProto::SubTaskDesc* Arena::CreateMaybeMessage< ::DAWNProto::SubTaskDesc >(Arena* arena) {
  return Arena::CreateMessageInternal< ::DAWNProto::SubTaskDesc >(arena);
}
template<> PROTOBUF_NOINLINE ::DAWNProto::BeginMissionReq* Arena::CreateMaybeMessage< ::DAWNProto::BeginMissionReq >(Arena* arena) {
  return Arena::CreateMessageInternal< ::DAWNProto::BeginMissionReq >(arena);
}
template<> PROTOBUF_NOINLINE ::DAWNProto::EndMissionReq* Arena::CreateMaybeMessage< ::DAWNProto::EndMissionReq >(Arena* arena) {
  return Arena::CreateMessageInternal< ::DAWNProto::EndMissionReq >(arena);
}
template<> PROTOBUF_NOINLINE ::DAWNProto::AbortMissionReq* Arena::CreateMaybeMessage< ::DAWNProto::AbortMissionReq >(Arena* arena) {
  return Arena::CreateMessageInternal< ::DAWNProto::AbortMissionReq >(arena);
}
template<> PROTOBUF_NOINLINE ::DAWNProto::TaskResult* Arena::CreateMaybeMessage< ::DAWNProto::TaskResult >(Arena* arena) {
  return Arena::CreateMessageInternal< ::DAWNProto::TaskResult >(arena);
}
template<> PROTOBUF_NOINLINE ::DAWNProto::ReportMsg* Arena::CreateMaybeMessage< ::DAWNProto::ReportMsg >(Arena* arena) {
  return Arena::CreateMessageInternal< ::DAWNProto::ReportMsg >(arena);
}
template<> PROTOBUF_NOINLINE ::DAWNProto::MissionStatisticMsg* Arena::CreateMaybeMessage< ::DAWNProto::MissionStatisticMsg >(Arena* arena) {
  return Arena::CreateMessageInternal< ::DAWNProto::MissionStatisticMsg >(arena);
}
template<> PROTOBUF_NOINLINE ::DAWNProto::MissionStatisticMsgArray* Arena::CreateMaybeMessage< ::DAWNProto::MissionStatisticMsgArray >(Arena* arena) {
  return Arena::CreateMessageInternal< ::DAWNProto::MissionStatisticMsgArray >(arena);
}
template<> PROTOBUF_NOINLINE ::DAWNProto::MissionStatisticMsgReq* Arena::CreateMaybeMessage< ::DAWNProto::MissionStatisticMsgReq >(Arena* arena) {
  return Arena::CreateMessageInternal< ::DAWNProto::MissionStatisticMsgReq >(arena);
}
template<> PROTOBUF_NOINLINE ::DAWNProto::PerfStatsMsgArray* Arena::CreateMaybeMessage< ::DAWNProto::PerfStatsMsgArray >(Arena* arena) {
  return Arena::CreateMessageInternal< ::DAWNProto::PerfStatsMsgArray >(arena);
}
template<> PROTOBUF_NOINLINE ::DAWNProto::PerfStatsMsgReq* Arena::CreateMaybeMessage< ::DAWNProto::PerfStatsMsgReq >(Arena* arena) {
  return Arena::CreateMessageInternal< ::DAWNProto::PerfStatsMsgReq >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
