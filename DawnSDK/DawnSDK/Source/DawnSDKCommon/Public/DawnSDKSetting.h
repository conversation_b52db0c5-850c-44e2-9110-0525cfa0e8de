#pragma once

#include "CoreMinimal.h"
#include "UObject/Object.h"
#include "UObject/UObjectGlobals.h"
#include "DawnSDKSetting.generated.h"

DECLARE_DELEGATE_OneParam(UDawnSDKSettingOnPropertyChangeDelegate, FProperty*);

UENUM()
enum EDawnSDKCalculationScheme
{
	/*
	* Only use remote services to participate in mission
	*/
	ONLY_REMOTE = 0 UMETA(DisplayName = "Remote Machines Only"),
	/*
	* Mix remote and local services to participate in mission
	*/
	MIX = 1 UMETA(DisplayName = "Mix Remote and Local Machines"),
	/*
	* Only invoke a local process to participate in mission
	*/
	ONLY_LOCAL = 2 UMETA(DisplayName = "Local Machine Only"),
};

UCLASS(config = DawnSDK, ConfigDoNotCheckDefaults)
class DAWNSDKCOMMON_API UDawnSDKSetting : public UObject {
    // GENERATED_UCLASS_BODY()
    GENERATED_BODY()

 public:
	UPROPERTY(config, EditAnywhere, Category = IPAddress, meta = (ShowOnlyInnerProperties))
	TEnumAsByte<enum EDawnSDKCalculationScheme> CalculationScheme = EDawnSDKCalculationScheme::ONLY_LOCAL;
	/**
	 * The IP Address of Coordinator.
	 */
    UPROPERTY(config, EditAnywhere, Category = IPAddress, DisplayName = "Coordinator IP Address", meta = (ShowOnlyInnerProperties, EditCondition = /*"CalculationScheme != EDawnSDKCalculationScheme::ONLY_LOCAL"*/"true", EditConditionHides))
    FString CoordinatorIPAddress;
	/**
	 * The IP Address of Deamon, which should be the default value "127.0.0.1:5009" in almost all cases.
	 */
    UPROPERTY(config, VisibleAnywhere, Category = IPAddress, DisplayName = "Deamon IP Address", meta = (ShowOnlyInnerProperties, EditCondition = /*"CalculationScheme != EDawnSDKCalculationScheme::ONLY_LOCAL"*/"false", EditConditionHides))
    FString ClientLogIPAddress = "127.0.0.1:5009";
	/**
	 * In some cases, the Client cannot directly establish communication with the remote Agent,
	 * but needs to be intermediary through a Proxy Server.
	 * Please turn this option on if needed.
	 */
	UPROPERTY(config, EditAnywhere, Category = IPAddress, meta = (ShowOnlyInnerProperties, EditCondition = "CalculationScheme != EDawnSDKCalculationScheme::ONLY_LOCAL", EditConditionHides))
	bool IfRequireProxy = false;
	UPROPERTY(config, EditAnywhere, Category = IPAddress, meta = (ShowOnlyInnerProperties, EditCondition = "IfRequireProxy && CalculationScheme != EDawnSDKCalculationScheme::ONLY_LOCAL", EditConditionHides))
	FString ProxyAddress = "127.0.0.1:50051";
    UPROPERTY(config, EditAnywhere, Category = UserInfo, meta = (ShowOnlyInnerProperties, EditCondition = /*"CalculationScheme != EDawnSDKCalculationScheme::ONLY_LOCAL"*/"false", EditConditionHides))
    FString ProjectId;
    UPROPERTY(config, EditAnywhere, Category = UserInfo, meta = (ShowOnlyInnerProperties, EditCondition = /*"CalculationScheme != EDawnSDKCalculationScheme::ONLY_LOCAL"*/"false", EditConditionHides))
    FString UserId;
    UPROPERTY(config, EditAnywhere, Category = UserInfo, meta = (ShowOnlyInnerProperties, EditCondition = /*"CalculationScheme != EDawnSDKCalculationScheme::ONLY_LOCAL"*/"true", EditConditionHides))
    FString UserToken;
	/*
	 Dawn supports dynamic loading different baker versions in the cloud.
	 - Just fill in the tag you want to use, or the tag that Dawn has prepared for you.
	 Dawn支持云端动态加载不同的烘焙器版本
	 - 在此处填写你想要用的版本的tag，或者Dawn官方为您预先定制并准备好的tag即可
	*/
    UPROPERTY(config, EditAnywhere, Category = DawnBaker, DisplayName = "Version Tag", meta = (ShowOnlyInnerProperties))
    FString BranchTag = "default";
	/*
	 The number of Nodes you want during baking.
	 - This is actually corresponding to the item in DawnDeamon's config with the same name.
	 - After launching DawnDeamon.exe, it will be meaningless to make any further changes here.
	 - In this case, please modify the value directly in the "Options" window in DawnDeamon.exe.
	 烘焙时希望申请到的算力数量
	 - 此配置项实际上对应于DawnDeamon中的同名配置项
	 - 若你已经启动了DawnDeamon.exe，再在此处进行修改是没有意义的
	 - 这种情况下，请直接在DawnDeamon.exe中的Options窗口里进行修改
	*/
	UPROPERTY(AdvancedDisplay, EditAnywhere, Category = DawnBaker, DisplayName = "Desired Agents Num", meta = (UIMin = "1", UIMax = "80", ClampMin = "1", ClampMax = "80", ShowOnlyInnerProperties, EditCondition = "CalculationScheme != EDawnSDKCalculationScheme::ONLY_LOCAL && bIsBelowSettingsAvailable", EditConditionHides))
	int32 DesiredNumAgents = 1;
	/*
	 优先级调度下，本机的烘焙任务的优先度。
	 此值会影响算力池的抢占与排队，请与其他同学协商后调整此值。
	 - 此配置项实际上对应于DawnDeamon中的同名配置项
	 - 若你已经启动了DawnDeamon.exe，再在此处进行修改是没有意义的
	 - 这种情况下，请直接在DawnDeamon.exe中的Options窗口里进行修改
	*/
	UPROPERTY(AdvancedDisplay, EditAnywhere, Category = DawnBaker, meta = (UIMin = "1", UIMax = "128", ClampMin = "1", ClampMax = "128", ShowOnlyInnerProperties, EditCondition = "CalculationScheme != EDawnSDKCalculationScheme::ONLY_LOCAL && bIsBelowSettingsAvailable", EditConditionHides))
	int32 JobPriority = 1;
	/*
	 是否在算力节点不够的时候等待空闲算力的出现。
	 勾选此项时，在发起烘焙时若算力池中的空闲节点数小于上面设置的DesiredNumAgents，则会无限等待直到拿到（等待时可以手动取消）
	 不勾选此项时，在算力不足的情况下会立刻认为任务失败。
	 - 此配置项实际上对应于DawnDeamon中的同名配置项
	 - 若你已经启动了DawnDeamon.exe，再在此处进行修改是没有意义的
	 - 这种情况下，请直接在DawnDeamon.exe中的Options窗口里进行修改
	*/
	UPROPERTY(AdvancedDisplay, EditAnywhere, Category = DawnBaker, DisplayName = "Waiting when Agents insufficient", meta = (ShowOnlyInnerProperties, EditCondition = "CalculationScheme != EDawnSDKCalculationScheme::ONLY_LOCAL && bIsBelowSettingsAvailable", EditConditionHides))
	bool bWaitForAgentsAvailable = true;
	/*
	 此项并不是一个配置，而是在发现UE没法修改上面三个配置项的时候（比如没有文件访问权限），用于将上面三个配置项隐藏起来
	*/
	UPROPERTY()
	bool bIsBelowSettingsAvailable = true;


#if WITH_EDITOR
private:
	TArray<UDawnSDKSettingOnPropertyChangeDelegate> OnPropertyChangeDelegatesList;

public:
	void AddListenerPostEditChange(const UDawnSDKSettingOnPropertyChangeDelegate& OnPropertyChangeDelegate)
	{
		OnPropertyChangeDelegatesList.Emplace(OnPropertyChangeDelegate);
	}

	void ClearListenerPostEditChange()
	{
		OnPropertyChangeDelegatesList.Empty();
	}

	void PostEditChangeProperty(struct FPropertyChangedEvent& PropertyChangedEvent) override
	{
		Super::PostEditChangeProperty(PropertyChangedEvent);
		
		for (auto& dele : OnPropertyChangeDelegatesList)
		{
			dele.ExecuteIfBound(PropertyChangedEvent.Property);
		}
	}
#endif
};