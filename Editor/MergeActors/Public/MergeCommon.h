// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "Engine/StaticMeshActor.h"
#include "Components/StaticMeshComponent.h"
#include "AssetRegistry/AssetRegistryModule.h"
#include "ObjectTools.h"
#include "FileHelpers.h"
#include "Engine/Selection.h"
#include "WorldPartition/WorldPartition.h"
#include "LevelEditor.h"
#include "ISceneOutliner.h"
#include "Kismet/KismetMathLibrary.h"
#include "RenderingThread.h"
#include "ActorFolder.h"
#include "AssetToolsModule.h"

namespace M2_MergeActorSpace
{
	enum MergeOrBatchType
	{
		Merged,
		Batched
	};

	enum class ECollisionType : uint8
	{
		NoCollision,
		Default
	};

	struct ActorAndMeshInfo
	{
		AStaticMeshActor* Actor = nullptr;
		UStaticMesh* OriginalMesh = nullptr;
		UStaticMesh* TempMesh = nullptr; //为了进行生成LightmapUV时，不更改Actor的原始Mesh而做的临时Mesh
	};

	static FName M2_MergedActorTag = FName("SMActor"); //用于标记合并后的Actor
	static FName M2_BatchActorTag = FName("ISMActor"); //用于标记Batch后的Actor
	static FName M2_SourceActorsTag = FName("M2_SourceActors"); //给合并或Batch后的Actor 记录源Actor的包名列表

	static bool IsMergedActor(AActor* InActor)
	{
		if (!InActor || !InActor->Tags.Num())
		{
			return false;
		}

		return InActor->Tags.Contains(M2_MergedActorTag);
	}

	static bool IsBatchedActor(AActor* InActor)
	{
		if (!InActor || !InActor->Tags.Num())
		{
			return false;
		}

		return InActor->Tags.Contains(M2_BatchActorTag);
	}

	static TArray<FString> GenerateMergedTag(TSet<AActor*>& InActors)
	{
		TArray<FString> AllTags;
		if (InActors.Num() == 0) return AllTags;

		const FString Prefix = M2_SourceActorsTag.ToString() + "&";
		FString CurrentItem = Prefix;
		int32 CurrentItemLength = Prefix.Len();

		//因为FName允许的最大长度为NAME_SIZE，所以Tag要拆分存储
		for (AActor* Actor : InActors)
		{
			if (!Actor)
			{
				continue;
			}

			FString ActorPathName = Actor->GetPathName();

			// 计算添加当前元素需要的长度：当前元素长度 + 分号（如果不是第一个元素）
			int32 RequiredLength = ActorPathName.Len() + (CurrentItemLength > Prefix.Len() ? 1 : 0);

			if (CurrentItemLength + RequiredLength > NAME_SIZE)
			{
				// 将当前字符串存入 并重置
				AllTags.Add(CurrentItem);
				CurrentItem = Prefix + ActorPathName;
				CurrentItemLength = Prefix.Len() + ActorPathName.Len();
			}
			else
			{
				if (CurrentItemLength > Prefix.Len())
				{
					CurrentItem += TEXT(";");
					CurrentItemLength++;
				}
				// 添加当前元素
				CurrentItem += ActorPathName;
				CurrentItemLength += ActorPathName.Len();
			}
		}

		// 添加最后一个构建的字符串
		if (CurrentItemLength > Prefix.Len())
		{
			AllTags.Add(CurrentItem);
		}

		return AllTags;
	}

	static bool AnalysisMergedOrBatchedTag(MergeOrBatchType Type, AActor* InActor, TSet<AActor*>& OutActors)
	{
		if (Type == MergeOrBatchType::Merged)
		{
			if (!IsMergedActor(InActor))
			{
				return false;
			}
		}
		else
		{
			if (!IsBatchedActor(InActor))
			{
				return false;
			}
		}

		OutActors.Empty();

		for (FName& Tag : InActor->Tags)
		{
			FString sTag = Tag.ToString();
			if (sTag.IsEmpty())
			{
				continue;
			}

			if (sTag.StartsWith(M2_SourceActorsTag.ToString(), ESearchCase::Type::CaseSensitive))
			{
				TArray<FString> Paths;
				sTag.ParseIntoArray(Paths, TEXT("&"), true);

				if (Paths.Num() <= 1)
				{
					continue;
				}

				TArray<FString> ActorPaths;
				(Paths[1]).ParseIntoArray(ActorPaths, TEXT(";"), true);

				for (FString& TargetPath : ActorPaths)
				{
					if(TargetPath.IsEmpty()) continue;

					AActor* FoundActor = Cast<AActor>(StaticFindObject(AActor::StaticClass(), nullptr, *TargetPath));
					if (FoundActor)
					{
						OutActors.Add(FoundActor);
					}
				}
			}
		}

		return true;
	}

	static void ChangeActorsCollision(AStaticMeshActor* MeshActor, M2_MergeActorSpace::ECollisionType Type)
	{
		if (!MeshActor)
		{
			return;
		}

		if (Type == M2_MergeActorSpace::ECollisionType::Default)
		{
			TArray<UPrimitiveComponent*> PrimComponents;
			MeshActor->GetComponents(PrimComponents);

			for (UPrimitiveComponent* PrimComponent : PrimComponents)
			{
				if (UStaticMeshComponent* Component = Cast<UStaticMeshComponent>(PrimComponent))
				{
					Component->bUseDefaultCollision = true;
				}

				FBodyInstance& BodyInstance = PrimComponent->BodyInstance;
				BodyInstance.SetCollisionProfileName(UCollisionProfile::DefaultProjectile_ProfileName);
				PrimComponent->RecreatePhysicsState();
				PrimComponent->Modify();
			}

		}
		else if (Type == M2_MergeActorSpace::ECollisionType::NoCollision)
		{
			TArray<UPrimitiveComponent*> PrimComponents;
			MeshActor->GetComponents(PrimComponents);

			for (UPrimitiveComponent* PrimComponent : PrimComponents)
			{
				if (UStaticMeshComponent* Component = Cast<UStaticMeshComponent>(PrimComponent))
				{
					Component->bUseDefaultCollision = false;
				}

				FBodyInstance& BodyInstance = PrimComponent->BodyInstance;
				BodyInstance.SetCollisionProfileName(UCollisionProfile::NoCollision_ProfileName);
				PrimComponent->RecreatePhysicsState();
				PrimComponent->Modify();
			}
		}

		MeshActor->PostEditChange();
		MeshActor->MarkPackageDirty();
	}

	static void VisibilityActor(AActor* InActor, bool bVisible)
	{
		if (!InActor)
		{
			return;
		}

		InActor->Modify();
		InActor->bIsEditorOnlyActor = !bVisible;
		InActor->SetHidden(!bVisible);
		InActor->bHiddenEd = !bVisible;
		InActor->SetActorHiddenInGame(!bVisible);
		InActor->GetRootComponent()->SetVisibility(bVisible, true);

		InActor->SetIsTemporarilyHiddenInEditor(!bVisible);

		if (AStaticMeshActor* StaticMeshActor = Cast<AStaticMeshActor>(InActor))
		{
			if (bVisible)
			{
				StaticMeshActor->SetMobility(EComponentMobility::Static);
				ChangeActorsCollision(StaticMeshActor, M2_MergeActorSpace::ECollisionType::Default);

			}
			else
			{
				StaticMeshActor->SetMobility(EComponentMobility::Movable);
				ChangeActorsCollision(StaticMeshActor, M2_MergeActorSpace::ECollisionType::NoCollision);
			}
		}

		InActor->MarkActorPackageDirty();

		if (InActor->GetWorld() && !InActor->GetWorld()->GetWorldPartition())
		{
			InActor->GetWorld()->MarkPackageDirty();
		}
	}

	static void RefreshAllBrowsers()
	{
		FAssetRegistryModule& AssetRegistryModule = FModuleManager::LoadModuleChecked<FAssetRegistryModule>("AssetRegistry");
		AssetRegistryModule.Get().ScanPathsSynchronous({ "/Game" }, true);

		if (GEditor)
		{
			GEditor->BroadcastLevelActorListChanged();
			GEditor->RedrawLevelEditingViewports();
			FEditorDelegates::RefreshAllBrowsers.Broadcast();
		}

		//FLevelEditorModule& LevelEditor = FModuleManager::LoadModuleChecked<FLevelEditorModule>("LevelEditor");
		//TSharedPtr<ILevelEditor> LevelEditorPtr = LevelEditor.GetFirstLevelEditor();
		//if (LevelEditorPtr.IsValid())
		//{
		//	LevelEditorPtr->GetMostRecentlyUsedSceneOutliner()->FullRefresh();
		//}

	}

	static void ClearMergedOrBatchedActorAndRestoreSourceActors(MergeOrBatchType Type, TSet<AActor*>& MergedActors)
	{
		if (!MergedActors.Num())
		{
			return;
		}

		TSet<AActor*> AllSourceActors;

		TSet<AActor*> NeedDeleteActors;
		TArray<UObject*> NeedDeleteAssets;

		for (AActor* Actor : MergedActors)
		{
			if (!Actor)
			{
				continue;
			}

			if (Type == MergeOrBatchType::Merged)
			{
				if (!IsMergedActor(Actor))
				{
					continue;
				}
			}
			else
			{
				if (!IsBatchedActor(Actor))
				{
					continue;
				}
			}

			if (Actor->GetWorld() && !Actor->GetWorld()->GetWorldPartition())
			{
				Actor->GetWorld()->MarkPackageDirty();
			}

			TSet<AActor*> SourceActors;
			AnalysisMergedOrBatchedTag(Type, Actor, SourceActors);

			for (AActor* Item : SourceActors)
			{
				if(!Item) continue;

				AllSourceActors.Add(Item);
			}

			if (AStaticMeshActor* StaticMeshActor = Cast<AStaticMeshActor>(Actor))
			{
				if (UStaticMeshComponent* StaticMeshComponent = StaticMeshActor->GetStaticMeshComponent())
				{
					if (UStaticMesh* StaticMesh = StaticMeshComponent->GetStaticMesh())
					{
						NeedDeleteAssets.AddUnique(StaticMesh);
					}
				}
			}

			NeedDeleteActors.Add(Actor);
		}

		int32 ScopeNum = NeedDeleteActors.Num() + (NeedDeleteAssets.Num() ? 1 : 0) + AllSourceActors.Num();

		if (ScopeNum)
		{
			FScopedSlowTask SlowTask(ScopeNum, FText::FromString(TEXT("清理合并的信息")));
			SlowTask.MakeDialogDelayed(1.0f);

			for (AActor* Actor : NeedDeleteActors)
			{
				SlowTask.EnterProgressFrame(1.0f, FText::Format(FText::FromString(TEXT("删除 Actor : {0}")), FText::FromString(Actor->GetActorLabel())));

				Actor->MarkActorPackageDirty();
				Actor->Destroy();
			}

			FEditorFileUtils::SaveDirtyPackages(false, true, true, false, false, false);

			if (NeedDeleteAssets.Num())
			{
				SlowTask.EnterProgressFrame(1.0f, FText::FromString(TEXT("删除合并生成的Mesh、Texture、Material等资产")));

				ObjectTools::ForceDeleteObjects(NeedDeleteAssets, false);
			}

			for (AActor* SouceActor : AllSourceActors)
			{
				SlowTask.EnterProgressFrame(1.0f, FText::Format(FText::FromString(TEXT("恢复原始Actor : {0} 的状态")), FText::FromString(SouceActor->GetActorLabel())));

				VisibilityActor(SouceActor, true);
			}
		}

		FEditorFileUtils::SaveDirtyPackages(false, true, true, false, false, false);
	}

	static void ClearAllMergedAndBatched()
	{
		USelection* SelectedActors = GEditor->GetSelectedActors();

		TSet<AActor*> Actors;

		for (FSelectionIterator Iter(*SelectedActors); Iter; ++Iter)
		{
			AActor* Actor = Cast<AActor>(*Iter);
			if (Actor)
			{
				Actors.Add(Actor);
				Actor->EditorGetUnderlyingActors(Actors);
			}
		}

		TSet<AActor*> MergedActors;
		TSet<AActor*> BatchedActors;

		for (AActor* Actor : Actors)
		{
			if(!Actor) continue;
			
			if (IsMergedActor(Actor))
			{
				MergedActors.Add(Actor);
			}
			else if (IsBatchedActor(Actor))
			{
				BatchedActors.Add(Actor);
			}
			else {}
		}

		ClearMergedOrBatchedActorAndRestoreSourceActors(MergeOrBatchType::Merged, MergedActors);
		ClearMergedOrBatchedActorAndRestoreSourceActors(MergeOrBatchType::Batched, BatchedActors);
	}

	static void ComputedLightMapResolution(AStaticMeshActor* Actor)
	{
		if (!IsValid(Actor))
		{
			return;
		}

		UStaticMeshComponent* StaticMeshComponent = Actor->GetStaticMeshComponent();

		if (!StaticMeshComponent)
		{
			return;
		}

		FVector Origin = FVector::ZeroVector;
		FVector BoxExtent = FVector::ZeroVector;

		Actor->GetActorBounds(false, Origin, BoxExtent, false);

		BoxExtent = BoxExtent * 2.0f;

		double AverageValue = (BoxExtent.X + BoxExtent.Y + BoxExtent.Z) / 3.0f;

		int32 Floor1 = UKismetMathLibrary::FFloor(AverageValue);
		int32 Floor2 = UKismetMathLibrary::FFloor(UKismetMathLibrary::Log(Floor1, 2.0f));

		double Power1 = UKismetMathLibrary::MultiplyMultiply_FloatFloat(2.0f, Floor2);
		double Power2 = UKismetMathLibrary::MultiplyMultiply_FloatFloat(2.0f, Floor2 - 1.0f);

		int32 Truncate = UKismetMathLibrary::FTrunc(Power2);

		int32 LightResolutionRatio = 64;
		if (Floor1 == Power1)
		{
			LightResolutionRatio = Truncate;
		}
		else
		{
			if ((Floor1 - Power1) >= (Power1 - Floor1))
			{
				LightResolutionRatio = UKismetMathLibrary::FTrunc(Power1);
			}
			else
			{
				LightResolutionRatio = Truncate;
			}
		}

		StaticMeshComponent->bOverrideLightMapRes = true;
		StaticMeshComponent->OverriddenLightMapRes = (LightResolutionRatio > 2048 ? 2048 : LightResolutionRatio);
		StaticMeshComponent->MarkRenderStateDirty();
		StaticMeshComponent->MarkPackageDirty();
	}

	static void GeneratedLightMapUV(TArray<AStaticMeshActor*> StaticMeshActors, bool IsMergedActor = false)
	{
		if (!StaticMeshActors.Num())
		{
			return;
		}

		for (AStaticMeshActor* Actor : StaticMeshActors)
		{
			if (!Actor) continue;

			UStaticMeshComponent* StaticMeshComponent = Actor->GetStaticMeshComponent();
			if (!StaticMeshComponent) continue;

			UStaticMesh* StaticMesh = StaticMeshComponent->GetStaticMesh();
			if (!StaticMesh) continue;

			int32 MeshUVsNum = StaticMesh->GetNumUVChannels(0);
			if (!MeshUVsNum) continue;

			bool NeedGenerate = false;

			if (MeshUVsNum == 1)
			{
				NeedGenerate = true;
			}
			else
			{
				if (IsMergedActor)
				{
					NeedGenerate = true;
				}
			}

			if (NeedGenerate)
			{
				FlushRenderingCommands();
				FStaticMeshSourceModel& SrcModel = StaticMesh->GetSourceModel(0);
				SrcModel.BuildSettings.bGenerateLightmapUVs = true;
				SrcModel.BuildSettings.SrcLightmapIndex = IsMergedActor ? 1 : 0;
				SrcModel.BuildSettings.DstLightmapIndex = 1;
				SrcModel.ScreenSize.Default = 1.0f;
				StaticMesh->SetLightMapCoordinateIndex(1);
				StaticMesh->PostEditChange();
				if (IsMergedActor)
				{
					StaticMesh->MarkPackageDirty();
				}
			}
		}
	}

	//只处理 bool int float double FString FName Enum(支持带class与非class)，其他类型不支持
	static void SetActorPropertiesWithFolderTags(UActorFolder* SourceActorFolder, AStaticMeshActor* DestActor)
	{
		if (!SourceActorFolder || !SourceActorFolder->Tags.Num()  || !DestActor) return;

		for (FName& Tag : SourceActorFolder->Tags)
		{
			if(Tag.IsNone()) continue;
			
			FString TagStr = Tag.ToString();
			if(!TagStr.Contains("|")) continue; //没使用:避免带 ::的枚举（可能不需要这个判断）

			TArray<FString> KeyValue;
			TagStr.ParseIntoArray(KeyValue, TEXT("|"), true);

			if (KeyValue.Num() != 3) continue;

			FString ClassName = KeyValue[0];
			FString PropertyName = KeyValue[1];
			FString PropertyValue = KeyValue[2];

			UObject* TargetObject = nullptr;
			UClass* TargetClass = nullptr;

			//类型转换
			{
				if (ClassName.Equals("AStaticMeshActor", ESearchCase::IgnoreCase))
				{
					TargetObject = Cast<AStaticMeshActor>(DestActor);
					TargetClass = AStaticMeshActor::StaticClass();
				}
				else if (ClassName.Equals("AActor", ESearchCase::IgnoreCase))
				{
					TargetObject = Cast<AActor>(DestActor);
					TargetClass = AActor::StaticClass();
				}
				else if (ClassName.Equals("UStaticMeshComponent", ESearchCase::IgnoreCase))
				{
					TargetObject = Cast<UStaticMeshComponent>(DestActor->GetStaticMeshComponent());
					TargetClass = UStaticMeshComponent::StaticClass();
				}
				else if (ClassName.Equals("UMeshComponent", ESearchCase::IgnoreCase))
				{
					TargetObject = Cast<UMeshComponent>(DestActor->GetStaticMeshComponent());
					TargetClass = UMeshComponent::StaticClass();
				}
				else if (ClassName.Equals("UPrimitiveComponent", ESearchCase::IgnoreCase))
				{
					TargetObject = Cast<UPrimitiveComponent>(DestActor->GetStaticMeshComponent());
					TargetClass = UPrimitiveComponent::StaticClass();
				}
				else if (ClassName.Equals("USceneComponent", ESearchCase::IgnoreCase))
				{
					TargetObject = Cast<USceneComponent>(DestActor->GetStaticMeshComponent());
					TargetClass = USceneComponent::StaticClass();
				}
			}

			if (!TargetObject || !TargetClass)
			{
				UE_LOG(LogTemp, Warning, TEXT("M2_MergeActorSpace::SetActorPropertiesWithFolderTags : Class Type conversion failed"));
				continue;
			}

			FProperty* FoundProperty = TargetClass->FindPropertyByName(FName(*PropertyName));
			if (!FoundProperty)
			{
				UE_LOG(LogTemp, Warning, TEXT("M2_MergeActorSpace::SetActorPropertiesWithFolderTags : Property %s not found"), *PropertyName);
				continue;
			}

			// 属性内存位置
			void* PropertyValuePtr = FoundProperty->ContainerPtrToValuePtr<void>(TargetObject);

			UEnum* Enum = nullptr;
			FNumericProperty* UnderlyingProperty = nullptr;

			// 带class的枚举
			if (FEnumProperty* AsEnumProperty = CastField<FEnumProperty>(FoundProperty))
			{
				Enum = AsEnumProperty->GetEnum();
				UnderlyingProperty = AsEnumProperty->GetUnderlyingProperty();
			}
			// 传统枚举
			else if (FByteProperty* AsByteProperty = CastField<FByteProperty>(FoundProperty))
			{
				if (AsByteProperty->IsEnum())
				{
					Enum = AsByteProperty->Enum;
					UnderlyingProperty = AsByteProperty;
				}
			}
			// 先处理枚举，防止普通枚举被当成bool处理了
			if (Enum && UnderlyingProperty) 
			{
				int64 EnumValue = INDEX_NONE;

				// 尝试直接匹配枚举值名称
				EnumValue = Enum->GetValueByName(FName(*PropertyValue), EGetByNameFlags::CheckAuthoredName);

				// 如果直接匹配失败，尝试去除可能的命名空间前缀
				if (EnumValue == INDEX_NONE)
				{
					FString CleanValue = PropertyValue;

					// 去除可能的 "EnumType::" 前缀
					int32 ColonPos = PropertyValue.Find(TEXT("::"));
					if (ColonPos != INDEX_NONE)
					{
						CleanValue = PropertyValue.Mid(ColonPos + 2);
					}

					EnumValue = Enum->GetValueByName(FName(*CleanValue), EGetByNameFlags::CheckAuthoredName);
				}

				// 如果还是失败 尝试匹配显示名称
				if (EnumValue == INDEX_NONE)
				{
					for (int32 i = 0; i < Enum->NumEnums() - 1; i++) // 跳过最后的 _MAX 条目
					{
						FString DisplayName = Enum->GetDisplayNameTextByIndex(i).ToString();
						if (DisplayName.Equals(PropertyValue, ESearchCase::IgnoreCase))
						{
							EnumValue = Enum->GetValueByIndex(i);
							break;
						}
					}
				}

				if (EnumValue != INDEX_NONE)
				{
					UnderlyingProperty->SetIntPropertyValue(PropertyValuePtr, EnumValue);
					continue;
				}
				else
				{
					UE_LOG(LogTemp, Warning, TEXT("M2_MergeActorSpace::SetActorPropertiesWithFolderTags : This Enum = %s not found"), *PropertyValue);
					continue;
				}
			}
			// bool 先处理bool防止传入的是1或者0 当成int处理了。（原则上不会，因为同一个类的属性，不允许同名的变量）
			else if (FBoolProperty* BoolProperty = CastField<FBoolProperty>(FoundProperty))
			{
				FString LowerValue = PropertyValue.ToLower();
				bool bBoolValue = false;

				// 支持多种常见布尔表示法
				if (LowerValue == TEXT("true") || LowerValue == TEXT("1"))
				{
					bBoolValue = true;
				}
				else if (LowerValue == TEXT("false") ||	LowerValue == TEXT("0"))
				{
					bBoolValue = false;
				}
				else
				{
					if (LowerValue.IsNumeric())
					{
						int32 NumericValue = FCString::Atoi(*PropertyValue);
						bBoolValue = (NumericValue != 0);
					}
					else
					{
						UE_LOG(LogTemp, Warning, TEXT("M2_MergeActorSpace::SetActorPropertiesWithFolderTags : PropertyName = %s, mast use 'true'/'1' or 'false'/'0'"),
						*PropertyName);
						continue;
					}
				}

				BoolProperty->SetPropertyValue(PropertyValuePtr, bBoolValue);
				continue;
			}
			// int
			else if (FIntProperty* IntProperty = CastField<FIntProperty>(FoundProperty))
			{
				IntProperty->SetPropertyValue(PropertyValuePtr, FCString::Atoi(*PropertyValue));
				continue;
			}
			// float
			else if (FFloatProperty* FloatProperty = CastField<FFloatProperty>(FoundProperty))
			{
				FloatProperty->SetPropertyValue(PropertyValuePtr, FCString::Atof(*PropertyValue));
				continue;
			}
			// double
			else if (FDoubleProperty* DoubleProperty = CastField<FDoubleProperty>(FoundProperty))
			{
				DoubleProperty->SetPropertyValue(PropertyValuePtr, FCString::Atod(*PropertyValue));
				continue;
			}
			// FString
			else if (FStrProperty* StrProperty = CastField<FStrProperty>(FoundProperty))
			{
				StrProperty->SetPropertyValue(PropertyValuePtr, PropertyValue);
				continue;
			}
			// FName
			else if (FNameProperty* NameProperty = CastField<FNameProperty>(FoundProperty))
			{
				NameProperty->SetPropertyValue(PropertyValuePtr, FName(*PropertyValue));
				continue;
			}
			else
			{
				UE_LOG(LogTemp, Warning, TEXT("M2_MergeActorSpace::SetActorPropertiesWithFolderTags : PropertyName = %s Error"), *PropertyName);
			}
		}
	}


	static UStaticMesh* DuplicateMesh(UStaticMesh* Mesh)
	{
		UStaticMesh* DupMesh = nullptr;
		if (!Mesh) return DupMesh;

		FString NewPackageName = Mesh->GetPackage()->GetName();
		FString NewObjectName = Mesh->GetName();
		FAssetToolsModule& AssetToolsModule = FModuleManager::LoadModuleChecked<FAssetToolsModule>("AssetTools");
		AssetToolsModule.Get().CreateUniqueAssetName(*NewPackageName, TEXT(""), NewPackageName, NewObjectName); //生成新的包名和ObjectName

		//GEditor->GetSelectedObjects()->Deselect(Mesh);

		FObjectDuplicationParameters Params = InitStaticDuplicateObjectParams(Mesh, CreatePackage(*NewPackageName), *NewObjectName, RF_AllFlags, nullptr, EDuplicateMode::Normal);
		UObject* NewObject = StaticDuplicateObjectEx(Params);
		if (NewObject)
		{
			DupMesh = Cast<UStaticMesh>(NewObject);
		}

		return DupMesh;
	}

	//记录原始Actor与Mesh的组合
	static void RecordCombinationActorAndMesh(TArray<AStaticMeshActor*> Actors, TMap<AActor*, M2_MergeActorSpace::ActorAndMeshInfo>& OutCombinationMap)
	{
		if(!Actors.Num()) return;

		OutCombinationMap.Empty();

		for (auto& Item : Actors)
		{
			UStaticMeshComponent* StaticMeshComponent = Item->GetStaticMeshComponent();
			if (StaticMeshComponent)
			{
				UStaticMesh* Mesh = StaticMeshComponent->GetStaticMesh();
				if (!Mesh) continue;

				int32 MeshUVsNum = Mesh->GetNumUVChannels(0);
				if (MeshUVsNum == 1)
				{
					UStaticMesh* DupMesh = M2_MergeActorSpace::DuplicateMesh(StaticMeshComponent->GetStaticMesh());
					if (DupMesh)
					{
						DupMesh->AddToRoot();
						StaticMeshComponent->SetStaticMesh(DupMesh); //设置临时Mesh
						StaticMeshComponent->PostEditChange();

						M2_MergeActorSpace::ActorAndMeshInfo& Info = OutCombinationMap.FindOrAdd(Item);
						Info.Actor = Item;
						Info.OriginalMesh = Mesh;
						Info.TempMesh = DupMesh;
					}
				}
			}
		}
	}

	//恢复原始Actor与Mesh的组合
	static void RecoveryCombinationActorAndMesh(TMap<AActor*, M2_MergeActorSpace::ActorAndMeshInfo>& CombinationMap)
	{
		if (!CombinationMap.Num()) return;

		TArray<UObject*> ObjectsToDelete;
		TArray<UPackage*> PackagesToDelete;

		for (auto& Item : CombinationMap)
		{
			M2_MergeActorSpace::ActorAndMeshInfo& Info = Item.Value;
			
			if (Info.Actor)
			{
				if (UStaticMeshComponent* StaticMeshComponent = Info.Actor->GetStaticMeshComponent())
				{
					StaticMeshComponent->SetStaticMesh(Info.OriginalMesh);
					StaticMeshComponent->PostEditChange();
					StaticMeshComponent->MarkPackageDirty();
				}
			}

			if (Info.TempMesh)
			{
				Info.TempMesh->RemoveFromRoot();

				ObjectsToDelete.AddUnique(Info.TempMesh);

				if (UPackage* Package = Info.TempMesh->GetPackage())
				{
					PackagesToDelete.AddUnique(Package);

					TArray<UObject*> ObjectsInPackage;
					GetObjectsWithPackage(Package, ObjectsInPackage, false);

					UPackage* TransientPackage = GetTransientPackage();

					for (UObject* Obj : ObjectsInPackage)
					{
						if (Obj->IsA(UPackage::StaticClass()) || Obj->IsA(UMetaData::StaticClass()))
						{
							continue;
						}

						//重设置包名， 否则ObjectTools::ForceDeleteObjects后Package内容会改变，导致ObjectTools::CleanupAfterSuccessfulDelete崩溃
						Obj->Rename(
							nullptr, // 保持原名
							TransientPackage,
							REN_ForceNoResetLoaders |
							REN_DoNotDirty |
							REN_DontCreateRedirectors
						);
					}

					Package->SetDirtyFlag(false);
					Package->ClearFlags(RF_Public | RF_Standalone);
				}
			}
		}

		ObjectTools::ForceDeleteObjects(ObjectsToDelete, false);

		auto IsPackageEmpty = [](UPackage* Package) ->bool
			{
				TArray<UObject*> ObjectsInPackage;
				GetObjectsWithPackage(Package, ObjectsInPackage, false);

				for (UObject* Obj : ObjectsInPackage)
				{
					if (Obj->IsAsset())
					{
						return false;
					}
				}

				return true;
			};

		TArray<UPackage*> EmptyPackages;
		for (UPackage* Package : PackagesToDelete)
		{
			if (Package && IsPackageEmpty(Package))
			{
				EmptyPackages.Add(Package);
			}
		}

		if (EmptyPackages.Num() > 0)
		{
			ObjectTools::CleanupAfterSuccessfulDelete(EmptyPackages);
		}

		CombinationMap.Empty();
	}
};


