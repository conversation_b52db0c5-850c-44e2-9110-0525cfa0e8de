// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "ISequencer.h"
#include "Animation/UGCAnimationSequence.h"
#include "Widgets/SCompoundWidget.h"

/**
 * 
 */

struct FUGCAnimationListItem
{
	FUGCAnimationListItem(UUGCAnimationSequence* InAnimation)
		: Animation(InAnimation)
	{
	}

	UUGCAnimationSequence* Animation;
};

typedef SListView<TSharedPtr<FUGCAnimationListItem>> SUGCAnimationListView;

class SUGCAnimationListItem : public STableRow<TSharedPtr<FUGCAnimationListItem>>
{
public:
	SLATE_BEGIN_ARGS(SUGCAnimationListItem)
		{
		}

	SLATE_END_ARGS()

	void Construct(const FArguments& InArgs, const TSharedRef<STableViewBase>& InOwnerTableView, TSharedPtr<FUGCAnimationListItem> InItem);

private:
	FText GetMovieSceneText() const;

	TWeakPtr<FUGCAnimationListItem> Item;
	TSharedPtr<SInlineEditableTextBlock> InlineTextBlock;
};

class UGCBASEEDITOR_API SUGCAnimationList : public SCompoundWidget
{
public:
	SLATE_BEGIN_ARGS(SUGCAnimationList)
		{
		}

	SLATE_END_ARGS()

	/** Constructs this widget with InArgs */
	void Construct(const FArguments& InArgs, TSharedPtr<ISequencer> InSequencer);

private:
	TSharedRef<ITableRow> OnGenerateRowForUGCSequence(TSharedPtr<FUGCAnimationListItem> InListItem,
		const TSharedRef<STableViewBase>& InOwnerTableView);

	void OnSelectionChanged(TSharedPtr<FUGCAnimationListItem> InSelectedItem, ESelectInfo::Type SelectionInfo);

	TWeakPtr<ISequencer> Sequencer;
	TSharedPtr<SUGCAnimationListView> AnimationListView;
	TArray<TSharedPtr<FUGCAnimationListItem>> Animations;
	bool bIsDrawerTab = false;
};
