// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Factories/Factory.h"
#include "UGCSlotNameDataTableFactory.generated.h"

/**
 * 
 */
UCLASS(HideCategories=Object, MinimalAPI)
class UUGCSlotNameDataTableFactory : public UFactory
{
	GENERATED_UCLASS_BODY()
	UPROPERTY()
	TObjectPtr<class UUGCBaseProfile> TargetProfile;

	//~ Begin UFactory Interface
	virtual UObject* FactoryCreateNew(UClass* Class, UObject* InParent, FName Name, EObjectFlags Flags, UObject* Context,
		FFeedbackContext* Warn) override;
	//~ Begin UFactory Interface	
};
