// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Fabric.h"
#include "Data/UGCMappingData.h"

/**
 * 
 */
class UGCBASE_API FFabricLibrary
{
public:
	static UUGCMappingData* GetFabricDefaultMappingData();

	static void UpdateFabric(UFabricProfile* FabricProfile, TArray<UMeshComponent*>& MeshComponents);
	static void UpdateFabric(UFabricProfile* FabricProfile, UMeshComponent* MeshComponent);

	static void UpdateFabric(UFabricProfile* FabricProfile, TArray<UMeshComponent*>& MeshComponents, TArray<FFabricCustomData>& CustomDatas);
	static void UpdateFabric(UFabricProfile* FabricProfile, UMeshComponent* MeshComponent, TArray<FFabricCustomData>& CustomDatas);

private:
	static void UpdateFabricInternal(UMeshComponent* MeshComponent, FMaterialFabricSlot& Slot, FFabricCustomData& CustomData);

public:
	static void OverrideParameters(const UMaterialInterface* OriginMaterial, UMaterialInstanceDynamic* NewMaterial);

	static void UpdateFabricToMaterial(UFabric* Fabric, UMaterialInstanceDynamic* MID);
};
