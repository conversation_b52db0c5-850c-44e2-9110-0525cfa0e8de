// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Engine/DataAsset.h"
#include "Applique/Applique.h"
#include "Cut/Cut.h"
#include "Dye/Dye.h"
#include "Fabric/Fabric.h"
#include "Pattern/Pattern.h"
#include "UGCUserData.generated.h"

DECLARE_DYNAMIC_DELEGATE_RetVal_OneParam(UUGCUserData*, FLoadUGCUserData, UUGCBaseProfile*, Profile);

DECLARE_DYNAMIC_DELEGATE_RetVal_TwoParams(bool, FSaveUGCUserData, UUGCBaseProfile*, Profile, UUGCUserData*, UserData);

DECLARE_MULTICAST_DELEGATE_TwoParams(FOnUGCUserDataUpdate, UUGCUserData*, bool);
/**
 * 
 */
UCLASS(BlueprintType)
class UGCBASE_API UUGCUserData : public UDataAsset
{
	GENERATED_BODY()

public:
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, DisplayName = "数据UGC配置文件", Category = "UGC配置", AssetRegistrySearchable)
	TObjectPtr<UUGCBaseProfile> Profile;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, DisplayName = "默认染色UGC数据", Category = "染色")
	int DefaultDyeCustomDataIndex = 0;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, DisplayName = "染色UGC数据", Category = "染色")
	TArray<FDyeCustomData> DyeCustomDatas;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, EditFixedSize, DisplayName = "花纹UGC数据", Category = "花纹", meta=(EditfixedOrder))
	TArray<FPatternCustomData> PatternCustomDatas;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, EditFixedSize, DisplayName = "图案UGC数据", Category = "图案", meta=(EditfixedOrder))
	TArray<FAppliqueCustomData> AppliqueCustomDatas;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, EditFixedSize, DisplayName = "裁剪UGC数据", Category = "裁剪", meta=(EditfixedOrder))
	TArray<FCutCustomData> CutCustomDatas;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, EditFixedSize, DisplayName = "面料UGC数据", Category = "面料", meta=(EditfixedOrder))
	TArray<FFabricCustomData> FabricCustomDatas;

	/* Notify the MeshComponent to update the UGC customization */
	FOnUGCUserDataUpdate OnUpdateDelegate;

#if WITH_EDITOR
	virtual void PostEditChangeProperty(FPropertyChangedEvent& PropertyChangedEvent) override;
	virtual void PostEditChangeChainProperty(struct FPropertyChangedChainEvent& PropertyChangedEvent) override;
#endif

	// Initialize the UGC data with the default data in given profile
	UFUNCTION(BlueprintCallable, Category = "UGCUserData")
	bool Initialize(UUGCBaseProfile* InProfile);

	UFUNCTION(BlueprintCallable, Category = "UGCUserData")
	void ResetToInitial();

	UFUNCTION(BlueprintCallable, Category = "UGCUserData")
	void Clear();

	/**
	 * Refresh the UGC data. Fix the num of each type of custom data to match the profile.
	 * Refresh the UGC Parameters of Fabric in this UGCUserData.
	 */
	UFUNCTION(BlueprintCallable, Category = "UGCUserData",
		meta=(ToolTip=
			"Refresh the UGC data. Fix the num of each type of custom data to match the profile. Refresh the UGC Parameters of Fabric in this UGCUserData."
		))
	void RefreshInnerCustomData();

	/**
	 *  Call this function after modifying the UGC data to notify the ugc component to update
	 *	@param UpdateDyeSequence: If true, the dye sequence data will be updated, otherwise only the other data will be updated.
	 */
	UFUNCTION(BlueprintCallable, Category = "UGCUserData")
	void BroadCastUpdate(bool UpdateDyeSequence = false);

#pragma region Dye Custom Data

	UFUNCTION(BlueprintCallable, Category = "UGCUserData|Dye")
	int GetDyeColorSlotNum() const;

	UFUNCTION(BlueprintCallable, Category = "UGCUserData|Dye")
	int GetDyeGradientColorSlotNum() const;

	UFUNCTION(BlueprintCallable, Category = "UGCUserData|Dye")
	void SetDyeCustomDataNum(int Num);

	UFUNCTION(BlueprintCallable, Category = "UGCUserData|Dye")
	int GetDyeCustomDataNum() const;

	UFUNCTION(BlueprintCallable, Category = "UGCUserData|Dye")
	void SetDefaultDyeCustomDataIndex(int DyeCustomDataIndex);

	UFUNCTION(BlueprintCallable, Category = "UGCUserData|Dye")
	int GetDefaultDyeCustomDataIndex() const;

	// Change the Num of Dye Scheme(If Num is increased, the new Dye Scheme will be copied from the Index 0 Dye Scheme)
	UFUNCTION(BlueprintCallable, Category = "UGCUserData|Dye")
	void SetDyeSchemeNum(int DyeCustomDataIndex, int Num);

	/**
	 * Get the number of Dye Schemes in the Dye Custom Data.
	 * @param DyeCustomDataIndex The index of the Dye Custom Data. If -1, it will be the Default Dye Custom Data Index in the UserData.
	 * @return The number of Dye Schemes in the Dye Custom Data.
	 */
	UFUNCTION(BlueprintCallable, Category = "UGCUserData|Dye")
	int GetDyeSchemeNum(int DyeCustomDataIndex = -1) const;

	// Change the Default Dye Scheme Index. Default Dye Scheme will be used for UGC and will be the start when Dyeing is dynamic.
	UFUNCTION(BlueprintCallable, Category = "UGCUserData|Dye")
	bool SetDefaultDyeSchemeIndex(int DyeCustomDataIndex, int DyeSchemeIndex);

	/**
	 * Get the Default Dye Scheme Index in the Dye Custom Data.
	 * @param DyeCustomDataIndex The index of the Dye Custom Data. If -1, it will be the Default Dye Custom Data Index in the UserData.
	 * @return The Default Dye Scheme Index in the Dye Custom Data.
	 */
	UFUNCTION(BlueprintCallable, Category = "UGCUserData|Dye")
	int GetDefaultDyeSchemeIndex(int DyeCustomDataIndex = -1) const;

	/**
	 * Set the Dye Color.
	 * @param ColorSlotIndex The index of the color slot in the Dye Scheme.
	 * @param DyeColor The color to set.
	 * @param DyeCustomDataIndex If is -1, the value will be the Default Dye Custom Data Index in the UserData.
	 * @param DyeSchemeIndex If is -1, the value will be the Default Dye Scheme Index in the Dye Custom Data.
	 */
	UFUNCTION(BlueprintCallable, Category = "UGCUserData|Dye",
		meta = (ToolTip =
			"Set the Dye Color. If DyeCustomDataIndex is -1, the value will be the Default Dye Custom Data Index in the UserData. If DyeSchemeIndex is -1, the value will be the Default Dye Scheme Index in the Dye Custom Data."
		))
	bool SetDyeColor(const int ColorSlotIndex, const FLinearColor& DyeColor, int DyeSchemeIndex = -1, int DyeCustomDataIndex = -1);

	/**
	 * Get the Dye Color.
	 * @param ColorSlotIndex The index of the color slot in the Dye Scheme.
	 * @param DyeCustomDataIndex If is -1, the value will be the Default Dye Custom Data Index in the UserData.
	 * @param DyeSchemeIndex If is -1, the value will be the Default Dye Scheme Index in the Dye Custom Data.
	 * @return The Dye Color in the specified slot.
	 */
	UFUNCTION(BlueprintCallable, Category = "UGCUserData|Dye",
		meta = (ToolTip =
			"Get the Dye Color. If DyeCustomDataIndex is -1, the value will be the Default Dye Custom Data Index in the UserData. If DyeSchemeIndex is -1, the value will be the Default Dye Scheme Index in the Dye Custom Data."
		))
	FLinearColor GetDyeColor(const int ColorSlotIndex, int DyeSchemeIndex = -1, int DyeCustomDataIndex = -1) const;

	/**
	 * Set the Dye Gradient Offset.
	 * @param GradientSlotIndex The index of the gradient.
	 * @param DyeGradientOffset The offset value to set. The range is suggested to be [-1, 1].
	 * @param DyeCustomDataIndex If is -1, the value will be the Default Dye Custom Data Index in the UserData.
	 * @return True if the operation was successful, false otherwise.
	 * @note The Dye Gradient Offset is used to control the gradient effect of the dye.
	 */
	UFUNCTION(Blueprintable, Category = "UGCUserData|Dye",
		meta = (ToolTip =
			"Set the Dye Gradient Color. The range for DyeGradientOffset is suggested to be [-1, 1]. If DyeCustomDataIndex is -1, the value will be the Default Dye Custom Data Index in the UserData."
		))
	bool SetDyeGradientOffset(const int GradientSlotIndex, float DyeGradientOffset = 0, int DyeCustomDataIndex = -1);

	/**
	 * Get the Dye Gradient Offset.
	 * @param GradientSlotIndex The index of the gradient.
	 * @param DyeCustomDataIndex If is -1, the value will be the Default Dye Custom Data Index in the UserData.
	 * @return The Dye Gradient Offset in the specified slot.
	 */
	UFUNCTION(BlueprintCallable, Category = "UGCUserData|Dye")
	float GetDyeGradientOffset(const int GradientSlotIndex, int DyeCustomDataIndex = -1) const;

#pragma endregion

#pragma region Pattern Custom Data

	UFUNCTION(BlueprintCallable, Category = "UGCUserData|Pattern")
	int GetPatternSlotNum() const;

	UFUNCTION(BlueprintCallable, Category = "UGCUserData|Pattern")
	bool SetPattern(int PatternCustomDataIndex, UPattern* InPattern);

	UFUNCTION(BlueprintCallable, Category = "UGCUserData|Pattern")
	UPattern* GetPattern(int PatternCustomDataIndex) const;

	UFUNCTION(BlueprintCallable, Category = "UGCUserData|Pattern", meta = (ToolTip = "Offset range is [-1, 1]"))
	bool SetPatternOffset(const int PatternCustomDataIndex, const FVector2f& Offset);

	UFUNCTION(BlueprintCallable, Category = "UGCUserData|Pattern")
	FVector2f GetPatternOffset(int PatternCustomDataIndex) const;

	/**
	 * Set the scale of the Pattern.
	 * @param PatternCustomDataIndex The index of the Pattern Custom Data.
	 * @param Scale The scale to set.
	 * @param bScaleBaseOnDefault If true, the scale will be set based on the default scale in PatternProfile. Otherwise, it will be set directly.
	 */
	UFUNCTION(BlueprintCallable, Category = "UGCUserData|Pattern",
		meta = (ToolTip="If bScaleBaseOnDefault is true, set Scale base on default scale in PatternProfile. Otherwise, set Scale directly."))
	bool SetPatternScale(const int PatternCustomDataIndex, const FVector2f& Scale, bool bScaleBaseOnDefault = true);

	/**
	 * Get the scale of the Pattern.
	 * @param PatternCustomDataIndex The index of the Pattern Custom Data.
	 * @param bScaleBaseOnDefault If true, the scale will be based on the default scale in PatternProfile. Otherwise, it will return the scale directly.
	 * @return The scale of the Pattern.
	 */
	UFUNCTION(BlueprintCallable, Category = "UGCUserData|Pattern",
		meta = (ToolTip="If bScaleBaseOnDefault is true, get Scale base on default scale in PatternProfile. Otherwise, get Scale directly."))
	FVector2f GetPatternScale(const int PatternCustomDataIndex, bool bScaleBaseOnDefault = true) const;

	UFUNCTION(BlueprintCallable, Category = "UGCUserData|Pattern", meta = (ToolTip = "Rotation range is [0, 1]"))
	bool SetPatternRotation(const int PatternCustomDataIndex, const float Rotation);

	UFUNCTION(BlueprintCallable, Category = "UGCUserData|Pattern")
	float GetPatternRotation(int PatternCustomDataIndex) const;

#pragma endregion

#pragma region Applique Custom Data

	UFUNCTION(BlueprintCallable, Category = "UGCUserData|Applique")
	int GetAppliqueSlotNum() const;

	UFUNCTION(BlueprintCallable, Category = "UGCUserData|Applique")
	bool SetApplique(int AppliqueCustomDataIndex, UApplique* InApplique);

	UFUNCTION(BlueprintCallable, Category = "UGCUserData|Applique")
	UApplique* GetApplique(int AppliqueCustomDataIndex) const;

	UFUNCTION(BlueprintCallable, Category = "UGCUserData|Applique", meta = (ToolTip = "Offset range is [-1, 1]"))
	bool SetAppliqueOffset(const int AppliqueCustomDataIndex, const FVector2f& Offset);

	UFUNCTION(BlueprintCallable, Category = "UGCUserData|Applique")
	FVector2f GetAppliqueOffset(int AppliqueCustomDataIndex) const;

	/**
	 * Set the scale of the Applique.
	 * @param AppliqueCustomDataIndex The index of the Applique Custom Data.
	 * @param Scale The scale to set.
	 * @param bScaleBaseOnDefault If true, the scale will be set based on the default scale in AppliqueProfile. Otherwise, it will be set directly.
	 */
	UFUNCTION(BlueprintCallable, Category = "UGCUserData|Applique",
		meta = (ToolTip="If bScaleBaseOnDefault is true, set Scale base on default scale in AppliqueProfile. Otherwise, set Scale directly."))
	bool SetAppliqueScale(const int AppliqueCustomDataIndex, const float Scale, bool bScaleBaseOnDefault = true);

	/**
	 * Get the scale of the Applique.
	 * @param AppliqueCustomDataIndex The index of the Applique Custom Data.
	 * @param bScaleBaseOnDefault If true, the scale will be based on the default scale in AppliqueProfile. Otherwise, it will return the scale directly.
	 * @return 
	 */
	UFUNCTION(BlueprintCallable, Category = "UGCUserData|Applique",
		meta = (ToolTip="If bScaleBaseOnDefault is true, get Scale base on default scale in AppliqueProfile. Otherwise, get Scale directly."))
	float GetAppliqueScale(const int AppliqueCustomDataIndex, bool bScaleBaseOnDefault = true) const;

	UFUNCTION(BlueprintCallable, Category = "UGCUserData|Applique", meta = (ToolTip = "Rotation range is [0, 1]"))
	bool SetAppliqueRotation(const int AppliqueCustomDataIndex, const float Rotation);

	UFUNCTION(BlueprintCallable, Category = "UGCUserData|Applique")
	float GetAppliqueRotation(int AppliqueCustomDataIndex) const;

	UFUNCTION(BlueprintCallable, Category = "UGCUserData|Applique")
	bool SetAppliqueDyeColor(const int AppliqueCustomDataIndex, const FLinearColor& DyeColor);

	UFUNCTION(BlueprintCallable, Category = "UGCUserData|Applique")
	FLinearColor GetAppliqueDyeColor(int AppliqueCustomDataIndex) const;

#pragma endregion

#pragma region Cut Custom Data

	UFUNCTION(BlueprintCallable, Category = "UGCUserData|Cut")
	int GetCutSlotNum() const;

	UFUNCTION(BlueprintCallable, Category = "UGCUserData|Cut")
	bool SetCutBandPattern(int CutCustomDataIndex, UPattern* InCutBandPattern);

	UFUNCTION(BlueprintCallable, Category = "UGCUserData|Cut")
	UPattern* GetCutBandPattern(int CutCustomDataIndex) const;

	UFUNCTION(BlueprintCallable, Category = "UGCUserData|Cut")
	bool SetCutBandStyle(int CutCustomDataIndex, UCutBandStyle* InCutBandStyle);

	UFUNCTION(BlueprintCallable, Category = "UGCUserData|Cut")
	UCutBandStyle* GetCutBandStyle(int CutCustomDataIndex) const;

	UFUNCTION(BlueprintCallable, Category = "UGCUserData|Cut")
	bool SetCutEdgeCurve(int CutCustomDataIndex, UCutEdgeCurve* InCutEdgeCurve);

	UFUNCTION(BlueprintCallable, Category = "UGCUserData|Cut")
	UCutEdgeCurve* GetCutEdgeCurve(int CutCustomDataIndex) const;

	UFUNCTION(BlueprintCallable, Category = "UGCUserData|Cut")
	bool SetCutPlaneOffset(int CutCustomDataIndex, float Offset);

	UFUNCTION(BlueprintCallable, Category = "UGCUserData|Cut")
	float GetCutPlaneOffset(int CutCustomDataIndex) const;

	UFUNCTION(BlueprintCallable, Category = "UGCUserData|Cut")
	bool SetCutDirectionXZAngel(int CutCustomDataIndex, int Angle);

	UFUNCTION(BlueprintCallable, Category = "UGCUserData|Cut")
	int GetCutDirectionXZAngel(int CutCustomDataIndex) const;

	UFUNCTION(BlueprintCallable, Category = "UGCUserData|Cut")
	bool SetCutDirectionYZAngel(int CutCustomDataIndex, int Angle);

	UFUNCTION(BlueprintCallable, Category = "UGCUserData|Cut")
	int GetCutDirectionYZAngel(int CutCustomDataIndex) const;

	UFUNCTION(BlueprintCallable, Category = "UGCUserData|Cut")
	bool SetCutBandPatternDensity(int CutCustomDataIndex, int Density);

	UFUNCTION(BlueprintCallable, Category = "UGCUserData|Cut")
	int GetCutBandPatternDensity(int CutCustomDataIndex) const;

#pragma endregion

#pragma region Fabric Custom Data

	UFUNCTION(BlueprintCallable, Category = "UGCUserData|Fabric")
	int GetFabricSlotNum() const;

	UFUNCTION(BlueprintCallable, Category = "UGCUserData|Fabric")
	bool SetFabric(int FabricCustomDataIndex, UFabric* InFabric);

	UFUNCTION(BlueprintCallable, Category = "UGCUserData|Fabric")
	UFabric* GetFabric(int FabricCustomDataIndex) const;

	UFUNCTION(BlueprintCallable, Category = "UGCUserData|Fabric")
	bool SetFabricFloatParameter(int FabricCustomDataIndex, const int FloatParameterIndex, const float Value);

	UFUNCTION(BlueprintCallable, Category = "UGCUserData|Fabric")
	float GetFabricFloatParameter(int FabricCustomDataIndex, const int FloatParameterIndex) const;

	UFUNCTION(BlueprintCallable, Category = "UGCUserData|Fabric")
	bool SetFabricVectorParameter(int FabricCustomDataIndex, const int VectorParameterIndex, const FLinearColor& Value);

	UFUNCTION(BlueprintCallable, Category = "UGCUserData|Fabric")
	FLinearColor GetFabricVectorParameter(int FabricCustomDataIndex, const int VectorParameterIndex) const;

	UFUNCTION(BlueprintCallable, Category = "UGCUserData|Fabric")
	TArray<FMaterialFloatParameterWithName> GetFabricUGCFloatParameters(int FabricCustomDataIndex) const;

	UFUNCTION(BlueprintCallable, Category = "UGCUserData|Fabric")
	TArray<FMaterialVectorParameterWithName> GetFabricUGCVectorParameters(int FabricCustomDataIndex) const;

#pragma endregion
};
