// Fill out your copyright notice in the Description page of Project Settings.


#include "SAvatarPodAnimationBrowser.h"

#include "AvatarPodSettings.h"
#include "ContentBrowserModule.h"
#include "IContentBrowserSingleton.h"
#include "SlateOptMacros.h"

#define LOCTEXT_NAMESPACE "AvatarPod"

BEGIN_SLATE_FUNCTION_BUILD_OPTIMIZATION

void SAvatarPodAnimationBrowser::Construct(const FArguments& InArgs, UAvatarModel* InAvatarModel)
{
	AvatarModel = InAvatarModel;

	AssetContextMenu = MakeShared<FAvatarPodAssetContextMenu>(AvatarModel);

	SAssignNew(BrowserSegmentedControl, SSegmentedControl<int>)
	.UniformPadding(FMargin(10, 5))
	.Value(AnimationSkeletonType)
	.OnValueChanged(this, &SAvatarPodAnimationBrowser::OnSegmentedControlChanged);

	BrowserSegmentedControl->AddSlot(0).Text(LOCTEXT("FaceAnimation", "Face Animation"));
	BrowserSegmentedControl->AddSlot(1).Text(LOCTEXT("BodyAnimation", "Body Animation"));

	auto AssetPicker = CreateBrowser();

	ChildSlot
	[
		SNew(SVerticalBox)

		+ SVerticalBox::Slot()
		.Padding(4.0f)
		.AutoHeight()
		[
			BrowserSegmentedControl
			.ToSharedRef()
		]

		+ SVerticalBox::Slot()
		.Padding(0, 10, 0, 0)
		.FillHeight(1.0f)
		[
			AssetPicker
		]
	];
}


TSharedRef<SWidget> SAvatarPodAnimationBrowser::CreateBrowser()
{
	FAssetPickerConfig AssetPickerConfig;
	AssetPickerConfig.bAutohideSearchBar = false;

	AssetPickerConfig.Filter.ClassPaths.Add(UAnimationAsset::StaticClass()->GetClassPathName());
	AssetPickerConfig.Filter.bRecursiveClasses = true;
	AssetPickerConfig.OnShouldFilterAsset.BindLambda([this](const FAssetData& InAssetData)
	{
		if (InAssetData.IsValid())
		{
			// if (InAssetData.GetTagValueRef<FString>("GarmentGenderType") == UEnum::GetValueOrBitfieldAsString(
			// 		UWardrobeEditorSettings::Get()->DefaultPreviewGender)
			FString FilterSkeleton;
			if (AnimationSkeletonType == 0)
			{
				FilterSkeleton = UAvatarPodSettings::Get()->FaceSkeleton.ToString();
			}
			else if (AnimationSkeletonType == 1)
			{
				FilterSkeleton = UAvatarPodSettings::Get()->BodySkeleton.ToString();
			}
			auto AssetTagValue = InAssetData.GetTagValueRef<FString>("Skeleton");
			AssetTagValue = FSoftObjectPath(AssetTagValue).ToString();
			if (AssetTagValue == FilterSkeleton)
			{
				return false;
			}
		}
		return true;
	});

	AssetPickerConfig.bForceShowEngineContent = false;
	AssetPickerConfig.bForceShowPluginContent = false;
	AssetPickerConfig.bAllowNullSelection = true;
	AssetPickerConfig.bShowPathInColumnView = true;
	AssetPickerConfig.bShowTypeInColumnView = true;
	AssetPickerConfig.bShowBottomToolbar = true;
	AssetPickerConfig.bCanShowRealTimeThumbnails = true;
	AssetPickerConfig.bCanShowClasses = true;
	AssetPickerConfig.bCanShowReadOnlyFolders = true;
	AssetPickerConfig.SelectionMode = ESelectionMode::SingleToggle;
	AssetPickerConfig.InitialAssetViewType = EAssetViewType::Tile;
	AssetPickerConfig.OnAssetSelected.BindSP(this, &SAvatarPodAnimationBrowser::OnAssetSelected, false);
	AssetPickerConfig.OnAssetDoubleClicked.BindSP(this, &SAvatarPodAnimationBrowser::OnAssetSelected, true);
	AssetPickerConfig.OnGetAssetContextMenu.BindSP(this, &SAvatarPodAnimationBrowser::OnGetAssetContextMenu);

	FContentBrowserModule& ContentBrowserModule = FModuleManager::Get().LoadModuleChecked<FContentBrowserModule>(TEXT("ContentBrowser"));
	auto AssetPicker = ContentBrowserModule.Get().CreateAssetPicker(AssetPickerConfig);

	FChildren* Children = AssetPicker->GetChildren();
	auto VerticalBox = Children->GetChildAt(0);
	Children = VerticalBox->GetChildren();
	AssetView = StaticCastSharedRef<SAssetView>(Children->GetChildAt(2));

	return AssetPicker;
}

END_SLATE_FUNCTION_BUILD_OPTIMIZATION

void SAvatarPodAnimationBrowser::OnSegmentedControlChanged(int InAnimationSkeletonType)
{
	AnimationSkeletonType = InAnimationSkeletonType;
	if (AssetView)
	{
		AssetView->RequestSlowFullListRefresh();
	}
}

void SAvatarPodAnimationBrowser::OnAssetSelected(const FAssetData& AssetData, bool bDoubleClicked) const
{
	if (AvatarModel && AvatarModel->WardrobeComponent)
	{
		USkeletalMeshComponent* AnimationComponent = nullptr;
		if (AnimationSkeletonType == 0)
		{
			AnimationComponent = AvatarModel->WardrobeComponent->GetFaceMeshComponent();
		}
		else if (AnimationSkeletonType == 1)
		{
			AnimationComponent = AvatarModel->WardrobeComponent->GetBodyMeshComponent();
		}

		if (AnimationComponent)
		{
			if (auto AnimationAsset = Cast<UAnimationAsset>(AssetData.GetAsset()))
			{
				if (bDoubleClicked)
				{
					if (AnimationSkeletonType == 1)
					{
						AnimationComponent->PlayAnimation(AnimationAsset, true);
					}
					else if (AnimationSkeletonType == 0)
					{
						if (auto AnimInstance = AnimationComponent->GetAnimInstance())
						{
							static const FName FaceAnimation(TEXT("Sequence"));
							if (FObjectProperty* Property = FindFProperty<FObjectProperty>(AnimInstance->GetClass(), FaceAnimation))
							{
								Property->SetObjectPropertyValue_InContainer(AnimInstance, AnimationAsset);
							}
						}
					}
				}
			}
			else
			{
				if (!bDoubleClicked)
				{
					AnimationComponent->Stop();
					AnimationComponent->SetAnimation(nullptr);
				}
			}
		}
	}
}

TSharedPtr<SWidget> SAvatarPodAnimationBrowser::OnGetAssetContextMenu(const TArray<FAssetData>& AssetDataArray)
{
	if (AssetDataArray.Num() == 1 && AssetContextMenu)
	{
		return AssetContextMenu->MakeContextMenu(AssetDataArray[0]);
	}
	return SNullWidget::NullWidget;
}

#undef LOCTEXT_NAMESPACE
