// Fill out your copyright notice in the Description page of Project Settings.


#include "AvatarPodPreviewScene.h"

FAvatarPodPreviewScene::FAvatarPodPreviewScene(ConstructionValues CVS)
	: FPreviewScene(CVS)
{
	if (GEditor != nullptr)
	{
		PreviewWorld->ChangeFeatureLevel(GEditor->DefaultWorldFeatureLevel);
	}
}

FAvatarPodPreviewScene::~FAvatarPodPreviewScene()
{
}

void FAvatarPodPreviewScene::Tick(float DeltaTime)
{
	UpdateCaptureContents();
}

TStatId FAvatarPodPreviewScene::GetStatId() const
{
	return TStatId();
}

FLinearColor FAvatarPodPreviewScene::GetBackgroundColor() const
{
	return FLinearColor::Black;
}

void FAvatarPodPreviewScene::ChangePreviewScene(FSoftObjectPath WorldPath)
{
	FScopedSlowTask ExploreFoldersTask(1, INVTEXT("Loading PreviewMap ... "));
	ExploreFoldersTask.MakeDialog();

	PreviewMap = WorldPath;
	if (!LevelInstanceActor)
	{
		LevelInstanceActor = PreviewWorld->SpawnActor<ALevelInstance>(ALevelInstance::StaticClass(), FTransform::Identity);
		if (LevelInstanceActor)
		{
			LevelInstanceActor->SetWorldAsset(TSoftObjectPtr<UWorld>(WorldPath));
			LevelInstanceActor->LoadLevelInstance();
			LevelInstanceActor->PostEditChange();
			LevelInstanceActor->PostEditMove(true);
			LevelInstanceActor->InvalidateLightingCache();
		}
	}
	else
	{
		LevelInstanceActor->SetWorldAsset(TSoftObjectPtr<UWorld>(WorldPath));
		LevelInstanceActor->UpdateLevelInstanceFromWorldAsset();
	}

	ExploreFoldersTask.EnterProgressFrame();
}

FSoftObjectPath FAvatarPodPreviewScene::GetCurrentWorldPath()
{
	return PreviewMap;
}
