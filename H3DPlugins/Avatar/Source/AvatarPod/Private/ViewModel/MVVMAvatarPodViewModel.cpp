// Fill out your copyright notice in the Description page of Project Settings.


#include "ViewModel/MVVMAvatarPodViewModel.h"

void UMVVMAvatarPodViewModel::SetWardrobeComponent(class UWardrobeComponent* InWardrobeComponent)
{
	if (UE_MVVM_SET_PROPERTY_VALUE(WardrobeComponent, InWardrobeComponent))
	{
		UE_MVVM_BROADCAST_FIELD_VALUE_CHANGED(IsWardrobeValid);
	}
}

void UMVVMAvatarPodViewModel::PutOnGarments_Implementation(const TArray<FAssetData>& AssetDatas)
{
	if (WardrobeComponent)
	{
		TArray<UGarment*> GarmentsToWear;
		for (const FAssetData& AssetData : AssetDatas)
		{
			if (AssetData.GetClass() == UGarment::StaticClass())
			{
				if (UGarment* Garment = Cast<UGarment>(AssetData.GetAsset()))
				{
					GarmentsToWear.Add(Garment);
				}
			}
		}
		WardrobeComponent->PutOnGarmentAssets(GarmentsToWear);

		TArray<FAssetData> GarmentAssets;
		for (UGarment* AllGarment : WardrobeComponent->GetAllGarments())
		{
			GarmentAssets.Add(FAssetData(AllGarment));
		}
		if (UE_MVVM_SET_PROPERTY_VALUE(WearingGarments, GarmentAssets))
		{
			//UE_MVVM_BROADCAST_FIELD_VALUE_CHANGED(GetWearingGarments);
		}
	}
}

void UMVVMAvatarPodViewModel::SetCabinetComponent(UCabinetComponent* InCabinetComponent)
{
	if (UE_MVVM_SET_PROPERTY_VALUE(CabinetComponent, InCabinetComponent))
	{
		UE_MVVM_BROADCAST_FIELD_VALUE_CHANGED(IsCabinetValid);
	}
}

void UMVVMAvatarPodViewModel::K2_PutOnAccessories(const TArray<FAssetData>& AssetDatas)
{
	if (CabinetComponent)
	{
		TArray<UAccessory*> AccessoriesToWear;
		for (const FAssetData& AssetData : AssetDatas)
		{
			if (AssetData.GetClass() == UAccessory::StaticClass())
			{
				if (UAccessory* Accessory = Cast<UAccessory>(AssetData.GetAsset()))
				{
					CabinetComponent->PutOn(Accessory);
				}
			}
		}
	}
}

// void UMVVMAvatarPodViewModel::PutOnGarments(const TArray<FAssetData>& AssetDatas)
// {
// 	if (WardrobeComponent)
// 	{
// 		TArray<UGarment*> GarmentsToWear;
// 		for (const FAssetData& AssetData : AssetDatas)
// 		{
// 			if (AssetData.GetClass() == UGarment::StaticClass())		
// 			{
// 				UGarment* Garment = Cast<UGarment>(AssetData.GetAsset());
// 				if (Garment)
// 				{
// 					GarmentsToWear.Add(Garment);
// 				}
// 			}
// 		}
// 		WardrobeComponent->PutOnGarmentAssets(GarmentsToWear);
//
// 		TArray<FAssetData> GarmentAssets;
// 		for (UGarment* AllGarment : WardrobeComponent->GetAllGarments())
// 		{
// 			GarmentAssets.Add(FAssetData(AllGarment));
// 		}
// 		if (UE_MVVM_SET_PROPERTY_VALUE(WearingGarments, GarmentAssets))
// 		{
// 			//UE_MVVM_BROADCAST_FIELD_VALUE_CHANGED(GetWearingGarments);
// 		}
// 	}
// }
