<!-- This file was generated by UnrealBuildTool.ProjectFileGenerator.CreateRulesAssemblyProject() -->
<Project Sdk="Microsoft.NET.Sdk">
  <Import Project="..\..\..\..\..\..\trunk\ue_engine\Windows\Engine\Source\Programs\Shared\UnrealEngine.csproj.props" />
  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <Configurations>Debug;Release;Development</Configurations>
    <DefineConstants>$(DefineConstants);WITH_FORWARDED_MODULE_RULES_CTOR;WITH_FORWARDED_TARGET_RULES_CTOR;UE_4_17_OR_LATER;UE_4_18_OR_LATER;UE_4_19_OR_LATER;UE_4_20_OR_LATER;UE_4_21_OR_LATER;UE_4_22_OR_LATER;UE_4_23_OR_LATER;UE_4_24_OR_LATER;UE_4_25_OR_LATER;UE_4_26_OR_LATER;UE_4_27_OR_LATER;UE_4_28_OR_LATER;UE_4_29_OR_LATER;UE_4_30_OR_LATER;UE_5_0_OR_LATER;UE_5_1_OR_LATER;UE_5_2_OR_LATER;UE_5_3_OR_LATER;UE_5_4_OR_LATER</DefineConstants>
  </PropertyGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\..\..\..\..\trunk\ue_engine\Windows\Engine\Intermediate\Build\BuildRulesProjects\MarketplaceRules\MarketplaceRules.csproj"><Private>false</Private></ProjectReference>
    <ProjectReference Include="..\..\..\..\..\..\trunk\ue_engine\Windows\Engine\Intermediate\Build\BuildRulesProjects\UE5ProgramRules\UE5ProgramRules.csproj"><Private>false</Private></ProjectReference>
    <ProjectReference Include="..\..\..\..\..\..\trunk\ue_engine\Windows\Engine\Intermediate\Build\BuildRulesProjects\UE5Rules\UE5Rules.csproj"><Private>false</Private></ProjectReference>
    <ProjectReference Include="..\..\..\..\..\..\trunk\ue_engine\Windows\Engine\Source\Programs\Shared\EpicGames.Build\EpicGames.Build.csproj"><Private>false</Private></ProjectReference>
    <ProjectReference Include="..\..\..\..\..\..\trunk\ue_engine\Windows\Engine\Source\Programs\UnrealBuildTool\UnrealBuildTool.csproj"><Private>false</Private></ProjectReference>
  </ItemGroup>
  <ItemGroup>
  <Compile Include="..\..\..\..\Plugins\Developer\RiderLink\Source\RD\RD.Build.cs"><Link>Plugins\Developer\RiderLink\Source\RD\RD.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Developer\RiderLink\Source\RiderBlueprint\RiderBlueprint.Build.cs"><Link>Plugins\Developer\RiderLink\Source\RiderBlueprint\RiderBlueprint.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Developer\RiderLink\Source\RiderDebuggerSupport\RiderDebuggerSupport.Build.cs"><Link>Plugins\Developer\RiderLink\Source\RiderDebuggerSupport\RiderDebuggerSupport.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Developer\RiderLink\Source\RiderGameControl\RiderGameControl.Build.cs"><Link>Plugins\Developer\RiderLink\Source\RiderGameControl\RiderGameControl.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Developer\RiderLink\Source\RiderLC\RiderLC.Build.cs"><Link>Plugins\Developer\RiderLink\Source\RiderLC\RiderLC.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Developer\RiderLink\Source\RiderLink\RiderLink.Build.cs"><Link>Plugins\Developer\RiderLink\Source\RiderLink\RiderLink.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Developer\RiderLink\Source\RiderLogging\RiderLogging.Build.cs"><Link>Plugins\Developer\RiderLink\Source\RiderLogging\RiderLogging.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Developer\RiderLink\Source\RiderShaderInfo\RiderShaderInfo.Build.cs"><Link>Plugins\Developer\RiderLink\Source\RiderShaderInfo\RiderShaderInfo.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\HBS\AITemp\RodinDemo\Source\Rodin\Rodin.Build.cs"><Link>Plugins\HBS\AITemp\RodinDemo\Source\Rodin\Rodin.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\HBS\AITemp\RuntimeFBXImport\Source\RuntimeFBXImport\RuntimeFBXImport.Build.cs"><Link>Plugins\HBS\AITemp\RuntimeFBXImport\Source\RuntimeFBXImport\RuntimeFBXImport.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\HBS\HomesteadBuildingSystem\Source\HBSEditor\HBSEditor.Build.cs"><Link>Plugins\HBS\HomesteadBuildingSystem\Source\HBSEditor\HBSEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\HBS\HomesteadBuildingSystem\Source\HomesteadBuildingSystem\HomesteadBuildingSystem.Build.cs"><Link>Plugins\HBS\HomesteadBuildingSystem\Source\HomesteadBuildingSystem\HomesteadBuildingSystem.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\HomeBuildUGC.Target.cs"><Link>Source\HomeBuildUGC.Target.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\HomeBuildUGCEditor.Target.cs"><Link>Source\HomeBuildUGCEditor.Target.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\HomeBuildUGC\HomeBuildUGC.Build.cs"><Link>Source\HomeBuildUGC\HomeBuildUGC.Build.cs</Link></Compile>
  </ItemGroup>
</Project>
