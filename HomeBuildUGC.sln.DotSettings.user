<wpf:ResourceDictionary xml:space="preserve" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:s="clr-namespace:System;assembly=mscorlib" xmlns:ss="urn:shemas-jetbrains-com:settings-storage-xaml" xmlns:wpf="http://schemas.microsoft.com/winfx/2006/xaml/presentation">
	<s:String x:Key="/Default/CodeInspection/ExcludedFiles/FilesAndFoldersToSkip2/=7020124F_002D9FFC_002D4AC3_002D8F3D_002DAAB8E0240759_002Ff_003ALuhacyq_002Easm_002Fl_003AC_0021_003FUsers_003Fwujianxuan_003FAppData_003FLocal_003FTemp_003FSandboxFiles_003FMecycyt_003FLuhacyq_002Easm/@EntryIndexedValue">ForceIncluded</s:String></wpf:ResourceDictionary>