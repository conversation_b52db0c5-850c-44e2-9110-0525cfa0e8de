#include "FurEditorTools.h"
#include "DetailLayoutBuilder.h"
#include "FurEditorToolkit.h"

#define LOCTEXT_NAMESPACE "FurEditorTools"

FFurDetails::FFurDetails(class FFurEditorToolkit& InFurEditor)
	:FurEditor(InFurEditor)
{
}

void FFurDetails::CustomizeDetails(IDetailLayoutBuilder& DetailBuilder)
{
	// IDetailCategoryBuilder& ImportSettingsCategory = DetailBuilder.EditCategory("FurSettings");
}

#undef LOCTEXT_NAMESPACE