{"FileVersion": 3, "Version": 1, "VersionName": "1.0", "FriendlyName": "UIFramework", "Description": "A framework to control UMG from server.", "Category": "UI", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "https://epicgames.com", "DocsURL": "", "MarketplaceURL": "", "SupportURL": "", "EnabledByDefault": false, "CanContainContent": true, "IsBetaVersion": false, "Installed": false, "Modules": [{"Name": "UIFramework", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}], "Plugins": [{"Name": "ModelViewViewModel", "Enabled": true}, {"Name": "LocalizableMessage", "Enabled": true}, {"Name": "StructUtils", "Enabled": true}]}