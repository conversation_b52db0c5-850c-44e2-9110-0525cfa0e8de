// Copyright Epic Games, Inc. All Rights Reserved.

#include "Graph/AnimNextGraph_EdGraphSchema.h"

#include "AnimNextRigVMAsset.h"
#include "AnimNextRigVMAssetEntry.h"
#include "Editor.h"

#define LOCTEXT_NAMESPACE "AnimNextGraph_EdGraphSchema"

void UAnimNextGraph_EdGraphSchema::TrySetDefaultValue(UEdGraphPin& InPin, const FString& InNewDefaultValue, bool bMarkAsModified) const
{
#if WITH_EDITOR
	if (GEditor)
	{
		GEditor->CancelTransaction(0);
	}
#endif

	FString UseDefaultValue;
	TObjectPtr<UObject> UseDefaultObject = nullptr;
	FText UseDefaultText;

	GetDefault<UEdGraphSchema_K2>()->GetPinDefaultValuesFromString(InPin.PinType, InPin.GetOwningNodeUnchecked(), InNewDefaultValue, UseDefaultValue, UseDefaultObject, UseDefaultText, /*bPreserveTextIdentity*/false);

	// Check the default value and make it an error if it's bogus
	if (GetDefault<UEdGraphSchema_K2>()->DefaultValueSimpleValidation(InPin.PinType, InPin.PinName, UseDefaultValue, UseDefaultObject, UseDefaultText))
	{
		InPin.DefaultObject = UseDefaultObject;
		InPin.DefaultValue = UseDefaultValue;
		InPin.DefaultTextValue = UseDefaultText;

		UEdGraphNode* Node = InPin.GetOwningNode();
		Node->PinDefaultValueChanged(&InPin);

		// If the default value is manually set then treat it as if the value was reset to default and remove the orphaned InPin
		if (InPin.bOrphanedPin && InPin.DoesDefaultValueMatchAutogenerated())
		{
			Node->PinConnectionListChanged(&InPin);
		}

		if (bMarkAsModified)
		{
			Node->MarkPackageDirty();
		}
	}
}

void UAnimNextGraph_EdGraphSchema::GetGraphDisplayInformation(const UEdGraph& Graph, /*out*/ FGraphDisplayInfo& DisplayInfo) const
{
	Super::GetGraphDisplayInformation(Graph, DisplayInfo);
	
	if(UAnimNextRigVMAssetEntry* AssetEntry = Cast<UAnimNextRigVMAssetEntry>(Graph.GetOuter()))
	{
		DisplayInfo.DisplayName = FText::Format(LOCTEXT("GraphTabTitleFormat", "{0}: {1}"), FText::FromName(AssetEntry->GetEntryName()), FText::FromName(AssetEntry->GetTypedOuter<UAnimNextRigVMAsset>()->GetFName()));
		DisplayInfo.Tooltip = FText::Format(LOCTEXT("GraphTabTooltipFormat", "{0} in:\n{1}"), FText::FromName(AssetEntry->GetEntryName()), FText::FromString(AssetEntry->GetTypedOuter<UAnimNextRigVMAsset>()->GetPathName()));
	}
}

#undef LOCTEXT_NAMESPACE