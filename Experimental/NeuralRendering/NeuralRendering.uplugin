{"FileVersion": 3, "Version": 0.1, "VersionName": "0.1", "FriendlyName": "Neural Rendering", "Description": "Enable neural rendering features including: neural post processing", "Category": "Experimental", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "https://epicgames.com", "DocsURL": "", "MarketplaceURL": "", "SupportURL": "", "EnabledByDefault": false, "CanContainContent": false, "IsBetaVersion": false, "IsExperimentalVersion": true, "Installed": false, "SupportedTargetPlatforms": ["Win64", "Linux", "<PERSON>"], "Modules": [{"Name": "NeuralPostProcessing", "Type": "RuntimeAndProgram", "LoadingPhase": "PostConfigInit", "PlatformAllowList": ["Win64", "Linux", "<PERSON>"]}], "Plugins": [{"Name": "NNERuntimeRDG", "Enabled": true}]}