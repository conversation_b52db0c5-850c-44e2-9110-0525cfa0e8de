// Copyright Epic Games, Inc. All Rights Reserved.

#include "PostProcess/PostProcessDownsampleWithCustomStencil.h"
#include "PixelShaderUtils.h"
#include "DataDrivenShaderPlatformInfo.h"
#include "SceneRendering.h"

namespace
{
const int32 GDownsampleWithCustomStencilTileSizeX = 8;
const int32 GDownsampleWithCustomStencilTileSizeY = 8;

BEGIN_SHADER_PARAMETER_STRUCT(FDownsampleWithCustomStencilParameters, )
	SHADER_PARAMETER_STRUCT_REF(FViewUniformShaderParameters, ViewUniformBuffer)
	SHADER_PARAMETER_STRUCT(FScreenPassTextureViewportParameters, Input)
	SHADER_PARAMETER_STRUCT(FScreenPassTextureViewportParameters, Output)
	SHADER_PARAMETER_RDG_TEXTURE(Texture2D, InputTexture)
	SHADER_PARAMETER_SAMPLER(SamplerState, InputSampler)
	SHADER_PARAMETER_RDG_TEXTURE_SRV(Texture2D<uint2>, CustomStencilTexture)
	SHADER_PARAMETER(FIntVector4, CustomStencilValues)
	SHADER_PARAMETER(FVector4f, CustomBloomScales)
END_SHADER_PARAMETER_STRUCT()

FDownsampleWithCustomStencilParameters GetDownsampleParameters(
	const FViewInfo& View,
	FScreenPassTexture Output,
	FScreenPassTexture Input,
	FRDGTextureSRVRef CustomStencil,
	const TArray<FCustomBloomScale>& CustomBloomScales)
{
	check(Output.IsValid());
	check(Input.IsValid());

	const FScreenPassTextureViewportParameters InputParameters = GetScreenPassTextureViewportParameters(FScreenPassTextureViewport(Input));
	const FScreenPassTextureViewportParameters OutputParameters = GetScreenPassTextureViewportParameters(FScreenPassTextureViewport(Output));

	FDownsampleWithCustomStencilParameters Parameters;
	Parameters.ViewUniformBuffer = View.ViewUniformBuffer;
	Parameters.Input = InputParameters;
	Parameters.Output = OutputParameters;
	Parameters.InputTexture = Input.Texture;
	Parameters.InputSampler = TStaticSamplerState<SF_Bilinear, AM_Clamp, AM_Clamp, AM_Clamp>::GetRHI();
	Parameters.CustomStencilTexture = CustomStencil;
	if (CustomBloomScales.Num() > 0)
	{
		Parameters.CustomStencilValues.X = CustomBloomScales[0].CustomStencilValue;
		Parameters.CustomBloomScales.X = CustomBloomScales[0].BloomScale;
	}
	if (CustomBloomScales.Num() > 1)
	{
		Parameters.CustomStencilValues.Y = CustomBloomScales[1].CustomStencilValue;
		Parameters.CustomBloomScales.Y = CustomBloomScales[1].BloomScale;
	}
	if (CustomBloomScales.Num() > 2)
	{
		Parameters.CustomStencilValues.Z = CustomBloomScales[2].CustomStencilValue;
		Parameters.CustomBloomScales.Z = CustomBloomScales[2].BloomScale;
	}
	if (CustomBloomScales.Num() > 3)
	{
		Parameters.CustomStencilValues.W = CustomBloomScales[3].CustomStencilValue;
		Parameters.CustomBloomScales.W = CustomBloomScales[3].BloomScale;
	}
	return Parameters;
}

class FDownsampleWithCustomStencilQualityDimension : SHADER_PERMUTATION_ENUM_CLASS("DOWNSAMPLE_QUALITY", EDownsampleQuality);
class FDownsampleWithCustomStencilDownsampleDisableDimension : SHADER_PERMUTATION_BOOL("DOWNSAMPLE_DISABLE");
using FDownsampleWithCustomStencilPermutationDomain = TShaderPermutationDomain<FDownsampleWithCustomStencilQualityDimension, FDownsampleWithCustomStencilDownsampleDisableDimension>;
	

class FDownsampleWithCustomStencilPS : public FGlobalShader
{
public:
	DECLARE_GLOBAL_SHADER(FDownsampleWithCustomStencilPS);
	SHADER_USE_PARAMETER_STRUCT(FDownsampleWithCustomStencilPS, FGlobalShader);

	using FPermutationDomain = FDownsampleWithCustomStencilPermutationDomain;

	BEGIN_SHADER_PARAMETER_STRUCT(FParameters, )
		SHADER_PARAMETER_STRUCT_INCLUDE(FDownsampleWithCustomStencilParameters, Common)
		RENDER_TARGET_BINDING_SLOTS()
	END_SHADER_PARAMETER_STRUCT()

	static bool ShouldCompilePermutation(const FGlobalShaderPermutationParameters& Parameters)
	{
		return true;
	}
};

IMPLEMENT_GLOBAL_SHADER(FDownsampleWithCustomStencilPS, "/Engine/Private/PostProcessDownsampleWithCustomStencil.usf", "MainPS", SF_Pixel);

class FDownsampleWithCustomStencilCS : public FGlobalShader
{
public:
	DECLARE_GLOBAL_SHADER(FDownsampleWithCustomStencilCS);
	SHADER_USE_PARAMETER_STRUCT(FDownsampleWithCustomStencilCS, FGlobalShader);

	using FPermutationDomain = FDownsampleWithCustomStencilPermutationDomain;

	BEGIN_SHADER_PARAMETER_STRUCT(FParameters, )
		SHADER_PARAMETER_STRUCT_INCLUDE(FDownsampleWithCustomStencilParameters, Common)
		SHADER_PARAMETER(FScreenTransform, DispatchThreadIdToInputUV)
		SHADER_PARAMETER_RDG_TEXTURE_UAV(RWTexture2D<float4>, OutComputeTexture)
	END_SHADER_PARAMETER_STRUCT()

	static bool ShouldCompilePermutation(const FGlobalShaderPermutationParameters& Parameters)
	{
		return IsFeatureLevelSupported(Parameters.Platform, ERHIFeatureLevel::SM5);
	}

	static void ModifyCompilationEnvironment(const FGlobalShaderPermutationParameters& Parameters, FShaderCompilerEnvironment& OutEnvironment)
	{
		FGlobalShader::ModifyCompilationEnvironment(Parameters,OutEnvironment);
		OutEnvironment.SetDefine(TEXT("THREADGROUP_SIZEX"), GDownsampleWithCustomStencilTileSizeX);
		OutEnvironment.SetDefine(TEXT("THREADGROUP_SIZEY"), GDownsampleWithCustomStencilTileSizeY);
	}
};

IMPLEMENT_GLOBAL_SHADER(FDownsampleWithCustomStencilCS, "/Engine/Private/PostProcessDownsampleWithCustomStencil.usf", "MainCS", SF_Compute);
} //! namespace

TArray<FCustomBloomScale> ComposeCustomBloomSettings(const FFinalPostProcessSettings& FinalPostProcessSettings)
{
	TArray<FCustomBloomScale> CustomBloomScales;
	if (FinalPostProcessSettings.CustomBloomScales.IsValidIndex(0))
	{
		CustomBloomScales.Add(FinalPostProcessSettings.CustomBloomScales[0]);
	}
	if (FinalPostProcessSettings.CustomBloomScales.IsValidIndex(1))
	{
		CustomBloomScales.Add(FinalPostProcessSettings.CustomBloomScales[1]);
	}

	if (FinalPostProcessSettings.CustomBloomStencil1 > 0)
	{
		CustomBloomScales.Add({FinalPostProcessSettings.CustomBloomStencil1, FinalPostProcessSettings.CustomBloomIntensity1});
	}

	if (FinalPostProcessSettings.CustomBloomStencil2 > 0)
	{
		CustomBloomScales.Add({FinalPostProcessSettings.CustomBloomStencil2, FinalPostProcessSettings.CustomBloomIntensity2});
	}

	return CustomBloomScales;
}

FScreenPassTextureSlice AddDownsamplePassWithCustomStencil(
	FRDGBuilder& GraphBuilder,
	const FViewInfo& View,
	const FDownsamplePassInputs& Inputs,
	FRDGTextureSRVRef CustomStencil,
	const TArray<FCustomBloomScale>& CustomBloomScales,
	bool bDownsampleDisable)
{
	check(Inputs.SceneColor.IsValid());

	bool bIsComputePass = View.bUseComputePasses;

	if ((Inputs.Flags & EDownsampleFlags::ForceRaster) == EDownsampleFlags::ForceRaster)
	{
		bIsComputePass = false;
	}

	FScreenPassRenderTarget Output;

	// Construct the output texture to be half resolution (rounded up to even) with an optional format override.
	{
		FRDGTextureDesc Desc = Inputs.SceneColor.TextureSRV->Desc.Texture->Desc;
		Desc.Reset();
		Desc.Extent = bDownsampleDisable ? Desc.Extent : FIntPoint::DivideAndRoundUp(Desc.Extent, 2);
		Desc.Extent.X = FMath::Max(1, Desc.Extent.X);
		Desc.Extent.Y = FMath::Max(1, Desc.Extent.Y);
		Desc.Flags &= ~(TexCreate_RenderTargetable | TexCreate_UAV | TexCreate_Presentable);
		Desc.Flags |= bIsComputePass ? TexCreate_UAV : (TexCreate_RenderTargetable | TexCreate_NoFastClear);
		Desc.Flags |= GFastVRamConfig.Downsample;
		Desc.ClearValue = FClearValueBinding(FLinearColor(0, 0, 0, 0));

		if (Inputs.FormatOverride != PF_Unknown)
		{
			Desc.Format = Inputs.FormatOverride;
		}

		if (Inputs.UserSuppliedOutput && Translate(Inputs.UserSuppliedOutput->GetDesc()) == Desc)
		{
			Output.Texture = GraphBuilder.RegisterExternalTexture(Inputs.UserSuppliedOutput, Inputs.Name);
		}
		else
		{
			Output.Texture = GraphBuilder.CreateTexture(Desc, Inputs.Name);
		}
		Output.ViewRect = bDownsampleDisable ? Inputs.SceneColor.ViewRect : FIntRect::DivideAndRoundUp(Inputs.SceneColor.ViewRect, 2);
		Output.LoadAction = ERenderTargetLoadAction::ENoAction;
	}

	if (bIsComputePass)
	{
		FDownsampleWithCustomStencilCS::FParameters* PassParameters = GraphBuilder.AllocParameters<FDownsampleWithCustomStencilCS::FParameters>();
		PassParameters->Common = GetDownsampleParameters(View, Output, FScreenPassTexture(Inputs.SceneColor), CustomStencil, CustomBloomScales);
		PassParameters->DispatchThreadIdToInputUV = ((FScreenTransform::Identity + 0.5f) / Output.ViewRect.Size()) * FScreenTransform::ChangeTextureBasisFromTo(FScreenPassTextureViewport(Inputs.SceneColor), FScreenTransform::ETextureBasis::ViewportUV, FScreenTransform::ETextureBasis::TextureUV);
		PassParameters->OutComputeTexture = GraphBuilder.CreateUAV(Output.Texture);

		FDownsampleWithCustomStencilPermutationDomain PermutationVector;
		PermutationVector.Set<FDownsampleWithCustomStencilQualityDimension>(Inputs.Quality);
		PermutationVector.Set<FDownsampleWithCustomStencilDownsampleDisableDimension>(bDownsampleDisable);

		TShaderMapRef<FDownsampleWithCustomStencilCS> ComputeShader(View.ShaderMap, PermutationVector);
		FComputeShaderUtils::AddPass(
			GraphBuilder,
			RDG_EVENT_NAME("Downsample(%s Quality=%s CS) %dx%d -> %dx%d",
				Output.Texture->Name,
				Inputs.Quality == EDownsampleQuality::High ? TEXT("High") : TEXT("Bilinear"),
				Inputs.SceneColor.ViewRect.Width(), Inputs.SceneColor.ViewRect.Height(),
				Output.ViewRect.Width(), Output.ViewRect.Height()),
			ERDGPassFlags::Compute,
			ComputeShader,
			PassParameters,
			FComputeShaderUtils::GetGroupCount(Output.ViewRect.Size(), FIntPoint(GDownsampleWithCustomStencilTileSizeX, GDownsampleWithCustomStencilTileSizeY)));
	}
	else
	{
		FDownsampleWithCustomStencilPermutationDomain PermutationVector;
		PermutationVector.Set<FDownsampleWithCustomStencilQualityDimension>(Inputs.Quality);
		PermutationVector.Set<FDownsampleWithCustomStencilDownsampleDisableDimension>(bDownsampleDisable);

		FDownsampleWithCustomStencilPS::FParameters* PassParameters = GraphBuilder.AllocParameters<FDownsampleWithCustomStencilPS::FParameters>();
		PassParameters->Common = GetDownsampleParameters(View, Output, FScreenPassTexture(Inputs.SceneColor), CustomStencil, CustomBloomScales);
		PassParameters->RenderTargets[0] = Output.GetRenderTargetBinding();

		TShaderMapRef<FDownsampleWithCustomStencilPS> PixelShader(View.ShaderMap, PermutationVector);
		FPixelShaderUtils::AddFullscreenPass(
			GraphBuilder,
			View.ShaderMap,
			RDG_EVENT_NAME("Downsample(%s Quality=%s PS) %dx%d -> %dx%d",
				Output.Texture->Name,
				Inputs.Quality == EDownsampleQuality::High ? TEXT("High") : TEXT("Bilinear"),
				Inputs.SceneColor.ViewRect.Width(), Inputs.SceneColor.ViewRect.Height(),
				Output.ViewRect.Width(), Output.ViewRect.Height()),
			PixelShader,
			PassParameters,
			Output.ViewRect);
	}

	return (FScreenPassTextureSlice::CreateFromScreenPassTexture(GraphBuilder, MoveTemp(Output)));
}
