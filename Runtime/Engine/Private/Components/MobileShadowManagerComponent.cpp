// Copyright Epic Games, Inc. All Rights Reserved.

#include "Components/MobileShadowManagerComponent.h"
#include "Components/BillboardComponent.h"
#include "Engine/Texture2D.h"
#include "Engine/World.h"
#include "SceneInterface.h"
#include "UObject/ConstructorHelpers.h"

#include UE_INLINE_GENERATED_CPP_BY_NAME(MobileShadowManagerComponent)

#define LOCTEXT_NAMESPACE "MobileShadowManagerComponent"


UMobileShadowManagerComponent::UMobileShadowManagerComponent(const FObjectInitializer& ObjectInitializer)
	: Super(ObjectInitializer)
{

}

void UMobileShadowManagerComponent::CreateRenderState_Concurrent(FRegisterComponentContext* Context)
{
	Super::CreateRenderState_Concurrent(Context);
	
	GetWorld()->Scene->SetShadowGroupResScale(HighResShadowGroup.HighResShadowGroupIndex, 2.0f);
}

void UMobileShadowManagerComponent::DestroyRenderState_Concurrent()
{
	Super::DestroyRenderState_Concurrent();

	GetWorld()->Scene->ResetShadowGroupResScales();
}



void UMobileShadowManagerComponent::SetHighResShadowGroup(int32 NewHighResShadowGroupIndex)
{
	if (NewHighResShadowGroupIndex != HighResShadowGroup.HighResShadowGroupIndex)
	{
		HighResShadowGroup.HighResShadowGroupIndex = NewHighResShadowGroupIndex;
		RecreateRenderState_Concurrent();
	}
}




AMobileShadowManager::AMobileShadowManager(const FObjectInitializer& ObjectInitializer)
	: Super(ObjectInitializer)
{
	MobileShadowManagerComponent = CreateDefaultSubobject<UMobileShadowManagerComponent>(TEXT("MobileShadowManagerComponent"));
	RootComponent = MobileShadowManagerComponent;

#if WITH_EDITORONLY_DATA
	if (!IsRunningCommandlet())
	{
		// Structure to hold one-time initialization
		struct FConstructorStatics
		{
			ConstructorHelpers::FObjectFinderOptional<UTexture2D> MobileShadowManagerTextureObject;
			FName ID_MobileShadowManager;
			FText NAME_MobileShadowManager;
			FConstructorStatics()
				: MobileShadowManagerTextureObject(TEXT("/Engine/EditorResources/S_SkyAtmosphere"))
				, ID_MobileShadowManager(TEXT("MobileShadowManager"))
				, NAME_MobileShadowManager(NSLOCTEXT("SpriteCategory", "MobileShadowManager", "MobileShadowManager"))
			{
			}
		};
		static FConstructorStatics ConstructorStatics;

		if (GetSpriteComponent())
		{
			GetSpriteComponent()->Sprite = ConstructorStatics.MobileShadowManagerTextureObject.Get();
			GetSpriteComponent()->SetRelativeScale3D(FVector(0.5f, 0.5f, 0.5f));
			GetSpriteComponent()->SpriteInfo.Category = ConstructorStatics.ID_MobileShadowManager;
			GetSpriteComponent()->SpriteInfo.DisplayName = ConstructorStatics.NAME_MobileShadowManager;
			GetSpriteComponent()->SetupAttachment(MobileShadowManagerComponent);
		}		
	}
#endif // WITH_EDITORONLY_DATA

	PrimaryActorTick.bCanEverTick = true;
	SetHidden(false);
}

#undef LOCTEXT_NAMESPACE



