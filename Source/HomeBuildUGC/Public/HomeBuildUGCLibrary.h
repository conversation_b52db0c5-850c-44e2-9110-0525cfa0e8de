// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "HomeBuildUGCLibrary.generated.h"

/**
 *
 */
UCLASS()
class HOMEBUILDUGC_API UHomeBuildUGCLibrary : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()


public:
	UFUNCTION(BlueprintCallable, Category = "HomeBuildUGC")
	static UObject* GetDefaultObjectFromBlueprint(UObject* BlueprintObject)
	{
		UBlueprint* Blueprint = Cast<UBlueprint>(BlueprintObject);
		if (Blueprint && Blueprint->GeneratedClass)
		{
			return Blueprint->GeneratedClass->GetDefaultObject();
		}
		return nullptr;
	}
};
