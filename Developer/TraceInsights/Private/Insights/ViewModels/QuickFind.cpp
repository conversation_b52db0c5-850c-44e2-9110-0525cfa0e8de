// Copyright Epic Games, Inc. All Rights Reserved.

#include "QuickFind.h"

namespace Insights
{

////////////////////////////////////////////////////////////////////////////////////////////////////

FQuickFind::FQuickFind(TSharedPtr<FFilterConfigurator> InFilterConfiguratorViewModel)
{
	FilterConfigurator = InFilterConfiguratorViewModel;
}

////////////////////////////////////////////////////////////////////////////////////////////////////

FQuickFind::~FQuickFind()
{
	OnDestroyedEvent.Broadcast();
}

////////////////////////////////////////////////////////////////////////////////////////////////////

} // namespace Insights