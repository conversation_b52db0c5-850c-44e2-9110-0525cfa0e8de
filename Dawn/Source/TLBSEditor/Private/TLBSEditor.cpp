#include "TLBSEditor.h"
#include "TLBSEditorStyle.h"
#include "TLBSEditorCommands.h"
#include "LevelEditor.h"
#include "Widgets/Docking/SDockTab.h"
#include "Widgets/Layout/SBox.h"
#include "Widgets/Text/STextBlock.h"
#include "Misc/ScopedSlowTask.h"
#include "Widgets/Notifications/SNotificationList.h"
#include "Framework/Notifications/NotificationManager.h"
#include "Framework/MultiBox/MultiBoxBuilder.h"
#include "LightingBuildOptions.h"
#include "EditorBuildUtils.h"
#include "Async/Async.h"

#include "PackageTools.h"
#include "Engine/World.h"
#include "TutorialMetaData.h"
#include "UnrealEdGlobals.h"
#include "Editor/UnrealEdEngine.h"
#include "FeedbackContextEditor.h"
#include "Dialogs/SBuildProgress.h"
#include "ViewVisibilityPrimitivesData.h"
#include "TLBSBase.h"
#include "PropertyEditorModule.h"
#include "Components/LightProbeComponent.h"
#include "Components/RayTracingDebugComponent.h"
#include "Editor.h"
#include "ISettingsModule.h"
#include "SceneViewExtension.h"
#include "SceneRendering.h"
#include "ScenePrivate.h"
#include "HAL/ConsoleManager.h"
#include "Interfaces/IPluginManager.h"

#include "DawnUI.h"
#include "DawnSDKSetting.h"
#include "DawnDeamonUtil.h"
#include "Version.h"
#include "DawnCommonMacros.h"
#include "EngineUtils.h"
#include "Components/LocalLightComponent.h"
#include "Engine/StaticMeshActor.h"
#include "Engine/RendererSettings.h"
#include "AssetScanUtils.h"
#include "DawnAssetHelper.h"
#include "AssetScanWidget.h"
#include "CoreGlobals.h"
#include "HAL/PlatformFileManager.h"
#include "Misc/ConfigCacheIni.h"
#include "Framework/Application/SlateApplication.h"

#define LOCTEXT_NAMESPACE "FTLBSEditorModule"

DEFINE_LOG_CATEGORY_STATIC(DawnClearCachedDataLog, Log, All);
DEFINE_LOG_CATEGORY_STATIC(DawnCustomizationLog, Log, All);

// 注册设置菜单
static void RegisterSettings()
{
	FDawnUIModule::RegisterNewSettingSection("Project", "MagicDawn", "DawnQualityPreview",
		LOCTEXT("DawnSettingsPreviewName", "Dawn Quality Preview"),
		LOCTEXT("DawnSettingsPreviewDescription", "Preview Quality Setting for Dawn GPU Baking"),
		GetMutableDefault<UDawnSettingsPreview>());
	FDawnUIModule::RegisterNewSettingSection("Project", "MagicDawn", "DawnQualityMedium",
		LOCTEXT("DawnSettingsMediumName", "Dawn Quality Medium"),
		LOCTEXT("DawnSettingsMediumDescription", "Medium Quality Setting for Dawn GPU Baking"),
		GetMutableDefault<UDawnSettingsMedium>());
	FDawnUIModule::RegisterNewSettingSection("Project", "MagicDawn", "DawnQualityHigh",
		LOCTEXT("DawnSettingsHighName", "Dawn Quality High"),
		LOCTEXT("DawnSettingsHighDescription", "High Quality Setting for Dawn GPU Baking"),
		GetMutableDefault<UDawnSettingsHigh>());
	FDawnUIModule::RegisterNewSettingSection("Project", "MagicDawn", "DawnQualityProduction",
		LOCTEXT("DawnSettingsProductionName", "Dawn Quality Production"),
		LOCTEXT("DawnSettingsProductionDescription", "Production Quality Setting for Dawn GPU Baking"),
		GetMutableDefault<UDawnSettingsProduction>());
	FDawnUIModule::RegisterNewSettingSection("Project", "MagicDawn", "DawnStandaloneSettings",
		LOCTEXT("DawnStandaloneSettingsName", "Dawn Standalone Settings"),
		LOCTEXT("DawnStandaloneSettingsDescription", "Standalone Settings for Dawn GPU Baking that irrelevant to bake quality"),
		GetMutableDefault<UDawnStandaloneSettings>());
}

// 注销设置菜单
static void UnregisterSettings()
{

}

class FSceneVisibilityViewExtension : public FSceneViewExtensionBase
{
public:
	FSceneVisibilityViewExtension(const FAutoRegister& AutoRegister, const FDawnBuildOptions& InDawnOptions)
		: FSceneViewExtensionBase(AutoRegister), DawnBuildOptions(InDawnOptions) {

	}
public:
	virtual void SetupViewFamily(FSceneViewFamily& InViewFamily) {}
	virtual void SetupView(FSceneViewFamily& InViewFamily, FSceneView& InView) {}
	virtual void BeginRenderViewFamily(FSceneViewFamily& InViewFamily) {}
	virtual void PreRenderViewFamily_RenderThread(FRHICommandListImmediate& RHICmdList, FSceneViewFamily& InViewFamily) {}
	virtual void PreRenderView_RenderThread(FRHICommandListImmediate& RHICmdList, FSceneView& InView) {}
	virtual int32 GetPriority() const { return 0; }
	// Store those components who is in the field of the View
	virtual void PostRenderView_RenderThread(FRHICommandListImmediate& RHICmdList, FSceneView& InView) {
		return;
		//Not Surpport InteractiveMode
#if 0
		if (!InView.bIsViewInfo || DawnBuildOptions.BakingMode != EDawnBakingMode::InteractiveMode) {
			return;
		}

		// Limit the execution frequency of PostRenderView_RenderThread(), and update the statement at least every "TimeInterval" seconds
		{
			const static double TimeInterval = 1.0;
			static double LastRenderThreadUpdateTimestamp = 0.0;
			double CurrentTimestamp = FDateTime::UtcNow().ToUnixTimestamp();
			if (CurrentTimestamp - LastRenderThreadUpdateTimestamp < TimeInterval) return;
			LastRenderThreadUpdateTimestamp = CurrentTimestamp;
		}

		TMap< FPrimitiveComponentId, uint32> PrimitiveVisibilityComponentIds;
		TArray< FPrimitiveComponentId> NewPrimitiveVisibilityComponentIds;
		FViewInfo& View = static_cast<FViewInfo&>(InView);
		FScene* Scene = View.Family->Scene->GetRenderScene();

		// Get all FPrimitiveComponentId that need to refactor lighting
		TSet<FPrimitiveComponentId> PrimitivesWithUnbuiltInteractions;

		if (!ViewVisibilityPrimitivesData.bRebuildObjectsThatHaveBeenBuilt)
		{
			for (TSparseArray<FLightSceneInfoCompact>::TConstIterator It(Scene->Lights); It; ++It)
			{
				const FLightSceneInfoCompact& LightCompactInfo = *It;
				FLightSceneInfo* LightSceneInfo = LightCompactInfo.LightSceneInfo;

				for (FLightPrimitiveInteraction* Interaction = LightSceneInfo->DynamicInteractionStaticPrimitiveList;
					Interaction;
					Interaction = Interaction->GetNextPrimitive())
				{
					if (Interaction->IsUncachedStaticLighting())
					{
						auto PrimitiveSceneInfo = Interaction->GetPrimitiveSceneInfo();
						check(PrimitiveSceneInfo);
						PrimitivesWithUnbuiltInteractions.Add(PrimitiveSceneInfo->PrimitiveComponentId);
					}
				}
			}
		}

		const TArray< FPrimitiveComponentId>& PrimitiveComponentIds = Scene->PrimitiveComponentIds;
		for (FSceneSetBitIterator BitIt(View.PrimitiveVisibilityMap); BitIt; ++BitIt)
		{
			uint32 PrimitiveIndex = BitIt.GetIndex();
			bool bVisible = View.PrimitiveVisibilityMap.AccessCorrespondingBit(BitIt);
			if (bVisible && Scene->GetPrimitiveSceneInfo(PrimitiveIndex)->StaticMeshes.Num() > 0) 
			{
				const FPrimitiveComponentId& PrimId = PrimitiveComponentIds[PrimitiveIndex];
				if (!ViewVisibilityPrimitivesData.bRebuildObjectsThatHaveBeenBuilt && !PrimitivesWithUnbuiltInteractions.Find(PrimId)) continue;
				PrimitiveVisibilityComponentIds.Add(PrimId, GFrameNumber);
				NewPrimitiveVisibilityComponentIds.Add(PrimId);
			}
		}
		ViewVisibilityPrimitivesData.Update_RenderThread(View, PrimitiveVisibilityComponentIds, NewPrimitiveVisibilityComponentIds);
#endif // 0
	}

public:
	const FDawnBuildOptions& DawnBuildOptions;
	FViewVisibilityPrimitivesData ViewVisibilityPrimitivesData;
};

// 启动模块
void FTLBSEditorModule::StartupModule()
{
	bIsCleaningCache = false;

	int32 quality;
	if (GConfig->GetInt(TEXT("DawnQualityLevel"), TEXT("QualityLevel"), quality, GEditorIni))
		DawnBuildOptions.QualityLevel = (EDawnQualityLevel)quality;

	// This code will execute after your module is loaded into memory; the exact timing is specified in the .uplugin file per-module
	FTLBSEditorStyle::Initialize();
	FTLBSEditorStyle::ReloadTextures();

	// 注册UICommand
	FTLBSEditorCommands::Register();

	// 注册Setting界面
	RegisterSettings();

	FDawnUIModule::BeginSection("DawnBaker", LOCTEXT("BakerHeading", "Dawn Baker"));
	FDawnUIModule::AddButton_1(this, FTLBSEditorCommands::Get().BakingWithTLBS, &FTLBSEditorModule::StartBaking, EDawnBakingMode::FullBuild);
	//FDawnUIModule::AddButton_1(this, FTLBSEditorCommands::Get().BakingWithInteractive, &FTLBSEditorModule::StartBaking, EDawnBakingMode::InteractiveMode);
	FDawnUIModule::AddButton_1(this, FTLBSEditorCommands::Get().BakingOnlySelectedActors, &FTLBSEditorModule::StartBaking, EDawnBakingMode::BakingSelectedActors);
	FDawnUIModule::AddButton_1(this, FTLBSEditorCommands::Get().BakingOnlySelectedAreas, &FTLBSEditorModule::StartBaking, EDawnBakingMode::BakingDawnAreas);
	// CTG Begin, gabbrotang: Bake probe only
	FDawnUIModule::AddButton_1(this, FTLBSEditorCommands::Get().BakingOnlyProbes, &FTLBSEditorModule::StartBaking, EDawnBakingMode::BakingOnlyProbes);
	// CTG End
	FDawnUIModule::EndSection();

	// CTG begin, marcusjzhou Asset Scan
	FDawnUIModule::BeginSection("DawnAssetScan", LOCTEXT("AssetScanHeading", "Dawn Asset Scan"));
	FDawnUIModule::AddButton_0(this, FTLBSEditorCommands::Get().StartAssetScan, &FTLBSEditorModule::AssetScanPluginButtonClicked);
	FDawnUIModule::AddButton_0(this, FTLBSEditorCommands::Get().DawnAssetHelper, &FTLBSEditorModule::DawnAssetHelperButtonClicked);
	FDawnUIModule::EndSection();
	// CTG begin, marcusjzhou Asset Scan

	FDawnUIModule::BeginSection("DawnBaker Options", LOCTEXT("DawnBakerOptionsHeading", "Dawn Baker Options"));
	{
		FDawnUIModule::BeginSubMenu(LOCTEXT("DawnQualitySubMenu", "Baking Quality"), LOCTEXT("DawnQualitySubMenu_ToolTip", "You could select baking quality level of DawnBaker"));
		FDawnUIModule::BeginSection("DawnQuality", LOCTEXT("DawnQualityHeading", "Quality Level"));
		FDawnUIModule::AddCheckButton_1_1(this, FTLBSEditorCommands::Get().DawnQuality_Production, &FTLBSEditorModule::SelectBakingQuality, EDawnQualityLevel::Production, &FTLBSEditorModule::IsBakingQualitySelected, EDawnQualityLevel::Production);
		FDawnUIModule::AddCheckButton_1_1(this, FTLBSEditorCommands::Get().DawnQuality_High, &FTLBSEditorModule::SelectBakingQuality, EDawnQualityLevel::High, &FTLBSEditorModule::IsBakingQualitySelected, EDawnQualityLevel::High);
		FDawnUIModule::AddCheckButton_1_1(this, FTLBSEditorCommands::Get().DawnQuality_Medium, &FTLBSEditorModule::SelectBakingQuality, EDawnQualityLevel::Medium, &FTLBSEditorModule::IsBakingQualitySelected, EDawnQualityLevel::Medium);
		FDawnUIModule::AddCheckButton_1_1(this, FTLBSEditorCommands::Get().DawnQuality_Preview, &FTLBSEditorModule::SelectBakingQuality, EDawnQualityLevel::Preview, &FTLBSEditorModule::IsBakingQualitySelected, EDawnQualityLevel::Preview);
		FDawnUIModule::EndSection();
		FDawnUIModule::EndSubMenu();
	}
	{
		//FDawnUIModule::BeginSubMenu(LOCTEXT("ChangeDawnSettingSubMenu", "Change DawnBaker Setting"), LOCTEXT("ChangeDawnSettingSubMenu_ToolTip", "You can change the settings of Dawn GPU light baking"));
		//FDawnUIModule::BeginSection("ChangeDawnSetting", LOCTEXT("ChangeDawnSettingHeading", "Configurable settings"));
		FDawnUIModule::AddButton_0(this, FTLBSEditorCommands::Get().ChangeSetting, &FTLBSEditorModule::ChangeQualitySetting);
		//FDawnUIModule::AddButton_0(this, FTLBSEditorCommands::Get().ChangeStandaloneSettings, &FTLBSEditorModule::ChangeStandaloneSettings);
		//FDawnUIModule::EndSection();
		//FDawnUIModule::EndSubMenu();
	}

	// CTG Begin, superqqli: PRT20
#if ENABLE_PRTLQ_API
	{
		FDawnUIModule::BeginSubMenu(LOCTEXT("DawnPRT20SettingsSubMenu", "Dawn PRT"), LOCTEXT("DawnPRT20SettingsSubMenu_ToolTip", "You can change the settings of Dawn PRT20"));
		FDawnUIModule::BeginSection("DawnPRT20Settings", LOCTEXT("DawnPRT20SettingsHeading", "Configurable settings"));
		FDawnUIModule::AddButton_0(this, FTLBSEditorCommands::Get().SetPrtBakingModeAuto, &FTLBSEditorModule::SetPrtLQBakingMode);
		FDawnUIModule::AddButton_1(this, FTLBSEditorCommands::Get().SetPrtBakingModePrtOnly, &FTLBSEditorModule::ResetPrtBakingMode, EPrtBakingMode::PrtOnly);
		FDawnUIModule::AddButton_1(this, FTLBSEditorCommands::Get().SetPrtBakingModePrtAndStatic, &FTLBSEditorModule::ResetPrtBakingMode, EPrtBakingMode::PrtAndStatic);
		FDawnUIModule::EndSection();
		FDawnUIModule::EndSubMenu();
	}
#endif // ENABLE_PRTLQ_API
	// CTG End

	FDawnUIModule::AddButton_0(this, FTLBSEditorCommands::Get().ClearCachedData, &FTLBSEditorModule::ClearCachedData);
	FDawnUIModule::AddButton_0(this, FTLBSEditorCommands::Get().RequireLightingUnbuiltObjects, &FTLBSEditorModule::RequireLightingUnbuiltObjects);
	FDawnUIModule::AddButton_0(this, FTLBSEditorCommands::Get().ShowVersion, &FTLBSEditorModule::ShowVersion);
	FDawnUIModule::EndSection();

	FDawnUIModule::RegisterDelegate_WhichNeedToBeDoneInPostEngineInit(DawnUINeedToBeDoneDelegate::CreateLambda([this]()->void {
		if (GUnrealEd != nullptr) {
			GUnrealEd->RegisterComponentVisualizer(ULightProbeComponent::StaticClass()->GetFName(), MakeShareable(new FLightProbeVisualizer));
			GUnrealEd->RegisterComponentVisualizer(URayTracingDebugComponent::StaticClass()->GetFName(), MakeShareable(new FRayTracingDebugVisualizer));

			ViewExtension = FSceneViewExtensions::NewExtension<FSceneVisibilityViewExtension>(this->DawnBuildOptions);
		}

		{
			auto CVar = IConsoleManager::Get().FindConsoleVariable(TEXT("r.Shadow.WholeSceneShadowUnbuiltInteractionThreshold"));
			if (CVar) {
				CVar->Set(TNumericLimits<int32>::Max());
			}
		}

		}));

	FDawnUIModule::RegisterDelegate_WhichNeedToBeDoneInPostEngineInit(DawnUINeedToBeDoneDelegate::CreateRaw(this, &FTLBSEditorModule::RegisterDetailCustomization));

	// CTG-marcusjzhou begin: asset scan
	FGlobalTabmanager::Get()->RegisterNomadTabSpawner(SAssetScanWidget::DawnAssetScanTabName, FOnSpawnTab::CreateRaw(this, &FTLBSEditorModule::OnAssetScanSpawnPluginTab))
		.SetDisplayName(LOCTEXT("FResourceCheckTabTitle", "资产扫描"))
		.SetMenuType(ETabSpawnerMenuType::Hidden);
	// CTG-marcusjzhou end

	FindAndLoadCustomizedFilePack();
}

// CTG-marcusjzhou begin: asset scan
TSharedRef<SDockTab> FTLBSEditorModule::OnAssetScanSpawnPluginTab(const FSpawnTabArgs& SpawnTabArgs)
{
	return SNew(SDockTab)
		.TabRole(ETabRole::NomadTab)
		.ShouldAutosize(false)
		[
			SNew(SBox)
				.HAlign(HAlign_Left)
				.VAlign(VAlign_Center)
				[
					SNew(SAssetScanWidget)
				]
		];
}

void FTLBSEditorModule::AssetScanPluginButtonClicked()
{
	FGlobalTabmanager::Get()->TryInvokeTab(SAssetScanWidget::DawnAssetScanTabName);
}

void FTLBSEditorModule::DawnAssetHelperButtonClicked()
{
	TSharedRef<SDawnAssetHelperWidget> MyWidget = SNew(SDawnAssetHelperWidget);

	TSharedRef<SWindow> DawnAssetHelperWindow = SNew(SWindow)
		.Title(FText::FromString(TEXT("Dawn资产处理")))
		.ClientSize(FVector2D(512, 400))
		.SupportsMinimize(false)
		.SupportsMaximize(false)
		[
			MyWidget
		];
	FSlateApplication::Get().AddWindow(DawnAssetHelperWindow);
}
// CTG-marcusjzhou end

//void FTLBSEditorModule::ClearCachedData()
//{
//	FText DialogText = LOCTEXT("ClearCachedData", "Are you sure you want to delete all cached data?");
//	EAppReturnType::Type ReturnType = FMessageDialog::Open(EAppMsgType::OkCancel, DialogText);
//	if (ReturnType == EAppReturnType::Type::Ok)
//	{
//		FString CacheFolderPath = FPaths::ConvertRelativePathToFull(IPluginManager::Get().FindPlugin(TEXT("MagicDawn"))->GetBaseDir() + "/Binaries/DotNET/Dawn/Win64/TLBSCache");
//		
//		//UE_LOG(LogTemp, Display, TEXT("%s"), *CacheFolderPath);
//		IPlatformFile& fileManager = FPlatformFileManager::Get().GetPlatformFile();
//
//		FString JobsCacheFolderPath = CacheFolderPath + "/Jobs";
//
//		fileManager.DeleteDirectoryRecursively(*JobsCacheFolderPath);
//
//		TArray<FString> files;
//		fileManager.FindFiles(files, *CacheFolderPath, nullptr);
//		for (const auto& file : files)
//		{
//			UE_LOG(DawnClearCachedDataLog, Display, TEXT("Remove file: %s"), *file);
//			fileManager.DeleteFile(*file);
//		}
//	}
//}

void FTLBSEditorModule::ShutdownModule()
{
	// This function may be called during shutdown to clean up your module.  For modules that support dynamic reloading,
	// we call this function before unloading the module.
	FTLBSEditorStyle::Shutdown();

	FTLBSEditorCommands::Unregister();

	UnregisterSettings();

	/*
	FLevelEditorModule& LevelEditorModule = FModuleManager::LoadModuleChecked<FLevelEditorModule>("LevelEditor");
	LevelEditorModule.OnMapChanged().RemoveAll(this);
	*/

	FDawnUIModule::RemoveAllBindings(this);

	if (GUnrealEd != nullptr) {
		GUnrealEd->UnregisterComponentVisualizer(ULightProbeComponent::StaticClass()->GetFName());
	}

	// CTG-marcusjzhou begin: asset scan
	FGlobalTabmanager::Get()->UnregisterNomadTabSpawner(SAssetScanWidget::DawnAssetScanTabName);
	// CTG-marcusjzhou end

	UnregisterDetailCustomization();
}
// 改变Dawn的Setting
void FTLBSEditorModule::ChangeQualitySetting()
{
	FName SectionNames[] = {
		FName("DawnQualityPreview"),
		FName("DawnQualityMedium"),
		FName("DawnQualityHigh"),
		FName("DawnQualityProduction"),
		//FName("DawnQualityInteractive"),
	};
	//FModuleManager::LoadModuleChecked<ISettingsModule>("Settings").ShowViewer(FName("Project"), FName("Plugins"), SectionNames[(int)DawnBuildOptions.QualityLevel]);
	FModuleManager::LoadModuleChecked<ISettingsModule>("Settings").ShowViewer(FName("Project"), FName("MagicDawn"), SectionNames[(int)DawnBuildOptions.QualityLevel]);
}

void FTLBSEditorModule::ChangeStandaloneSettings()
{
	FModuleManager::LoadModuleChecked<ISettingsModule>("Settings").ShowViewer(FName("Project"), FName("MagicDawn"), FName("DawnStandaloneSettings"));
}

// 清理TLBSCache
void FTLBSEditorModule::ClearCachedData()
{
	if (bIsCleaningCache) return;

	FString CacheFolderPath = FPaths::ConvertRelativePathToFull(IPluginManager::Get().FindPlugin(TEXT("MagicDawn"))->GetBaseDir() + "/Binaries/TLBSCache");
	FString DialogTextContent = "Are you sure you want to delete all cached data in the folder \n" + CacheFolderPath;
	EAppReturnType::Type ReturnType = FMessageDialog::Open(EAppMsgType::OkCancel, FText::FromString(DialogTextContent));
	if (ReturnType == EAppReturnType::Type::Ok)
	{
		FNotificationInfo DuringInfo(FText::FromString("DawnBaker cleaning cache..."));
		DuringInfo.bFireAndForget = false;
		DuringInfo.ExpireDuration = 0.f;
		DuringInfo.bUseThrobber = true;
		TSharedPtr<SNotificationItem> DuringInfoPtr = FSlateNotificationManager::Get().AddNotification(DuringInfo);
		if (DuringInfoPtr.IsValid())
		{
			DuringInfoPtr->SetCompletionState(SNotificationItem::CS_Pending);
		}

		AsyncTask(ENamedThreads::AnyBackgroundThreadNormalTask, [CacheFolderPath, DuringInfoPtr, &bIsCleaningCache = this->bIsCleaningCache]()
			{
				bIsCleaningCache = true;

				FString JobsCacheFolderPath = CacheFolderPath + "/Jobs";
				IPlatformFile& FileManager = FPlatformFileManager::Get().GetPlatformFile();
				{
					//FScopedSlowTask SlowTask(2.f, LOCTEXT("Cleaning the jobs cache", "Cleaning the jobs cache..."));
					//SlowTask.MakeDialog();
					//SlowTask.EnterProgressFrame(1.f);
					FileManager.DeleteDirectoryRecursively(*JobsCacheFolderPath);
					FileManager.CreateDirectory(*JobsCacheFolderPath);
					//SlowTask.EnterProgressFrame();
				}

				{
					TArray<FString> Files;
					FileManager.FindFiles(Files, *CacheFolderPath, nullptr);

					//FScopedSlowTask SlowTask(Files.Num(), LOCTEXT("Cleaning the general cache", "Cleaning the general cache..."));
					//SlowTask.MakeDialog();
					int32 counter = 0;
					for (const auto& File : Files)
					{
						UE_LOG(DawnClearCachedDataLog, Verbose, TEXT("Remove File: %s"), *File);
						FileManager.DeleteFile(*File);
						//SlowTask.EnterProgressFrame(1);
					}
				}

				bIsCleaningCache = false;

				AsyncTask(ENamedThreads::GameThread, [DuringInfoPtr]()
					{
						if (DuringInfoPtr.IsValid())
						{
							DuringInfoPtr->SetText(FText::FromString("DawnBaker completes cache cleanup."));
							DuringInfoPtr->SetFadeInDuration(0.5f);
							DuringInfoPtr->SetExpireDuration(0.5f);
							DuringInfoPtr->SetCompletionState(SNotificationItem::CS_Success);

							DuringInfoPtr->ExpireAndFadeout();
						}
					});
			});	
	}
}

void FTLBSEditorModule::RegisterDetailCustomization()
{
	RegisterTLBSMaterialBakingDetailCustomization();
}

void FTLBSEditorModule::UnregisterDetailCustomization()
{
	UnregisterTLBSMaterialBakingDetailCustomization();
}

// CTG Begin, superqqli: PRT20
#if ENABLE_PRTLQ_API

void FTLBSEditorModule::SetPrtLQBakingMode()
{
	RETURN_IF(GEditor == nullptr);
	UWorld* World = GEditor->GetEditorWorldContext().World();
	RETURN_IF(World == nullptr);

	// Collect all local lights
	TArray<ULocalLightComponent*> LocalLightComponents;
	for (TActorIterator<AActor> It(World); It; ++It)
	{
		const AActor* Actor = *It;
		TArray<ULocalLightComponent*> Components;
		Actor->GetComponents<ULocalLightComponent>(Components);
		LocalLightComponents.Append(Components);
	}

	// Collect all StaticMesh
	TArray<UStaticMeshComponent*> StaticMeshComponents;
	for (TActorIterator<AActor> It(World); It; ++It)
	{
		AActor* Actor = *It;
		TArray<UStaticMeshComponent*> ActorStaticMeshComponents;
		Actor->GetComponents<UStaticMeshComponent>(ActorStaticMeshComponents);
		StaticMeshComponents.Append(ActorStaticMeshComponents);
	}
	int32 TotalStaticMeshComponents = StaticMeshComponents.Num();

	FScopedSlowTask SlowTask(100.f, FText::FromString("Setting PRT Baking Mode for Static Meshes..."));
	SlowTask.MakeDialog(true);

	// Iterate all StaticMesh
	int32 ProcessedStaticMeshComponents = 0;
	for (UStaticMeshComponent* StaticMeshComponent : StaticMeshComponents)
	{
		CONTINUE_IF(!StaticMeshComponent || !StaticMeshComponent->GetStaticMesh());

		bool bIsAffectedByLocalLight = false;
		const FBoxSphereBounds MeshBounds = StaticMeshComponent->Bounds;

		for (const ULocalLightComponent* LocalLightComponent : LocalLightComponents)
		{
			if (LocalLightComponent && (LocalLightComponent->Mobility == EComponentMobility::Type::Static || LocalLightComponent->Mobility == EComponentMobility::Type::Stationary)
				&& LocalLightComponent->AffectsIndirectBounds(MeshBounds))
			{
				bIsAffectedByLocalLight = true;
				break;
			}
		}

		StaticMeshComponent->PrtBakingMode = bIsAffectedByLocalLight ? EPrtBakingMode::PrtAndStatic : EPrtBakingMode::PrtOnly;

		ProcessedStaticMeshComponents++;
		SlowTask.EnterProgressFrame(100.f / TotalStaticMeshComponents, FText::Format(FText::FromString("Processing Static Meshes {0}/{1}"), ProcessedStaticMeshComponents, TotalStaticMeshComponents));
	}

	FText DialogText = FText::FromString(TEXT("PRT Baking Mode has been set for all Static Mesh Actors."));
	FMessageDialog::Open(EAppMsgType::Ok, DialogText);
}

void FTLBSEditorModule::ResetPrtBakingMode(EPrtBakingMode PrtBakingMode)
{
	RETURN_IF(GEditor == nullptr);
	UWorld* World = GEditor->GetEditorWorldContext().World();
	RETURN_IF(World == nullptr);
	
	// Collect all StaticMesh
	TArray<UStaticMeshComponent*> StaticMeshComponents;
	for (TActorIterator<AActor> It(World); It; ++It)
	{
		AActor* Actor = *It;
		TArray<UStaticMeshComponent*> ActorStaticMeshComponents;
		Actor->GetComponents<UStaticMeshComponent>(ActorStaticMeshComponents);
		StaticMeshComponents.Append(ActorStaticMeshComponents);
	}
	int32 TotalStaticMeshComponents = StaticMeshComponents.Num();

	FScopedSlowTask SlowTask(100.f, FText::FromString("Resetting PRT Baking Mode for Static Meshes..."));
	SlowTask.MakeDialog(true);
	
	// Iterate all StaticMesh
	int32 ProcessedStaticMeshComponents = 0;
	for (UStaticMeshComponent* StaticMeshComponent : StaticMeshComponents)
	{
		CONTINUE_IF(!StaticMeshComponent || !StaticMeshComponent->GetStaticMesh());

		StaticMeshComponent->PrtBakingMode = PrtBakingMode;

		ProcessedStaticMeshComponents++;
		SlowTask.EnterProgressFrame(100.f / TotalStaticMeshComponents, FText::Format(FText::FromString("Processing Static Meshes {0}/{1}"), ProcessedStaticMeshComponents, TotalStaticMeshComponents));
	}

	FText DialogText = FText::FromString(TEXT("PRT Baking Mode has been reset for all Static Mesh Actors."));
	FMessageDialog::Open(EAppMsgType::Ok, DialogText);
}

#endif // ENABLE_PRTLQ_API
// CTG End

void FTLBSEditorModule::RequireLightingUnbuiltObjects()
{
	UWorld* world = GWorld;

	if (world)
	{
		world->Scene->DumpUnbuiltLightInteractions(*GLog);
	}
}

void FTLBSEditorModule::FindAndLoadCustomizedFilePack()
{
	FString PackFolderPath = FPaths::ConvertRelativePathToFull(IPluginManager::Get().FindPlugin(TEXT("MagicDawn"))->GetBaseDir() + "/Binaries/LinuxPack");
	TArray<FString> FoundFiles;

	{
		IPlatformFile& FileManager = FPlatformFileManager::Get().GetPlatformFile();

		if (FileManager.DirectoryExists(*PackFolderPath))
		{
			FileManager.FindFiles(FoundFiles, *PackFolderPath, TEXT("tgz"));
		}
		else
		{
			UE_LOG(DawnCustomizationLog, Warning, TEXT("PackFolder Path does not exist, it should be at %s."), *PackFolderPath);
		}
	}

	{
		auto& DawnSettingsCustomization = GetMutableDefault<UDawnStandaloneSettings>()->Customization;

		bool ExePackFounded = false;
		bool FullPackFounded = false;

		for (const auto File : FoundFiles)
		{
			if (ExePackFounded && FullPackFounded) break;

			if (!ExePackFounded && File.Contains("_Exe"))
			{
				/* \Engine\Plugins\Dawn\Binaries\LinuxPack\Dawn2.0_Exe.tgz */ 
				DawnSettingsCustomization.CustomizedBakerExePackPaths = File;
				ExePackFounded = true;
			}
			else if (!FullPackFounded && File.Contains("_FullPack"))
			{
				/* \Engine\Plugins\Dawn\Binaries\LinuxPack\Dawn2.0_FullPack.tgz */ 
				DawnSettingsCustomization.CustomizedBakerFullPackPaths = File;
				FullPackFounded = true;
			}
		}
	}
}

// 根据选择的烘焙模式进行烘焙
void FTLBSEditorModule::StartBaking(EDawnBakingMode InBakingMode)
{
	if (bIsCleaningCache)
	{
		FMessageDialog::Open(EAppMsgType::Ok, FText::FromString("Dawn is cleaning the cache.\nPlease wait until it is completed and retry."));
		return;
	}

	// 预检测
	{
		const auto DawnSDKSettings = GetDefault<UDawnSDKSetting>();
		const auto& DawnSettingsCustomization = GetDefault<UDawnStandaloneSettings>()->Customization;

		if (DawnSDKSettings->BranchTag == "")
		{
			FMessageDialog::Open(EAppMsgType::Ok, FText::FromString("\"Branch Tag\" in DawnSDK settings is EMPTY!"));
			return;
		}

		// 检查是否包含无效的特殊字符
		auto IsValidFileName = [](const FString& FileName) -> bool
		{
			static const FString InvalidChars = TEXT("\"*/:<>?\\|"); // 无效的特殊字符
			for (const TCHAR& Char : InvalidChars)
			{
				if (FileName.Contains(&Char))
				{
					return false;
				}
			}
			return true;
		};

		if (!IsValidFileName(DawnSDKSettings->BranchTag))
		{
			FMessageDialog::Open(EAppMsgType::Ok, FText::FromString("\"Branch Tag\" " + DawnSDKSettings->BranchTag + " in DawnSDK settings contain invalid char \"*/:<>?\\|"));
			return;
		}

		if (DawnSettingsCustomization.UseCustomizedBaker && DawnSDKSettings->BranchTag == "custom")
		{
			bool IsValidFile = true;
			FString ErrorMsg;

			FString CustomizedBakerPackPathStr = DawnSettingsCustomization.UseFullPack ?
				DawnSettingsCustomization.CustomizedBakerFullPackPaths :
				DawnSettingsCustomization.CustomizedBakerExePackPaths;

			if (FPaths::IsRelative(CustomizedBakerPackPathStr))
			{
				CustomizedBakerPackPathStr = FPaths::ConvertRelativePathToFull(CustomizedBakerPackPathStr);
			}

			if (IsValidFile && !FPaths::FileExists(CustomizedBakerPackPathStr))
			{
				IsValidFile = false;
				ErrorMsg = "The specified Pack File \"" + CustomizedBakerPackPathStr + "\" does not exist";
			}

			FString ext = FPaths::GetExtension(CustomizedBakerPackPathStr);
			if (IsValidFile && (ext != "tgz" && ext != "tar.gz"))
			{
				IsValidFile = false;
				ErrorMsg = "The specified Pack File extension \"" + ext + "\" of \"" + CustomizedBakerPackPathStr + "\" is not tgz or tar.gz";
			}

			// -------

			if (!IsValidFile)
			{
				FMessageDialog::Open(EAppMsgType::Ok, FText::FromString(ErrorMsg + "\n\n-- Baking is stopped --"));
				return;
			}
		}

		// Check if Enable Static Lighting
		const URendererSettings* EngineRenderSettings = GetDefault<URendererSettings>();
		if (EngineRenderSettings)
		{
			if (!EngineRenderSettings->bAllowStaticLighting)
			{
				FMessageDialog::Open(EAppMsgType::Ok, FText::FromString("Please Open EnableStaticLighting and Rebake"));
				return;
			}
		}
	}

	if (IsLightingBuildCurrentlyRunning())
	{
		return;
	}

	NDawnSDK::DawnDeamonUtil::LaunchDawnDeamon(false);

	DawnBuildOptions.BakingMode = InBakingMode;

	SetDawnBuildOptions(DawnBuildOptions);

	if (DawnBuildOptions.BakingMode == EDawnBakingMode::InteractiveMode) {

		const auto& InteractiveSettings = GetDefault<UDawnStandaloneSettings>()->Interactive;
		ViewExtension->ViewVisibilityPrimitivesData.Reset();
		ViewExtension->ViewVisibilityPrimitivesData.bRebuildObjectsThatHaveBeenBuilt = InteractiveSettings.RebuildObjectsThatHaveBeenBuilt;
		FlushRenderingCommands();
	}

	if (IsDawnBound())
	{
		UnbindDawn();
	}
	// check(!IsDawnBound());

	BindDawn(ViewExtension->ViewVisibilityPrimitivesData);

	if (BuildLighting_CanExecute())
	{
		BuildLightingOnly_Execute();
	}
}

// 添加[窗口]菜单的按钮
void FTLBSEditorModule::AddMenuExtension(FMenuBuilder& Builder)
{
	Builder.AddMenuEntry(FTLBSEditorCommands::Get().BakingWithTLBS);
}

// 添加[工具栏]的按钮
void FTLBSEditorModule::AddToolbarExtension(FToolBarBuilder& Builder)
{
	// 主按钮
	Builder.AddToolBarButton(FTLBSEditorCommands::Get().BakingWithTLBS);

	// 下拉菜单
	Builder.AddComboButton(
		FUIAction(),
		FOnGetContent::CreateRaw(this, &FTLBSEditorModule::GenerateOptionMenu, PluginCommands),
		LOCTEXT("BuildCombo_Label", "Dawn Options"),
		LOCTEXT("BuildComboToolTip", "Dawn options menu"),
		TAttribute<FSlateIcon>(),
		true
	);

}

// 生成[工具栏]按钮下拉菜单中[选择渲染质量]的子菜单
static void MakeQualityMenu(FMenuBuilder& builder)
{
	builder.BeginSection("DawnQuality", LOCTEXT("DawnQualityHeading", "Quality Level"));
	{
		builder.AddMenuEntry(FTLBSEditorCommands::Get().DawnQuality_Production);
		builder.AddMenuEntry(FTLBSEditorCommands::Get().DawnQuality_High);
		builder.AddMenuEntry(FTLBSEditorCommands::Get().DawnQuality_Medium);
		builder.AddMenuEntry(FTLBSEditorCommands::Get().DawnQuality_Preview);
	}
	builder.EndSection();
}

// 生成[工具栏]按钮下拉菜单
TSharedRef<SWidget> FTLBSEditorModule::GenerateOptionMenu(TSharedPtr<class FUICommandList> Commands)
{
	FMenuBuilder MenuBuilder(true, Commands);

	MenuBuilder.BeginSection("DawnSetting", LOCTEXT("DawnSettingyHeading", "Setting"));
	MenuBuilder.AddSubMenu(
		LOCTEXT("DawnQualitySubMenu", "Baking Quality"),
		LOCTEXT("DawnQualitySubMenu_ToolTip", "Allows you to select quality level for Dawn GPU light baking"),
		FNewMenuDelegate::CreateStatic(&MakeQualityMenu));

	MenuBuilder.AddMenuEntry(FTLBSEditorCommands::Get().ChangeSetting);
	MenuBuilder.AddMenuEntry(FTLBSEditorCommands::Get().ClearCachedData);
	MenuBuilder.EndSection();

	MenuBuilder.BeginSection("Baking", LOCTEXT("BakingHeading", "Baking"));
	MenuBuilder.AddMenuEntry(FTLBSEditorCommands::Get().BakingWithInteractive);
	MenuBuilder.AddMenuEntry(FTLBSEditorCommands::Get().BakingOnlySelectedActors);
	MenuBuilder.AddMenuEntry(FTLBSEditorCommands::Get().BakingOnlySelectedAreas);
	// CTG Begin, gabbrotang: Bake probe only
	MenuBuilder.AddMenuEntry(FTLBSEditorCommands::Get().BakingOnlyProbes);
	// CTG End
	MenuBuilder.EndSection();
	
	MenuBuilder.BeginSection("DawnVersion", LOCTEXT("VersionHeading", UE_EDITOR_VERSION_DISPLAY));
	MenuBuilder.AddMenuEntry(FTLBSEditorCommands::Get().ShowVersion);
	MenuBuilder.EndSection();

	return MenuBuilder.MakeWidget();
}

bool FTLBSEditorModule::CanBuildLighting()
{
	// Building lighting modifies the BuildData package, which the PIE session will also be referencing without getting notified
	return !(GEditor->PlayWorld || GUnrealEd->bIsSimulatingInEditor);
}

bool FTLBSEditorModule::CanBuildReflectionCaptures()
{
	// Building reflection captures modifies the BuildData package, which the PIE session will also be referencing without getting notified
	return !(GEditor->PlayWorld || GUnrealEd->bIsSimulatingInEditor)
		// Build reflection captures requires SM5.  Don't allow building when previewing other feature levels.
		&& GEditor->GetEditorWorldContext().World()->GetFeatureLevel() >= ERHIFeatureLevel::SM5;
}

bool FTLBSEditorModule::BuildLighting_CanExecute()
{
	static const auto AllowStaticLightingVar = IConsoleManager::Get().FindTConsoleVariableDataInt(TEXT("r.AllowStaticLighting"));
	const bool bAllowStaticLighting = (!AllowStaticLightingVar || AllowStaticLightingVar->GetValueOnGameThread() != 0);

	return bAllowStaticLighting && CanBuildLighting();
}

void FTLBSEditorModule::BuildLightingOnly_Execute()
{
	FLightingBuildOptions BuildOption;
	// Reset build options
	ConfigureLightingBuildOptions(BuildOption);

	// Build lighting!
	const bool bAllowLightingDialog = false;
	FEditorBuildUtils::EditorBuild(
		GEditor->GetEditorWorldContext().World(),
		FBuildOptions::BuildLighting, bAllowLightingDialog);
}

void FTLBSEditorModule::ConfigureLightingBuildOptions(const FLightingBuildOptions& Options)
{
	GConfig->SetBool(TEXT("LightingBuildOptions"), TEXT("OnlyBuildSelected"), Options.bOnlyBuildSelected, GEditorPerProjectIni);
	GConfig->SetBool(TEXT("LightingBuildOptions"), TEXT("OnlyBuildCurrentLevel"), Options.bOnlyBuildCurrentLevel, GEditorPerProjectIni);
	GConfig->SetBool(TEXT("LightingBuildOptions"), TEXT("OnlyBuildSelectedLevels"), Options.bOnlyBuildSelectedLevels, GEditorPerProjectIni);
	GConfig->SetBool(TEXT("LightingBuildOptions"), TEXT("OnlyBuildVisibility"), Options.bOnlyBuildVisibility, GEditorPerProjectIni);
}

void FTLBSEditorModule::SelectBakingQuality(EDawnQualityLevel Quality)
{
	DawnBuildOptions.QualityLevel = Quality;
	GConfig->SetInt(TEXT("DawnQualityLevel"), TEXT("QualityLevel"), (int32)Quality, GEditorIni);
}

#undef LOCTEXT_NAMESPACE

IMPLEMENT_MODULE(FTLBSEditorModule, TLBSEditor)