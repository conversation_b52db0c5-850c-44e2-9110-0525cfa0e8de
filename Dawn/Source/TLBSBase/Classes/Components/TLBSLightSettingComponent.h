// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Components/SceneComponent.h"
#include "TLBSLightSettingComponent.generated.h"


UCLASS( ClassGroup=(Dawn), meta=(BlueprintSpawnableComponent) )
class TLBSBASE_API UDawnLightSettingComponent : public UActorComponent
{
	GENERATED_BODY()

public:	
	UPROPERTY(EditAnywhere,Category = DawnSetting)
	float ImportantFactor;

	UPROPERTY(EditAnywhere, Category = DawnSetting)
	float CustomAttenuationFraction;

	UPROPERTY(EditAnywhere, Category = DawnSetting)
	float MeshLightAttenuationRadius;

	UPROPERTY(Category = BakingAreasForSkylight, EditAnywhere)
	bool isOverrideForBakingAreas = false;
	/** How often this component is allowed to move, override SkyLightComponent, for Dawn baking select areas. */
	UPROPERTY(Category = BakingAreasForSkylight, EditAnywhere, BlueprintReadOnly)
	TEnumAsByte<EComponentMobility::Type> Mobility;

	// Sets default values for this component's properties
	UDawnLightSettingComponent();

public:
};
