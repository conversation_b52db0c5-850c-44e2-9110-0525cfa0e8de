#include "MFLightProbeFilter.h"
#include "Misc/FileHelper.h"
// #include "MFTools/MFPerLevelBuildDataManager.h"
// #include "MFTools/MFDawnDataManager.h"

extern int32 ComputeLinearVoxelIndexTLBS(FIntVector VoxelCoordinate, FIntVector VolumeDimensions);

float FMFLightProbeFilter::GetSkyVisibility(const GPUBAKING_NS_NAME_Dawn20::FImportedVolumetricLightmapBrick& Brick, int LinearDestCellIndex)
{
	float SkyVisibility = 0.0f;
	if (Brick.SkyBentNormal.Num() > 0)
	{
		const FLinearColor SkyBentNormalUnpacked = Brick.SkyBentNormal[LinearDestCellIndex].ReinterpretAsLinear();

		const FVector SkyBentNormal = FVector(SkyBentNormalUnpacked.R * 2.0f - 1.0f, SkyBentNormalUnpacked.G * 2.0f - 1.0f, SkyBentNormalUnpacked.B * 2.0f - 1.0f) * (SkyBentNormalUnpacked.A * SkyBentNormalUnpacked.A);
		SkyVisibility = SkyBentNormal.Size();
	}
	return SkyVisibility;
}

void FMFLightProbeFilter::FindInsideGeometryProbe(
	TArray<struct FImportedVolumetricLightmapTaskData>& ImportedVolumetricLightmapTaskData,
	int		BrickSize, UWorld* world,
	bool UseEmbree)
{
	int32 PaddedBrickSize = BrickSize + 1;

	for (int32 TaskDataIndex = 0; TaskDataIndex < ImportedVolumetricLightmapTaskData.Num(); TaskDataIndex++)
	{
		FImportedVolumetricLightmapTaskData& TaskData = ImportedVolumetricLightmapTaskData[TaskDataIndex];
		for (int32 BrickIndex = 0; BrickIndex < TaskData.Bricks.Num(); BrickIndex++)
		{
			GPUBAKING_NS_NAME_Dawn20::FImportedVolumetricLightmapBrick& Brick = TaskData.Bricks[BrickIndex];

			static float MinSkyVisibility = 0.05f;

			for (int32 Z = 0; Z < BrickSize; Z++)
			{
				for (int32 Y = 0; Y < BrickSize; Y++)
				{
					for (int32 X = 0; X < BrickSize; X++)
					{
						FIntVector VoxelCoordinate(X, Y, Z);
						const int32 LinearVoxelIndex = ComputeLinearVoxelIndexTLBS(VoxelCoordinate, FIntVector(BrickSize, BrickSize, BrickSize));

						TArray<GPUBAKING_NS_NAME_Dawn20::FIrradianceVoxelImportProcessingData>& VoxelImportProcessingData = Brick.TaskVoxelImportProcessingData;
						GPUBAKING_NS_NAME_Dawn20::FIrradianceVoxelImportProcessingData& VoxelImportData = VoxelImportProcessingData[LinearVoxelIndex];

						FVector3f CellWorldPos = VoxelImportData.PositionAndRadius;

						float SkyVisibility = GetSkyVisibility(Brick, LinearVoxelIndex);
						
						if (!VoxelImportData.bInsideGeometry)
						{
							if (SkyVisibility < MinSkyVisibility/* && !VoxelImportData.bCompletelyInside*/ && ComputeInsideGeometryProbe(FVector(CellWorldPos), world, UseEmbree))
							{
								VoxelImportData.bInsideGeometry = true;
							}
						}
						
					}
				}
			}
		}
	}
}