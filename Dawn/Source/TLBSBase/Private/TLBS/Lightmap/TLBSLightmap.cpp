#include "TLBSLightmap.h"
#include "Components/PrimitiveComponent.h"
#include "Components/InstancedStaticMeshComponent.h"
#include "Components/LightComponent.h"
#include "Engine/Level.h"
#include "Misc/QueuedThreadPool.h"
#include "ShadowMap.h"
#include "Engine/ShadowMapTexture2D.h"
#include "UnrealEngine.h"
#include "Interfaces/ITargetPlatform.h"
#include "RenderUtils.h"
#include "StaticLighting.h"
#include "UObject/RenderingObjectVersion.h"
#include "UObject/UObjectIterator.h"
#include "Misc/FeedbackContext.h"
#include "GameFramework/WorldSettings.h"
#include "Engine/MapBuildDataRegistry.h"
#include "VT/LightmapVirtualTexture.h"
#include "VT/VirtualTexture.h"
#include "EngineModule.h"
#include "ProfilingDebugging/LoadTimeTracker.h"
#include "TextureResource.h"
#include "DataDrivenShaderPlatformInfo.h"
#include "TLBS/TLBSCommon.h"
#include "TLBS/TLBSLightingPrivate.h"
#include "TLBS/TLBSProcessor.h"
#include "StaticMeshComponentLODInfo.h"
#include "Engine/InstancedStaticMesh.h"
#include "LandscapeLight.h"
#include "Async/ParallelFor.h"

extern ENGINE_API bool GAllowStreamingLightmaps;
extern ENGINE_API bool GAllowLightmapPadding;
extern ENGINE_API bool GVisualizeLightmapTextures;
extern ENGINE_API bool GCompressLightmaps;
extern ENGINE_API int32 GLightmapCounter;

#define UNGROUP_MESH_HASH 0

static TAutoConsoleVariable<int32> CVarLightmapGroupSize(
	TEXT("r.LightmapGroupSize"),
	0,
	TEXT("Lightmap Group Actor Afffect Size"));

//#pragma optimize("", off)

#if ENABLE_PRTHQ_API
// CTG Begin, PRT20 HQ
struct FPackedTransfer16FColor {
	FFloat16 R, G, B, A;

	FPackedTransfer16FColor(const FFloat16& InR, const FFloat16& InG, const FFloat16& InB) :R(InR), G(InG), B(InB), A(1.0f) {
	}
	FPackedTransfer16FColor(const FLinearColor& InColor) :R(InColor.R), G(InColor.G), B(InColor.B), A(InColor.A) {
	}
	
	FLinearColor ReinterpretAsLinear() const {
		return FLinearColor(R, G, B, A);
	}
};

struct FPackedTransferRGBEColor {
	FColor Color;

	FPackedTransferRGBEColor(const FFloat16& InR, const FFloat16& InG, const FFloat16& InB) {
		Color = FLinearColor(InR, InG, InB, 1.0f).ToRGBE();
	}
	FPackedTransferRGBEColor(const FLinearColor& InColor) {
		Color = InColor.ToRGBE();
	}
	FLinearColor ReinterpretAsLinear() const {
		return Color.FromRGBE();
	}
};

struct FPackedTransferRGBAColor {
	FColor Color;

	FPackedTransferRGBAColor(const FFloat16& InR, const FFloat16& InG, const FFloat16& InB) {
		Color = FLinearColor(InR, InG, InB, 1.0f).QuantizeRound();
	}
	FPackedTransferRGBAColor(const FLinearColor& InColor) {
		Color = InColor.QuantizeRound();
	}
	FLinearColor ReinterpretAsLinear() const {
		return Color.ReinterpretAsLinear();
	}
};

template<typename PackedTransferColorType>
static void PreEncodeTransferCoefficientTexture(FTLBSLightMapPendingTexture* PendingTexture, int32 CoefficientIndex, UTexture* Texture, uint32 LayerIndex, const FColor& TextureColor, bool Compression, bool CompressionNoAlpha)
{
	const int32 NumMips = Texture->Source.GetNumMips();
	check(NumMips > 0);

	FTextureFormatSettings FormatSettings;
	FormatSettings.SRGB = false;
	FormatSettings.CompressionNoAlpha = true;
	if (Compression)
		FormatSettings.CompressionNoAlpha = CompressionNoAlpha;
	FormatSettings.CompressionNone = !Compression;
	//FormatSettings.bForcePVRTC4 = true;
	Texture->SetLayerFormatSettings(LayerIndex, FormatSettings);
	Texture->LossyCompressionAmount = TLCA_None;

	const int32 TextureSizeX = Texture->Source.GetSizeX();
	const int32 TextureSizeY = Texture->Source.GetSizeY();

	// Lock all mip levels.
	PackedTransferColorType** MipData0 = (PackedTransferColorType**)PendingTexture->PrtHQMipData[CoefficientIndex].MipData;
	PackedTransferColorType** MipData1 = (PackedTransferColorType**)PendingTexture->PrtHQMipData[CoefficientIndex + 2].MipData;
	int8** MipCoverageData0 = PendingTexture->PrtHQMipData[CoefficientIndex].MipCoverageData;
	int8** MipCoverageData1 = PendingTexture->PrtHQMipData[CoefficientIndex + 2].MipCoverageData;

	for (int32 MipIndex = 0; MipIndex < NumMips; ++MipIndex)
	{
		const int32 MipSizeX = FMath::Max(1, TextureSizeX >> MipIndex);
		const int32 MipSizeY = FMath::Max(1, TextureSizeY >> MipIndex);
		const int32 MipStartBottom = MipSizeX * (MipSizeY >> 1);

		MipData0[MipIndex] = (PackedTransferColorType*)Texture->Source.LockMip(0, LayerIndex, MipIndex);
		MipCoverageData0[MipIndex] = (int8*)FMemory::Malloc(MipSizeX * MipSizeY);

		// 2 coefficients are stored on the top/bottom halves of the same destination texture
		MipData1[MipIndex] = MipData0[MipIndex] + MipStartBottom;
		MipCoverageData1[MipIndex] = MipCoverageData0[MipIndex] + MipStartBottom;
	}
	// Create the uncompressed top mip-level.
	FMemory::Memzero(MipData0[0], TextureSizeX * TextureSizeY * sizeof(PackedTransferColorType));
	FMemory::Memzero(MipCoverageData0[0], TextureSizeX * TextureSizeY);
}

template<typename PackedTransferColorType>
static void PreEncode10rdTransferCoefficientTexture(FTLBSLightMapPendingTexture* PendingTexture, UTexture* Texture, uint32 LayerIndex, const FColor& TextureColor, bool Compression, bool CompressionNoAlpha)
{
	const int32 NumMips = Texture->Source.GetNumMips();
	check(NumMips > 0);

	FTextureFormatSettings FormatSettings;
	FormatSettings.SRGB = false;
	FormatSettings.CompressionNoAlpha = true;
	if (Compression)
		FormatSettings.CompressionNoAlpha = CompressionNoAlpha;
	FormatSettings.CompressionNone = !Compression;
	//FormatSettings.bForcePVRTC4 = true;
	Texture->SetLayerFormatSettings(LayerIndex, FormatSettings);
	Texture->LossyCompressionAmount = TLCA_None;

	const int32 TextureSizeX = Texture->Source.GetSizeX();
	const int32 TextureSizeY = Texture->Source.GetSizeY();

	// Lock all mip levels.
	PackedTransferColorType** MipData = (PackedTransferColorType**)PendingTexture->PrtHQMipData[0].MipData;
	int8** MipCoverageData = PendingTexture->PrtHQMipData[0].MipCoverageData;
	
	for (int32 MipIndex = 0; MipIndex < NumMips; ++MipIndex)
	{
		const int32 MipSizeX = FMath::Max(1, TextureSizeX >> MipIndex);
		const int32 MipSizeY = FMath::Max(1, TextureSizeY >> MipIndex);

		MipData[MipIndex] = (PackedTransferColorType*)Texture->Source.LockMip(0, LayerIndex, MipIndex);
		MipCoverageData[MipIndex] = (int8*)FMemory::Malloc(MipSizeX * MipSizeY);
	}

	// Create the uncompressed top mip-level.
	FMemory::Memzero(MipData[0], TextureSizeX * TextureSizeY * sizeof(PackedTransferColorType));
	FMemory::Memzero(MipCoverageData[0], TextureSizeX * TextureSizeY);
}

template<typename PackedTransferColorType>
static void PreEncode20rdTransferCoefficientTexture(FTLBSLightMapPendingTexture* PendingTexture, UTexture* Texture, uint32 LayerIndex, const FColor& TextureColor, bool Compression, bool CompressionNoAlpha)
{
	const int32 NumMips = Texture->Source.GetNumMips();
	check(NumMips > 0);

	FTextureFormatSettings FormatSettings;
	FormatSettings.SRGB = false;
	FormatSettings.CompressionNoAlpha = true;
	if (Compression)
		FormatSettings.CompressionNoAlpha = CompressionNoAlpha;
	FormatSettings.CompressionNone = !Compression;
	//FormatSettings.bForcePVRTC4 = true;
	Texture->SetLayerFormatSettings(LayerIndex, FormatSettings);
	Texture->LossyCompressionAmount = TLCA_None;

	const int32 TextureSizeX = Texture->Source.GetSizeX();
	const int32 TextureSizeY = Texture->Source.GetSizeY();

	// Lock all mip levels.
	PackedTransferColorType** MipData0 = (PackedTransferColorType**)PendingTexture->PrtHQMipData[1].MipData;
	PackedTransferColorType** MipData1 = (PackedTransferColorType**)PendingTexture->PrtHQMipData[2].MipData;
	int8** MipCoverageData0 = PendingTexture->PrtHQMipData[1].MipCoverageData;
	int8** MipCoverageData1 = PendingTexture->PrtHQMipData[2].MipCoverageData;

	for (int32 MipIndex = 0; MipIndex < NumMips; ++MipIndex)
	{
		const int32 MipSizeX = FMath::Max(1, TextureSizeX >> MipIndex);
		const int32 MipSizeY = FMath::Max(1, TextureSizeY >> MipIndex);
		const int32 MipStartBottom = MipSizeX * (MipSizeY >> 1);

		MipData0[MipIndex] = (PackedTransferColorType*)Texture->Source.LockMip(0, LayerIndex, MipIndex);
		MipCoverageData0[MipIndex] = (int8*)FMemory::Malloc(MipSizeX * MipSizeY);

		// 2 coefficients are stored on the top/bottom halves of the same destination texture
		MipData1[MipIndex] = MipData0[MipIndex] + MipStartBottom;
		MipCoverageData1[MipIndex] = MipCoverageData0[MipIndex] + MipStartBottom;
	}

	// Create the uncompressed top mip-level.
	FMemory::Memzero(MipData0[0], TextureSizeX * TextureSizeY * sizeof(PackedTransferColorType));
	FMemory::Memzero(MipCoverageData0[0], TextureSizeX * TextureSizeY);

	// Temp pixels data for mip0
	const int32 TempTextureSizeX = PendingTexture->GetSizeX();
	const int32 TempTextureSizeY = PendingTexture->GetSizeY() * 3;
	uint8** MipData2			= PendingTexture->PrtHQMipData[3].MipData;
	int8**	MipCoverageData2	= PendingTexture->PrtHQMipData[3].MipCoverageData;
	MipData2[0]					= (uint8*)FMemory::Malloc(TempTextureSizeX * TempTextureSizeY * sizeof(PackedTransferColorType));
	MipCoverageData2[0]			= (int8*)FMemory::Malloc(TempTextureSizeX * TempTextureSizeY);
	FMemory::Memzero(MipData2[0], TempTextureSizeX * TempTextureSizeY * sizeof(PackedTransferColorType));
	FMemory::Memzero(MipCoverageData2[0], TempTextureSizeX * TempTextureSizeY);
}

template<typename PackedTransferColorType>
static void GenerateLightmapMipsAndDilateColor(const TArray<FIntRect>& Ranges, int32 NumMips, int32 TextureSizeX, int32 TextureSizeY, FColor TextureColor, PackedTransferColorType** MipData, int8** MipCoverageData)
{
	for (int32 MipIndex = 1; MipIndex < NumMips; MipIndex++)
	{
		for (const auto& Range : Ranges) {
			const int32 SourceMipSizeX = FMath::Max(1, TextureSizeX >> (MipIndex - 1));
			const int32 SourceMipSizeY = FMath::Max(1, TextureSizeY >> (MipIndex - 1));
			const int32 DestMipSizeX = FMath::Max(1, TextureSizeX >> MipIndex);
			const int32 DestMipSizeY = FMath::Max(1, TextureSizeY >> MipIndex);

			const int32 MipRangeMinX = Range.Min.X >> MipIndex;
			const int32 MipRangeMinY = Range.Min.Y >> MipIndex;
			const int32 MipRangeMaxX = FMath::Max(1, Range.Max.X >> MipIndex);
			const int32 MipRangeMaxY = FMath::Max(1, Range.Max.Y >> MipIndex);

			// Downsample the previous mip-level, taking into account which texels are mapped.
			PackedTransferColorType* NextMipData = MipData[MipIndex];
			PackedTransferColorType* LastMipData = MipData[MipIndex - 1];

			int8* NextMipCoverageData = MipCoverageData[MipIndex];
			int8* LastMipCoverageData = MipCoverageData[MipIndex - 1];

			const int32 MipFactorX = SourceMipSizeX / DestMipSizeX;
			const int32 MipFactorY = SourceMipSizeY / DestMipSizeY;

			//@todo - generate mips before encoding lightmaps!  
			// Currently we are filtering in the encoded space, similar to generating mips of sRGB textures in sRGB space
			for (int32 Y = MipRangeMinY; Y < MipRangeMaxY; Y++)
			{
				for (int32 X = MipRangeMinX; X < MipRangeMaxX; X++)
				{
					FLinearColor AccumulatedColor = FLinearColor::Black;
					uint32 Coverage = 0;

					const uint32 MinSourceY = (Y + 0) * MipFactorY;
					const uint32 MaxSourceY = (Y + 1) * MipFactorY;
					for (uint32 SourceY = MinSourceY; SourceY < MaxSourceY; SourceY++)
					{
						const uint32 MinSourceX = (X + 0) * MipFactorX;
						const uint32 MaxSourceX = (X + 1) * MipFactorX;
						for (uint32 SourceX = MinSourceX; SourceX < MaxSourceX; SourceX++)
						{
							const PackedTransferColorType& SourceColor = LastMipData[SourceY * SourceMipSizeX + SourceX];
							int8 SourceCoverage = LastMipCoverageData[SourceY * SourceMipSizeX + SourceX];
							if (SourceCoverage)
							{
								AccumulatedColor += SourceColor.ReinterpretAsLinear() * SourceCoverage;
								Coverage += SourceCoverage;
							}
						}
					}
					PackedTransferColorType& DestColor = NextMipData[Y * DestMipSizeX + X];
					int8& DestCoverage = NextMipCoverageData[Y * DestMipSizeX + X];
					if (Coverage)
					{
						DestColor = (AccumulatedColor / Coverage);
						DestCoverage = Coverage / (MipFactorX * MipFactorY);
					}
					else
					{
						DestColor = PackedTransferColorType(0, 0, 0);
						DestCoverage = 0;
					}
				}
			}
		}
	}

	// Expand texels which are mapped into adjacent texels which are not mapped to avoid artifacts when using texture filtering
	for (int32 MipIndex = 0; MipIndex < NumMips; MipIndex++)
	{
		for (const auto& Range : Ranges) {
			PackedTransferColorType* MipLevelData = MipData[MipIndex];
			int8* MipLevelCoverageData = MipCoverageData[MipIndex];

			uint32 MipSizeX = FMath::Max(1, TextureSizeX >> MipIndex);
			uint32 MipSizeY = FMath::Max(1, TextureSizeY >> MipIndex);

			const uint32 MipRangeMinX = Range.Min.X >> MipIndex;
			const uint32 MipRangeMinY = Range.Min.Y >> MipIndex;
			const uint32 MipRangeMaxX = FMath::Max(1, Range.Max.X >> MipIndex);
			const uint32 MipRangeMaxY = FMath::Max(1, Range.Max.Y >> MipIndex);

			for (uint32 DestY = MipRangeMinY; DestY < MipRangeMaxY; DestY++)
			{
				for (uint32 DestX = MipRangeMinX; DestX < MipRangeMaxX; DestX++)
				{
					PackedTransferColorType& DestColor = MipLevelData[DestY * MipSizeX + DestX];
					int8& DestCoverage = MipLevelCoverageData[DestY * MipSizeX + DestX];
					if (DestCoverage == 0)
					{
						FLinearColor AccumulatedColor = FLinearColor::Black;
						uint32 Coverage = 0;

						const int32 MinSourceY = FMath::Max((int32)DestY - 1, (int32)0);
						const int32 MaxSourceY = FMath::Min((int32)DestY + 1, (int32)MipSizeY - 1);
						for (int32 SourceY = MinSourceY; SourceY <= MaxSourceY; SourceY++)
						{
							const int32 MinSourceX = FMath::Max((int32)DestX - 1, (int32)0);
							const int32 MaxSourceX = FMath::Min((int32)DestX + 1, (int32)MipSizeX - 1);
							for (int32 SourceX = MinSourceX; SourceX <= MaxSourceX; SourceX++)
							{
								PackedTransferColorType& SourceColor = MipLevelData[SourceY * MipSizeX + SourceX];
								int8 SourceCoverage = MipLevelCoverageData[SourceY * MipSizeX + SourceX];
								if (SourceCoverage > 0)
								{
									static const uint32 Weights[3][3] =
									{
										{ 1, 255, 1 },
										{ 255, 0, 255 },
										{ 1, 255, 1 },
									};
									AccumulatedColor += SourceColor.ReinterpretAsLinear() * SourceCoverage * Weights[SourceX - DestX + 1][SourceY - DestY + 1];
									Coverage += SourceCoverage * Weights[SourceX - DestX + 1][SourceY - DestY + 1];
								}
							}
						}

						if (Coverage)
						{
							DestColor = (AccumulatedColor / Coverage);
							DestCoverage = -1;
						}
					}
				}
			}
		}
	}

	// Fill zero coverage texels with closest colors using mips
	for (int32 MipIndex = NumMips - 2; MipIndex >= 0; MipIndex--)
	{
		for (const auto& Range : Ranges) {
			const int32 DstMipSizeX = FMath::Max(1, TextureSizeX >> MipIndex);
			const int32 DstMipSizeY = FMath::Max(1, TextureSizeY >> MipIndex);
			const int32 SrcMipSizeX = FMath::Max(1, TextureSizeX >> (MipIndex + 1));
			const int32 SrcMipSizeY = FMath::Max(1, TextureSizeY >> (MipIndex + 1));
			const int32 MipRangeMinX = Range.Min.X >> MipIndex;
			const int32 MipRangeMinY = Range.Min.Y >> MipIndex;
			const int32 MipRangeMaxX = FMath::Max(1, Range.Max.X >> MipIndex);
			const int32 MipRangeMaxY = FMath::Max(1, Range.Max.Y >> MipIndex);

			// Source from higher mip, taking into account which texels are mapped.
			PackedTransferColorType* DstMipData = MipData[MipIndex];
			PackedTransferColorType* SrcMipData = MipData[MipIndex + 1];

			int8* DstMipCoverageData = MipCoverageData[MipIndex];
			int8* SrcMipCoverageData = MipCoverageData[MipIndex + 1];

			for (int32 DstY = MipRangeMinY; DstY < MipRangeMaxY; DstY++)
			{
				for (int32 DstX = MipRangeMinX; DstX < MipRangeMaxX; DstX++)
				{
					const uint32 SrcX = DstX / 2;
					const uint32 SrcY = DstY / 2;

					const PackedTransferColorType& SrcColor = SrcMipData[SrcY * SrcMipSizeX + SrcX];
					int8 SrcCoverage = SrcMipCoverageData[SrcY * SrcMipSizeX + SrcX];

					PackedTransferColorType& DstColor = DstMipData[DstY * DstMipSizeX + DstX];
					int8& DstCoverage = DstMipCoverageData[DstY * DstMipSizeX + DstX];

					// Point upsample mip data for zero coverage texels
					// TODO bilinear upsample
					if (SrcCoverage != 0 && DstCoverage == 0)
					{
						DstColor = SrcColor;
						DstCoverage = SrcCoverage;
					}
				}
			}
		}
	}
}

template<typename PackedTransferColorType>
static void EncodeTransferCoefficientTextureOneLightmap(FTLBSLightMapPendingTexture* PendingTexture, int32 CoefficientIndex, UTexture* Texture, uint32 AllocationIndex, uint32 LayerIndex, const FColor& TextureColor)
{
	const int32 NumMips = Texture->Source.GetNumMips();
	const int32 TextureSizeX = PendingTexture->GetSizeX();
	const int32 TextureSizeY = PendingTexture->GetSizeY();

	PackedTransferColorType** MipData0 = (PackedTransferColorType**)PendingTexture->PrtHQMipData[CoefficientIndex].MipData;
	PackedTransferColorType** MipData1 = (PackedTransferColorType**)PendingTexture->PrtHQMipData[CoefficientIndex + 2].MipData;
	int8** MipCoverageData0 = PendingTexture->PrtHQMipData[CoefficientIndex].MipCoverageData;
	int8** MipCoverageData1 = PendingTexture->PrtHQMipData[CoefficientIndex + 2].MipCoverageData;

	auto& Allocation = PendingTexture->Allocations[AllocationIndex];
	for (int k = 0; k <= 2; k += 2)
	{
		Allocation->LightMap->PrtHQScaleVectors[CoefficientIndex % 2 + k] = FVector4f(
			Allocation->PrtHQScale[CoefficientIndex % 2 + k][0],
			Allocation->PrtHQScale[CoefficientIndex % 2 + k][1],
			Allocation->PrtHQScale[CoefficientIndex % 2 + k][2],
			Allocation->PrtHQScale[CoefficientIndex % 2 + k][3]
		);

		Allocation->LightMap->PrtHQAddVectors[CoefficientIndex % 2 + k] = FVector4f(
			Allocation->PrtHQAdd[CoefficientIndex % 2 + k][0],
			Allocation->PrtHQAdd[CoefficientIndex % 2 + k][1],
			Allocation->PrtHQAdd[CoefficientIndex % 2 + k][2],
			Allocation->PrtHQAdd[CoefficientIndex % 2 + k][3]
		);
	}
	// Skip encoding of this texture if we were asked not to bother
	if (!Allocation->bSkipEncoding)
	{
		FIntRect TextureRect(MAX_int32, MAX_int32, MIN_int32, MIN_int32);
		TextureRect.Min.X = FMath::Min<int32>(TextureRect.Min.X, Allocation->OffsetX);
		TextureRect.Min.Y = FMath::Min<int32>(TextureRect.Min.Y, Allocation->OffsetY);
		TextureRect.Max.X = FMath::Max<int32>(TextureRect.Max.X, Allocation->OffsetX + Allocation->MappedRect.Width());
		TextureRect.Max.Y = FMath::Max<int32>(TextureRect.Max.Y, Allocation->OffsetY + Allocation->MappedRect.Height());

		PendingTexture->NumNonPower2Texels += TextureRect.Width() * TextureRect.Height();

		// Copy the raw data for this light-map into the raw texture data array.
		for (int32 Y = Allocation->MappedRect.Min.Y; Y < Allocation->MappedRect.Max.Y; ++Y)
		{
			for (int32 X = Allocation->MappedRect.Min.X; X < Allocation->MappedRect.Max.X; ++X)
			{
				const auto& SourceCoefficients = Allocation->RawData[Y * Allocation->TotalSizeX + X];

				int32 DestY = Y - Allocation->MappedRect.Min.Y + Allocation->OffsetY;
				int32 DestX = X - Allocation->MappedRect.Min.X + Allocation->OffsetX;

				PackedTransferColorType& DestColor = MipData0[0][DestY * TextureSizeX + DestX];
				int8& DestCoverage = MipCoverageData0[0][DestY * TextureSizeX + DestX];

				PackedTransferColorType& DestBottomColor = MipData1[0][DestX + DestY * TextureSizeX];
				int8& DestBottomCoverage = MipCoverageData1[0][DestX + DestY * TextureSizeX];


				DestColor = PackedTransferColorType(
					SourceCoefficients.PrtHQCoefficients[0][CoefficientIndex % 2],
					SourceCoefficients.PrtHQCoefficients[1][CoefficientIndex % 2],
					SourceCoefficients.PrtHQCoefficients[2][CoefficientIndex % 2]);


				DestBottomColor = PackedTransferColorType(
					SourceCoefficients.PrtHQCoefficients[0][CoefficientIndex % 2 + 2],
					SourceCoefficients.PrtHQCoefficients[1][CoefficientIndex % 2 + 2],
					SourceCoefficients.PrtHQCoefficients[2][CoefficientIndex % 2 + 2]);


				// uint8 -> int8
				DestCoverage = DestBottomCoverage = SourceCoefficients.Coverage / 2;
				if (SourceCoefficients.Coverage > 0)
				{
					PendingTexture->NumLightmapMappedTexels++;
				}
				else
				{
					PendingTexture->NumLightmapUnmappedTexels++;
				}
			}
		}
	}

	FIntRect Range = {
		Allocation->MappedRect.Min.X + Allocation->OffsetX,
		Allocation->MappedRect.Min.Y + Allocation->OffsetY,
		Allocation->MappedRect.Max.X + Allocation->OffsetX,
		Allocation->MappedRect.Max.Y + Allocation->OffsetY
	};
	GenerateLightmapMipsAndDilateColor<PackedTransferColorType>({ Range }, NumMips, TextureSizeX, TextureSizeY, TextureColor, MipData0, MipCoverageData0);
	GenerateLightmapMipsAndDilateColor<PackedTransferColorType>({ Range }, NumMips, TextureSizeX, TextureSizeY, TextureColor, MipData1, MipCoverageData1);
}

template<typename PackedTransferColorType>
static void Encode10rdTransferCoefficientTextureOneLightmap(FTLBSLightMapPendingTexture* PendingTexture, UTexture* Texture, uint32 AllocationIndex, uint32 LayerIndex, const FColor& TextureColor)
{
	const int32 NumMips = Texture->Source.GetNumMips();
	const int32 TextureSizeX = PendingTexture->GetSizeX();
	const int32 TextureSizeY = PendingTexture->GetSizeY();

	PackedTransferColorType** MipData = (PackedTransferColorType**)PendingTexture->PrtHQMipData[0].MipData;
	int8** MipCoverageData = PendingTexture->PrtHQMipData[0].MipCoverageData;

	auto& Allocation = PendingTexture->Allocations[AllocationIndex];
	for (int k = 0; k <= 2; k += 2)
	{
		Allocation->LightMap->PrtHQScaleVectors[k] = FVector4f(
			Allocation->PrtHQScale[k][0],
			Allocation->PrtHQScale[k][1],
			Allocation->PrtHQScale[k][2],
			Allocation->PrtHQScale[k][3]
		);

		Allocation->LightMap->PrtHQAddVectors[k] = FVector4f(
			Allocation->PrtHQAdd[k][0],
			Allocation->PrtHQAdd[k][1],
			Allocation->PrtHQAdd[k][2],
			Allocation->PrtHQAdd[k][3]
		);
	}
	// Skip encoding of this texture if we were asked not to bother
	if (!Allocation->bSkipEncoding)
	{
		FIntRect TextureRect(MAX_int32, MAX_int32, MIN_int32, MIN_int32);
		TextureRect.Min.X = FMath::Min<int32>(TextureRect.Min.X, Allocation->OffsetX);
		TextureRect.Min.Y = FMath::Min<int32>(TextureRect.Min.Y, Allocation->OffsetY);
		TextureRect.Max.X = FMath::Max<int32>(TextureRect.Max.X, Allocation->OffsetX + Allocation->MappedRect.Width());
		TextureRect.Max.Y = FMath::Max<int32>(TextureRect.Max.Y, Allocation->OffsetY + Allocation->MappedRect.Height());

		PendingTexture->NumNonPower2Texels += TextureRect.Width() * TextureRect.Height();

		// Copy the raw data for this light-map into the raw texture data array.
		for (int32 Y = Allocation->MappedRect.Min.Y; Y < Allocation->MappedRect.Max.Y; ++Y)
		{
			for (int32 X = Allocation->MappedRect.Min.X; X < Allocation->MappedRect.Max.X; ++X)
			{
				const auto& SourceCoefficients = Allocation->RawData[Y * Allocation->TotalSizeX + X];

				int32 DestY = Y - Allocation->MappedRect.Min.Y + Allocation->OffsetY;
				int32 DestX = X - Allocation->MappedRect.Min.X + Allocation->OffsetX;

				PackedTransferColorType& DestColor = MipData[0][DestY * TextureSizeX + DestX];
				int8& DestCoverage = MipCoverageData[0][DestY * TextureSizeX + DestX];


				DestColor = PackedTransferColorType(
					SourceCoefficients.PrtHQCoefficients[0][0],
					SourceCoefficients.PrtHQCoefficients[1][0],
					SourceCoefficients.PrtHQCoefficients[2][0]);

				// uint8 -> int8
				DestCoverage = SourceCoefficients.Coverage / 2;
				if (SourceCoefficients.Coverage > 0)
				{
					PendingTexture->NumLightmapMappedTexels++;
				}
				else
				{
					PendingTexture->NumLightmapUnmappedTexels++;
				}
			}
		}
	}

	FIntRect Range = {
		Allocation->MappedRect.Min.X + Allocation->OffsetX,
		Allocation->MappedRect.Min.Y + Allocation->OffsetY,
		Allocation->MappedRect.Max.X + Allocation->OffsetX,
		Allocation->MappedRect.Max.Y + Allocation->OffsetY
	};
	GenerateLightmapMipsAndDilateColor<PackedTransferColorType>({ Range }, NumMips, TextureSizeX, TextureSizeY, TextureColor, MipData, MipCoverageData);
}

template<typename PackedTransferColorType>
static void Encode20rdTransferCoefficientTextureOneLightmap(FTLBSLightMapPendingTexture* PendingTexture, UTexture* Texture, uint32 AllocationIndex, uint32 LayerIndex, const FColor& TextureColor)
{
	const int32 NumMips = Texture->Source.GetNumMips();
	const int32 TextureSizeX = PendingTexture->GetSizeX();
	const int32 TextureSizeY = PendingTexture->GetSizeY();
	const int32 StartBottom = TextureSizeX * TextureSizeY;

	PackedTransferColorType** MipData0 = (PackedTransferColorType**)PendingTexture->PrtHQMipData[1].MipData;
	PackedTransferColorType** MipData1 = (PackedTransferColorType**)PendingTexture->PrtHQMipData[2].MipData;
	int8** MipCoverageData0 = PendingTexture->PrtHQMipData[1].MipCoverageData;
	int8** MipCoverageData1 = PendingTexture->PrtHQMipData[2].MipCoverageData;

	PackedTransferColorType* TempMipData0 = (PackedTransferColorType*)PendingTexture->PrtHQMipData[3].MipData[0];
	PackedTransferColorType* TempMipData1 = TempMipData0 + StartBottom;
	PackedTransferColorType* TempMipData2 = TempMipData0 + StartBottom * 2;
	int8* TempMipCoverageData0 = PendingTexture->PrtHQMipData[3].MipCoverageData[0];
	int8* TempMipCoverageData1 = TempMipCoverageData0 + StartBottom;
	int8* TempMipCoverageData2 = TempMipCoverageData0 + StartBottom * 2;

	auto& Allocation = PendingTexture->Allocations[AllocationIndex];
	for (int k = 0; k <= 2; k += 2)
	{
		Allocation->LightMap->PrtHQScaleVectors[k + 1] = FVector4f(
			Allocation->PrtHQScale[k + 1][0],
			Allocation->PrtHQScale[k + 1][1],
			Allocation->PrtHQScale[k + 1][2],
			Allocation->PrtHQScale[k + 1][3]
		);

		Allocation->LightMap->PrtHQAddVectors[k + 1] = FVector4f(
			Allocation->PrtHQAdd[k + 1][0],
			Allocation->PrtHQAdd[k + 1][1],
			Allocation->PrtHQAdd[k + 1][2],
			Allocation->PrtHQAdd[k + 1][3]
		);
	}
	// Skip encoding of this texture if we were asked not to bother
	if (!Allocation->bSkipEncoding)
	{
		FIntRect TextureRect(MAX_int32, MAX_int32, MIN_int32, MIN_int32);
		TextureRect.Min.X = FMath::Min<int32>(TextureRect.Min.X, Allocation->OffsetX);
		TextureRect.Min.Y = FMath::Min<int32>(TextureRect.Min.Y, Allocation->OffsetY);
		TextureRect.Max.X = FMath::Max<int32>(TextureRect.Max.X, Allocation->OffsetX + Allocation->MappedRect.Width());
		TextureRect.Max.Y = FMath::Max<int32>(TextureRect.Max.Y, Allocation->OffsetY + Allocation->MappedRect.Height());

		PendingTexture->NumNonPower2Texels += TextureRect.Width() * TextureRect.Height();

		// Copy the raw data for this light-map into the raw texture data array.
		for (int32 Y = Allocation->MappedRect.Min.Y; Y < Allocation->MappedRect.Max.Y; ++Y)
		{
			for (int32 X = Allocation->MappedRect.Min.X; X < Allocation->MappedRect.Max.X; ++X)
			{
				const auto& SourceCoefficients = Allocation->RawData[Y * Allocation->TotalSizeX + X];

				int32 DestY = Y - Allocation->MappedRect.Min.Y + Allocation->OffsetY;
				int32 DestX = X - Allocation->MappedRect.Min.X + Allocation->OffsetX;

				PackedTransferColorType& DestColor = TempMipData0[DestY * TextureSizeX + DestX];
				int8& DestCoverage = TempMipCoverageData0[DestY * TextureSizeX + DestX];

				PackedTransferColorType& DestMiddleColor = TempMipData1[DestX + DestY * TextureSizeX];
				int8& DestMiddleCoverage = TempMipCoverageData1[DestX + DestY * TextureSizeX];

				PackedTransferColorType& DestBottomColor = TempMipData2[DestX + DestY * TextureSizeX];
				int8& DestBottomCoverage = TempMipCoverageData2[DestX + DestY * TextureSizeX];


				DestColor = PackedTransferColorType(
					SourceCoefficients.PrtHQCoefficients[0][2],
					SourceCoefficients.PrtHQCoefficients[1][2],
					SourceCoefficients.PrtHQCoefficients[2][2]);

				DestMiddleColor = PackedTransferColorType(
					SourceCoefficients.PrtHQCoefficients[0][1],
					SourceCoefficients.PrtHQCoefficients[1][1],
					SourceCoefficients.PrtHQCoefficients[2][1]);

				DestBottomColor = PackedTransferColorType(
					SourceCoefficients.PrtHQCoefficients[0][3],
					SourceCoefficients.PrtHQCoefficients[1][3],
					SourceCoefficients.PrtHQCoefficients[2][3]);

				// uint8 -> int8
				DestCoverage = DestMiddleCoverage = DestBottomCoverage = SourceCoefficients.Coverage / 2;
				if (SourceCoefficients.Coverage > 0)
				{
					PendingTexture->NumLightmapMappedTexels++;
				}
				else
				{
					PendingTexture->NumLightmapUnmappedTexels++;
				}
			}
		}
	}

	FIntRect Range = {
		Allocation->MappedRect.Min.X + Allocation->OffsetX,
		(Allocation->MappedRect.Min.Y + Allocation->OffsetY) * 2,
		Allocation->MappedRect.Max.X + Allocation->OffsetX,
		(Allocation->MappedRect.Max.Y + Allocation->OffsetY) * 2
	};

	for (int32 X = Range.Min.X; X < Range.Max.X; ++X)
	{
		int32 TempY = Range.Min.Y / 2 * 3;
		for (int32 Y = Range.Min.Y; Y < Range.Max.Y; Y += 2, TempY += 3)
		{
			PackedTransferColorType* TmpDestColor0 = nullptr;
			PackedTransferColorType* TmpDestColor1 = nullptr;
			PackedTransferColorType* TmpDestColor2 = nullptr;
			int8* TmpDestCoverage0 = nullptr;
			int8* TmpDestCoverage1 = nullptr;
			int8* TmpDestCoverage2 = nullptr;

			if (TempY < TextureSizeY)
			{
				TmpDestColor0 = &TempMipData0[TempY * TextureSizeX + X];
				TmpDestCoverage0 = &TempMipCoverageData0[TempY * TextureSizeX + X];
			}
			else if (TempY >= TextureSizeY && TempY < TextureSizeY * 2)
			{
				TmpDestColor0 = &TempMipData1[(TempY - TextureSizeY) * TextureSizeX + X];
				TmpDestCoverage0 = &TempMipCoverageData1[(TempY - TextureSizeY) * TextureSizeX + X];
			}
			else
			{
				TmpDestColor0 = &TempMipData2[(TempY - TextureSizeY * 2) * TextureSizeX + X];
				TmpDestCoverage0 = &TempMipCoverageData2[(TempY - TextureSizeY * 2) * TextureSizeX + X];
			}

			if (TempY + 1 < TextureSizeY)
			{
				TmpDestColor1 = &TempMipData0[(TempY + 1) * TextureSizeX + X];
				TmpDestCoverage1 = &TempMipCoverageData0[(TempY + 1) * TextureSizeX + X];
			}
			else if (TempY + 1 >= TextureSizeY && TempY + 1 < TextureSizeY * 2)
			{
				TmpDestColor1 = &TempMipData1[(TempY + 1 - TextureSizeY) * TextureSizeX + X];
				TmpDestCoverage1 = &TempMipCoverageData1[(TempY + 1 - TextureSizeY) * TextureSizeX + X];
			}
			else
			{
				TmpDestColor1 = &TempMipData2[(TempY + 1 - TextureSizeY * 2) * TextureSizeX + X];
				TmpDestCoverage1 = &TempMipCoverageData2[(TempY + 1 - TextureSizeY * 2) * TextureSizeX + X];
			}

			if (TempY + 2 < TextureSizeY)
			{
				TmpDestColor2 = &TempMipData0[(TempY + 2) * TextureSizeX + X];
				TmpDestCoverage2 = &TempMipCoverageData0[(TempY + 2) * TextureSizeX + X];
			}
			else if (TempY + 2 >= TextureSizeY && TempY + 2 < TextureSizeY * 2)
			{
				TmpDestColor2 = &TempMipData1[(TempY + 2 - TextureSizeY) * TextureSizeX + X];
				TmpDestCoverage2 = &TempMipCoverageData1[(TempY + 2 - TextureSizeY) * TextureSizeX + X];
			}
			else
			{
				TmpDestColor2 = &TempMipData2[(TempY + 2 - TextureSizeY * 2) * TextureSizeX + X];
				TmpDestCoverage2 = &TempMipCoverageData2[(TempY + 2 - TextureSizeY * 2) * TextureSizeX + X];
			}

			PackedTransferColorType& DestColor0 = MipData0[0][Y * TextureSizeX + X];
			int8& DestCoverage0 = MipCoverageData0[0][Y * TextureSizeX + X];

			PackedTransferColorType& DestColor1 = MipData0[0][(Y + 1) * TextureSizeX + X];
			int8& DestCoverage1 = MipCoverageData0[0][(Y + 1) * TextureSizeX + X];

			FLinearColor LinearColor0 = TmpDestColor0->ReinterpretAsLinear();
			FLinearColor LinearColor1 = TmpDestColor1->ReinterpretAsLinear();
			FLinearColor LinearColor2 = TmpDestColor2->ReinterpretAsLinear();
			FLinearColor DestLinearColor0 = LinearColor0 * 2.0f / 3.0f + LinearColor1 / 3.0f;
			FLinearColor DestLinearColor1 = LinearColor2 * 2.0f / 3.0f + LinearColor1 / 3.0f;

			DestColor0 = PackedTransferColorType(DestLinearColor0);
			DestColor1 = PackedTransferColorType(DestLinearColor1);
			DestCoverage0 = *TmpDestCoverage0 * 2.0f / 3.0f + *TmpDestCoverage1 / 3.0f;
			DestCoverage1 = *TmpDestCoverage2 * 2.0f / 3.0f + *TmpDestCoverage1 / 3.0f;
		}
	}

	GenerateLightmapMipsAndDilateColor<PackedTransferColorType>({ Range }, NumMips, TextureSizeX, TextureSizeY * 2, TextureColor, MipData0, MipCoverageData0);
}
// CTG End
#endif

FTLBSLightMapPendingTexture::FTLBSLightMapPendingTexture(UWorld* InWorld, uint32 InSizeX, uint32 InSizeY, ETextureLayoutAspectRatio Aspect)
	: FLightMapPendingTexture(InWorld, InSizeX, InSizeY, Aspect)
{
}

FTLBSLightMapPendingTexture::~FTLBSLightMapPendingTexture()
{
	//check(bHasRunPostEncode && bIsFinishedEncoding);
}

void FTLBSLightMapPendingTexture::CreateUObjects() {
	++GLightmapCounter;
	// Only build VT lightmaps if they are enabled

	static const auto CVar = IConsoleManager::Get().FindTConsoleVariableDataInt(TEXT("r.VirtualTexturedLightmaps"));
	const bool bUseVirtualTextures = (CVar->GetValueOnAnyThread() != 0) && UseVirtualTexturing(GMaxRHIShaderPlatform);

	if (!bUseVirtualTextures)
	{
		if (NeedsSkyOcclusionTexture())
		{
			SkyOcclusionTexture = NewObject<ULightMapTexture2D>(Outer, GetSkyOcclusionTextureName(GLightmapCounter));
		}

		if (NeedsAOMaterialMaskTexture())
		{
			AOMaterialMaskTexture = NewObject<ULightMapTexture2D>(Outer, GetAOMaterialMaskTextureName(GLightmapCounter));
		}

#if ENABLE_PRTHQ_API
		if (NeedsPrtHQTextures())
		{
			for (int32 Index = 0; Index < UE_ARRAY_COUNT(PrtLQTextures); Index++)
			{
				PrtHQTextures[Index] = NewObject<ULightMapTexture2D>(Outer, GetPrtHQTextureName(GLightmapCounter, Index));
			}
		}
#endif

#if ENABLE_PRTLQ_API
		// CTG Begin, ryanthhuang: PRT20
		if (NeedsPrtLQTextures())
		{
			for (int32 Index = 0; Index < UE_ARRAY_COUNT(PrtLQTextures); Index++)
			{
				PrtLQTextures[Index] = NewObject<ULightMapTexture2D>(Outer, GetPrtLQTextureName(GLightmapCounter, Index));
			}
		}
#endif
		//CTG end
		if (NeedsStaticShadowTexture())
		{
			ShadowMapTexture = NewObject<UShadowMapTexture2D>(Outer, GetShadowTextureName(GLightmapCounter));
		}

#if ENABLE_PRT_API
		if (NeedsStaticTextures())
#endif
		{
			// Encode and compress the coefficient textures.
			for (uint32 CoefficientIndex = 0; CoefficientIndex < NUM_STORED_LIGHTMAP_COEF; CoefficientIndex += 2)
			{
				Textures[CoefficientIndex] = nullptr;
				// Skip generating simple lightmaps if wanted.
				static const auto CVarSupportLowQualityLightmaps = IConsoleManager::Get().FindTConsoleVariableDataInt(TEXT("r.SupportLowQualityLightmaps"));
				const bool bAllowLowQualityLightMaps = (!CVarSupportLowQualityLightmaps) || (CVarSupportLowQualityLightmaps->GetValueOnAnyThread() != 0);

				if ((!bAllowLowQualityLightMaps) && CoefficientIndex >= LQ_LIGHTMAP_COEF_INDEX)
				{
					continue;
				}

				// Create the light-map texture for this coefficient.
				auto Texture = NewObject<ULightMapTexture2D>(Outer, GetLightmapName(GLightmapCounter, CoefficientIndex));
				Textures[CoefficientIndex] = Texture;
			}
		}
		FMemory::Memzero(VirtualTextures);
	}
	else
	{
		for (uint32 CoefficientIndex = 0; CoefficientIndex < NUM_STORED_LIGHTMAP_COEF; CoefficientIndex += 2)
		{
			VirtualTextures[CoefficientIndex] = nullptr;
			NumVirtualTextureLayers[CoefficientIndex] = 0;
			// Skip generating simple lightmaps if wanted.
			static const auto CVarSupportLowQualityLightmaps = IConsoleManager::Get().FindTConsoleVariableDataInt(TEXT("r.SupportLowQualityLightmaps"));
			const bool bAllowLowQualityLightMaps = (!CVarSupportLowQualityLightmaps) || (CVarSupportLowQualityLightmaps->GetValueOnAnyThread() != 0);

			if ((!bAllowLowQualityLightMaps) && CoefficientIndex >= LQ_LIGHTMAP_COEF_INDEX)
			{
				continue;
			}

			auto VirtualTexture = NewObject<ULightMapVirtualTexture2D>(Outer, GetVirtualTextureName(GLightmapCounter, CoefficientIndex));
			VirtualTexture->VirtualTextureStreaming = true;

			VirtualTexture->SetLayerForType(ELightMapVirtualTextureType::LightmapLayer0, NumVirtualTextureLayers[CoefficientIndex]++);
			VirtualTexture->SetLayerForType(ELightMapVirtualTextureType::LightmapLayer1, NumVirtualTextureLayers[CoefficientIndex]++);

			if (CoefficientIndex < LQ_LIGHTMAP_COEF_INDEX)
			{

				if (NeedsAOMaterialMaskTexture())
				{
					VirtualTexture->SetLayerForType(ELightMapVirtualTextureType::AOMaterialMask, NumVirtualTextureLayers[CoefficientIndex]++);
				}
				if (NeedsSkyOcclusionTexture())
				{
					VirtualTexture->SetLayerForType(ELightMapVirtualTextureType::SkyOcclusion, NumVirtualTextureLayers[CoefficientIndex]++);
				}
			}

			if (NeedsStaticShadowTexture())
			{
				VirtualTexture->SetLayerForType(ELightMapVirtualTextureType::ShadowMask, NumVirtualTextureLayers[CoefficientIndex]++);
			}

			VirtualTextures[CoefficientIndex] = VirtualTexture;
		}
	}

	check(bUObjectsCreated == false);
	bUObjectsCreated = true;
}

void FTLBSLightMapPendingTexture::PreEncode(FTLBSLightingSystem* TLBSSystem)
{
	if (bIsInit)
		return;
	bIsInit = true;

	CreateUObjects();

	if (GVisualizeLightmapTextures)
	{
		TextureColor = FColor::MakeRandomColor();
	}

	// Create the uncompressed top mip-level.
	int32 NumShadowChannelsUsed = 0;
	if (NeedsStaticShadowTexture())
	{
		PreEncodeShadowmap(TLBSSystem, NumShadowChannelsUsed);
	}

	const ETextureSourceFormat SkyOcclusionFormat = TSF_BGRA8;
	const ETextureSourceFormat AOMaskFormat = TSF_G8;
	const ETextureSourceFormat ShadowMapFormat = NumShadowChannelsUsed == 1 ? TSF_G8 : TSF_BGRA8;
	const ETextureSourceFormat BaseFormat = TSF_BGRA8;
	const ETextureSourceFormat PRTLQFormat = TSF_BGRA8;

	if (!TLBSSystem->IsUseVirtualTextures())
	{
		if (SkyOcclusionTexture != nullptr)
		{
			auto Texture = SkyOcclusionTexture;
			Texture->Source.Init2DWithMipChain(GetSizeX(), GetSizeY(), SkyOcclusionFormat);
			Texture->MipGenSettings = TMGS_LeaveExistingMips;
			Texture->Filter = GUseBilinearLightmaps ? TF_Default : TF_Nearest;
			Texture->LODGroup = TEXTUREGROUP_Lightmap;
			Texture->LightmapFlags = ELightMapFlags(LightmapFlags);
			PreEncodeSkyOcclusionTexture(Texture, 0u, TextureColor);
		}

		if (AOMaterialMaskTexture != nullptr)
		{
			auto Texture = AOMaterialMaskTexture;
			Texture->Source.Init2DWithMipChain(GetSizeX(), GetSizeY(), AOMaskFormat);
			Texture->MipGenSettings = TMGS_LeaveExistingMips;
			Texture->Filter = GUseBilinearLightmaps ? TF_Default : TF_Nearest;
			Texture->LODGroup = TEXTUREGROUP_Lightmap;
			Texture->LightmapFlags = ELightMapFlags(LightmapFlags);
			PreEncodeAOMaskTexture(Texture, 0u, TextureColor);
		}
#if ENABLE_PRTHQ_API
		// CTG Begin, ryanthuang: PRT20
		static const auto CVarPRTSeparateSHCoefficient = IConsoleManager::Get().FindTConsoleVariableDataInt(TEXT("r.PRT.SeparateSHCoefficient"));
		bool bPRTSeparateSHCoefficient = CVarPRTSeparateSHCoefficient && CVarPRTSeparateSHCoefficient->GetValueOnAnyThread() == 1;
		static const auto CVarPRTLightmapFormat1 = IConsoleManager::Get().FindTConsoleVariableDataInt(TEXT("r.PRT.LightmapForma"));
		const int32 FormatType1 = CVarPRTLightmapFormat1 ? CVarPRTLightmapFormat1->GetValueOnAnyThread() : 0;
		static const auto CVarPRTLightmapFormat2 = IConsoleManager::Get().FindTConsoleVariableDataInt(TEXT("r.PRT.LightmapFormat2Ord"));
		const int32 FormatType2 = CVarPRTLightmapFormat2 ? CVarPRTLightmapFormat2->GetValueOnAnyThread() : -1;
		for (int32 Index = 0; Index < UE_ARRAY_COUNT(PrtHQTextures); Index++)
		{
			auto Texture = PrtHQTextures[Index];
			if (Texture == nullptr)
				continue;
			const int32 FormatType = Index == 0 ? FormatType1 : (FormatType2 != -1 ? FormatType2 : FormatType1);
			ETextureSourceFormat PRTHQFormat = FormatType == 0 ? TSF_RGBA16F : TSF_BGRA8;
			Texture->Source.Init2DWithMipChain(GetSizeX(), GetSizeY() * (bPRTSeparateSHCoefficient && Index == 0 ? 1 : 2), PRTHQFormat);
			Texture->MipGenSettings = TMGS_SimpleAverage;
			Texture->Filter = GUseBilinearLightmaps ? TF_Default : TF_Nearest;
			Texture->LODGroup = TEXTUREGROUP_Lightmap;
			Texture->LightmapFlags = ELightMapFlags(LightmapFlags);
			PreEncodePrtHQTexture(Index, FormatType, Texture, 0u, TextureColor);
		}
		// CTG End
#endif

#if ENABLE_PRTLQ_API
		// CTG Begin, ryanthuang: PRT20
		for (int32 Index = 0; Index < UE_ARRAY_COUNT(PrtLQTextures); Index++)
		{
			auto Texture = PrtLQTextures[Index];
			if (Texture == nullptr)
				continue;
			Texture->Source.Init2DWithMipChain(GetSizeX(), GetSizeY(), PRTLQFormat);
			Texture->MipGenSettings = TMGS_LeaveExistingMips;
			Texture->Filter = GUseBilinearLightmaps ? TF_Default : TF_Nearest;
			Texture->LODGroup = TEXTUREGROUP_Lightmap;
			Texture->LightmapFlags = ELightMapFlags(LightmapFlags);

			PreEncodePrtLQTexture(Index, Texture, 0u, TextureColor);
		}
		// CTG End
#endif
		if (ShadowMapTexture != nullptr)
		{
			auto Texture = ShadowMapTexture;
			Texture->Filter = GUseBilinearLightmaps ? TF_Default : TF_Nearest;
			Texture->LODGroup = TEXTUREGROUP_Shadowmap;

			// TODO - Unify these?  Currently the values match
			Texture->ShadowmapFlags = EShadowMapFlags(LightmapFlags);

			Texture->Source.Init2DWithMipChain(GetSizeX(), GetSizeY(), ShadowMapFormat);
			Texture->MipGenSettings = TMGS_LeaveExistingMips;
			PreEncodeShadowMapTexture(0, Texture, 0u);
		}

		// Encode and compress the coefficient textures.
		for (uint32 CoefficientIndex = 0; CoefficientIndex < NUM_STORED_LIGHTMAP_COEF; CoefficientIndex += 2)
		{
			auto Texture = Textures[CoefficientIndex];
			if (Texture == nullptr)
				continue;

			Texture->Source.Init2DWithMipChain(GetSizeX(), GetSizeY() * 2, BaseFormat);	// Top/bottom atlased
			Texture->MipGenSettings = TMGS_LeaveExistingMips;
			Texture->Filter = GUseBilinearLightmaps ? TF_Default : TF_Nearest;
			Texture->LODGroup = TEXTUREGROUP_Lightmap;
			Texture->LightmapFlags = ELightMapFlags(LightmapFlags);
			PreEncodeCoefficientTexture(CoefficientIndex, Texture, 0u, TextureColor, false);
		}
	}
	else
	{
		// Encode virtual texture light maps
		static const auto CVar = IConsoleManager::Get().FindTConsoleVariableDataInt(TEXT("r.VT.EnableLossyCompressLightmaps"));
		bool bEnableLossyCompressLightmaps = CVar->GetValueOnAnyThread() != 0;
		{
			const uint32 InvalidLayerId = ~0u;

			for (uint32 CoefficientIndex = 0; CoefficientIndex < NUM_STORED_LIGHTMAP_COEF; CoefficientIndex += 2)
			{
				auto VirtualTexture = VirtualTextures[CoefficientIndex];
				if (VirtualTexture == nullptr)
				{
					continue;
				}

				// Copy data from all the separate Lightmap textures into the proper layers of the VT source
				const uint32 SkyOcclusionLayer = VirtualTexture->GetLayerForType(ELightMapVirtualTextureType::SkyOcclusion);
				const uint32 AOMaterialMaskLayer = VirtualTexture->GetLayerForType(ELightMapVirtualTextureType::AOMaterialMask);
				const uint32 ShadowMaskLayer = VirtualTexture->GetLayerForType(ELightMapVirtualTextureType::ShadowMask);
				// CTG Begin, ryanthhuang: PRT20
				// Todo: Adapt the VT
				// CTG End
				TArray<ETextureSourceFormat> LayerFormat;
				LayerFormat.Init(TSF_Invalid, NumVirtualTextureLayers[CoefficientIndex]);
				LayerFormat[0] = BaseFormat;
				LayerFormat[1] = BaseFormat;

				if (SkyOcclusionLayer != InvalidLayerId)
				{
					LayerFormat[SkyOcclusionLayer] = SkyOcclusionFormat;
				}
				if (AOMaterialMaskLayer != InvalidLayerId)
				{
					LayerFormat[AOMaterialMaskLayer] = AOMaskFormat;
				}
				if (ShadowMaskLayer != InvalidLayerId)
				{
					LayerFormat[ShadowMaskLayer] = CoefficientIndex < LQ_LIGHTMAP_COEF_INDEX ? ShadowMapFormat : TSF_G8;
				}

				VirtualTexture->Source.InitLayered2DWithMipChain(GetSizeX(), GetSizeY(), NumVirtualTextureLayers[CoefficientIndex], LayerFormat.GetData());
				VirtualTexture->MipGenSettings = TMGS_LeaveExistingMips;
				VirtualTexture->SRGB = 0;
				VirtualTexture->Filter = GUseBilinearLightmaps ? TF_Default : TF_Nearest;
				VirtualTexture->LODGroup = TEXTUREGROUP_Lightmap;
				VirtualTexture->CompressionNoAlpha = false;
				VirtualTexture->CompressionNone = !GCompressLightmaps;
				VirtualTexture->LossyCompressionAmount = bEnableLossyCompressLightmaps ? TLCA_Default : TLCA_None;

				// VirtualTexture->OodleTextureSdkVersion will be set to latest by default constructor
				//  dynamic/generated textures use latest OodleTextureSdkVersion

				FTextureFormatSettings DefaultFormatSettings;
				VirtualTexture->GetDefaultFormatSettings(DefaultFormatSettings);
				VirtualTexture->LayerFormatSettings.Init(DefaultFormatSettings, NumVirtualTextureLayers[CoefficientIndex]);

				PreEncodeCoefficientTexture(CoefficientIndex, VirtualTexture, 0u, TextureColor, true);

				if (SkyOcclusionLayer != InvalidLayerId)
				{
					PreEncodeSkyOcclusionTexture(VirtualTexture, SkyOcclusionLayer, TextureColor);
				}
				if (AOMaterialMaskLayer != InvalidLayerId)
				{
					PreEncodeAOMaskTexture(VirtualTexture, AOMaterialMaskLayer, TextureColor);
				}
				if (ShadowMaskLayer != InvalidLayerId)
				{
					PreEncodeShadowMapTexture(CoefficientIndex, VirtualTexture, ShadowMaskLayer);
				}
			}
		}
	}	
}

void FTLBSLightMapPendingTexture::PreEncodeShadowmap(FTLBSLightingSystem* TLBSSystem, int32& MaxChannelsUsed)
{
	const int32 TextureSizeX = GetSizeX();
	const int32 TextureSizeY = GetSizeY();

	for (int32 AllocationIndex = 0; AllocationIndex < Allocations.Num(); AllocationIndex++)
	{
		auto& Allocation = *Allocations[AllocationIndex];

		for (int32 ChannelIndex = 0; ChannelIndex < 4; ChannelIndex++)
		{
			for (const auto& [CurrentLight, SourceSamples] : Allocation.ShadowMapData)
			{
				const FLightComponentMapBuildData* LightBuildData = TLBSSystem->GetOrCreateRegistryForActor(CurrentLight->GetOwner())->GetLightBuildData(CurrentLight->LightGuid);

				// Should have been setup by ReassignStationaryLightChannels
				check(LightBuildData);

				if (LightBuildData->ShadowMapChannel == ChannelIndex)
				{
					MaxChannelsUsed = FMath::Max(MaxChannelsUsed, ChannelIndex + 1);
				}
			}
		}
	}

	const int32 NumMips = FMath::Max(FMath::CeilLogTwo(TextureSizeX), FMath::CeilLogTwo(TextureSizeY)) + 1;
	for (int32 MipIndex = 0; MipIndex < NumMips; MipIndex++)
	{
		const uint32 DestMipSizeX = FMath::Max(1, TextureSizeX >> MipIndex);
		const uint32 DestMipSizeY = FMath::Max(1, TextureSizeY >> MipIndex);
		// Downsample the previous mip-level, taking into account which texels are mapped.
		ShadowMipData.MipCoverageData[MipIndex] = (FFourDistanceFieldSamples*)FMemory::Malloc(DestMipSizeX * DestMipSizeY * sizeof(FFourDistanceFieldSamples));
		FMemory::Memzero(ShadowMipData.MipCoverageData[MipIndex], DestMipSizeX * DestMipSizeY * sizeof(FFourDistanceFieldSamples));
	}
}

void FTLBSLightMapPendingTexture::PreEncodeCoefficientTexture(int32 CoefficientIndex, UTexture* Texture, uint32 LayerIndex, const FColor& InTextureColor, bool bEncodeVirtualTexture)
{
	auto& MipData0 = CoefficientMipData0[CoefficientIndex / 2];
	auto& MipData1 = CoefficientMipData1[CoefficientIndex / 2];

	const int32 NumMips = Texture->Source.GetNumMips();
	check(NumMips > 0);

	FTextureFormatSettings FormatSettings;
	FormatSettings.SRGB = false;
	FormatSettings.CompressionNoAlpha = CoefficientIndex >= LQ_LIGHTMAP_COEF_INDEX;
	FormatSettings.CompressionNone = !GCompressLightmaps;
	Texture->SetLayerFormatSettings(LayerIndex, FormatSettings);

	if (bEncodeVirtualTexture)
	{
		Texture->SetLayerFormatSettings(LayerIndex + 1, FormatSettings);
	}

	const int32 TextureSizeX = Texture->Source.GetSizeX();
	const int32 TextureSizeY = Texture->Source.GetSizeY();

	// Lock all mip levels.
	{
		const int32 StartBottom = GetSizeX() * GetSizeY();
		for (int32 MipIndex = 0; MipIndex < NumMips; ++MipIndex)
		{
			const int32 MipSizeX = FMath::Max(1, TextureSizeX >> MipIndex);
			const int32 MipSizeY = FMath::Max(1, TextureSizeY >> MipIndex);
			const int32 MipStartBottom = MipSizeX * (MipSizeY >> 1);

			MipData0.MipData[MipIndex] = (FColor*)Texture->Source.LockMip(0, LayerIndex, MipIndex);
			FMemory::Memzero(MipData0.MipData[MipIndex], MipSizeX * MipSizeY * sizeof(FColor));
			MipData0.MipCoverageData[MipIndex] = (int8*)FMemory::Malloc(MipSizeX * MipSizeY);
			
			if (bEncodeVirtualTexture)
			{
				// 2 coefficients are stored on adjacent VT layers
				MipData1.MipData[MipIndex] = (FColor*)Texture->Source.LockMip(0, LayerIndex + 1, MipIndex);
				FMemory::Memzero(MipData1.MipData[MipIndex], MipSizeX * MipSizeY * sizeof(FColor));
				MipData1.MipCoverageData[MipIndex] = (int8*)FMemory::Malloc(MipSizeX * MipSizeY);
			}
			else
			{
				// 2 coefficients are stored on the top/bottom halves of the same destination texture
				MipData1.MipData[MipIndex] = MipData0.MipData[MipIndex] + MipStartBottom;
				MipData1.MipCoverageData[MipIndex] = MipData0.MipCoverageData[MipIndex] + MipStartBottom;
			}
		}
	}

	FMemory::Memzero(MipData0.MipCoverageData[0], TextureSizeX * TextureSizeY);
	if (bEncodeVirtualTexture)
	{
		FMemory::Memzero(MipData1.MipCoverageData[0], TextureSizeX * TextureSizeY);
	}
}

void FTLBSLightMapPendingTexture::PreEncodeSkyOcclusionTexture(UTexture* Texture, uint32 LayerIndex, const FColor& InTextureColor)
{
	const int32 NumMips = Texture->Source.GetNumMips();
	check(NumMips > 0);

	FTextureFormatSettings FormatSettings;
	FormatSettings.SRGB = false;
	FormatSettings.CompressionNoAlpha = false;
	FormatSettings.CompressionNone = !GCompressLightmaps;
	Texture->SetLayerFormatSettings(LayerIndex, FormatSettings);

	int32 TextureSizeX = Texture->Source.GetSizeX();
	int32 TextureSizeY = Texture->Source.GetSizeY();

	// Lock all mip levels.
	for (int32 MipIndex = 0; MipIndex < NumMips; ++MipIndex)
	{
		const int32 MipSizeX = FMath::Max(1, TextureSizeX >> MipIndex);
		const int32 MipSizeY = FMath::Max(1, TextureSizeY >> MipIndex);

		SkyOcclusionMipData.MipData[MipIndex] = (FColor*)Texture->Source.LockMip(0, LayerIndex, MipIndex);
		SkyOcclusionMipData.MipCoverageData[MipIndex] = (int8*)FMemory::Malloc(MipSizeX * MipSizeY);
		FMemory::Memzero(SkyOcclusionMipData.MipData[MipIndex], MipSizeX * MipSizeY * sizeof(FColor));
	}

	// Create the uncompressed top mip-level.
	FMemory::Memzero(SkyOcclusionMipData.MipCoverageData[0], TextureSizeX * TextureSizeY);
}

void FTLBSLightMapPendingTexture::PreEncodeAOMaskTexture(UTexture* Texture, uint32 LayerIndex, const FColor& InTextureColor)
{
	const int32 NumMips = Texture->Source.GetNumMips();
	check(NumMips > 0);

	FTextureFormatSettings FormatSettings;
	FormatSettings.SRGB = false;
	FormatSettings.CompressionNoAlpha = false;
	FormatSettings.CompressionNone = !GCompressLightmaps;
	FormatSettings.CompressionSettings = TC_Alpha; // BC4
	Texture->SetLayerFormatSettings(LayerIndex, FormatSettings);

	int32 TextureSizeX = Texture->Source.GetSizeX();
	int32 TextureSizeY = Texture->Source.GetSizeY();

	// Lock all mip levels.
	for (int32 MipIndex = 0; MipIndex < NumMips; ++MipIndex)
	{
		const int32 MipSizeX = FMath::Max(1, TextureSizeX >> MipIndex);
		const int32 MipSizeY = FMath::Max(1, TextureSizeY >> MipIndex);

		AOMaskMipData.MipData[MipIndex] = (uint8*)Texture->Source.LockMip(0, LayerIndex, MipIndex);
		AOMaskMipData.MipCoverageData[MipIndex] = (int8*)FMemory::Malloc(MipSizeX * MipSizeY);
		FMemory::Memzero(AOMaskMipData.MipData[MipIndex], MipSizeX * MipSizeY * sizeof(FColor));
	}

	// Create the uncompressed top mip-level.
	FMemory::Memzero(AOMaskMipData.MipCoverageData[0], TextureSizeX * TextureSizeY);
}

void FTLBSLightMapPendingTexture::PreEncodePrtHQTexture(int32 Index, int32 FormatType, UTexture* Texture, uint32 LayerIndex, const FColor& InTextureColor)
{
#if ENABLE_PRTHQ_API
	static const auto CVarPRTSeparateSHCoefficient = IConsoleManager::Get().FindTConsoleVariableDataInt(TEXT("r.PRT.SeparateSHCoefficient"));
	bool bPRTSeparateSHCoefficient = CVarPRTSeparateSHCoefficient && CVarPRTSeparateSHCoefficient->GetValueOnAnyThread() == 1;
	static const auto CVarPRTCompressLightmap = IConsoleManager::Get().FindTConsoleVariableDataInt(TEXT("r.PRT.CompressLightmap"));
	bool bPRTCompressLightmap = CVarPRTCompressLightmap && CVarPRTCompressLightmap->GetValueOnAnyThread() != 0;

	if (bPRTSeparateSHCoefficient)
	{
		if (Index == 0)
		{
			if (FormatType == 0) {
				PreEncode10rdTransferCoefficientTexture<FPackedTransfer16FColor>(this, Texture, 0u, TextureColor, false, true);
			}
			else if (FormatType == 1) {
				PreEncode10rdTransferCoefficientTexture<FPackedTransferRGBAColor>(this, Texture, 0u, TextureColor, false, true);
			}
			else {
				PreEncode10rdTransferCoefficientTexture<FPackedTransferRGBEColor>(this, Texture, 0u, TextureColor, false, true);
			}
		}
		else
		{
			if (FormatType == 0) {
				PreEncode20rdTransferCoefficientTexture<FPackedTransfer16FColor>(this, Texture, 0u, TextureColor, bPRTCompressLightmap, true);
			}
			else if (FormatType == 1) {
				PreEncode20rdTransferCoefficientTexture<FPackedTransferRGBAColor>(this, Texture, 0u, TextureColor, bPRTCompressLightmap, true);
			}
			else {
				PreEncode20rdTransferCoefficientTexture<FPackedTransferRGBEColor>(this, Texture, 0u, TextureColor, bPRTCompressLightmap, false);
			}
		}
	}
	else
	{
		if (FormatType == 0) {
			PreEncodeTransferCoefficientTexture<FPackedTransfer16FColor>(this, Index, Texture, 0u, TextureColor, bPRTCompressLightmap, true);
		}
		else if (FormatType == 1) {
			PreEncodeTransferCoefficientTexture<FPackedTransferRGBAColor>(this, Index, Texture, 0u, TextureColor, bPRTCompressLightmap, true);
		}
		else {
			PreEncodeTransferCoefficientTexture<FPackedTransferRGBEColor>(this, Index, Texture, 0u, TextureColor, bPRTCompressLightmap, false);
		}
	}
#endif
}

// CTG Begin, ryanthhuang: PRT20
void FTLBSLightMapPendingTexture::PreEncodePrtLQTexture(int32 Index, UTexture* Texture, uint32 LayerIndex, const FColor& InTextureColor)
{
#if ENABLE_PRTLQ_API
	check(Index >= 0 && Index < UE_ARRAY_COUNT(PrtLQTextures))
	const int32 NumMips = Texture->Source.GetNumMips();
	check(NumMips > 0);

	FTextureFormatSettings FormatSettings;
	FormatSettings.CompressionSettings = TC_Default;
	FormatSettings.SRGB = false;
	// FormatSettings.CompressionNoAlpha = false;
	FormatSettings.CompressionNoAlpha = Index == 0 ? false : true;
	FormatSettings.CompressionNone = !GCompressLightmaps;
	Texture->SetLayerFormatSettings(LayerIndex, FormatSettings);

	int32 TextureSizeX = Texture->Source.GetSizeX();
	int32 TextureSizeY = Texture->Source.GetSizeY();

	// Lock all mip levels.
	for (int32 MipIndex = 0; MipIndex < NumMips; ++MipIndex)
	{
		const int32 MipSizeX = FMath::Max(1, TextureSizeX >> MipIndex);
		const int32 MipSizeY = FMath::Max(1, TextureSizeY >> MipIndex);
		PrtLQMipData[Index].MipData[MipIndex] = (FColor*)Texture->Source.LockMip(0, LayerIndex, MipIndex);
		PrtLQMipData[Index].MipCoverageData[MipIndex] = (int8*)FMemory::Malloc(MipSizeX * MipSizeY);
		FMemory::Memzero(PrtLQMipData[Index].MipData[MipIndex], MipSizeX * MipSizeY * sizeof(FColor));
	}

	// Create the uncompressed top mip-level.
	FMemory::Memzero(PrtLQMipData[Index].MipCoverageData[0], TextureSizeX * TextureSizeY);
#endif
}
// CTG End

void FTLBSLightMapPendingTexture::PreEncodeShadowMapTexture(int32 Index, UTexture* Texture, uint32 LayerIndex)
{
	const int32 NumMips = Texture->Source.GetNumMips();
	int32 TextureSizeX = Texture->Source.GetSizeX();
	int32 TextureSizeY = Texture->Source.GetSizeY();

	FTextureFormatSettings FormatSettings;
	FormatSettings.SRGB = false;
	FormatSettings.CompressionNone = true;
	Texture->SetLayerFormatSettings(LayerIndex, FormatSettings);

	for (int32 MipIndex = 0; MipIndex < NumMips; MipIndex++)
	{
		const int32 MipSizeX = FMath::Max(1, TextureSizeX >> MipIndex);
		const int32 MipSizeY = FMath::Max(1, TextureSizeY >> MipIndex);
		// Downsample the previous mip-level, taking into account which texels are mapped.
		if (Index == 0)
		{
			ShadowMipData.MipData0[MipIndex] = (uint8*)Texture->Source.LockMip(0, LayerIndex, MipIndex);
			FMemory::Memzero(ShadowMipData.MipData0[MipIndex], MipSizeX * MipSizeY);
		}
		else
		{
			ShadowMipData.MipData1[MipIndex] = (uint8*)Texture->Source.LockMip(0, LayerIndex, MipIndex);
			FMemory::Memzero(ShadowMipData.MipData1[MipIndex], MipSizeX * MipSizeY);
		}
	}
}

void FTLBSLightMapPendingTexture::EncodeOneLightmap(FTLBSLightingSystem* TLBSSystem, uint32 AllocationIndex)
{
	if (NeedsStaticShadowTexture()) 
	{
		EncodeShadowMapDataOneShadowmap(TLBSSystem, AllocationIndex);
	}

	if (!TLBSSystem->IsUseVirtualTextures())
	{
		if (SkyOcclusionTexture != nullptr)
		{
			EncodeSkyOcclusionTextureOneLightmap(SkyOcclusionTexture, AllocationIndex, 0u, TextureColor);
		}

		if (AOMaterialMaskTexture != nullptr)
		{
			EncodeAOMaskTextureOneLightmap(AOMaterialMaskTexture, AllocationIndex, 0u, TextureColor);
		}
#if ENABLE_PRTHQ_API
		// CTG Begin, ryanthhuang: PRT20
		static const auto CVarPRTLightmapFormat1 = IConsoleManager::Get().FindTConsoleVariableDataInt(TEXT("r.PRT.LightmapForma"));
		const int32 FormatType1 = CVarPRTLightmapFormat1 ? CVarPRTLightmapFormat1->GetValueOnAnyThread() : 0;
		static const auto CVarPRTLightmapFormat2 = IConsoleManager::Get().FindTConsoleVariableDataInt(TEXT("r.PRT.LightmapFormat2Ord"));
		const int32 FormatType2 = CVarPRTLightmapFormat2 ? CVarPRTLightmapFormat2->GetValueOnAnyThread() : -1;
		for (int32 Index = 0; Index < UE_ARRAY_COUNT(PrtHQTextures); Index++)
		{
			auto Texture = PrtHQTextures[Index];
			if (Texture == nullptr)
				continue;
			const int32 FormatType = Index == 0 ? FormatType1 : (FormatType2 != -1 ? FormatType2 : FormatType1);
			EncodePrtHQTextureOneLightmap(Index, FormatType, Texture, AllocationIndex, 0u, TextureColor);
		}
		// CTG end
#endif
#if ENABLE_PRTLQ_API
		// CTG Begin, ryanthhuang: PRT20
		for (int32 Index = 0; Index < UE_ARRAY_COUNT(PrtLQTextures); Index++)
		{
			auto Texture = PrtLQTextures[Index];
			if (Texture == nullptr)
				continue;
			EncodePrtLQTextureOneLightmap(Index, Texture,AllocationIndex,0u, TextureColor);
		}
		// CTG end
#endif
		if (ShadowMapTexture != nullptr) {
			EncodeShadowMapTextureOneShadowmap(0, ShadowMapTexture, AllocationIndex, 0u);
		}

		// Encode and compress the coefficient textures.
		for (uint32 CoefficientIndex = 0; CoefficientIndex < NUM_STORED_LIGHTMAP_COEF; CoefficientIndex += 2)
		{
			auto Texture = Textures[CoefficientIndex];
			if (Texture == nullptr)
				continue;
			EncodeCoefficientTextureOneLightmap(CoefficientIndex, Texture, AllocationIndex, 0u, TextureColor, false);
		}
	}
	else
	{
		// Encode virtual texture light maps
		const uint32 InvalidLayerId = ~0u;

		for (uint32 CoefficientIndex = 0; CoefficientIndex < NUM_STORED_LIGHTMAP_COEF; CoefficientIndex += 2)
		{
			auto VirtualTexture = VirtualTextures[CoefficientIndex];
			if (VirtualTexture == nullptr)
				continue;
			// Copy data from all the separate Lightmap textures into the proper layers of the VT source
			const uint32 SkyOcclusionLayer = VirtualTexture->GetLayerForType(ELightMapVirtualTextureType::SkyOcclusion);
			const uint32 AOMaterialMaskLayer = VirtualTexture->GetLayerForType(ELightMapVirtualTextureType::AOMaterialMask);
			const uint32 ShadowMaskLayer = VirtualTexture->GetLayerForType(ELightMapVirtualTextureType::ShadowMask);

			EncodeCoefficientTextureOneLightmap(CoefficientIndex, VirtualTexture, AllocationIndex, 0u, TextureColor, true);

			if (SkyOcclusionLayer != InvalidLayerId)
			{
				EncodeSkyOcclusionTextureOneLightmap(VirtualTexture, AllocationIndex, SkyOcclusionLayer, TextureColor);
			}
			if (AOMaterialMaskLayer != InvalidLayerId)
			{
				EncodeAOMaskTextureOneLightmap(VirtualTexture, AllocationIndex, AOMaterialMaskLayer, TextureColor);
			}
			if (ShadowMaskLayer != InvalidLayerId)
			{
				EncodeShadowMapTextureOneShadowmap(CoefficientIndex, VirtualTexture, AllocationIndex, ShadowMaskLayer);
			}
		}
	}

	// Link textures to allocations
	{
		FLightMapAllocation& Allocation = *Allocations[AllocationIndex];
		Allocation.LightMap->SkyOcclusionTexture = SkyOcclusionTexture;
		Allocation.LightMap->AOMaterialMaskTexture = AOMaterialMaskTexture;
#if ENABLE_PRTHQ_API
		//CTG begin, ryanthhuang : PRT20
		Allocation.LightMap->PrtHQTextures[0] = PrtHQTextures[0];
		Allocation.LightMap->PrtHQTextures[1] = PrtHQTextures[1];
		//CTG end, ryanthhuang : PRT20
#endif
#if ENABLE_PRTLQ_API
		//CTG begin, ryanthhuang : PRT20
		Allocation.LightMap->PrtLQTextures[0]=PrtLQTextures[0];
		Allocation.LightMap->PrtLQTextures[1]=PrtLQTextures[1];
		//CTG end, ryanthhuang : PRT20
#endif
		Allocation.LightMap->ShadowMapTexture = ShadowMapTexture;
		Allocation.LightMap->Textures[0] = Textures[0];
		Allocation.LightMap->Textures[1] = Textures[2];
		Allocation.LightMap->VirtualTextures[0] = VirtualTextures[0];
		Allocation.LightMap->VirtualTextures[1] = VirtualTextures[2];
	}

	//EncodedMapNum++;
}

void FTLBSLightMapPendingTexture::EncodeUnusedLightmap(FTLBSLightingSystem* TLBSSystem)
{
	int32 MaxUsedX = 0;  int32 MaxUsedY = 0;
	TArray<FIntRect> UnusedRects;
	TFunction<void(int32)> TraveceTextureNodes = [&](int32 Index) {
		auto& CurNode = Nodes[Index];
		// 叶子节点
		if (CurNode.ChildA == INDEX_NONE && CurNode.ChildB == INDEX_NONE)
		{
			// 未使用的
			if (!CurNode.bUsed && (uint32)CurNode.MinX < SizeX && (uint32)CurNode.MinY < SizeY)
			{
				UnusedRects.Emplace(
					(int32)CurNode.MinX, (int32)CurNode.MinY,
					FMath::Min((int32)CurNode.MinX + CurNode.SizeX, (int32)SizeX),
					FMath::Min((int32)CurNode.MinY + CurNode.SizeY, (int32)SizeY)
				);
			}
		}
		else
		{
			TraveceTextureNodes(CurNode.ChildA);
			TraveceTextureNodes(CurNode.ChildB);
		}
		MaxUsedX = FMath::Max(MaxUsedX, CurNode.MinX + CurNode.SizeX);
		MaxUsedY = FMath::Max(MaxUsedY, CurNode.MinY + CurNode.SizeY);
	};
	TraveceTextureNodes(0);
	if (MaxUsedX < (int32)SizeX)
	{
		UnusedRects.Emplace(MaxUsedX, 0, (int32)SizeX, (int32)SizeY);
	}
	if (MaxUsedY < (int32)SizeY)
	{
		UnusedRects.Emplace(0, MaxUsedY, (int32)SizeX, (int32)SizeY);
	}
	if (UnusedRects.Num() > 0){
		UnusedRects.Sort([](const FIntRect& A, const FIntRect& B) {
			if (((A.Min.Y >= B.Min.Y && A.Max.Y <= B.Max.Y) || (A.Min.Y <= B.Min.Y && A.Max.Y >= B.Max.Y)) && A.Min.X != B.Min.X)
				return A.Min.X < B.Min.X;
			return A.Min.Y < B.Min.Y;
			});
		EncodeOneUnusedLightmap(TLBSSystem, UnusedRects);
	}
}

void FTLBSLightMapPendingTexture::EncodeOneUnusedLightmap(FTLBSLightingSystem* TLBSSystem, const TArray<FIntRect>& Ranges)
{
	if (NeedsStaticShadowTexture())
	{
		EncodeShadowMapDataUnusedShadowmap(TLBSSystem, Ranges);
	}

	if (!TLBSSystem->IsUseVirtualTextures())
	{
		if (SkyOcclusionTexture != nullptr)
		{
			EncodeSkyOcclusionTextureUnusedLightmap(SkyOcclusionTexture, Ranges, 0u, TextureColor);
		}

		if (AOMaterialMaskTexture != nullptr)
		{
			EncodeAOMaskTextureUnusedLightmap(AOMaterialMaskTexture, Ranges, 0u, TextureColor);
		}
#if ENABLE_PRTHQ_API
		// CTG Begin, ryanthhuang: PRT20
		static const auto CVarPRTLightmapFormat1 = IConsoleManager::Get().FindTConsoleVariableDataInt(TEXT("r.PRT.LightmapForma"));
		const int32 FormatType1 = CVarPRTLightmapFormat1 ? CVarPRTLightmapFormat1->GetValueOnAnyThread() : 0;
		static const auto CVarPRTLightmapFormat2 = IConsoleManager::Get().FindTConsoleVariableDataInt(TEXT("r.PRT.LightmapFormat2Ord"));
		const int32 FormatType2 = CVarPRTLightmapFormat2 ? CVarPRTLightmapFormat2->GetValueOnAnyThread() : -1;
		for (int32 Index = 0; Index < UE_ARRAY_COUNT(PrtHQTextures); Index++)
		{
			auto Texture = PrtHQTextures[Index];
			if (Texture == nullptr)
				continue;
			const int32 FormatType = Index == 0 ? FormatType1 : (FormatType2 != -1 ? FormatType2 : FormatType1);
			EncodePrtHQTextureUnusedLightmap(Index, FormatType, PrtHQTextures[Index], Ranges, 0u, TextureColor);
		}
		// end
#endif

#if ENABLE_PRTLQ_API
		// CTG Begin, ryanthhuang: PRT20
		for (int32 Index = 0; Index < UE_ARRAY_COUNT(PrtLQTextures); Index++)
		{
			auto Texture = PrtLQTextures[Index];
			if (Texture == nullptr)
			{
				continue;
			}
			EncodePrtLQTextureUnusedLightmap(Index,PrtLQTextures[Index],Ranges,0u, TextureColor);
		}
		// end
#endif
		if (ShadowMapTexture != nullptr) {
			EncodeShadowMapTextureUnusedShadowmap(0, ShadowMapTexture, Ranges, 0u);
		}

		// Encode and compress the coefficient textures.
		for (uint32 CoefficientIndex = 0; CoefficientIndex < NUM_STORED_LIGHTMAP_COEF; CoefficientIndex += 2)
		{
			auto Texture = Textures[CoefficientIndex];
			if (Texture == nullptr)
				continue;
			EncodeCoefficientTextureUnusedLightmap(CoefficientIndex, Texture, Ranges, 0u, TextureColor, false);
		}
	}
	else
	{
		// Encode virtual texture light maps
		const uint32 InvalidLayerId = ~0u;

		for (uint32 CoefficientIndex = 0; CoefficientIndex < NUM_STORED_LIGHTMAP_COEF; CoefficientIndex += 2)
		{
			auto VirtualTexture = VirtualTextures[CoefficientIndex];
			if (VirtualTexture == nullptr)
				continue;
			// Copy data from all the separate Lightmap textures into the proper layers of the VT source
			const uint32 SkyOcclusionLayer = VirtualTexture->GetLayerForType(ELightMapVirtualTextureType::SkyOcclusion);
			const uint32 AOMaterialMaskLayer = VirtualTexture->GetLayerForType(ELightMapVirtualTextureType::AOMaterialMask);
			const uint32 ShadowMaskLayer = VirtualTexture->GetLayerForType(ELightMapVirtualTextureType::ShadowMask);

			EncodeCoefficientTextureUnusedLightmap(CoefficientIndex, VirtualTexture, Ranges, 0u, TextureColor, true);

			if (SkyOcclusionLayer != InvalidLayerId)
			{
				EncodeSkyOcclusionTextureUnusedLightmap(VirtualTexture, Ranges, SkyOcclusionLayer, TextureColor);
			}
			if (AOMaterialMaskLayer != InvalidLayerId)
			{
				EncodeAOMaskTextureUnusedLightmap(VirtualTexture, Ranges, AOMaterialMaskLayer, TextureColor);
			}
			if (ShadowMaskLayer != InvalidLayerId)
			{
				EncodeShadowMapTextureUnusedShadowmap(CoefficientIndex, VirtualTexture, Ranges, ShadowMaskLayer);
			}
		}
	}
}

void FTLBSLightMapPendingTexture::EncodeShadowMapDataOneShadowmap(FTLBSLightingSystem* TLBSSystem, uint32 AllocationIndex)
{
	const int32 TextureSizeX = GetSizeX();
	const int32 TextureSizeY = GetSizeY();
	const int32 NumMips = FMath::Max(FMath::CeilLogTwo(TextureSizeX), FMath::CeilLogTwo(TextureSizeY)) + 1;
	
	auto& Allocation = *Allocations[AllocationIndex];
	{
		bool bChannelUsed[4] = { 0 };
		FVector4f InvUniformPenumbraSize(0, 0, 0, 0);

		for (int32 ChannelIndex = 0; ChannelIndex < 4; ChannelIndex++)
		{
			for (const auto& [CurrentLight, SourceSamples] : Allocation.ShadowMapData)
			{
				const FLightComponentMapBuildData* LightBuildData = TLBSSystem->GetOrCreateRegistryForActor(CurrentLight->GetOwner())->GetLightBuildData(CurrentLight->LightGuid);

				// Should have been setup by ReassignStationaryLightChannels
				check(LightBuildData);

				if (LightBuildData->ShadowMapChannel == ChannelIndex)
				{
					bChannelUsed[ChannelIndex] = true;
					// Warning - storing one penumbra size for the whole shadowmap even though multiple lights can share a channel
					InvUniformPenumbraSize[ChannelIndex] = 1.0f / CurrentLight->GetUniformPenumbraSize();

					// Copy the raw data for this light-map into the raw texture data array.
					for (int32 Y = Allocation.MappedRect.Min.Y; Y < Allocation.MappedRect.Max.Y; ++Y)
					{
						for (int32 X = Allocation.MappedRect.Min.X; X < Allocation.MappedRect.Max.X; ++X)
						{
							int32 DestY = Y - Allocation.MappedRect.Min.Y + Allocation.OffsetY;
							int32 DestX = X - Allocation.MappedRect.Min.X + Allocation.OffsetX;

							FFourDistanceFieldSamples& DestSample = ShadowMipData.MipCoverageData[0][DestY * TextureSizeX + DestX];
							const FQuantizedSignedDistanceFieldShadowSample& SourceSample = SourceSamples[Y * Allocation.TotalSizeX + X];

							if (SourceSample.Coverage > 0)
							{
								// Note: multiple lights can write to different parts of the destination due to channel assignment
								DestSample.Samples[ChannelIndex] = SourceSample;
							}
						}
					}
				}
			}
		}

		// Free the shadow-map's raw data.
		for (auto& ShadowMapPair : Allocation.ShadowMapData)
		{
			ShadowMapPair.Value.Empty();
		}

		int32 PaddedSizeX = Allocation.TotalSizeX;
		int32 PaddedSizeY = Allocation.TotalSizeY;
		int32 BaseX = Allocation.OffsetX - Allocation.MappedRect.Min.X;
		int32 BaseY = Allocation.OffsetY - Allocation.MappedRect.Min.Y;

#if WITH_EDITOR
		if (GLightmassDebugOptions.bPadMappings && (Allocation.PaddingType == LMPT_NormalPadding))
		{
			if ((PaddedSizeX - 2 > 0) && ((PaddedSizeY - 2) > 0))
			{
				PaddedSizeX -= 2;
				PaddedSizeY -= 2;
				BaseX += 1;
				BaseY += 1;
			}
		}
#endif	

		for (int32 ChannelIndex = 0; ChannelIndex < 4; ChannelIndex++)
		{
			Allocation.LightMap->bShadowChannelValid[ChannelIndex] = bChannelUsed[ChannelIndex];
		}

		Allocation.LightMap->InvUniformPenumbraSize = InvUniformPenumbraSize;
	}

	FIntRect Range = {
		Allocation.MappedRect.Min.X + Allocation.OffsetX,
		Allocation.MappedRect.Min.Y + Allocation.OffsetY,
		Allocation.MappedRect.Max.X + Allocation.OffsetX,
		Allocation.MappedRect.Max.Y + Allocation.OffsetY
	};
	GenerateShadowmapMipsAndDilateSamples({ Range }, NumMips, TextureSizeX, TextureSizeY, ShadowMipData.MipCoverageData);
}

void FTLBSLightMapPendingTexture::EncodeShadowMapTextureOneShadowmap(int32 Index, UTexture* Texture, uint32 AllocationIndex, uint32 LayerIndex)
{
	const int32 NumMips = Texture->Source.GetNumMips();
	FLightMapAllocation& Allocation = *Allocations[AllocationIndex];

	const ETextureSourceFormat SourceFormat = Texture->Source.GetFormat(LayerIndex);
	check(SourceFormat == TSF_G8 || SourceFormat == TSF_BGRA8);

	const int32 TextureSizeX = Texture->Source.GetSizeX();
	const int32 TextureSizeY = Texture->Source.GetSizeY();

	FIntRect Range = {
		Allocation.MappedRect.Min.X + Allocation.OffsetX,
		Allocation.MappedRect.Min.Y + Allocation.OffsetY,
		Allocation.MappedRect.Max.X + Allocation.OffsetX,
		Allocation.MappedRect.Max.Y + Allocation.OffsetY
	};

	GenerateShadowmapMipsAndDilateByte({ Range }, NumMips, TextureSizeX, TextureSizeY, SourceFormat, Index == 0 ? ShadowMipData.MipData0 : ShadowMipData.MipData1, ShadowMipData.MipCoverageData);
}

void FTLBSLightMapPendingTexture::EncodeCoefficientTextureOneLightmap(int32 CoefficientIndex, UTexture* Texture, uint32 AllocationIndex, uint32 LayerIndex, const FColor& InTextureColor, bool bEncodeVirtualTexture)
{
	const int32 NumMips = Texture->Source.GetNumMips();
	check(NumMips > 0);
	auto& MipData0 = CoefficientMipData0[CoefficientIndex / 2];
	auto& MipData1 = CoefficientMipData1[CoefficientIndex / 2];

	const int32 TextureSizeX = GetSizeX();
	const int32 TextureSizeY = GetSizeY();

	auto& Allocation = Allocations[AllocationIndex];
	for (int k = 0; k < 2; k++)
	{
		Allocation->LightMap->ScaleVectors[CoefficientIndex + k] = FVector4f(
			Allocation->Scale[CoefficientIndex + k][0],
			Allocation->Scale[CoefficientIndex + k][1],
			Allocation->Scale[CoefficientIndex + k][2],
			Allocation->Scale[CoefficientIndex + k][3]
		);

		Allocation->LightMap->AddVectors[CoefficientIndex + k] = FVector4f(
			Allocation->Add[CoefficientIndex + k][0],
			Allocation->Add[CoefficientIndex + k][1],
			Allocation->Add[CoefficientIndex + k][2],
			Allocation->Add[CoefficientIndex + k][3]
		);
	}

	// Skip encoding of this texture if we were asked not to bother
	if (!Allocation->bSkipEncoding)
	{
		CoefficientMipTextureRect.Min.X = FMath::Min<int32>(CoefficientMipTextureRect.Min.X, Allocation->OffsetX);
		CoefficientMipTextureRect.Min.Y = FMath::Min<int32>(CoefficientMipTextureRect.Min.Y, Allocation->OffsetY);
		CoefficientMipTextureRect.Max.X = FMath::Max<int32>(CoefficientMipTextureRect.Max.X, Allocation->OffsetX + Allocation->MappedRect.Width());
		CoefficientMipTextureRect.Max.Y = FMath::Max<int32>(CoefficientMipTextureRect.Max.Y, Allocation->OffsetY + Allocation->MappedRect.Height());

		NumNonPower2Texels += CoefficientMipTextureRect.Width() * CoefficientMipTextureRect.Height();

		// Copy the raw data for this light-map into the raw texture data array.
		for (int32 Y = Allocation->MappedRect.Min.Y; Y < Allocation->MappedRect.Max.Y; ++Y)
		{
			for (int32 X = Allocation->MappedRect.Min.X; X < Allocation->MappedRect.Max.X; ++X)
			{
				const FLightMapCoefficients& SourceCoefficients = Allocation->RawData[Y * Allocation->TotalSizeX + X];

				int32 DestY = Y - Allocation->MappedRect.Min.Y + Allocation->OffsetY;
				int32 DestX = X - Allocation->MappedRect.Min.X + Allocation->OffsetX;

				FColor& DestColor = MipData0.MipData[0][DestY * TextureSizeX + DestX];
				int8& DestCoverage = MipData0.MipCoverageData[0][DestY * TextureSizeX + DestX];

				FColor& DestBottomColor = MipData1.MipData[0][DestX + DestY * TextureSizeX];
				int8& DestBottomCoverage = MipData1.MipCoverageData[0][DestX + DestY * TextureSizeX];

				DestColor.R = SourceCoefficients.Coefficients[CoefficientIndex][0];
				DestColor.G = SourceCoefficients.Coefficients[CoefficientIndex][1];
				DestColor.B = SourceCoefficients.Coefficients[CoefficientIndex][2];
				DestColor.A = SourceCoefficients.Coefficients[CoefficientIndex][3];

				DestBottomColor.R = SourceCoefficients.Coefficients[CoefficientIndex + 1][0];
				DestBottomColor.G = SourceCoefficients.Coefficients[CoefficientIndex + 1][1];
				DestBottomColor.B = SourceCoefficients.Coefficients[CoefficientIndex + 1][2];
				DestBottomColor.A = SourceCoefficients.Coefficients[CoefficientIndex + 1][3];

				if (GVisualizeLightmapTextures)
				{
					DestColor = InTextureColor;
				}

				// uint8 -> int8
				DestCoverage = DestBottomCoverage = SourceCoefficients.Coverage / 2;
				if (SourceCoefficients.Coverage > 0)
				{
					NumLightmapMappedTexels++;
				}
				else
				{
					NumLightmapUnmappedTexels++;
				}

				if (bTexelDebuggingEnabled)
				{
					int32 PaddedX = X;
					int32 PaddedY = Y;
					if (GLightmassDebugOptions.bPadMappings && (Allocation->PaddingType == LMPT_NormalPadding))
					{
						if (Allocation->TotalSizeX - 2 > 0 && Allocation->TotalSizeY - 2 > 0)
						{
							PaddedX -= 1;
							PaddedY -= 1;
						}
					}

					if (Allocation->bDebug
						&& PaddedX == GCurrentSelectedLightmapSample.LocalX
						&& PaddedY == GCurrentSelectedLightmapSample.LocalY)
					{
						extern FColor GTexelSelectionColor;
						DestColor = GTexelSelectionColor;
					}
				}
			}
		}
	}
	FIntRect Range = {
		Allocation->MappedRect.Min.X + Allocation->OffsetX,
		Allocation->MappedRect.Min.Y + Allocation->OffsetY,
		Allocation->MappedRect.Max.X + Allocation->OffsetX,
		Allocation->MappedRect.Max.Y + Allocation->OffsetY
	};
	GenerateLightmapMipsAndDilateColor({ Range }, NumMips, TextureSizeX, TextureSizeY, InTextureColor, MipData0.MipData, MipData0.MipCoverageData);
	GenerateLightmapMipsAndDilateColor({ Range }, NumMips, TextureSizeX, TextureSizeY, InTextureColor, MipData1.MipData, MipData1.MipCoverageData);
}

void FTLBSLightMapPendingTexture::EncodeSkyOcclusionTextureOneLightmap(UTexture* Texture, uint32 AllocationIndex, uint32 LayerIndex, const FColor& InTextureColor)
{
	const int32 NumMips = Texture->Source.GetNumMips();
	const int32 TextureSizeX = Texture->Source.GetSizeX();
	const int32 TextureSizeY = Texture->Source.GetSizeY();

	auto& Allocation = Allocations[AllocationIndex];

	// Skip encoding of this texture if we were asked not to bother
	if (!Allocation->bSkipEncoding)
	{
		// Copy the raw data for this light-map into the raw texture data array.
		for (int32 Y = Allocation->MappedRect.Min.Y; Y < Allocation->MappedRect.Max.Y; ++Y)
		{
			for (int32 X = Allocation->MappedRect.Min.X; X < Allocation->MappedRect.Max.X; ++X)
			{
				const FLightMapCoefficients& SourceCoefficients = Allocation->RawData[Y * Allocation->TotalSizeX + X];

				int32 DestY = Y - Allocation->MappedRect.Min.Y + Allocation->OffsetY;
				int32 DestX = X - Allocation->MappedRect.Min.X + Allocation->OffsetX;

				FColor& DestColor = SkyOcclusionMipData.MipData[0][DestY * TextureSizeX + DestX];

				DestColor.R = SourceCoefficients.SkyOcclusion[0];
				DestColor.G = SourceCoefficients.SkyOcclusion[1];
				DestColor.B = SourceCoefficients.SkyOcclusion[2];
				DestColor.A = SourceCoefficients.SkyOcclusion[3];

				int8& DestCoverage = SkyOcclusionMipData.MipCoverageData[0][DestY * TextureSizeX + DestX];
				DestCoverage = SourceCoefficients.Coverage / 2;
			}
		}
	}
	FIntRect Range = {
		Allocation->MappedRect.Min.X + Allocation->OffsetX,
		Allocation->MappedRect.Min.Y + Allocation->OffsetY,
		Allocation->MappedRect.Max.X + Allocation->OffsetX,
		Allocation->MappedRect.Max.Y + Allocation->OffsetY
	};
	GenerateLightmapMipsAndDilateColor({ Range }, NumMips, TextureSizeX, TextureSizeY, InTextureColor, SkyOcclusionMipData.MipData, SkyOcclusionMipData.MipCoverageData);
}

void FTLBSLightMapPendingTexture::EncodeAOMaskTextureOneLightmap(UTexture* Texture, uint32 AllocationIndex, uint32 LayerIndex, const FColor& InTextureColor)
{
	const int32 NumMips = Texture->Source.GetNumMips();
	const int32 TextureSizeX = GetSizeX();
	const int32 TextureSizeY = GetSizeY();

	auto& Allocation = Allocations[AllocationIndex];

	// Skip encoding of this texture if we were asked not to bother
	if (!Allocation->bSkipEncoding)
	{
		// Copy the raw data for this light-map into the raw texture data array.
		for (int32 Y = Allocation->MappedRect.Min.Y; Y < Allocation->MappedRect.Max.Y; ++Y)
		{
			for (int32 X = Allocation->MappedRect.Min.X; X < Allocation->MappedRect.Max.X; ++X)
			{
				const FLightMapCoefficients& SourceCoefficients = Allocation->RawData[Y * Allocation->TotalSizeX + X];

				int32 DestY = Y - Allocation->MappedRect.Min.Y + Allocation->OffsetY;
				int32 DestX = X - Allocation->MappedRect.Min.X + Allocation->OffsetX;

				uint8& DestValue = AOMaskMipData.MipData[0][DestY * TextureSizeX + DestX];

				DestValue = SourceCoefficients.AOMaterialMask;

				int8& DestCoverage = AOMaskMipData.MipCoverageData[0][DestY * TextureSizeX + DestX];
				DestCoverage = SourceCoefficients.Coverage / 2;
			}
		}
	}
	FIntRect Range = {
		Allocation->MappedRect.Min.X + Allocation->OffsetX,
		Allocation->MappedRect.Min.Y + Allocation->OffsetY,
		Allocation->MappedRect.Max.X + Allocation->OffsetX,
		Allocation->MappedRect.Max.Y + Allocation->OffsetY
	};
	GenerateLightmapMipsAndDilateByte({ Range }, NumMips, TextureSizeX, TextureSizeY, InTextureColor.R, AOMaskMipData.MipData, AOMaskMipData.MipCoverageData);
}

void FTLBSLightMapPendingTexture::EncodePrtHQTextureOneLightmap(int32 Index, int32 FormatType, UTexture* Texture, uint32 AllocationIndex, uint32 LayerIndex, const FColor& InTextureColor)
{
#if ENABLE_PRTHQ_API
	static const auto CVarPRTSeparateSHCoefficient = IConsoleManager::Get().FindTConsoleVariableDataInt(TEXT("r.PRT.SeparateSHCoefficient"));
	bool bPRTSeparateSHCoefficient = CVarPRTSeparateSHCoefficient && CVarPRTSeparateSHCoefficient->GetValueOnAnyThread() == 1;
	if (bPRTSeparateSHCoefficient)
	{
		if (Index == 0)
		{
			if (FormatType == 0) {
				Encode10rdTransferCoefficientTextureOneLightmap<FPackedTransfer16FColor>(this, Texture, AllocationIndex, 0u, InTextureColor);
			}
			else if (FormatType == 1) {
				Encode10rdTransferCoefficientTextureOneLightmap<FPackedTransferRGBAColor>(this, Texture, AllocationIndex, 0u, InTextureColor);
			}
			else {
				Encode10rdTransferCoefficientTextureOneLightmap<FPackedTransferRGBEColor>(this, Texture, AllocationIndex, 0u, InTextureColor);
			}
		}
		else
		{
			if (FormatType == 0) {
				Encode20rdTransferCoefficientTextureOneLightmap<FPackedTransfer16FColor>(this, Texture, AllocationIndex, 0u, InTextureColor);
			}
			else if (FormatType == 1) {
				Encode20rdTransferCoefficientTextureOneLightmap<FPackedTransferRGBAColor>(this, Texture, AllocationIndex, 0u, InTextureColor);
			}
			else {
				Encode20rdTransferCoefficientTextureOneLightmap<FPackedTransferRGBEColor>(this, Texture, AllocationIndex, 0u, InTextureColor);
			}
		}
	}
	else
	{
		if (FormatType == 0) {
			EncodeTransferCoefficientTextureOneLightmap<FPackedTransfer16FColor>(this, Index, Texture, AllocationIndex, 0u, InTextureColor);
		}
		else if (FormatType == 1) {
			EncodeTransferCoefficientTextureOneLightmap<FPackedTransferRGBAColor>(this, Index, Texture, AllocationIndex, 0u, InTextureColor);
		}
		else {
			EncodeTransferCoefficientTextureOneLightmap<FPackedTransferRGBEColor>(this, Index, Texture, AllocationIndex, 0u, InTextureColor);
		}
	}
#endif
}

// CTG Begin, ryanthhuang: PRT20
void FTLBSLightMapPendingTexture::EncodePrtLQTextureOneLightmap(int32 Index, UTexture* Texture, uint32 AllocationIndex,uint32 LayerIndex, const FColor& InTextureColor)
{
#if ENABLE_PRTLQ_API
	const int32 NumMips = Texture->Source.GetNumMips();
	const int32 TextureSizeX = Texture->Source.GetSizeX();
	const int32 TextureSizeY = Texture->Source.GetSizeY();

	FIntRect TextureRect(MAX_int32, MAX_int32, MIN_int32, MIN_int32);
	auto& Allocation = Allocations[AllocationIndex];
	{
		Allocation->LightMap->PrtLQScaleVector = FVector4f(
			Allocation->PrtLQScale[0],
			Allocation->PrtLQScale[1],
			Allocation->PrtLQScale[2],
			Allocation->PrtLQScale[3]
		);
		Allocation->LightMap->PrtLQAddVector = FVector4f(
			Allocation->PrtLQAdd[0],
			Allocation->PrtLQAdd[1],
			Allocation->PrtLQAdd[2],
			Allocation->PrtLQAdd[3]
		);
	}

	// Skip encoding of this texture if we were asked not to bother
	if (!Allocation->bSkipEncoding)
	{
		TextureRect.Min.X = FMath::Min<int32>(TextureRect.Min.X, Allocation->OffsetX);
		TextureRect.Min.Y = FMath::Min<int32>(TextureRect.Min.Y, Allocation->OffsetY);
		TextureRect.Max.X = FMath::Max<int32>(TextureRect.Max.X, Allocation->OffsetX + Allocation->MappedRect.Width());
		TextureRect.Max.Y = FMath::Max<int32>(TextureRect.Max.Y, Allocation->OffsetY + Allocation->MappedRect.Height());

		// Copy the raw data for this light-map into the raw texture data array.
		for (int32 Y = Allocation->MappedRect.Min.Y; Y < Allocation->MappedRect.Max.Y; ++Y)
		{
			for (int32 X = Allocation->MappedRect.Min.X; X < Allocation->MappedRect.Max.X; ++X)
			{
				const FLightMapCoefficients& SourceCoefficients = Allocation->RawData[Y * Allocation->TotalSizeX + X];

				int32 DestY = Y - Allocation->MappedRect.Min.Y + Allocation->OffsetY;
				int32 DestX = X - Allocation->MappedRect.Min.X + Allocation->OffsetX;

				FColor& DestColor = PrtLQMipData[Index].MipData[0][DestY * TextureSizeX + DestX];

				switch (Index)
				{
				case 0:
					DestColor.R = SourceCoefficients.PrtLQCoefficients[0][0];
					DestColor.G = SourceCoefficients.PrtLQCoefficients[0][1];
					DestColor.B = SourceCoefficients.PrtLQCoefficients[0][2];
					DestColor.A = SourceCoefficients.PrtLQCoefficients[0][3];
					// DestColor.A = SourceCoefficients.SkyOcclusion[3];
					break;
				case 1:
					DestColor.R = SourceCoefficients.PrtLQCoefficients[1][0];
					DestColor.G = SourceCoefficients.PrtLQCoefficients[1][1];
					DestColor.B = SourceCoefficients.PrtLQCoefficients[1][2];
					DestColor.A = 0;
					break;
				default:
					check(false);
					break;
				}

				int8& DestCoverage = PrtLQMipData[Index].MipCoverageData[0][DestY * TextureSizeX + DestX];
				DestCoverage = SourceCoefficients.Coverage / 2;
			}
		}
	}
	FIntRect Range = {
		Allocation->MappedRect.Min.X + Allocation->OffsetX,
		Allocation->MappedRect.Min.Y + Allocation->OffsetY,
		Allocation->MappedRect.Max.X + Allocation->OffsetX,
		Allocation->MappedRect.Max.Y + Allocation->OffsetY
	};
	GenerateLightmapMipsAndDilateColor({Range},NumMips, TextureSizeX, TextureSizeY, TextureColor, PrtLQMipData[Index].MipData,PrtLQMipData[Index].MipCoverageData);
#endif
}
// CTG End

void FTLBSLightMapPendingTexture::EncodeShadowMapDataUnusedShadowmap(FTLBSLightingSystem* TLBSSystem, const TArray<FIntRect>& Ranges)
{
	const int32 TextureSizeX = GetSizeX();
	const int32 TextureSizeY = GetSizeY();
	const int32 NumMips = FMath::Max(FMath::CeilLogTwo(TextureSizeX), FMath::CeilLogTwo(TextureSizeY)) + 1;
	GenerateShadowmapMipsAndDilateSamples(Ranges, NumMips, TextureSizeX, TextureSizeY, ShadowMipData.MipCoverageData);
}

void FTLBSLightMapPendingTexture::EncodeShadowMapTextureUnusedShadowmap(int32 Index, UTexture* Texture, const TArray<FIntRect>& Ranges, uint32 LayerIndex)
{
	const int32 NumMips = Texture->Source.GetNumMips();
	const ETextureSourceFormat SourceFormat = Texture->Source.GetFormat(LayerIndex);
	check(SourceFormat == TSF_G8 || SourceFormat == TSF_BGRA8);

	const int32 TextureSizeX = Texture->Source.GetSizeX();
	const int32 TextureSizeY = Texture->Source.GetSizeY();
	GenerateShadowmapMipsAndDilateByte(Ranges, NumMips, TextureSizeX, TextureSizeY, SourceFormat, Index == 0 ? ShadowMipData.MipData0 : ShadowMipData.MipData1, ShadowMipData.MipCoverageData);
}

void FTLBSLightMapPendingTexture::EncodeCoefficientTextureUnusedLightmap(int32 CoefficientIndex, UTexture* Texture, const TArray<FIntRect>& Ranges, uint32 LayerIndex, const FColor& InTextureColor, bool bEncodeVirtualTexture)
{
	const int32 NumMips = Texture->Source.GetNumMips();
	check(NumMips > 0);

	auto& MipData0 = CoefficientMipData0[CoefficientIndex / 2];
	auto& MipData1 = CoefficientMipData1[CoefficientIndex / 2];

	const int32 TextureSizeX = GetSizeX();
	const int32 TextureSizeY = GetSizeY();

	GenerateLightmapMipsAndDilateColor(Ranges, NumMips, TextureSizeX, TextureSizeY, InTextureColor, MipData0.MipData, MipData0.MipCoverageData);
	GenerateLightmapMipsAndDilateColor(Ranges, NumMips, TextureSizeX, TextureSizeY, InTextureColor, MipData1.MipData, MipData1.MipCoverageData);
}

void FTLBSLightMapPendingTexture::EncodeSkyOcclusionTextureUnusedLightmap(UTexture* Texture, const TArray<FIntRect>& Ranges, uint32 LayerIndex, const FColor& InTextureColor)
{
	const int32 NumMips = Texture->Source.GetNumMips();
	const int32 TextureSizeX = Texture->Source.GetSizeX();
	const int32 TextureSizeY = Texture->Source.GetSizeY();

	GenerateLightmapMipsAndDilateColor(Ranges, NumMips, TextureSizeX, TextureSizeY, InTextureColor, SkyOcclusionMipData.MipData, SkyOcclusionMipData.MipCoverageData);
}

void FTLBSLightMapPendingTexture::EncodePrtHQTextureUnusedLightmap(int32 Index, int32 FormatType, UTexture* Texture, const TArray<FIntRect>& Ranges, uint32 LayerIndex, const FColor& InTextureColor)
{
#if ENABLE_PRTHQ_API
	static const auto CVarPRTSeparateSHCoefficient = IConsoleManager::Get().FindTConsoleVariableDataInt(TEXT("r.PRT.SeparateSHCoefficient"));
	bool bPRTSeparateSHCoefficient = CVarPRTSeparateSHCoefficient && CVarPRTSeparateSHCoefficient->GetValueOnAnyThread() == 1;

	const int32 NumMips = Texture->Source.GetNumMips();
	const int32 TextureSizeX = GetSizeX();
	const int32 TextureSizeY = GetSizeY();

	if (bPRTSeparateSHCoefficient)
	{
		if (Index == 0)
		{
			if (FormatType == 0) {
				::GenerateLightmapMipsAndDilateColor<FPackedTransfer16FColor>(Ranges, NumMips, TextureSizeX, TextureSizeY, InTextureColor, (FPackedTransfer16FColor**)PrtHQMipData[Index].MipData, PrtHQMipData[Index].MipCoverageData);
			}
			else if (FormatType == 1) {
				::GenerateLightmapMipsAndDilateColor<FPackedTransferRGBAColor>(Ranges, NumMips, TextureSizeX, TextureSizeY, InTextureColor, (FPackedTransferRGBAColor**)PrtHQMipData[Index].MipData, PrtHQMipData[Index].MipCoverageData);
			}
			else {
				::GenerateLightmapMipsAndDilateColor<FPackedTransferRGBEColor>(Ranges, NumMips, TextureSizeX, TextureSizeY, InTextureColor, (FPackedTransferRGBEColor**)PrtHQMipData[Index].MipData, PrtHQMipData[Index].MipCoverageData);
			}
		}
		else
		{
			TArray<FIntRect> NewRanges;
			NewRanges.Reserve(Ranges.Num());
			for (const auto& Range : Ranges)
			{
				NewRanges.Add({ Range.Min.X, Range.Min.Y * 2 , Range.Max.X, Range.Max.Y * 2});
			}
			if (FormatType == 0) {
				::GenerateLightmapMipsAndDilateColor<FPackedTransfer16FColor>(NewRanges, NumMips, TextureSizeX, TextureSizeY * 2, InTextureColor, (FPackedTransfer16FColor**)PrtHQMipData[Index].MipData, PrtHQMipData[Index].MipCoverageData);
			}
			else if (FormatType == 1) {
				::GenerateLightmapMipsAndDilateColor<FPackedTransferRGBAColor>(NewRanges, NumMips, TextureSizeX, TextureSizeY * 2, InTextureColor, (FPackedTransferRGBAColor**)PrtHQMipData[Index].MipData, PrtHQMipData[Index].MipCoverageData);
			}
			else {
				::GenerateLightmapMipsAndDilateColor<FPackedTransferRGBEColor>(NewRanges, NumMips, TextureSizeX, TextureSizeY * 2, InTextureColor, (FPackedTransferRGBEColor**)PrtHQMipData[Index].MipData, PrtHQMipData[Index].MipCoverageData);
			}
		}
	}
	else
	{
		if (FormatType == 0) {
			::GenerateLightmapMipsAndDilateColor<FPackedTransfer16FColor>(Ranges, NumMips, TextureSizeX, TextureSizeY, InTextureColor, (FPackedTransfer16FColor**)PrtHQMipData[Index].MipData, PrtHQMipData[Index].MipCoverageData);
			::GenerateLightmapMipsAndDilateColor<FPackedTransfer16FColor>(Ranges, NumMips, TextureSizeX, TextureSizeY, InTextureColor, (FPackedTransfer16FColor**)PrtHQMipData[Index + 2].MipData, PrtHQMipData[Index + 2].MipCoverageData);
		}
		else if (FormatType == 1) {
			::GenerateLightmapMipsAndDilateColor<FPackedTransferRGBAColor>(Ranges, NumMips, TextureSizeX, TextureSizeY, InTextureColor, (FPackedTransferRGBAColor**)PrtHQMipData[Index].MipData, PrtHQMipData[Index].MipCoverageData);
			::GenerateLightmapMipsAndDilateColor<FPackedTransferRGBAColor>(Ranges, NumMips, TextureSizeX, TextureSizeY, InTextureColor, (FPackedTransferRGBAColor**)PrtHQMipData[Index + 2].MipData, PrtHQMipData[Index + 2].MipCoverageData);
		}
		else {
			::GenerateLightmapMipsAndDilateColor<FPackedTransferRGBEColor>(Ranges, NumMips, TextureSizeX, TextureSizeY, InTextureColor, (FPackedTransferRGBEColor**)PrtHQMipData[Index].MipData, PrtHQMipData[Index].MipCoverageData);
			::GenerateLightmapMipsAndDilateColor<FPackedTransferRGBEColor>(Ranges, NumMips, TextureSizeX, TextureSizeY, InTextureColor, (FPackedTransferRGBEColor**)PrtHQMipData[Index + 2].MipData, PrtHQMipData[Index + 2].MipCoverageData);
		}
	}
#endif
}

void FTLBSLightMapPendingTexture::EncodePrtLQTextureUnusedLightmap(int32 Index,UTexture* Texture, const TArray<FIntRect>& Ranges, uint32 LayerIndex, const FColor& InTextureColor)
{
#if ENABLE_PRTLQ_API
	const int32 NumMips = Texture->Source.GetNumMips();
	const int32 TextureSizeX = Texture->Source.GetSizeX();
	const int32 TextureSizeY = Texture->Source.GetSizeY();

	GenerateLightmapMipsAndDilateColor(Ranges, NumMips, TextureSizeX, TextureSizeY, InTextureColor, PrtLQMipData[Index].MipData, PrtLQMipData[Index].MipCoverageData);
#endif
}

void FTLBSLightMapPendingTexture::EncodeAOMaskTextureUnusedLightmap(UTexture* Texture, const TArray<FIntRect>& Ranges, uint32 LayerIndex, const FColor& InTextureColor)
{
	const int32 NumMips = Texture->Source.GetNumMips();
	const int32 TextureSizeX = Texture->Source.GetSizeX();
	const int32 TextureSizeY = Texture->Source.GetSizeY();

	GenerateLightmapMipsAndDilateByte(Ranges, NumMips, TextureSizeX, TextureSizeY, InTextureColor.R, AOMaskMipData.MipData, AOMaskMipData.MipCoverageData);
}

void FTLBSLightMapPendingTexture::PostEncode(FTLBSLightingSystem* TLBSSystem)
{
	if (!TLBSSystem->IsUseVirtualTextures())
	{
		if (SkyOcclusionTexture != nullptr)
		{
			PostEncodeSkyOcclusionTexture(SkyOcclusionTexture, 0u);
		}

		if (AOMaterialMaskTexture != nullptr)
		{
			PostEncodeAOMaskTexture(AOMaterialMaskTexture, 0u);
		}
#if ENABLE_PRTHQ_API
		// CTG Begin, ryanthhuang: PRT20
		for (int32 Index = 0; Index < UE_ARRAY_COUNT(PrtHQTextures); Index++)
		{
			auto Texture = PrtHQTextures[Index];
			if (Texture == nullptr)
			{
				continue;
			}
			PostEncodePrtHQTexture(Index, PrtHQTextures[Index], 0u);
		}
		// CTG End
#endif

#if ENABLE_PRTLQ_API
		// CTG Begin, ryanthhuang: PRT20
		for (int32 Index = 0; Index < UE_ARRAY_COUNT(PrtLQTextures); Index++)
		{
			auto Texture = PrtLQTextures[Index];
			if (Texture == nullptr)
			{
				continue;
			}
			PostEncodePrtLQTexture(Index,PrtLQTextures[Index],0u);
		}
		// CTG End
#endif
		if (ShadowMapTexture != nullptr)
		{
			PostEncodeShadowMapTexture(0, ShadowMapTexture, 0u);
		}
		for (uint32 CoefficientIndex = 0; CoefficientIndex < NUM_STORED_LIGHTMAP_COEF; CoefficientIndex += 2) 
		{
			if (auto CoefficientTexture = Textures[CoefficientIndex])
			{
				PostEncodeCoefficientTexture(CoefficientIndex, CoefficientTexture, 0u, false);
			}
		}
	}
	else
	{
		const uint32 InvalidLayerId = ~0u;
		for (uint32 CoefficientIndex = 0; CoefficientIndex < NUM_STORED_LIGHTMAP_COEF; CoefficientIndex += 2) 
		{
			if (auto VirtualTexture = VirtualTextures[CoefficientIndex])
			{
				const uint32 SkyOcclusionLayer = VirtualTexture->GetLayerForType(ELightMapVirtualTextureType::SkyOcclusion);
				const uint32 AOMaterialMaskLayer = VirtualTexture->GetLayerForType(ELightMapVirtualTextureType::AOMaterialMask);
				const uint32 ShadowMaskLayer = VirtualTexture->GetLayerForType(ELightMapVirtualTextureType::ShadowMask);

				PostEncodeCoefficientTexture(CoefficientIndex, VirtualTexture, 0u, true);
				if (SkyOcclusionLayer != InvalidLayerId)
				{
					PostEncodeSkyOcclusionTexture(VirtualTexture, SkyOcclusionLayer);
				}
				if (AOMaterialMaskLayer != InvalidLayerId)
				{
					PostEncodeAOMaskTexture(VirtualTexture, AOMaterialMaskLayer);
				}
				if (ShadowMaskLayer != InvalidLayerId)
				{
					PostEncodeShadowMapTexture(CoefficientIndex, VirtualTexture, ShadowMaskLayer);
				}
			}
		}
	}
	FLightMapPendingTexture::PostEncode();
}

void FTLBSLightMapPendingTexture::PostEncodeShadowMapTexture(int32 Index, UTexture* Texture, uint32 LayerIndex)
{
	for (int32 MipIndex = 0; MipIndex < Texture->Source.GetNumMips(); MipIndex++)
	{
		Texture->Source.UnlockMip(0, LayerIndex, MipIndex);
		if (Index == 0)
		{
			ShadowMipData.MipData0[MipIndex] = 0;
			FMemory::Free(ShadowMipData.MipCoverageData[MipIndex]);
			ShadowMipData.MipCoverageData[MipIndex] = 0;
		}
		else
		{
			ShadowMipData.MipData1[MipIndex] = 0;
		}
	}
}

void FTLBSLightMapPendingTexture::PostEncodeCoefficientTexture(int32 CoefficientIndex, UTexture* Texture, uint32 LayerIndex, bool bEncodeVirtualTexture)
{
	auto& MipData0 = CoefficientMipData0[CoefficientIndex / 2];
	auto& MipData1 = CoefficientMipData1[CoefficientIndex / 2];
	for (int32 MipIndex = 0; MipIndex < Texture->Source.GetNumMips(); MipIndex++)
	{
		Texture->Source.UnlockMip(0, LayerIndex, MipIndex);
		MipData0.MipData[MipIndex] = 0;
		FMemory::Free(MipData0.MipCoverageData[MipIndex]);
		MipData0.MipCoverageData[MipIndex] = 0;
		if (bEncodeVirtualTexture)
		{
			Texture->Source.UnlockMip(0, LayerIndex + 1, MipIndex);
			FMemory::Free(MipData1.MipCoverageData[MipIndex]);
		}
		MipData1.MipData[MipIndex] = 0;
		MipData1.MipCoverageData[MipIndex] = 0;
	}
}

void FTLBSLightMapPendingTexture::PostEncodeSkyOcclusionTexture(UTexture* Texture, uint32 LayerIndex)
{
	for (int32 MipIndex = 0; MipIndex < Texture->Source.GetNumMips(); MipIndex++)
	{
		Texture->Source.UnlockMip(0, LayerIndex, MipIndex);
		SkyOcclusionMipData.MipData[MipIndex] = 0;
		FMemory::Free(SkyOcclusionMipData.MipCoverageData[MipIndex]);
		SkyOcclusionMipData.MipCoverageData[MipIndex] = 0;
	}
}

void FTLBSLightMapPendingTexture::PostEncodeAOMaskTexture(UTexture* Texture, uint32 LayerIndex)
{
	for (int32 MipIndex = 0; MipIndex < Texture->Source.GetNumMips(); MipIndex++)
	{
		Texture->Source.UnlockMip(0, LayerIndex, MipIndex);
		AOMaskMipData.MipData[MipIndex] = 0;
		FMemory::Free(AOMaskMipData.MipCoverageData[MipIndex]);
		AOMaskMipData.MipCoverageData[MipIndex] = 0;
	}
}

void FTLBSLightMapPendingTexture::PostEncodePrtHQTexture(int32 Index, UTexture* Texture, uint32 LayerIndex)
{
#if ENABLE_PRTHQ_API
	static const auto CVarPRTSeparateSHCoefficient = IConsoleManager::Get().FindTConsoleVariableDataInt(TEXT("r.PRT.SeparateSHCoefficient"));
	bool bPRTSeparateSHCoefficient = CVarPRTSeparateSHCoefficient && CVarPRTSeparateSHCoefficient->GetValueOnAnyThread() == 1;

	for (int32 MipIndex = 0; MipIndex < Texture->Source.GetNumMips(); MipIndex++)
	{
		Texture->Source.UnlockMip(0, LayerIndex, MipIndex);
		PrtHQMipData[Index].MipData[MipIndex] = 0;
		FMemory::Free(PrtHQMipData[Index].MipCoverageData[MipIndex]);
		PrtHQMipData[Index].MipCoverageData[MipIndex] = 0;

		if (bPRTSeparateSHCoefficient && Index == 1)
		{
			PrtHQMipData[Index + 1].MipData[MipIndex] = 0;
			PrtHQMipData[Index + 1].MipCoverageData[MipIndex] = 0;
			if (PrtHQMipData[Index + 2].MipData[MipIndex])
				FMemory::Free(PrtHQMipData[Index + 2].MipData[MipIndex]);
			PrtHQMipData[Index + 2].MipData[MipIndex] = 0;
			if (PrtHQMipData[Index + 2].MipCoverageData[MipIndex])
				FMemory::Free(PrtHQMipData[Index + 2].MipCoverageData[MipIndex]);
			PrtHQMipData[Index + 2].MipCoverageData[MipIndex] = 0;
		}
		else if (!bPRTSeparateSHCoefficient)
		{
			PrtHQMipData[Index + 2].MipData[MipIndex] = 0;
			PrtHQMipData[Index + 2].MipCoverageData[MipIndex] = 0;
		}
	}
#endif
}

// CTG ryanthhuang, begin : PRT
void FTLBSLightMapPendingTexture::PostEncodePrtLQTexture(int32 Index, UTexture* Texture, uint32 LayerIndex)
{
#if ENABLE_PRTLQ_API
	for (int32 MipIndex = 0; MipIndex < Texture->Source.GetNumMips(); MipIndex++)
	{
		Texture->Source.UnlockMip(0, LayerIndex, MipIndex);
		PrtLQMipData[Index].MipData[MipIndex] = 0;
		FMemory::Free(PrtLQMipData[Index].MipCoverageData[MipIndex]);
		PrtLQMipData[Index].MipCoverageData[MipIndex] = 0;
	}
#endif
}
// CTG ryanthhuang, end

void FTLBSLightMapPendingTexture::GenerateShadowmapMipsAndDilateSamples(const TArray<FIntRect>& Ranges, int32 NumMips, int32 TextureSizeX, int32 TextureSizeY, FFourDistanceFieldSamples** MipCoverageData)
{
	for (int32 MipIndex = 1; MipIndex < NumMips; MipIndex++)
	{
		for (const auto& Range : Ranges)
		{
			const uint32 SourceMipSizeX = FMath::Max(1, TextureSizeX >> (MipIndex - 1));
			const uint32 SourceMipSizeY = FMath::Max(1, TextureSizeY >> (MipIndex - 1));
			const uint32 DestMipSizeX = FMath::Max(1, TextureSizeX >> MipIndex);
			const uint32 DestMipSizeY = FMath::Max(1, TextureSizeY >> MipIndex);
			const uint32 MipRangeMinX = Range.Min.X >> MipIndex;
			const uint32 MipRangeMinY = Range.Min.Y >> MipIndex;
			const uint32 MipRangeMaxX = FMath::Max(1, Range.Max.X >> MipIndex);
			const uint32 MipRangeMaxY = FMath::Max(1, Range.Max.Y >> MipIndex);

			// Downsample the previous mip-level, taking into account which texels are mapped.
			FFourDistanceFieldSamples* NextMipData = ShadowMipData.MipCoverageData[MipIndex];
			const uint32 MipFactorX = SourceMipSizeX / DestMipSizeX;
			const uint32 MipFactorY = SourceMipSizeY / DestMipSizeY;

			for (uint32 Y = MipRangeMinY; Y < MipRangeMaxY; Y++)
			{
				for (uint32 X = MipRangeMinX; X < MipRangeMaxX; X++)
				{
					float AccumulatedFilterableComponents[4][FQuantizedSignedDistanceFieldShadowSample::NumFilterableComponents];

					for (int32 ChannelIndex = 0; ChannelIndex < 4; ChannelIndex++)
					{
						for (int32 i = 0; i < FQuantizedSignedDistanceFieldShadowSample::NumFilterableComponents; i++)
						{
							AccumulatedFilterableComponents[ChannelIndex][i] = 0;
						}
					}
					uint32 Coverage[4] = { 0 };

					for (uint32 SourceY = Y * MipFactorY; SourceY < (Y + 1) * MipFactorY; SourceY++)
					{
						for (uint32 SourceX = X * MipFactorX; SourceX < (X + 1) * MipFactorX; SourceX++)
						{
							for (int32 ChannelIndex = 0; ChannelIndex < 4; ChannelIndex++)
							{
								const FFourDistanceFieldSamples& FourSourceSamples = ShadowMipData.MipCoverageData[MipIndex - 1][SourceY * SourceMipSizeX + SourceX];
								const FQuantizedSignedDistanceFieldShadowSample& SourceSample = FourSourceSamples.Samples[ChannelIndex];

								if (SourceSample.Coverage)
								{
									for (int32 i = 0; i < FQuantizedSignedDistanceFieldShadowSample::NumFilterableComponents; i++)
									{
										AccumulatedFilterableComponents[ChannelIndex][i] += SourceSample.GetFilterableComponent(i) * SourceSample.Coverage;
									}

									Coverage[ChannelIndex] += SourceSample.Coverage;
								}
							}
						}
					}

					FFourDistanceFieldSamples& FourDestSamples = NextMipData[Y * DestMipSizeX + X];

					for (int32 ChannelIndex = 0; ChannelIndex < 4; ChannelIndex++)
					{
						FQuantizedSignedDistanceFieldShadowSample& DestSample = FourDestSamples.Samples[ChannelIndex];

						if (Coverage[ChannelIndex])
						{
							for (int32 i = 0; i < FQuantizedSignedDistanceFieldShadowSample::NumFilterableComponents; i++)
							{
								DestSample.SetFilterableComponent(AccumulatedFilterableComponents[ChannelIndex][i] / (float)Coverage[ChannelIndex], i);
							}

							DestSample.Coverage = (uint8)(Coverage[ChannelIndex] / (MipFactorX * MipFactorY));
						}
						else
						{
							for (int32 i = 0; i < FQuantizedSignedDistanceFieldShadowSample::NumFilterableComponents; i++)
							{
								AccumulatedFilterableComponents[ChannelIndex][i] = 0;
							}
							DestSample.Coverage = 0;
						}
					}
				}
			}
		}
	}

	const FIntPoint Neighbors[] =
	{
		// Check immediate neighbors first
		FIntPoint(1,0),
		FIntPoint(0,1),
		FIntPoint(-1,0),
		FIntPoint(0,-1),
		// Check diagonal neighbors if no immediate neighbors are found
		FIntPoint(1,1),
		FIntPoint(-1,1),
		FIntPoint(-1,-1),
		FIntPoint(1,-1)
	};

	// Extrapolate texels which are mapped onto adjacent texels which are not mapped to avoid artifacts when using texture filtering.
	for (int32 MipIndex = 0; MipIndex < NumMips; MipIndex++)
	{
		for (const auto& Range : Ranges) {
			uint32 MipSizeX = FMath::Max(1, TextureSizeX >> MipIndex);
			uint32 MipSizeY = FMath::Max(1, TextureSizeY >> MipIndex);
			const uint32 MipRangeMinX = Range.Min.X >> MipIndex;
			const uint32 MipRangeMinY = Range.Min.Y >> MipIndex;
			const uint32 MipRangeMaxX = FMath::Max(1, Range.Max.X >> MipIndex);
			const uint32 MipRangeMaxY = FMath::Max(1, Range.Max.Y >> MipIndex);

			for (uint32 DestY = MipRangeMinY; DestY < MipRangeMaxY; DestY++)
			{
				for (uint32 DestX = MipRangeMinX; DestX < MipRangeMaxX; DestX++)
				{
					FFourDistanceFieldSamples& FourDestSamples = ShadowMipData.MipCoverageData[MipIndex][DestY * MipSizeX + DestX];

					for (int32 ChannelIndex = 0; ChannelIndex < 4; ChannelIndex++)
					{
						FQuantizedSignedDistanceFieldShadowSample& DestSample = FourDestSamples.Samples[ChannelIndex];

						if (DestSample.Coverage == 0)
						{
							float ExtrapolatedFilterableComponents[FQuantizedSignedDistanceFieldShadowSample::NumFilterableComponents];

							for (int32 i = 0; i < FQuantizedSignedDistanceFieldShadowSample::NumFilterableComponents; i++)
							{
								ExtrapolatedFilterableComponents[i] = 0;
							}

							for (int32 NeighborIndex = 0; NeighborIndex < UE_ARRAY_COUNT(Neighbors); NeighborIndex++)
							{
								if (static_cast<int32>(DestY) + Neighbors[NeighborIndex].Y >= 0
									&& DestY + Neighbors[NeighborIndex].Y < MipSizeY
									&& static_cast<int32>(DestX) + Neighbors[NeighborIndex].X >= 0
									&& DestX + Neighbors[NeighborIndex].X < MipSizeX)
								{
									const FFourDistanceFieldSamples& FourNeighborSamples = ShadowMipData.MipCoverageData[MipIndex][(DestY + Neighbors[NeighborIndex].Y) * MipSizeX + DestX + Neighbors[NeighborIndex].X];
									const FQuantizedSignedDistanceFieldShadowSample& NeighborSample = FourNeighborSamples.Samples[ChannelIndex];

									if (NeighborSample.Coverage > 0)
									{
										if (static_cast<int32>(DestY) + Neighbors[NeighborIndex].Y * 2 >= 0
											&& DestY + Neighbors[NeighborIndex].Y * 2 < MipSizeY
											&& static_cast<int32>(DestX) + Neighbors[NeighborIndex].X * 2 >= 0
											&& DestX + Neighbors[NeighborIndex].X * 2 < MipSizeX)
										{
											// Lookup the second neighbor in the first neighbor's direction
											//@todo - check the second neighbor's coverage?
											const FFourDistanceFieldSamples& SecondFourNeighborSamples = ShadowMipData.MipCoverageData[MipIndex][(DestY + Neighbors[NeighborIndex].Y * 2) * MipSizeX + DestX + Neighbors[NeighborIndex].X * 2];
											const FQuantizedSignedDistanceFieldShadowSample& SecondNeighborSample = SecondFourNeighborSamples.Samples[ChannelIndex];

											for (int32 i = 0; i < FQuantizedSignedDistanceFieldShadowSample::NumFilterableComponents; i++)
											{
												// Extrapolate while maintaining the first derivative, which is especially important for signed distance fields
												ExtrapolatedFilterableComponents[i] = NeighborSample.GetFilterableComponent(i) * 2.0f - SecondNeighborSample.GetFilterableComponent(i);
											}
										}
										else
										{
											// Couldn't find a second neighbor to use for extrapolating, just copy the neighbor's values
											for (int32 i = 0; i < FQuantizedSignedDistanceFieldShadowSample::NumFilterableComponents; i++)
											{
												ExtrapolatedFilterableComponents[i] = NeighborSample.GetFilterableComponent(i);
											}
										}
										break;
									}
								}
							}
							for (int32 i = 0; i < FQuantizedSignedDistanceFieldShadowSample::NumFilterableComponents; i++)
							{
								DestSample.SetFilterableComponent(ExtrapolatedFilterableComponents[i], i);
							}
						}
					}
				}
			}
		}
	}
}

void FTLBSLightMapPendingTexture::GenerateShadowmapMipsAndDilateByte(const TArray<FIntRect>& Ranges, int32 NumMips, int32 TextureSizeX, int32 TextureSizeY, ETextureSourceFormat SourceFormat, uint8** MipData, FFourDistanceFieldSamples** MipCoverageData)
{
	for (int32 MipIndex = 0; MipIndex < NumMips; MipIndex++)
	{
		for (const auto& Range : Ranges) {
			uint8* DestMipData = MipData[MipIndex];
			uint32 MipSizeX = FMath::Max<uint32>(1, TextureSizeX >> MipIndex);
			uint32 MipSizeY = FMath::Max<uint32>(1, TextureSizeY >> MipIndex);
			const uint32 MipRangeMinX = Range.Min.X >> MipIndex;
			const uint32 MipRangeMinY = Range.Min.Y >> MipIndex;
			const uint32 MipRangeMaxX = FMath::Max(1, Range.Max.X >> MipIndex);
			const uint32 MipRangeMaxY = FMath::Max(1, Range.Max.Y >> MipIndex);

			for (uint32 Y = MipRangeMinY; Y < MipRangeMaxY; Y++)
			{
				for (uint32 X = MipRangeMinX; X < MipRangeMaxX; X++)
				{
					const FFourDistanceFieldSamples& SourceSample = MipCoverageData[MipIndex][Y * MipSizeX + X];

					if (SourceFormat == TSF_G8)
					{
						DestMipData[Y * MipSizeX + X] = SourceSample.Samples[0].Distance;
					}
					else
					{
						((FColor*)DestMipData)[Y * MipSizeX + X] = FColor(SourceSample.Samples[0].Distance, SourceSample.Samples[1].Distance, SourceSample.Samples[2].Distance, SourceSample.Samples[3].Distance);
					}
				}
			}
		}
	}
}

void FTLBSLightMapPendingTexture::GenerateLightmapMipsAndDilateColor(const TArray<FIntRect>& Ranges, int32 NumMips, int32 TextureSizeX, int32 TextureSizeY, FColor InTextureColor, FColor** MipData, int8** MipCoverageData)
{
	for (int32 MipIndex = 1; MipIndex < NumMips; MipIndex++)
	{
		for (const auto& Range : Ranges) {
			const int32 SourceMipSizeX = FMath::Max(1, TextureSizeX >> (MipIndex - 1));
			const int32 SourceMipSizeY = FMath::Max(1, TextureSizeY >> (MipIndex - 1));
			const int32 DestMipSizeX = FMath::Max(1, TextureSizeX >> MipIndex);
			const int32 DestMipSizeY = FMath::Max(1, TextureSizeY >> MipIndex);

			const int32 MipRangeMinX = Range.Min.X >> MipIndex;
			const int32 MipRangeMinY = Range.Min.Y >> MipIndex;
			const int32 MipRangeMaxX = FMath::Max(1, Range.Max.X >> MipIndex);
			const int32 MipRangeMaxY = FMath::Max(1, Range.Max.Y >> MipIndex);

			// Downsample the previous mip-level, taking into account which texels are mapped.
			FColor* NextMipData = MipData[MipIndex];
			FColor* LastMipData = MipData[MipIndex - 1];

			int8* NextMipCoverageData = MipCoverageData[MipIndex];
			int8* LastMipCoverageData = MipCoverageData[MipIndex - 1];

			const int32 MipFactorX = SourceMipSizeX / DestMipSizeX;
			const int32 MipFactorY = SourceMipSizeY / DestMipSizeY;

			//@todo - generate mips before encoding lightmaps!  
			// Currently we are filtering in the encoded space, similar to generating mips of sRGB textures in sRGB space
			for (int32 Y = MipRangeMinY; Y < MipRangeMaxY; Y++)
			{
				for (int32 X = MipRangeMinX; X < MipRangeMaxX; X++)
				{
					FLinearColor AccumulatedColor = FLinearColor::Black;
					uint32 Coverage = 0;

					const uint32 MinSourceY = (Y + 0) * MipFactorY;
					const uint32 MaxSourceY = (Y + 1) * MipFactorY;
					for (uint32 SourceY = MinSourceY; SourceY < MaxSourceY; SourceY++)
					{
						const uint32 MinSourceX = (X + 0) * MipFactorX;
						const uint32 MaxSourceX = (X + 1) * MipFactorX;
						for (uint32 SourceX = MinSourceX; SourceX < MaxSourceX; SourceX++)
						{
							const FColor& SourceColor = LastMipData[SourceY * SourceMipSizeX + SourceX];
							int8 SourceCoverage = LastMipCoverageData[SourceY * SourceMipSizeX + SourceX];
							if (SourceCoverage)
							{
								AccumulatedColor += SourceColor.ReinterpretAsLinear() * SourceCoverage;
								Coverage += SourceCoverage;
							}
						}
					}
					FColor& DestColor = NextMipData[Y * DestMipSizeX + X];
					int8& DestCoverage = NextMipCoverageData[Y * DestMipSizeX + X];
					if (GVisualizeLightmapTextures)
					{
						DestColor = InTextureColor;
						DestCoverage = 127;
					}
					else if (Coverage)
					{
						DestColor = (AccumulatedColor / Coverage).QuantizeRound();
						DestCoverage = Coverage / (MipFactorX * MipFactorY);
					}
					else
					{
						DestColor = FColor(0, 0, 0);
						DestCoverage = 0;
					}
				}
			}
		}
	}

	// Expand texels which are mapped into adjacent texels which are not mapped to avoid artifacts when using texture filtering
	for (int32 MipIndex = 0; MipIndex < NumMips; MipIndex++)
	{
		for (const auto& Range : Ranges) {
			FColor* MipLevelData = MipData[MipIndex];
			int8* MipLevelCoverageData = MipCoverageData[MipIndex];

			uint32 MipSizeX = FMath::Max(1, TextureSizeX >> MipIndex);
			uint32 MipSizeY = FMath::Max(1, TextureSizeY >> MipIndex);

			const uint32 MipRangeMinX = Range.Min.X >> MipIndex;
			const uint32 MipRangeMinY = Range.Min.Y >> MipIndex;
			const uint32 MipRangeMaxX = FMath::Max(1, Range.Max.X >> MipIndex);
			const uint32 MipRangeMaxY = FMath::Max(1, Range.Max.Y >> MipIndex);

			for (uint32 DestY = MipRangeMinY; DestY < MipRangeMaxY; DestY++)
			{
				for (uint32 DestX = MipRangeMinX; DestX < MipRangeMaxX; DestX++)
				{
					FColor& DestColor = MipLevelData[DestY * MipSizeX + DestX];
					int8& DestCoverage = MipLevelCoverageData[DestY * MipSizeX + DestX];
					if (DestCoverage == 0)
					{
						FLinearColor AccumulatedColor = FLinearColor::Black;
						uint32 Coverage = 0;

						const int32 MinSourceY = FMath::Max((int32)DestY - 1, bIsFinishedEncoding ? 0 : (int32)MipRangeMinY);
						const int32 MaxSourceY = FMath::Min((int32)DestY + 1, bIsFinishedEncoding ? (int32)MipSizeY: (int32)MipRangeMaxY);
						for (int32 SourceY = MinSourceY; SourceY < MaxSourceY; SourceY++)
						{
							const int32 MinSourceX = FMath::Max((int32)DestX - 1, bIsFinishedEncoding ? 0 : (int32)MipRangeMinX);
							const int32 MaxSourceX = FMath::Min((int32)DestX + 1, bIsFinishedEncoding ? (int32)MipSizeX: (int32)MipRangeMaxX);
							for (int32 SourceX = MinSourceX; SourceX < MaxSourceX; SourceX++)
							{
								FColor& SourceColor = MipLevelData[SourceY * MipSizeX + SourceX];
								int8 SourceCoverage = MipLevelCoverageData[SourceY * MipSizeX + SourceX];
								if (SourceCoverage > 0)
								{
									static const uint32 Weights[3][3] =
									{
										{ 1, 255, 1 },
										{ 255, 0, 255 },
										{ 1, 255, 1 },
									};
									AccumulatedColor += SourceColor.ReinterpretAsLinear() * SourceCoverage * Weights[SourceX - DestX + 1][SourceY - DestY + 1];
									Coverage += SourceCoverage * Weights[SourceX - DestX + 1][SourceY - DestY + 1];
								}
							}
						}

						if (Coverage)
						{
							DestColor = (AccumulatedColor / Coverage).QuantizeRound();
							DestCoverage = -1;
						}
					}
				}
			}
		}
	}
	
	// Fill zero coverage texels with closest colors using mips
	for (int32 MipIndex = NumMips - 2; MipIndex >= 0; MipIndex--)
	{
		for (const auto& Range : Ranges) {
			const int32 DstMipSizeX = FMath::Max(1, TextureSizeX >> MipIndex);
			const int32 DstMipSizeY = FMath::Max(1, TextureSizeY >> MipIndex);
			const int32 SrcMipSizeX = FMath::Max(1, TextureSizeX >> (MipIndex + 1));
			const int32 SrcMipSizeY = FMath::Max(1, TextureSizeY >> (MipIndex + 1));
			const int32 MipRangeMinX = Range.Min.X >> MipIndex;
			const int32 MipRangeMinY = Range.Min.Y >> MipIndex;
			const int32 MipRangeMaxX = FMath::Max(1, Range.Max.X >> MipIndex);
			const int32 MipRangeMaxY = FMath::Max(1, Range.Max.Y >> MipIndex);

			// Source from higher mip, taking into account which texels are mapped.
			FColor* DstMipData = MipData[MipIndex];
			FColor* SrcMipData = MipData[MipIndex + 1];

			int8* DstMipCoverageData = MipCoverageData[MipIndex];
			int8* SrcMipCoverageData = MipCoverageData[MipIndex + 1];

			for (int32 DstY = MipRangeMinY; DstY < MipRangeMaxY; DstY++)
			{
				for (int32 DstX = MipRangeMinX; DstX < MipRangeMaxX; DstX++)
				{
					const uint32 SrcX = DstX / 2;
					const uint32 SrcY = DstY / 2;

					const FColor& SrcColor = SrcMipData[SrcY * SrcMipSizeX + SrcX];
					int8 SrcCoverage = SrcMipCoverageData[SrcY * SrcMipSizeX + SrcX];

					FColor& DstColor = DstMipData[DstY * DstMipSizeX + DstX];
					int8& DstCoverage = DstMipCoverageData[DstY * DstMipSizeX + DstX];

					// Point upsample mip data for zero coverage texels
					// TODO bilinear upsample
					if (SrcCoverage != 0 && DstCoverage == 0)
					{
						DstColor = SrcColor;
						DstCoverage = SrcCoverage;
					}
				}
			}
		}
	}
}

void FTLBSLightMapPendingTexture::GenerateLightmapMipsAndDilateByte(const TArray<FIntRect>& Ranges, int32 NumMips, int32 TextureSizeX, int32 TextureSizeY, uint8 InTextureColor, uint8** MipData, int8** MipCoverageData)
{
	for (int32 MipIndex = 1; MipIndex < NumMips; MipIndex++)
	{
		for (const auto& Range : Ranges) {
			const int32 SourceMipSizeX = FMath::Max(1, TextureSizeX >> (MipIndex - 1));
			const int32 SourceMipSizeY = FMath::Max(1, TextureSizeY >> (MipIndex - 1));
			const int32 DestMipSizeX = FMath::Max(1, TextureSizeX >> MipIndex);
			const int32 DestMipSizeY = FMath::Max(1, TextureSizeY >> MipIndex);
			const int32 MipRangeMinX = Range.Min.X >> MipIndex;
			const int32 MipRangeMinY = Range.Min.Y >> MipIndex;
			const int32 MipRangeMaxX = FMath::Max(1, Range.Max.X >> MipIndex);
			const int32 MipRangeMaxY = FMath::Max(1, Range.Max.Y >> MipIndex);

			// Downsample the previous mip-level, taking into account which texels are mapped.
			uint8* NextMipData = MipData[MipIndex];
			uint8* LastMipData = MipData[MipIndex - 1];

			int8* NextMipCoverageData = MipCoverageData[MipIndex];
			int8* LastMipCoverageData = MipCoverageData[MipIndex - 1];

			const int32 MipFactorX = SourceMipSizeX / DestMipSizeX;
			const int32 MipFactorY = SourceMipSizeY / DestMipSizeY;

			//@todo - generate mips before encoding lightmaps!  
			// Currently we are filtering in the encoded space, similar to generating mips of sRGB textures in sRGB space
			for (int32 Y = MipRangeMinY; Y < MipRangeMaxY; Y++)
			{
				for (int32 X = MipRangeMinX; X < MipRangeMaxX; X++)
				{
					float AccumulatedColor = 0;
					uint32 Coverage = 0;

					const uint32 MinSourceY = (Y + 0) * MipFactorY;
					const uint32 MaxSourceY = (Y + 1) * MipFactorY;
					for (uint32 SourceY = MinSourceY; SourceY < MaxSourceY; SourceY++)
					{
						const uint32 MinSourceX = (X + 0) * MipFactorX;
						const uint32 MaxSourceX = (X + 1) * MipFactorX;
						for (uint32 SourceX = MinSourceX; SourceX < MaxSourceX; SourceX++)
						{
							const uint8& SourceColor = LastMipData[SourceY * SourceMipSizeX + SourceX];
							int8 SourceCoverage = LastMipCoverageData[SourceY * SourceMipSizeX + SourceX];
							if (SourceCoverage)
							{
								AccumulatedColor += SourceColor / 255.0f * SourceCoverage;
								Coverage += SourceCoverage;
							}
						}
					}
					uint8& DestColor = NextMipData[Y * DestMipSizeX + X];
					int8& DestCoverage = NextMipCoverageData[Y * DestMipSizeX + X];
					if (GVisualizeLightmapTextures)
					{
						DestColor = InTextureColor;
						DestCoverage = 127;
					}
					else if (Coverage)
					{
						DestColor = (uint8)FMath::Clamp<int32>(FMath::TruncToInt(AccumulatedColor / Coverage * 255.f), 0, 255);
						DestCoverage = Coverage / (MipFactorX * MipFactorY);
					}
					else
					{
						DestColor = 0;
						DestCoverage = 0;
					}
				}
			}
		}
	}

	// Expand texels which are mapped into adjacent texels which are not mapped to avoid artifacts when using texture filtering.
	for (int32 MipIndex = 0; MipIndex < NumMips; MipIndex++)
	{
		for (const auto& Range : Ranges) {
			uint8* MipLevelData = MipData[MipIndex];
			int8* MipLevelCoverageData = MipCoverageData[MipIndex];

			uint32 MipSizeX = FMath::Max(1, TextureSizeX >> MipIndex);
			uint32 MipSizeY = FMath::Max(1, TextureSizeY >> MipIndex);

			const int32 MipRangeMinX = Range.Min.X >> MipIndex;
			const int32 MipRangeMinY = Range.Min.Y >> MipIndex;
			const int32 MipRangeMaxX = FMath::Max(1, Range.Max.X >> MipIndex);
			const int32 MipRangeMaxY = FMath::Max(1, Range.Max.Y >> MipIndex);

			for (int32 DestY = MipRangeMinY; DestY < MipRangeMaxY; DestY++)
			{
				for (int32 DestX = MipRangeMinX; DestX < MipRangeMaxX; DestX++)
				{
					uint8& DestColor = MipLevelData[DestY * MipSizeX + DestX];
					int8& DestCoverage = MipLevelCoverageData[DestY * MipSizeX + DestX];
					if (DestCoverage == 0)
					{
						float AccumulatedColor = 0;
						uint32 Coverage = 0;

						const int32 MinSourceY = FMath::Max((int32)DestY - 1, bIsFinishedEncoding ? 0 : (int32)MipRangeMinY);
						const int32 MaxSourceY = FMath::Min((int32)DestY + 1, bIsFinishedEncoding ? (int32)MipSizeY : (int32)MipRangeMaxY);
						for (int32 SourceY = MinSourceY; SourceY < MaxSourceY; SourceY++)
						{
							const int32 MinSourceX = FMath::Max((int32)DestX - 1, bIsFinishedEncoding ? 0 : (int32)MipRangeMinX);
							const int32 MaxSourceX = FMath::Min((int32)DestX + 1, bIsFinishedEncoding ? (int32)MipSizeX : (int32)MipRangeMaxX);
							for (int32 SourceX = MinSourceX; SourceX < MaxSourceX; SourceX++)
							{
								uint8& SourceColor = MipLevelData[SourceY * MipSizeX + SourceX];
								int8 SourceCoverage = MipLevelCoverageData[SourceY * MipSizeX + SourceX];
								if (SourceCoverage > 0)
								{
									static const uint32 Weights[3][3] =
									{
										{ 1, 255, 1 },
										{ 255, 0, 255 },
										{ 1, 255, 1 },
									};
									AccumulatedColor += SourceColor / 255.0f * SourceCoverage * Weights[SourceX - DestX + 1][SourceY - DestY + 1];
									Coverage += SourceCoverage * Weights[SourceX - DestX + 1][SourceY - DestY + 1];
								}
							}
						}

						if (Coverage)
						{
							DestColor = (uint8)FMath::Clamp<int32>(FMath::TruncToInt(AccumulatedColor / Coverage * 255.f), 0, 255);
							DestCoverage = -1;
						}
					}
				}
			}
		}
	}

	// Fill zero coverage texels with closest colors using mips
	for (int32 MipIndex = NumMips - 2; MipIndex >= 0; MipIndex--)
	{
		for (const auto& Range : Ranges) {
			const int32 DstMipSizeX = FMath::Max(1, TextureSizeX >> MipIndex);
			const int32 DstMipSizeY = FMath::Max(1, TextureSizeY >> MipIndex);
			const int32 SrcMipSizeX = FMath::Max(1, TextureSizeX >> (MipIndex + 1));
			const int32 SrcMipSizeY = FMath::Max(1, TextureSizeY >> (MipIndex + 1));
			const int32 MipRangeMinX = Range.Min.X >> MipIndex;
			const int32 MipRangeMinY = Range.Min.Y >> MipIndex;
			const int32 MipRangeMaxX = FMath::Max(1, Range.Max.X >> MipIndex);
			const int32 MipRangeMaxY = FMath::Max(1, Range.Max.Y >> MipIndex);

			// Source from higher mip, taking into account which texels are mapped.
			uint8* DstMipData = MipData[MipIndex];
			uint8* SrcMipData = MipData[MipIndex + 1];

			int8* DstMipCoverageData = MipCoverageData[MipIndex];
			int8* SrcMipCoverageData = MipCoverageData[MipIndex + 1];

			for (int32 DstY = MipRangeMinY; DstY < MipRangeMaxY; DstY++)
			{
				for (int32 DstX = MipRangeMinX; DstX < MipRangeMaxX; DstX++)
				{
					const uint32 SrcX = DstX / 2;
					const uint32 SrcY = DstY / 2;

					const uint8& SrcColor = SrcMipData[SrcY * SrcMipSizeX + SrcX];
					int8 SrcCoverage = SrcMipCoverageData[SrcY * SrcMipSizeX + SrcX];

					uint8& DstColor = DstMipData[DstY * DstMipSizeX + DstX];
					int8& DstCoverage = DstMipCoverageData[DstY * DstMipSizeX + DstX];

					// Point upsample mip data for zero coverage texels
					// TODO bilinear upsample
					if (SrcCoverage != 0 && DstCoverage == 0)
					{
						DstColor = SrcColor;
						DstCoverage = SrcCoverage;
					}
				}
			}
		}
	}
}

TSharedPtr<FTLBSLightMapAllocationGroup> FTLBSLightMap2D::GetLightMapAllocationGroup(FTLBSLightingSystem* TLBSSystem, FGuid MappingGuid)
{
	return TLBSSystem->TLBSProcessor->BakingContext.LightmapEncodePenddingGroupMap[MappingGuid];
}

TUniquePtr<FLightMapAllocation>* FTLBSLightMap2D::GetLightMapAllocation(FTLBSLightingSystem* TLBSSystem, FGuid MappingGuid)
{
	auto& PenddingGrop = TLBSSystem->TLBSProcessor->BakingContext.LightmapEncodePenddingGroupMap[MappingGuid];
	check(PenddingGrop->LightMapAllocationNum == PenddingGrop->MappingGuids.Num());
	for (int32 i = 0; i < PenddingGrop->LightMapAllocationNum; i++)
	{
		if (PenddingGrop->MappingGuids[i] == MappingGuid)
		{
			if (PenddingGrop->PenddingTexture)
			{
				return &(PenddingGrop->PenddingTexture->Allocations[PenddingGrop->AllocationOffset + i]);
			}
			else
			{
				return &(PenddingGrop->Allocations[i]);
			}
		}
	}
	return nullptr;
}

TUniquePtr<FLightMapAllocation>* FTLBSLightMap2D::GetLightMapAllocation(FTLBSLightingSystem* TLBSSystem, FGuid MappingGuid, int32 Index)
{
	auto& PenddingGrop = TLBSSystem->TLBSProcessor->BakingContext.LightmapEncodePenddingGroupMap[MappingGuid];
	check(PenddingGrop->LightMapAllocationNum == PenddingGrop->MappingGuids.Num());
	check(PenddingGrop->MappingGuids[Index] == MappingGuid);

	if (PenddingGrop->PenddingTexture)
	{
		return &(PenddingGrop->PenddingTexture->Allocations[PenddingGrop->AllocationOffset + Index]);
	}
	else
	{
		return &(PenddingGrop->Allocations[Index]);
	}
}

TRefCountPtr<FLightMap2D> FTLBSLightMap2D::AllocateLightMap(FTLBSLightingSystem* TLBSSystem, FGuid MappingGuid)
{
	auto PenddingGroup = TLBSSystem->TLBSProcessor->BakingContext.LightmapEncodePenddingGroupMap[MappingGuid];
	if (PenddingGroup->IsImported() && !PenddingGroup->LightMap)
	{
		TArray<TUniquePtr<FQuantizedLightmapData>> AllQuantizedData;
		AllQuantizedData.Reserve(PenddingGroup->LightMapAllocationNum);
		TArray<TMap<ULightComponent*, TUniquePtr<FShadowMapData2D>>> AllShadowMapData;
		AllShadowMapData.Reserve(PenddingGroup->LightMapAllocationNum);
		for (int32 Index = 0; Index < PenddingGroup->LightMapAllocationNum; Index++)
		{
			auto TexuteMapping = const_cast<FTLBSPenddingLightingData*>(TLBSSystem->TLBSProcessor->GetStaticLightingTextureMapping(PenddingGroup->MappingGuids[Index]));
			if (TLBSSystem->bUseVirtualTextures && TexuteMapping->ShadowMapData.Num() > 0)
			{
				auto& ShadowMapData = AllShadowMapData.AddDefaulted_GetRef();
				for (auto Iter : TexuteMapping->ShadowMapData)
				{
					ShadowMapData.Add(Iter.Key, TUniquePtr<FShadowMapData2D>(Iter.Value));
				}
			}
			check(TexuteMapping->QuantizedData);
			AllQuantizedData.Add(TUniquePtr<FQuantizedLightmapData>(TexuteMapping->QuantizedData));
		}
		if (PenddingGroup->LightMapAllocationNum == 1)
		{
			TMap<ULightComponent*, TUniquePtr<FShadowMapData2D>> EmptyShadowData;
			PenddingGroup->LightMap = AllocateLightMap(TLBSSystem, PenddingGroup->MappingGuids[0], AllQuantizedData[0], AllShadowMapData.Num() > 0 ? AllShadowMapData[0] : EmptyShadowData);
		}
		else
		{
			PenddingGroup->LightMap = AllocateInstanceLightMap(TLBSSystem, PenddingGroup->MappingGuids, MoveTemp(AllQuantizedData), MoveTemp(AllShadowMapData));
		}
	}
	return PenddingGroup->LightMap;
}

TRefCountPtr<FLightMap2D> FTLBSLightMap2D::AllocateLightMap(FTLBSLightingSystem* TLBSSystem, const FGuid& MappingGuid, TUniquePtr<FQuantizedLightmapData>& SourceQuantizedData, TMap<ULightComponent*, TUniquePtr<FShadowMapData2D>>& SourceShadowMapData)
{
	FTLBSStatistics::FScopedStatistics ScopedStatistics("AllocateLightMap");
	auto AllocationPtr = GetLightMapAllocation(TLBSSystem, MappingGuid);
	auto& Allocation = *AllocationPtr;

	TRefCountPtr<FLightMap2D> LightMap = TRefCountPtr<FLightMap2D>(new FLightMap2D());
	if (SourceQuantizedData)
	{
		FMemory::Memcpy(Allocation->Scale, SourceQuantizedData->Scale, sizeof(Allocation->Scale));
		FMemory::Memcpy(Allocation->Add, SourceQuantizedData->Add, sizeof(Allocation->Add));
		Allocation->RawData = MoveTemp(SourceQuantizedData->Data);
		LightMap->LightGuids = SourceQuantizedData->LightGuids;

#if ENABLE_PRTHQ_API
		FMemory::Memcpy(Allocation->PrtHQScale, SourceQuantizedData->PrtHQScale, sizeof(Allocation->PrtHQScale));
		FMemory::Memcpy(Allocation->PrtHQAdd, SourceQuantizedData->PrtHQAdd, sizeof(Allocation->PrtHQAdd));
		Allocation->bHasPrtHQTextures = SourceQuantizedData->bHasPrtHQTextures;
#endif

#if ENABLE_PRTLQ_API
		// CTG Begin, cedruszhang: PRT20
		FMemory::Memcpy(Allocation->PrtLQScale, SourceQuantizedData->PrtLQScale, sizeof(Allocation->PrtLQScale));
		FMemory::Memcpy(Allocation->PrtLQAdd, SourceQuantizedData->PrtLQAdd, sizeof(Allocation->PrtLQAdd));
		Allocation->bHasPrtLQTextures = SourceQuantizedData->bHasPrtLQTextures;
		Allocation->bHasStaticTextures = SourceQuantizedData->bHasStaticTextures;
		// CTG End
#endif
		SourceQuantizedData.Reset();
	}
	for (auto& It : SourceShadowMapData)
	{
		LightMap->LightGuids.AddUnique(It.Key->LightGuid);

		TUniquePtr<FShadowMapData2D>& RawData = It.Value;
		TArray<FQuantizedSignedDistanceFieldShadowSample>& DistanceFieldShadowData = Allocation->ShadowMapData.Add(It.Key, TArray<FQuantizedSignedDistanceFieldShadowSample>());

		switch (RawData->GetType())
		{
		case FShadowMapData2D::SHADOW_SIGNED_DISTANCE_FIELD_DATA:
		case FShadowMapData2D::SHADOW_SIGNED_DISTANCE_FIELD_DATA_QUANTIZED:
			// If the data is already quantized, this will just copy the data
			RawData->Quantize(DistanceFieldShadowData);
			break;
		default:
			check(0);
		}
		RawData.Reset();
	}
	Allocation->LightMap = LightMap;
	return LightMap;
}

TRefCountPtr<FLightMap2D> FTLBSLightMap2D::AllocateInstanceLightMap(FTLBSLightingSystem* TLBSSystem, const TArray<FGuid>& MappingGuids, TArray<TUniquePtr<FQuantizedLightmapData>>&& InstancedSourceQuantizedData, TArray<TMap<ULightComponent*, TUniquePtr<FShadowMapData2D>>>&& InstancedShadowMapData)
{
	FTLBSStatistics::FScopedStatistics ScopedStatistics("AllocateInstanceLightMap");
	check(InstancedSourceQuantizedData.Num() > 0);
	check(InstancedShadowMapData.Num() == 0 || InstancedShadowMapData.Num() == InstancedSourceQuantizedData.Num());

	// Verify all instance lightmaps are the same size
	const uint32 SizeX = InstancedSourceQuantizedData[0]->SizeX;
	const uint32 SizeY = InstancedSourceQuantizedData[0]->SizeY;
	for (int32 InstanceIndex = 1; InstanceIndex < InstancedSourceQuantizedData.Num(); ++InstanceIndex)
	{
		auto& SourceQuantizedData = InstancedSourceQuantizedData[InstanceIndex];
		check(SourceQuantizedData->SizeX == SizeX);
		check(SourceQuantizedData->SizeY == SizeY);
	}

	TSet<ULightComponent*> AllLights;
	for (int32 InstanceIndex = 0; InstanceIndex < InstancedShadowMapData.Num(); ++InstanceIndex)
	{
		for (const auto& It : InstancedShadowMapData[InstanceIndex])
		{
			const FShadowMapData2D* ShadowData = It.Value.Get();
			check(ShadowData->GetSizeX() == SizeX);
			check(ShadowData->GetSizeY() == SizeY);
			AllLights.Add(It.Key);
		}
	}

	// Unify all the shadow map data to contain the same lights in the same order
	for (auto& ShadowMapData : InstancedShadowMapData)
	{
		for (ULightComponent* Light : AllLights)
		{
			if (!ShadowMapData.Contains(Light))
			{
				ShadowMapData.Add(Light, MakeUnique<FQuantizedShadowSignedDistanceFieldData2D>(SizeX, SizeY));
			}
		}
	}

	// Requantize source data to the same quantization
	// Most of the following code is cloned from UModel::ApplyStaticLighting(), possibly it can be shared in future?
	// or removed, if instanced mesh components can be given per-instance lightmap unpack coefficients
	float MinCoefficient[NUM_STORED_LIGHTMAP_COEF][4];
	float MaxCoefficient[NUM_STORED_LIGHTMAP_COEF][4];
	for (int32 CoefficientIndex = 0; CoefficientIndex < NUM_STORED_LIGHTMAP_COEF; CoefficientIndex += 2)
	{
		for (int32 ColorIndex = 0; ColorIndex < 4; ColorIndex++)
		{
			// Color
			MinCoefficient[CoefficientIndex][ColorIndex] = FLT_MAX;
			MaxCoefficient[CoefficientIndex][ColorIndex] = 0.0f;

			// Direction
			MinCoefficient[CoefficientIndex + 1][ColorIndex] = FLT_MAX;
			MaxCoefficient[CoefficientIndex + 1][ColorIndex] = -FLT_MAX;
		}
	}

	// first, we need to find the max scale for all mappings, and that will be the scale across all instances of this component
	for (auto& SourceQuantizedData : InstancedSourceQuantizedData)
	{
		for (int32 CoefficientIndex = 0; CoefficientIndex < NUM_STORED_LIGHTMAP_COEF; CoefficientIndex++)
		{
			for (int32 ColorIndex = 0; ColorIndex < 4; ColorIndex++)
			{
				// The lightmap data for directional coefficients was packed in lightmass with
				// Pack: y = (x - Min) / (Max - Min)
				// We need to solve for Max and Min in order to combine BSP mappings into a lighting group.
				// QuantizedData->Scale and QuantizedData->Add were calculated in lightmass in order to unpack the lightmap data like so
				// Unpack: x = y * UnpackScale + UnpackAdd
				// Which means
				// Scale = Max - Min
				// Add = Min
				// Therefore we can solve for min and max using substitution

				float Scale = SourceQuantizedData->Scale[CoefficientIndex][ColorIndex];
				float Add = SourceQuantizedData->Add[CoefficientIndex][ColorIndex];
				float Min = Add;
				float Max = Scale + Add;

				MinCoefficient[CoefficientIndex][ColorIndex] = FMath::Min(MinCoefficient[CoefficientIndex][ColorIndex], Min);
				MaxCoefficient[CoefficientIndex][ColorIndex] = FMath::Max(MaxCoefficient[CoefficientIndex][ColorIndex], Max);
			}
		}
	}

	// Now calculate the new unpack scale and add based on the composite min and max
	float Scale[NUM_STORED_LIGHTMAP_COEF][4];
	float Add[NUM_STORED_LIGHTMAP_COEF][4];
	for (int32 CoefficientIndex = 0; CoefficientIndex < NUM_STORED_LIGHTMAP_COEF; CoefficientIndex++)
	{
		for (int32 ColorIndex = 0; ColorIndex < 4; ColorIndex++)
		{
			Scale[CoefficientIndex][ColorIndex] = FMath::Max(MaxCoefficient[CoefficientIndex][ColorIndex] - MinCoefficient[CoefficientIndex][ColorIndex], UE_DELTA);
			Add[CoefficientIndex][ColorIndex] = MinCoefficient[CoefficientIndex][ColorIndex];
		}
	}

	// perform requantization
	ParallelFor(InstancedSourceQuantizedData.Num(), [&](int32 Index) {
		auto& SourceQuantizedData = InstancedSourceQuantizedData[Index];
		for (uint32 Y = 0; Y < SourceQuantizedData->SizeY; Y++)
		{
			for (uint32 X = 0; X < SourceQuantizedData->SizeX; X++)
			{
				// get source from input, dest from the rectangular offset in the group
				FLightMapCoefficients& LightmapSample = SourceQuantizedData->Data[Y * SourceQuantizedData->SizeX + X];

				// Treat alpha special because of residual
				{
					// Decode LogL
					float LogL = (float)LightmapSample.Coefficients[0][3] / 255.0f;
					float Residual = (float)LightmapSample.Coefficients[1][3] / 255.0f;
					LogL += (Residual - 0.5f) / 255.0f;
					LogL = LogL * SourceQuantizedData->Scale[0][3] + SourceQuantizedData->Add[0][3];

					// Encode LogL
					LogL = (LogL - Add[0][3]) / Scale[0][3];
					Residual = LogL * 255.0f - FMath::RoundToFloat(LogL * 255.0f) + 0.5f;

					LightmapSample.Coefficients[0][3] = (uint8)FMath::Clamp<int32>(FMath::RoundToInt(LogL * 255.0f), 0, 255);
					LightmapSample.Coefficients[1][3] = (uint8)FMath::Clamp<int32>(FMath::RoundToInt(Residual * 255.0f), 0, 255);
				}

				// go over each color coefficient and dequantize and requantize with new Scale/Add
				for (int32 CoefficientIndex = 0; CoefficientIndex < NUM_STORED_LIGHTMAP_COEF; CoefficientIndex++)
				{
					// Don't touch alpha here
					for (int32 ColorIndex = 0; ColorIndex < 3; ColorIndex++)
					{
						// dequantize it
						float Dequantized = (float)LightmapSample.Coefficients[CoefficientIndex][ColorIndex] / 255.0f;
						const float Exponent = CoefficientIndex == 0 ? 2.0f : 1.0f;
						Dequantized = FMath::Pow(Dequantized, Exponent);

						const float Unpacked = Dequantized * SourceQuantizedData->Scale[CoefficientIndex][ColorIndex] + SourceQuantizedData->Add[CoefficientIndex][ColorIndex];
						const float Repacked = (Unpacked - Add[CoefficientIndex][ColorIndex]) / Scale[CoefficientIndex][ColorIndex];

						// requantize it
						LightmapSample.Coefficients[CoefficientIndex][ColorIndex] = (uint8)FMath::Clamp<int32>(FMath::RoundToInt(FMath::Pow(Repacked, 1.0f / Exponent) * 255.0f), 0, 255);
					}
				}
			}
		}

		// Save new requantized Scale/Add
		FMemory::Memcpy(SourceQuantizedData->Scale, Scale, sizeof(Scale));
		FMemory::Memcpy(SourceQuantizedData->Add, Add, sizeof(Add));
	});

	TRefCountPtr<FLightMap2D> BaseLightmap = nullptr;

	for (int32 InstanceIndex = 0; InstanceIndex < InstancedSourceQuantizedData.Num(); ++InstanceIndex)
	{
		auto& SourceQuantizedData = InstancedSourceQuantizedData[InstanceIndex];

		// Create a new light-map.
		TRefCountPtr<FLightMap2D> LightMap = TRefCountPtr<FLightMap2D>(new FLightMap2D(SourceQuantizedData->LightGuids));

		if (InstancedShadowMapData.Num() != 0)
		{
			// Include light guids from shadowmap lights, if present
			for (auto& It : InstancedShadowMapData[InstanceIndex])
			{
				LightMap->LightGuids.AddUnique(It.Key->LightGuid);
				if (BaseLightmap)
				{
					// Base lightmap contains guids for all lights used by group
					BaseLightmap->LightGuids.AddUnique(It.Key->LightGuid);
				}
			}
		}

		if (InstanceIndex == 0)
		{
			BaseLightmap = LightMap;
		}
		else
		{
			// we need the base lightmap to contain all of the lights used by all lightmaps in the group
			for (auto& LightGuid : SourceQuantizedData->LightGuids)
			{
				BaseLightmap->LightGuids.AddUnique(LightGuid);
			}
		}

		auto& Allocation = *GetLightMapAllocation(TLBSSystem, MappingGuids[InstanceIndex], InstanceIndex);
#if ENABLE_PRTHQ_API
		FMemory::Memcpy(Allocation->PrtHQScale, SourceQuantizedData->PrtHQScale, sizeof(Allocation->PrtHQScale));
		FMemory::Memcpy(Allocation->PrtHQAdd, SourceQuantizedData->PrtHQAdd, sizeof(Allocation->PrtHQAdd));
		Allocation->bHasPrtHQTextures = SourceQuantizedData->bHasPrtHQTextures;
#endif

#if ENABLE_PRTLQ_API
		// CTG Begin, cedruszhang: PRT20
		FMemory::Memcpy(Allocation->PrtLQScale, SourceQuantizedData->PrtLQScale, sizeof(Allocation->PrtLQScale));
		FMemory::Memcpy(Allocation->PrtLQAdd, SourceQuantizedData->PrtLQAdd, sizeof(Allocation->PrtLQAdd));
		Allocation->bHasPrtLQTextures = SourceQuantizedData->bHasPrtLQTextures;
		Allocation->bHasStaticTextures = SourceQuantizedData->bHasStaticTextures;
		// CTG End
#endif
		FMemory::Memcpy(Allocation->Scale, SourceQuantizedData->Scale, sizeof(Scale));
		FMemory::Memcpy(Allocation->Add, SourceQuantizedData->Add, sizeof(Add));
		Allocation->LightMap = MoveTemp(LightMap);
		Allocation->RawData = MoveTemp(SourceQuantizedData->Data);

		if (InstancedShadowMapData.Num() != 0)
		{
			for (auto& It : InstancedShadowMapData[InstanceIndex])
			{
				TUniquePtr<FShadowMapData2D>& RawData = It.Value;
				TArray<FQuantizedSignedDistanceFieldShadowSample>& DistanceFieldShadowData = Allocation->ShadowMapData.Add(It.Key, TArray<FQuantizedSignedDistanceFieldShadowSample>());

				switch (RawData->GetType())
				{
				case FShadowMapData2D::SHADOW_SIGNED_DISTANCE_FIELD_DATA:
				case FShadowMapData2D::SHADOW_SIGNED_DISTANCE_FIELD_DATA_QUANTIZED:
					// If the data is already quantized, this will just copy the data
					RawData->Quantize(DistanceFieldShadowData);
					break;
				default:
					check(0);
				}
				RawData.Reset();
				check(DistanceFieldShadowData.Num() > 0);
			}
		}

		// SourceQuantizedData is no longer needed now that FLightMapAllocation has what it needs
		SourceQuantizedData.Reset();
	}
	return BaseLightmap;
}

void FTLBSLightMap2D::AllocateLightMapAllocationGroup(FTLBSLightingSystem* TLBSSystem, FGuid MappingGuid, FTLBSLightMapCache& Cache)
{
	auto PenddingLightingData = TLBSSystem->TLBSProcessor->GetStaticLightingTextureMapping(MappingGuid);
	if (Cache.TextureMappingPenddingGroupMap.Find(PenddingLightingData->GetLightingGuid()))
		return;

	auto TextureMapping = PenddingLightingData->GetTextureMapping();
	if (TextureMapping->GetDescription() == "InstancedSMLightingMapping")
	{
		auto InsTextureMapping = StaticCast<const FStaticLightingTextureMapping_InstancedStaticMesh*>(TextureMapping);
		AllocateLightMapAllocationGroup(TLBSSystem, MappingGuid, InsTextureMapping, Cache);
	}
	else if (TextureMapping->GetDescription() == "SMTextureMapping")
	{
		auto SMTextureMapping = StaticCast<const FStaticMeshStaticLightingTextureMapping*>(TextureMapping);
		AllocateLightMapAllocationGroup(TLBSSystem, MappingGuid, SMTextureMapping, Cache);
	}
	else if (TextureMapping->GetDescription() == "LandscapeMapping")
	{
		auto LSTextureMapping = StaticCast<const FLandscapeStaticLightingTextureMapping*>(TextureMapping);
		AllocateLightMapAllocationGroup(TLBSSystem, MappingGuid, LSTextureMapping, Cache);
	}
	else
	{
		check(false);
	}

}

void FTLBSLightMap2D::AllocateShadowMapAllocationGroup(FTLBSLightingSystem* TLBSSystem, FGuid MappingGuid, FTLBSLightMapCache& Cache)
{
	FGuid LightmapMappingGuid = MappingGuid;
	LightmapMappingGuid.B = GPUBaking::EGUID_B_FOR_LIGHTMAP;
	if (!Cache.TextureMappingPenddingGroupMap.Contains(LightmapMappingGuid))
	{
		AllocateLightMapAllocationGroup(TLBSSystem, LightmapMappingGuid, Cache);
	}

	auto PenddingLightingData = TLBSSystem->TLBSProcessor->GetStaticLightingTextureMapping(LightmapMappingGuid);
	auto TextureMapping = PenddingLightingData->GetTextureMapping();
	auto InsTextureMapping = StaticCast<const FStaticLightingTextureMapping_InstancedStaticMesh*>(TextureMapping);
	if (InsTextureMapping)
	{
		AllocateShadowMapAllocationGroup(TLBSSystem, MappingGuid, InsTextureMapping, Cache);
		return;
	}
	auto SMTextureMapping = StaticCast<const FStaticMeshStaticLightingTextureMapping*>(TextureMapping);
	if (SMTextureMapping != nullptr)
	{
		AllocateShadowMapAllocationGroup(TLBSSystem, MappingGuid, SMTextureMapping, Cache);
		return;
	}

	auto LSTextureMapping = StaticCast<const FLandscapeStaticLightingTextureMapping*>(TextureMapping);
	if (LSTextureMapping != nullptr)
	{
		AllocateShadowMapAllocationGroup(TLBSSystem, MappingGuid, LSTextureMapping, Cache);
		return;
	}
}

void FTLBSLightMap2D::AllocateLightMapAllocationGroup(uint32 InSizeX, uint32 InSizeY, ELightMapPaddingType InPaddingType, bool HasSkyShadowing, bool HasPrtHQTexture, bool HasPrtLQTexture, bool HasStaticTexture, TSharedPtr<FTLBSLightMapAllocationGroup> PenddingLightMap)
{
	TUniquePtr<FLightMapAllocation> Allocation = MakeUnique<FLightMapAllocation>();
	Allocation->TotalSizeX = InSizeX;
	Allocation->TotalSizeY = InSizeY;
	Allocation->MappedRect.Max.X = InSizeX;
	Allocation->MappedRect.Max.Y = InSizeY;
	Allocation->PaddingType = InPaddingType;
	Allocation->bHasSkyShadowing = HasSkyShadowing;
#if ENABLE_PRTLQ_API
	Allocation->bHasPrtHQTextures	= HasPrtHQTexture;
	Allocation->bHasPrtLQTextures	= HasPrtLQTexture;
	Allocation->bHasStaticTextures	= HasStaticTexture;
#endif

	PenddingLightMap->TotalTexels = ((Allocation->MappedRect.Width() + 3) & ~3) * ((Allocation->MappedRect.Height() + 3) & ~3);
	PenddingLightMap->Allocations.Add(MoveTemp(Allocation));
}

void FTLBSLightMap2D::AllocateInstancedLightMapAllocationGroup(UInstancedStaticMeshComponent* Component, UMapBuildDataRegistry* Registry, FGuid MapBuildDataId, int32 InstanceIndex, uint32 InSizeX, uint32 InSizeY, ELightMapPaddingType InPaddingType, bool HasSkyShadowing, bool HasPrtHQTexture, bool HasPrtLQTexture, bool HasStaticTexture, TSharedPtr<FTLBSLightMapAllocationGroup> PenddingLightMap)
{
	TUniquePtr<FLightMapAllocation> Allocation = MakeUnique<FLightMapAllocation>();
	Allocation->TotalSizeX = InSizeX;
	Allocation->TotalSizeY = InSizeY;
	Allocation->MappedRect.Max.X = InSizeX;
	Allocation->MappedRect.Max.Y = InSizeY;
	Allocation->PaddingType = InPaddingType;
	Allocation->Primitive = Component;
	Allocation->Registry = Registry;
	Allocation->MapBuildDataId = MapBuildDataId;
	Allocation->bHasSkyShadowing = HasSkyShadowing;
#if ENABLE_PRT_API
	Allocation->bHasPrtHQTextures = HasPrtHQTexture;
	Allocation->bHasPrtLQTextures = HasPrtLQTexture;
#endif
	Allocation->InstanceIndex = InstanceIndex;

	PenddingLightMap->TotalTexels += ((Allocation->MappedRect.Width() + 3) & ~3) * ((Allocation->MappedRect.Height() + 3) & ~3);
	PenddingLightMap->Allocations[InstanceIndex] = MoveTemp(Allocation);
}

void FTLBSLightMap2D::AllocateLightMapPendingTexture(UWorld* InWorld, TArray<TSharedPtr<FTLBSLightMapAllocationGroup>>& InPenddingLightMaps, FTLBSLightMapCache& Cache)
{
	static const auto CVar = IConsoleManager::Get().FindTConsoleVariableDataInt(TEXT("r.VirtualTexturedLightmaps"));
	const bool bUseVirtualTextures = (CVar->GetValueOnAnyThread() != 0) && UseVirtualTexturing(GMaxRHIShaderPlatform);

	int32 PackedLightAndShadowMapTextureSizeX = InWorld->GetWorldSettings()->PackedLightAndShadowMapTextureSize;
	int32 PackedLightAndShadowMapTextureSizeY = PackedLightAndShadowMapTextureSizeX / 2;

	// Sort the light-maps in descending order by size.
	//Algo::SortBy(InPenddingLightMaps, &FTLBSLightMapAllocationGroup::TotalTexels, TGreater<>());
	GroupLightMapAllocationGroups(InWorld, InPenddingLightMaps);
	InPenddingLightMaps.Sort([](TSharedPtr<FTLBSLightMapAllocationGroup> A, TSharedPtr<FTLBSLightMapAllocationGroup> B) {
		if (A->TotalTexels > B->TotalTexels)
			return true;
		if (A->TotalTexels < B->TotalTexels)
			return false;
		return B->MappingGuids[0] < A->MappingGuids[0];
	});

	TArray<TSharedPtr<FTLBSLightMapPendingTexture>> PendingTextures;
	for (int32 PendingGroupIndex = 0; PendingGroupIndex < InPenddingLightMaps.Num(); PendingGroupIndex++)
	{
		auto& PendingGroup = *InPenddingLightMaps[PendingGroupIndex];
		if (!ensure(PendingGroup.Allocations.Num() >= 1))
		{
			continue;
		}
		int32 MaxWidth = 0;
		int32 MaxHeight = 0;
		for (auto& Allocation : PendingGroup.Allocations)
		{
			if (Allocation)
			{
				MaxWidth = FMath::Max(MaxWidth, Allocation->MappedRect.Width());
				MaxHeight = FMath::Max(MaxHeight, Allocation->MappedRect.Height());
			}
		}

		TSharedPtr<FTLBSLightMapPendingTexture> Texture = nullptr;

		// Find an existing texture which the light-map can be stored in.
		// Lightmaps will always be 4-pixel aligned...
		for (auto ExistingTexture : PendingTextures)
		{
			if (ExistingTexture->AddElement(PendingGroup))
			{
				Texture = ExistingTexture;
				break;
			}
		}

		if (!Texture)
		{
			int32 NewTextureSizeX = PackedLightAndShadowMapTextureSizeX;
			int32 NewTextureSizeY = PackedLightAndShadowMapTextureSizeY;

			// Assumes identically-sized allocations, fit into the smallest 2x1 rectangle
			const int32 AllocationCountX = FMath::CeilToInt(FMath::Sqrt(static_cast<float>(FMath::DivideAndRoundUp(PendingGroup.Allocations.Num() * 2 * MaxHeight, MaxWidth))));
			const int32 AllocationCountY = FMath::DivideAndRoundUp(PendingGroup.Allocations.Num(), AllocationCountX);
			const int32 AllocationSizeX = AllocationCountX * MaxWidth;
			const int32 AllocationSizeY = AllocationCountY * MaxHeight;

			if (AllocationSizeX > NewTextureSizeX || AllocationSizeY > NewTextureSizeY)
			{
				NewTextureSizeX = FMath::RoundUpToPowerOfTwo(AllocationSizeX);
				NewTextureSizeY = FMath::RoundUpToPowerOfTwo(AllocationSizeY);

				// Force 2:1 aspect
				if (!bUseVirtualTextures)
				{
					NewTextureSizeX = FMath::Max(NewTextureSizeX, NewTextureSizeY * 2);
					NewTextureSizeY = FMath::Max(NewTextureSizeY, NewTextureSizeX / 2);
				}
			}

			// If there is no existing appropriate texture, create a new one.
			// If we have non-VT, need 2to1 aspect ratio to handle packing lightmap into top/bottom texture region
			// With only VT, better to use square lightmaps (better page table usage)
			Texture = MakeShared<FTLBSLightMapPendingTexture>(InWorld, NewTextureSizeX, NewTextureSizeY, !bUseVirtualTextures ? ETextureLayoutAspectRatio::Force2To1 : ETextureLayoutAspectRatio::ForceSquare);
			PendingTextures.Add(Texture);
			Texture->Outer = PendingGroup.Outer;
			Texture->Bounds = PendingGroup.Bounds;
			Texture->LightmapFlags = PendingGroup.LightmapFlags;
			verify(Texture->AddElement(PendingGroup));
		}

		// Give the texture ownership of the allocations
		if (PendingGroup.GroupedPenddingGroups.Num() > 0)
		{
			uint32 AllocationOffset = Texture->Allocations.Num();
			for (auto GroupedPenddingGroup : PendingGroup.GroupedPenddingGroups)
			{
				GroupedPenddingGroup->AllocationOffset = AllocationOffset;
				AllocationOffset += GroupedPenddingGroup->MappingGuids.Num();
				GroupedPenddingGroup->PenddingTexture = Texture;
			}
		}
		else
		{
			PendingGroup.AllocationOffset = Texture->Allocations.Num();
			PendingGroup.PenddingTexture = Texture;
		}
		for (auto& Allocation : PendingGroup.Allocations)
		{
			Texture->Allocations.Add(MoveTemp(Allocation));
		}
		PendingGroup.Allocations.Empty();
	}

	InPenddingLightMaps.Empty();
}

void FTLBSLightMap2D::GroupLightMapAllocationGroups(UWorld* InWorld, TArray<TSharedPtr<FTLBSLightMapAllocationGroup>>& InPenddingLightMaps)
{
	static const auto CVar = IConsoleManager::Get().FindTConsoleVariableDataInt(TEXT("r.LightmapGroupSize"));
	int32 GroupSize = CVar ? CVar->GetValueOnAnyThread() : 0;
	if (GroupSize <= 0)
	{
		return;
	}
	TArray<TSharedPtr<FTLBSLightMapAllocationGroup>> OutPenddingLightMaps;;

	static const auto CVarVirtualTexturedLightmaps = IConsoleManager::Get().FindTConsoleVariableDataInt(TEXT("r.VirtualTexturedLightmaps"));
	const bool bUseVirtualTextures = (CVarVirtualTexturedLightmaps->GetValueOnAnyThread() != 0) && UseVirtualTexturing(GMaxRHIShaderPlatform);

	int32 PackedTextureSizeX = InWorld->GetWorldSettings()->PackedLightAndShadowMapTextureSize;
	int32 PackedTextureSize = PackedTextureSizeX * PackedTextureSizeX / (bUseVirtualTextures ? 1 : 2);

	OutPenddingLightMaps.Reserve(InPenddingLightMaps.Num());

	TMap<uint32, TArray<TSharedPtr<FTLBSLightMapAllocationGroup>>> MeshPenddingLightMaps;
	// step 1 按mesh划分
	for (auto PenddingLightMap : InPenddingLightMaps)
	{
		if (PenddingLightMap->MappingMeshHash != UNGROUP_MESH_HASH)
		{
			auto& PenddingLightMaps = MeshPenddingLightMaps.FindOrAdd(PenddingLightMap->MappingMeshHash);
			PenddingLightMaps.Add(PenddingLightMap);
		}
		else
		{
			OutPenddingLightMaps.Add(PenddingLightMap);
		}
	}

	auto GroupPenddingLightMaps = [&OutPenddingLightMaps, PackedTextureSize](TArray<TSharedPtr<FTLBSLightMapAllocationGroup>>& PenddingLightMaps, double AffectDis) {
		uint32 MeshIndex = 1;
		TMap<uint32, TArray<TSharedPtr<FTLBSLightMapAllocationGroup>>> GroupedPenddingLightMapsMap;
		TMap<TSharedPtr<FTLBSLightMapAllocationGroup>, uint32> PenddingLightMapMeshIndexMap;
		for (int i = 0; i < PenddingLightMaps.Num(); i++)
		{
			auto PenddingLightMap0 = PenddingLightMaps[i];
			for (int j = 1; j < PenddingLightMaps.Num(); j++)
			{
				auto PenddingLightMap1 = PenddingLightMaps[j];
				if (i == j || PenddingLightMap0->Outer != PenddingLightMap1->Outer || FVector::Dist2D(PenddingLightMap0->MappingPosition, PenddingLightMap1->MappingPosition) > AffectDis)
					continue;

				uint32 OldMeshIndex0 = PenddingLightMapMeshIndexMap.Find(PenddingLightMap0) ? PenddingLightMapMeshIndexMap[PenddingLightMap0] : 0;
				uint32 OldMeshIndex1 = PenddingLightMapMeshIndexMap.Find(PenddingLightMap1) ? PenddingLightMapMeshIndexMap[PenddingLightMap1] : 0;
				uint32 TargetMeshIndex = OldMeshIndex0 > 0 ? OldMeshIndex0 : (OldMeshIndex1 > 0 ? OldMeshIndex1 : MeshIndex++);

				auto& GroupedPenddingLightMaps = GroupedPenddingLightMapsMap.FindOrAdd(TargetMeshIndex);
				if (OldMeshIndex0 != TargetMeshIndex)
				{
					if (OldMeshIndex0 > 0)
					{
						for (auto PenddingLightMap : GroupedPenddingLightMapsMap[OldMeshIndex0])
						{
							GroupedPenddingLightMaps.Add(PenddingLightMap);
							PenddingLightMapMeshIndexMap[PenddingLightMap] = TargetMeshIndex;
						}
						GroupedPenddingLightMapsMap.Remove(OldMeshIndex0);
					}
					else
					{
						GroupedPenddingLightMaps.Add(PenddingLightMap0);
						PenddingLightMapMeshIndexMap.Add(PenddingLightMap0, TargetMeshIndex);
					}
				}

				if (OldMeshIndex1 != TargetMeshIndex )
				{
					if (OldMeshIndex1 > 0)
					{
						if (OldMeshIndex0 != OldMeshIndex1)
						{
							for (auto PenddingLightMap : GroupedPenddingLightMapsMap[OldMeshIndex1])
							{
								GroupedPenddingLightMaps.Add(PenddingLightMap);
								PenddingLightMapMeshIndexMap[PenddingLightMap] = TargetMeshIndex;
							}
							GroupedPenddingLightMapsMap.Remove(OldMeshIndex1);
						}
					}
					else
					{
						GroupedPenddingLightMaps.Add(PenddingLightMap1);
						PenddingLightMapMeshIndexMap.Add(PenddingLightMap1, TargetMeshIndex);
					}
				}
			}
		}

		for (auto PenddingLightMap : PenddingLightMaps)
		{
			if (auto IndexPtr = PenddingLightMapMeshIndexMap.Find(PenddingLightMap))
			{
				if (auto GroupedPenddingLightMaps = GroupedPenddingLightMapsMap.Find(*IndexPtr))
				{
					GroupedPenddingLightMaps->Sort([](const TSharedPtr<FTLBSLightMapAllocationGroup>& A, const TSharedPtr<FTLBSLightMapAllocationGroup>& B) { return A->MappingGuids[0] < B->MappingGuids[0]; });
					
					TSharedPtr<FTLBSLightMapAllocationGroup> NewPenddingLightMap;;
					for (int Index = 0; Index < GroupedPenddingLightMaps->Num(); Index++)
					{
						if (!NewPenddingLightMap.IsValid() || NewPenddingLightMap->TotalTexels >= PackedTextureSize)
						{
							NewPenddingLightMap = MakeShared<FTLBSLightMapAllocationGroup>();
							NewPenddingLightMap->Outer = PenddingLightMap->Outer;
							NewPenddingLightMap->LightmapFlags = PenddingLightMap->LightmapFlags;
							NewPenddingLightMap->Bounds = PenddingLightMap->Bounds;
							NewPenddingLightMap->TotalTexels = 0;
							NewPenddingLightMap->GroupedPenddingGroups.Reserve(GroupedPenddingLightMaps->Num());
							OutPenddingLightMaps.Add(NewPenddingLightMap);
						}
						auto GroupedPenddingLightMap = (*GroupedPenddingLightMaps)[Index];
						NewPenddingLightMap->TotalTexels += GroupedPenddingLightMap->TotalTexels;
						NewPenddingLightMap->MappingGuids.Append(GroupedPenddingLightMap->MappingGuids);
						NewPenddingLightMap->Allocations.Append(MoveTemp(GroupedPenddingLightMap->Allocations));
						NewPenddingLightMap->GroupedPenddingGroups.Add(GroupedPenddingLightMap);
						GroupedPenddingLightMap->Allocations.Empty();

					}
					GroupedPenddingLightMapsMap.Remove(*IndexPtr);
				}
			}
			else
			{
				OutPenddingLightMaps.Add(PenddingLightMap);
			}
		}
	};

	for (auto& [MeshHash, PenddingLightMaps] : MeshPenddingLightMaps)
	{
		if (PenddingLightMaps.Num() == 1)
		{
			OutPenddingLightMaps.Add(PenddingLightMaps[0]);
		}
		else
		{
			GroupPenddingLightMaps(PenddingLightMaps, GroupSize);
		}
	}
	std::swap(InPenddingLightMaps, OutPenddingLightMaps);
}

void FTLBSLightMap2D::AllocateLightMapAllocationGroup(FTLBSLightingSystem* TLBSSystem, FGuid MappingGuid, const FStaticMeshStaticLightingTextureMapping* TextureMapping, FTLBSLightMapCache& Cache)
{
	auto PenddingLightMap = MakeShared<FTLBSLightMapAllocationGroup>();

	const UStaticMeshComponent* StaticMeshComponent = TextureMapping->Primitive.Get();
	PenddingLightMap->Outer = TLBSSystem->GetOrCreateRegistryForActor(StaticMeshComponent->GetOwner());
	PenddingLightMap->LightmapFlags = GAllowStreamingLightmaps ? LMF_Streamed : ELightMapFlags(LMF_Streamed & ~LMF_Streamed);
	PenddingLightMap->Bounds = StaticMeshComponent->Bounds;
	PenddingLightMap->MappingGuids.Add(MappingGuid);
	PenddingLightMap->MappingPosition = StaticMeshComponent->GetComponentLocation();
	PenddingLightMap->MappingMeshHash = GetTypeHash(StaticMeshComponent->GetStaticMesh());

	ELightMapPaddingType PaddingType = GAllowLightmapPadding ? LMPT_NormalPadding : LMPT_NoPadding;
	bool HasSkyShadowing	= TLBSSystem->TLBSProcessor->BakingContext.HasSkyShadowing();
#if ENABLE_PRT_API
	bool HasPrtTexture = StaticMeshComponent->PrtBakingMode == EPrtBakingMode::PrtAndStatic || StaticMeshComponent->PrtBakingMode == EPrtBakingMode::PrtOnly;
	bool HasPrtHQTextures	= TLBSSystem->TLBSProcessor->BakingContext.bPrtHQEnabled && HasPrtTexture;
	bool HasPrtLQTextures	= TLBSSystem->TLBSProcessor->BakingContext.bPrtLQEnabled && HasPrtTexture;
	bool HasStaticTextures	= !TLBSSystem->TLBSProcessor->BakingContext.bPrtEnabled || StaticMeshComponent->PrtBakingMode == EPrtBakingMode::PrtAndStatic || StaticMeshComponent->PrtBakingMode == EPrtBakingMode::StaticOnly;
	AllocateLightMapAllocationGroup(TextureMapping->SizeX, TextureMapping->SizeY, PaddingType, HasSkyShadowing, HasPrtHQTextures, HasPrtLQTextures, HasStaticTextures, PenddingLightMap);
#else
	AllocateLightMapAllocationGroup(TextureMapping->SizeX, TextureMapping->SizeY, PaddingType, HasSkyShadowing, false, false, true, PenddingLightMap);
#endif
	PenddingLightMap->LightMapAllocationNum = PenddingLightMap->Allocations.Num();
	
	Cache.PendingGroups.Add(PenddingLightMap);
	Cache.TextureMappingPenddingGroupMap.Add(MappingGuid, PenddingLightMap);
}

void FTLBSLightMap2D::AllocateLightMapAllocationGroup(FTLBSLightingSystem* TLBSSystem, FGuid MappingGuid, const FLandscapeStaticLightingTextureMapping* TextureMapping, FTLBSLightMapCache& Cache)
{
	auto PenddingLightMap = MakeShared<FTLBSLightMapAllocationGroup>();

	const ULandscapeComponent* LandscapeComponent = TextureMapping->LandscapeComponent;

	PenddingLightMap->Outer = TLBSSystem->GetOrCreateRegistryForActor(LandscapeComponent->GetOwner());
	PenddingLightMap->LightmapFlags = GAllowStreamingLightmaps ? LMF_Streamed : ELightMapFlags(LMF_Streamed & ~LMF_Streamed);
	PenddingLightMap->Bounds = LandscapeComponent->Bounds;
	PenddingLightMap->MappingGuids.Add(MappingGuid);
	PenddingLightMap->MappingPosition = {0, 0, 0};
	PenddingLightMap->MappingMeshHash = UNGROUP_MESH_HASH;
	bool HasSkyShadowing = TLBSSystem->TLBSProcessor->BakingContext.HasSkyShadowing();
#if ENABLE_PRT_API
	bool HasPrtTexture		= LandscapeComponent->PrtBakingMode == EPrtBakingMode::PrtAndStatic || LandscapeComponent->PrtBakingMode == EPrtBakingMode::PrtOnly;
	bool HasPrtHQTextures	= TLBSSystem->TLBSProcessor->BakingContext.bPrtHQEnabled && HasPrtTexture;
	bool HasPrtLQTextures	= TLBSSystem->TLBSProcessor->BakingContext.bPrtLQEnabled && HasPrtTexture;
	bool HasStaticTextures	= !TLBSSystem->TLBSProcessor->BakingContext.bPrtEnabled || LandscapeComponent->PrtBakingMode == EPrtBakingMode::PrtAndStatic || LandscapeComponent->PrtBakingMode == EPrtBakingMode::StaticOnly;
	AllocateLightMapAllocationGroup(TextureMapping->SizeX, TextureMapping->SizeY, LMPT_NoPadding, HasSkyShadowing, HasPrtHQTextures, HasPrtLQTextures, HasStaticTextures, PenddingLightMap);
#else
	AllocateLightMapAllocationGroup(TextureMapping->SizeX, TextureMapping->SizeY, LMPT_NoPadding, HasSkyShadowing, false, false, true, PenddingLightMap);
#endif
	PenddingLightMap->LightMapAllocationNum = PenddingLightMap->Allocations.Num();
	
	Cache.PendingGroups.Add(PenddingLightMap);
	Cache.TextureMappingPenddingGroupMap.Add(MappingGuid, PenddingLightMap);
}

void FTLBSLightMap2D::AllocateLightMapAllocationGroup(FTLBSLightingSystem* TLBSSystem, FGuid MappingGuid, const FStaticLightingTextureMapping_InstancedStaticMesh* TextureMapping, FTLBSLightMapCache& Cache)
{
	UInstancedStaticMeshComponent* InstancedComponent = Cast<UInstancedStaticMeshComponent>(TextureMapping->Primitive.Get());
	UMapBuildDataRegistry* Registry = TLBSSystem->GetOrCreateRegistryForActor(InstancedComponent->GetOwner());
	ELightMapPaddingType PaddingType = GAllowLightmapPadding ? LMPT_PrePadding : LMPT_NoPadding;
	bool HasSkyShadowing = TLBSSystem->TLBSProcessor->BakingContext.HasSkyShadowing();
#if ENABLE_PRT_API
	bool HasPrtTexture		= InstancedComponent->PrtBakingMode == EPrtBakingMode::PrtAndStatic || InstancedComponent->PrtBakingMode == EPrtBakingMode::PrtOnly;
	bool HasPrtHQTextures	= TLBSSystem->TLBSProcessor->BakingContext.bPrtHQEnabled && HasPrtTexture;
	bool HasPrtLQTextures	= TLBSSystem->TLBSProcessor->BakingContext.bPrtLQEnabled && HasPrtTexture;
	bool HasStaticTextures	= !TLBSSystem->TLBSProcessor->BakingContext.bPrtEnabled || InstancedComponent->PrtBakingMode == EPrtBakingMode::PrtAndStatic || InstancedComponent->PrtBakingMode == EPrtBakingMode::StaticOnly;
#endif
	if (auto PenddingLightMapPtr = Cache.InstanceComPendingGroupMap.Find(InstancedComponent))
	{
		auto PenddingLightMap = (*PenddingLightMapPtr);
		PenddingLightMap->MappingGuids[TextureMapping->InstanceIndex] = MappingGuid;
		FGuid MapBuildDataId = InstancedComponent->LODData[0].MapBuildDataId;
#if ENABLE_PRT_API
		AllocateInstancedLightMapAllocationGroup(InstancedComponent, Registry, MapBuildDataId, TextureMapping->InstanceIndex, TextureMapping->SizeX, TextureMapping->SizeY, PaddingType, HasSkyShadowing, HasPrtHQTextures, HasPrtLQTextures, HasStaticTextures, PenddingLightMap);
#else
		AllocateInstancedLightMapAllocationGroup(InstancedComponent, Registry, MapBuildDataId, TextureMapping->InstanceIndex, TextureMapping->SizeX, TextureMapping->SizeY, PaddingType, HasSkyShadowing, false, false, true, PenddingLightMap);
#endif
		PenddingLightMap->LightMapAllocationNum += 1;
		Cache.TextureMappingPenddingGroupMap.Add(MappingGuid, PenddingLightMap);
	}
	else
	{
		UStaticMesh* ResolvedMesh = InstancedComponent->GetStaticMesh();
		if (InstancedComponent->LODData.Num() != ResolvedMesh->GetNumLODs())
		{
			InstancedComponent->SetLODDataCount(ResolvedMesh->GetNumLODs(), ResolvedMesh->GetNumLODs());
			InstancedComponent->MarkPackageDirty();
		}
		InstancedComponent->LODData[0].CreateMapBuildDataId(0);

		auto PenddingLightMap = MakeShared<FTLBSLightMapAllocationGroup>();
		PenddingLightMap->Allocations.SetNum(InstancedComponent->PerInstanceSMData.Num());
		PenddingLightMap->MappingGuids.SetNum(InstancedComponent->PerInstanceSMData.Num());
		PenddingLightMap->Outer = Registry;
		PenddingLightMap->LightmapFlags = GAllowStreamingLightmaps ? LMF_None : ELightMapFlags(LMF_None & ~LMF_Streamed);
		PenddingLightMap->Bounds = InstancedComponent->Bounds;
		PenddingLightMap->LightMapAllocationNum = 1;
		PenddingLightMap->MappingGuids[TextureMapping->InstanceIndex] = MappingGuid;
		PenddingLightMap->MappingPosition = { 0, 0, 0 };
		PenddingLightMap->MappingMeshHash = UNGROUP_MESH_HASH;

		FGuid MapBuildDataId = InstancedComponent->LODData[0].MapBuildDataId;
#if ENABLE_PRT_API
		AllocateInstancedLightMapAllocationGroup(InstancedComponent, Registry, MapBuildDataId, TextureMapping->InstanceIndex, TextureMapping->SizeX,  TextureMapping->SizeY, PaddingType, HasSkyShadowing, HasPrtHQTextures, HasPrtLQTextures, HasStaticTextures, PenddingLightMap);
#else
		AllocateInstancedLightMapAllocationGroup(InstancedComponent, Registry, MapBuildDataId, TextureMapping->InstanceIndex, TextureMapping->SizeX, TextureMapping->SizeY, PaddingType, HasSkyShadowing, false, false, true, PenddingLightMap);
#endif
		Cache.PendingGroups.Add(PenddingLightMap);
		Cache.TextureMappingPenddingGroupMap.Add(MappingGuid, PenddingLightMap);
		Cache.InstanceComPendingGroupMap.Add(InstancedComponent, PenddingLightMap);
	}
}

void FTLBSLightMap2D::AllocateShadowMapAllocationGroup(FTLBSLightingSystem* TLBSSystem, FGuid MappingGuid, const FStaticMeshStaticLightingTextureMapping* TextureMapping, FTLBSLightMapCache& Cache)
{
	auto PenddingLightMap = Cache.TextureMappingPenddingGroupMap[TextureMapping->GetLightingGuid()];
	PenddingLightMap->ShadowMapAllocationNum++;
}

void FTLBSLightMap2D::AllocateShadowMapAllocationGroup(FTLBSLightingSystem* TLBSSystem, FGuid MappingGuid, const FLandscapeStaticLightingTextureMapping* TextureMapping, FTLBSLightMapCache& Cache)
{
	auto PenddingLightMap = Cache.TextureMappingPenddingGroupMap[TextureMapping->GetLightingGuid()];
	PenddingLightMap->ShadowMapAllocationNum++;
}

void FTLBSLightMap2D::AllocateShadowMapAllocationGroup(FTLBSLightingSystem* TLBSSystem, FGuid MappingGuid, const FStaticLightingTextureMapping_InstancedStaticMesh* TextureMapping, FTLBSLightMapCache& Cache)
{
	auto PenddingLightMap = Cache.TextureMappingPenddingGroupMap[TextureMapping->GetLightingGuid()];
	PenddingLightMap->ShadowMapAllocationNum++;
}

//#pragma optimize("", on)