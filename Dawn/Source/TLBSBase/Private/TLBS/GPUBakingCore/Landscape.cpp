#pragma once

#include "Landscape.h"
#include "TLBS/TLBS.h"

#define LANDSCAPE_LIGHTMAP_UV_INDEX 1

NS_BEGIN


inline float3 VectorToFloat3(const FVector3f& Input) {
	return { Input.X,Input.Y, Input.Z };
}


FLandscape::FLandscape(const FLandscapeInfo& InLandscapeInfo) :LandscapeInfo(InLandscapeInfo) {

	NumVertices = LandscapeInfo.ComponentSizeQuads + 2 * LandscapeInfo.ExpandQuadsX + 1;
	NumQuads = NumVertices - 1;
	UVFactor = LandscapeInfo.LightMapRatio / NumVertices;
	bReverseWinding = InLandscapeInfo.Flags & MESH_INSTANCE_FLAGS_REVERSE_WINDING;

	static_assert(sizeof(FMatrix44f) == sizeof(LandscapeInfo.Transform.mm), "LocalToWorld");
	FMatrix44f TransformMatrix;
	memcpy(&TransformMatrix, &(LandscapeInfo.Transform.mm[0]), sizeof(FMatrix44f));
	LocalToWorld.SetFromMatrix(TransformMatrix);	
}


bool FLandscape::CreateMesh(FMeshInfo& MeshInfo) {

	check(LandscapeInfo.NumTriangles > 0 && NumVertices > 0);

	MeshInfo.Guid = LandscapeInfo.Guid;
	MeshInfo.LODIndex = 0;
	MeshInfo.NumTriangles = LandscapeInfo.NumTriangles;
	MeshInfo.NumVertices = NumVertices * NumVertices;
	MeshInfo.NumElements = 1;

	GPUBaking::FMeshElementInfo& MeshElementInfo = MeshInfo.ElementInfos.AddDefaultRef();
	MeshElementInfo.Flags = 0;
	MeshElementInfo.MaterialIndex = -1;
	MeshElementInfo.StartIndex = 0;
	MeshElementInfo.PrimitiveCount = MeshInfo.NumTriangles;

	int32 MaxVertexIndex = 0;	
	if (MeshInfo.NumVertices >= MAX_INDICES16) {
		MeshInfo.Index32BitBuffer.Resize(MeshInfo.NumTriangles * 3);
		for (uint32 i = 0; i < MeshInfo.NumTriangles; ++i) {
			int32 I0, I1, I2;
			GetTriangleIndices(i, I0, I1, I2);
			MeshInfo.Index32BitBuffer[i * 3] = I0;
			MeshInfo.Index32BitBuffer[i * 3 + 1] = I1;
			MeshInfo.Index32BitBuffer[i * 3 + 2] = I2;
			MaxVertexIndex = FMath::Max(MaxVertexIndex, FMath::Max3(I0, I1, I2));
		}
	}
	else {
		MeshInfo.Index16BitBuffer.Resize(MeshInfo.NumTriangles * 3);
		for (uint32 i = 0; i < MeshInfo.NumTriangles; ++i) {
			int32 I0, I1, I2;
			GetTriangleIndices(i, I0, I1, I2);
			MeshInfo.Index16BitBuffer[i * 3] = I0;
			MeshInfo.Index16BitBuffer[i * 3 + 1] = I1;
			MeshInfo.Index16BitBuffer[i * 3 + 2] = I2;
			MaxVertexIndex = FMath::Max(MaxVertexIndex, FMath::Max3(I0, I1, I2));
		}
	}

	check(MaxVertexIndex + 1 == MeshInfo.NumVertices);

	MeshInfo.VertexBuffer.Resize(MeshInfo.NumVertices);
	MeshInfo.NormalBuffer.Resize(MeshInfo.NumVertices);
	MeshInfo.TangentBuffer.Resize(MeshInfo.NumVertices);
	MeshInfo.BiTangentBuffer.Resize(MeshInfo.NumVertices);
	MeshInfo.TexcoordBuffers.Resize(LANDSCAPE_LIGHTMAP_UV_INDEX+1);
	for (uint32 TextureCoordinateIndex = 0; TextureCoordinateIndex <= LANDSCAPE_LIGHTMAP_UV_INDEX; ++TextureCoordinateIndex) {
		MeshInfo.TexcoordBuffers[TextureCoordinateIndex].TexcoordBuffer.Resize(MeshInfo.NumVertices);
	}

	float3 WorldPosition;
	float3 WorldTangentX;
	float3 WorldTangentY;
	float3 WorldTangentZ;
	float2 UV[2];
	for (uint32 VertexIndex = 0; VertexIndex < MeshInfo.NumVertices; VertexIndex++) {
		this->GetLandscapeVertex(VertexIndex, WorldPosition, WorldTangentX, WorldTangentY, WorldTangentZ, UV);

		MeshInfo.VertexBuffer[VertexIndex] = WorldPosition;
		MeshInfo.TangentBuffer[VertexIndex] = WorldTangentX;
		MeshInfo.BiTangentBuffer[VertexIndex] = WorldTangentY;
		MeshInfo.NormalBuffer[VertexIndex] = WorldTangentZ;
		for (int UVIndex = 0; UVIndex <= LANDSCAPE_LIGHTMAP_UV_INDEX; UVIndex++)
		{
			MeshInfo.TexcoordBuffers[UVIndex].TexcoordBuffer[VertexIndex] = UV[UVIndex];
		}
	}

	return true;
}

bool FLandscape::CreateMeshInstance(FMeshInstanceInfo& MeshInstanceInfo) {

	MeshInstanceInfo.LevelGuid = LandscapeInfo.LevelGuid;
	MeshInstanceInfo.Guid = LandscapeInfo.Guid;
	MeshInstanceInfo.TexcoordIndex = 0;
	MeshInstanceInfo.MeshIndex = -1;
	MeshInstanceInfo.Flags = LandscapeInfo.Flags;
	MeshInstanceInfo.EmissiveBoost = LandscapeInfo.EmissiveBoost;
	MeshInstanceInfo.DiffuseBoost = LandscapeInfo.DiffuseBoost;
	MeshInstanceInfo.FullyOccludedSamplesFraction = LandscapeInfo.FullyOccludedSamplesFraction;

	MeshInstanceInfo.MaterialOverrides = LandscapeInfo.MaterialOverrides;
	MeshInstanceInfo.RelevantLights = LandscapeInfo.RelevantLights;
	MeshInstanceInfo.MeshInstanceVisibilityMask = LandscapeInfo.MeshInstanceVisibilityMask;
	MeshInstanceInfo.MeshInstanceVisibilityMaskInHLODScene = LandscapeInfo.MeshInstanceVisibilityMaskInHLODScene;
	MeshInstanceInfo.LODLevelInHLODScene = -1;

	MeshInstanceInfo.Transform = {
		1,0,0,0,
		0,1,0,0,
		0,0,1,0,
		0,0,0,1
	};
	MeshInstanceInfo.InverseTransform = {
		1,0,0,0,
		0,1,0,0,
		0,0,1,0,
		0,0,0,1
	};

	MeshInstanceInfo.BoundingBox = LandscapeInfo.BoundingBox;

	return true;
}

void FLandscape::GetTriangleIndices(int32 TriangleIndex, int32& OutI0, int32& OutI1, int32& OutI2) const
{
	int32 QuadIndex = TriangleIndex >> 1;
	int32 QuadTriIndex = TriangleIndex & 1;

	int32 QuadX = QuadIndex % (NumVertices - 1);
	int32 QuadY = QuadIndex / (NumVertices - 1);

	switch (QuadTriIndex)
	{
	case 0:
		OutI0 = (QuadX + 0) + (QuadY + 0) * NumVertices;
		OutI1 = (QuadX + 1) + (QuadY + 1) * NumVertices;
		OutI2 = (QuadX + 1) + (QuadY + 0) * NumVertices;
		break;
	case 1:
		OutI0 = (QuadX + 0) + (QuadY + 0) * NumVertices;
		OutI1 = (QuadX + 0) + (QuadY + 1) * NumVertices;
		OutI2 = (QuadX + 1) + (QuadY + 1) * NumVertices;
		break;
	}

	if (bReverseWinding)
	{
		Swap(OutI1, OutI2);
	}
}


#define LANDSCAPE_ZSCALE		(1.0f/128.0f)
const int32 MaxValue = 65535;
const float MidValue = 32768.f;
inline float GetLocalHeight(uint16 Height)
{
	return ((float)Height - MidValue) * LANDSCAPE_ZSCALE;
}

//void FLandscape::GetStaticLightingVertex(int32 VertexIndex, FStaticLightingVertex& OutVertex) const
//{
//	const int32 X = VertexIndex % NumVertices;
//	const int32 Y = VertexIndex / NumVertices;
//
//	const int32 LocalX = X - LandscapeInfo.ExpandQuadsX;
//	const int32 LocalY = Y - LandscapeInfo.ExpandQuadsY;
//
//	const ubyte4* Data = &LandscapeInfo.HeightMap[X + Y * NumVertices];
//
//	//union { struct{ uint8 B,G,R,A; }
//	//x = B,y=G,R=z,A=w
//
//	uint8 B = Data->x;
//	uint8 G = Data->y;
//	uint8 R = Data->z;
//	uint8 A = Data->w;
//
//	OutVertex.WorldTangentZ.X = 2.0f / 255.f * (float)B - 1.0f;
//	OutVertex.WorldTangentZ.Y = 2.0f / 255.f * (float)A - 1.0f;
//	OutVertex.WorldTangentZ.Z = FMath::Sqrt(1.0f - (FMath::Square(OutVertex.WorldTangentZ.X) + FMath::Square(OutVertex.WorldTangentZ.Y)));
//	OutVertex.WorldTangentX = DDFVector3f4f(OutVertex.WorldTangentZ.Z, 0.0f, -OutVertex.WorldTangentZ.X);
//	OutVertex.WorldTangentY = OutVertex.WorldTangentZ ^ OutVertex.WorldTangentX;
//
//	// Copied from FLandscapeComponentDataInterface::GetWorldPositionTangents to fix bad lighting when rotated
//	OutVertex.WorldTangentX = LocalToWorld.TransformVectorNoScale(OutVertex.WorldTangentX);
//	OutVertex.WorldTangentY = LocalToWorld.TransformVectorNoScale(OutVertex.WorldTangentY);
//	OutVertex.WorldTangentZ = LocalToWorld.TransformVectorNoScale(OutVertex.WorldTangentZ);
//
//	const uint16 Height = (R << 8) + G;
//	OutVertex.WorldPosition = LocalToWorld.TransformPosition(FVector3f(LocalX, LocalY, GetLocalHeight(Height)));
//
//	OutVertex.TextureCoordinates[0] = FVector2f((float)X / NumVertices, (float)Y / NumVertices);
//	OutVertex.TextureCoordinates[LANDSCAPE_LIGHTMAP_UV_INDEX].X = X * UVFactor;
//	OutVertex.TextureCoordinates[LANDSCAPE_LIGHTMAP_UV_INDEX].Y = Y * UVFactor;
//}

void FLandscape::GetLandscapeVertex(int32 VertexIndex, float3& WorldPosition, float3& WorldTangentX, float3& WorldTangentY, float3& WorldTangentZ, float2 UV[2]) const
{
	const int32 X = VertexIndex % NumVertices;
	const int32 Y = VertexIndex / NumVertices;

	const int32 LocalX = X - LandscapeInfo.ExpandQuadsX;
	const int32 LocalY = Y - LandscapeInfo.ExpandQuadsY;

	const ubyte4* Data = &LandscapeInfo.HeightMap[X + Y * NumVertices];

	//union { struct{ uint8 B,G,R,A; }
	//x = B,y=G,R=z,A=w

	uint8 B = Data->x;
	uint8 G = Data->y;
	uint8 R = Data->z;
	uint8 A = Data->w;

	WorldTangentZ.x = 2.0f / 255.f * (float)B - 1.0f;
	WorldTangentZ.y = 2.0f / 255.f * (float)A - 1.0f;
	WorldTangentZ.z = FMath::Sqrt(1.0f - (FMath::Square(WorldTangentZ.x) + FMath::Square(WorldTangentZ.y)));
	WorldTangentX = float3(WorldTangentZ.z, 0.0f, -WorldTangentZ.x);
	WorldTangentY = WorldTangentZ ^ WorldTangentX;

	// Copied from FLandscapeComponentDataInterface::GetWorldPositionTangents to fix bad lighting when rotated
	WorldTangentX = GPUBaking::VectorToFloat3(LocalToWorld.TransformVectorNoScale(FVector3f(WorldTangentX.x, WorldTangentX.y, WorldTangentX.z)));
	WorldTangentY = GPUBaking::VectorToFloat3(LocalToWorld.TransformVectorNoScale(FVector3f(WorldTangentY.x, WorldTangentY.y, WorldTangentY.z)));
	WorldTangentZ = GPUBaking::VectorToFloat3(LocalToWorld.TransformVectorNoScale(FVector3f(WorldTangentZ.x, WorldTangentZ.y, WorldTangentZ.z)));

	const uint16 Height = (R << 8) + G;
	WorldPosition = GPUBaking::VectorToFloat3(LocalToWorld.TransformPosition(FVector3f(LocalX, LocalY, GetLocalHeight(Height))));

	UV[0] = float2((float)X / NumVertices, (float)Y / NumVertices);
	UV[LANDSCAPE_LIGHTMAP_UV_INDEX].x = X * UVFactor;
	UV[LANDSCAPE_LIGHTMAP_UV_INDEX].y = Y * UVFactor;
}

NS_END
