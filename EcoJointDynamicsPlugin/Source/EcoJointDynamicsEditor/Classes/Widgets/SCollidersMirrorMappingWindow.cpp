#include "SCollidersMirrorMappingWindow.h"
#include "Widgets/Colors/SColorBlock.h"
#include "Widgets/Input/SEditableTextBox.h"
#include "InputCoreTypes.h"

#define LOCTEXT_NAMESPACE "FEcoJointDynamicsEditorModule"

void SCollidersMirrorMappingWindow::Construct(const FArguments& InArgs)
{
	check(InArgs._SourceColliderBones != nullptr);
	check(InArgs._ResultColliderMirrorSettings != nullptr);
	check(InArgs._RefSkeletonBoneList != nullptr);

	WidgetWindow = InArgs._WidgetWindow;
	SourceColliderBones = InArgs._SourceColliderBones;
	ResultColliderMirrorSettings = InArgs._ResultColliderMirrorSettings;
	RefSkeletonBoneList = InArgs._RefSkeletonBoneList;
	ResultMirrorFace = InArgs._ResultMirrorFace;

	for (int32 i = 0; i < SourceColliderBones->Num(); ++i)
		RowIndices.Add(MakeShared<int32>(i));

	ResultNameComboBoxItems.Add(FName("NoMirror"));
	ResultNameComboBoxItems.Add(FName("Self_Mirror_YZ"));
	ResultNameComboBoxItems.Add(FName("Self_Mirror_XY"));
	ResultNameComboBoxItems.Add(FName("Self_Mirror_XZ"));
	for (const FName& Option : *RefSkeletonBoneList)
		ResultNameComboBoxItems.Add(FName(Option));

	const TSharedPtr<SWidget> CollidersMirrorSection = ConstructCollidersMirrorTable();

	this->ChildSlot
	[
		SNew(SVerticalBox)
		+ SVerticalBox::Slot()
		.FillHeight(1.0f)
		.Padding(2.0f)
		[
			SNew(SScrollBox)
			+ SScrollBox::Slot()
			[
				SNew(SBorder)
				.BorderImage(FAppStyle::GetBrush("ToolPanel.DarkGroupBorder"))
				[
					SNew(SVerticalBox)
					+ SVerticalBox::Slot()
					.FillHeight(1.0f)
					.Padding(2.0f)
					[
						SNew(SVerticalBox)
						+ SVerticalBox::Slot()
						.AutoHeight()
						.Padding(2.0f)
						[
							CollidersMirrorSection.ToSharedRef()
						]
					]
				]
			]
		]
		+ SVerticalBox::Slot()
		.AutoHeight()
		.HAlign(HAlign_Right)
		.Padding(2)
		[
			SNew(SHorizontalBox)
			+ SHorizontalBox::Slot()
			.AutoWidth()
			.Padding(3.0f, 0.0f)
			[
				SNew(SButton)
				.HAlign(HAlign_Center)
				.ToolTipText(LOCTEXT("SCollidersMirrorMappingWindow_Reset_Tooltip",
				                     "如果Matching Collider Bones选择不为None，则按照对应关系进行碰撞体的对称。（SelfMatched Colliders不会进行碰撞体生成）"))
				.Text(LOCTEXT("SCollidersMirrorMappingWindow_Done", "Colliders Mirror"))
				.OnClicked(this, &SCollidersMirrorMappingWindow::OnDone)
			]
			+ SHorizontalBox::Slot()
			.AutoWidth()
			.Padding(3.0f, 0.0f)
			[
				SNew(SButton)
				.HAlign(HAlign_Center)
				.Text(LOCTEXT("SCollidersMirrorMappingWindow_Preview_Cancel", "Cancel"))
				.OnClicked(this, &SCollidersMirrorMappingWindow::OnCancel)
			]
		]
	];
}

TSharedPtr<SWidget> SCollidersMirrorMappingWindow::ConstructCollidersMirrorTable()
{
	const FText CollidersMirrorInstruction = LOCTEXT("SCollidersMirrorMappingWindow_CollidersMirrorDocumentation",
	                                                 "请检查非SelfMateched Colliders的Matching Collider Bones，修复对应关系\n (Adjustable Capsule Colliders不会参与对称)");
	const FText CollidersMirrorInstructionTooltip = LOCTEXT("SCollidersMirrorMappingWindow_MaterialCompareDocumentationTooltip",
	                                                        "若要修复请点击下拉框进行选择.");

	return SNew(SBox)
		.MaxDesiredHeight(500.0f)
		[
			SNew(SBorder)
			.Padding(FMargin(3))
			.BorderImage(FAppStyle::GetBrush("ToolPanel.GroupBorder"))
			[
				SNew(SVerticalBox)
				+ SVerticalBox::Slot()
				.AutoHeight()
				.Padding(2)
				[
					SNew(SHorizontalBox)
					+ SHorizontalBox::Slot()
					.AutoWidth()
					[
						SNew(STextBlock)
						.Font(FAppStyle::GetFontStyle("DetailsView.CategoryFontStyle"))
						.Text(LOCTEXT("SCollidersMirrorMappingWindow_CollidersMirrorHeader", "Colliders Joints"))
					]
				]
				+ SVerticalBox::Slot()
				.FillHeight(1.0f)
				.Padding(2)
				[
					SNew(SBox)
					[
						SNew(SVerticalBox)
						+ SVerticalBox::Slot()
						.AutoHeight()
						.Padding(2)
						[
							SNew(STextBlock)
							.Text(CollidersMirrorInstruction)
							.ToolTipText(CollidersMirrorInstructionTooltip)
						]
						+ SVerticalBox::Slot()
						.FillHeight(1.0f)
						.Padding(2)
						[
							SNew(SListView<TSharedPtr<int32>>)
							.ListItemsSource(&RowIndices)
							.OnGenerateRow(this, &SCollidersMirrorMappingWindow::OnGenerateRowForCollidersMirror)
							.HeaderRow
							(
								SNew(SHeaderRow)
								+ SHeaderRow::Column("RowIndex")
								.DefaultLabel(LOCTEXT("SCollidersMirrorMappingWindow_index_ColumnHeader", "Index"))
								.FillWidth(0.2f)
								+ SHeaderRow::Column("Source")
								.DefaultLabel(LOCTEXT("SCollidersMirrorMappingWindow_Source_ColumnHeader", "Source Collider Bones "))
								.FillWidth(0.4f)
								+ SHeaderRow::Column("Matching")
								.DefaultLabel(LOCTEXT("SCollidersMirrorMappingWindow_Matching_ColumnHeader", "Matching Collider Bones"))
								.FillWidth(0.4f)
							)
						]
						+ SVerticalBox::Slot()
						.AutoHeight()
						.Padding(2)
						[
							SNew(SHorizontalBox)
							+ SHorizontalBox::Slot()
							.AutoWidth()
							[
								SNew(SColorBlock)
								.Color(SelfMatchedColor)
								.Size(FVector2D(14, 14))
							]
							+ SHorizontalBox::Slot()
							.Padding(0.0f, 0.0f, 10.0f, 0.0f)
							.AutoWidth()
							[
								SNew(STextBlock)
								.Text(LOCTEXT("SCollidersMirrorMappingWindow_CollidersMirrorLegend_SelfMatched", " SelfMatched Colliders"))
								.ToolTipText(LOCTEXT("SCollidersMirrorMappingWindow_CollidersMirrorLegend_SelfMatched_Tooltip",
								                     "SelfMatched Colliders是定义的碰撞体中已经形成对称关系的碰撞体，不会进行新的对称碰撞体生成。"))
								.ColorAndOpacity(FSlateColor(SelfMatchedColor))
							]
							+ SHorizontalBox::Slot()
							.AutoWidth()
							[
								SNew(SColorBlock)
								.Color(CustomMatchedColor)
								.Size(FVector2D(14, 14))
							]
							+ SHorizontalBox::Slot()
							.Padding(0.0f, 0.0f, 10.0f, 0.0f)
							.AutoWidth()
							[
								SNew(STextBlock)
								.Text(LOCTEXT("SCollidersMirrorMappingWindow_CollidersMirrorLegend_CustomMatched", " CustomMatched Colliders"))
								.ToolTipText(LOCTEXT("SCollidersMirrorMappingWindow_CollidersMirrorLegend_CustomMatched_Tooltip",
								                     "CustomMatched Colliders是没有已定义的碰撞体可以对应，但是在RefenceSkeleton的Bones中找到了有对称关系的DrivingBone，按照对应关系可以进行对称碰撞体的生成。"))
								.ColorAndOpacity(FSlateColor(CustomMatchedColor))
							]
							+ SHorizontalBox::Slot()
							.AutoWidth()
							[
								SNew(SColorBlock)
								.Color(UnMatchedColor)
								.Size(FVector2D(14, 14))
							]
							+ SHorizontalBox::Slot()
							.Padding(0.0f, 0.0f, 10.0f, 0.0f)
							.AutoWidth()
							[
								SNew(STextBlock)
								.Text(LOCTEXT("SCollidersMirrorMappingWindow_CollidersMirrorLegend_UnMatched", " UnMatched Colliders"))
								.ToolTipText(LOCTEXT("SCollidersMirrorMappingWindow_CollidersMirrorLegend_UnMatched_Tooltip",
								                     "UnMatched Colliders是在RefenceSkeleton的Bones未找到对称关系的DrivingBone，若要进行碰撞体生成，请手动选择对应的DrivingBone。"))
								.ColorAndOpacity(FSlateColor(UnMatchedColor))
							]
						]
						+ SVerticalBox::Slot()
						.AutoHeight()
						.Padding(2, 10, 2, 2)
						[
							SNew(SHorizontalBox)
							+ SHorizontalBox::Slot()
							.Padding(0.0f, 2.0f, 10.0f, 0.0f)
							.AutoWidth()
							[
								SNew(STextBlock)
								.Text(LOCTEXT("SCollidersMirrorMappingWindow_CollidersMirrorLegend_MirrorFace", "Mirror Face"))
								.ToolTipText(LOCTEXT("SCollidersMirrorMappingWindow_CollidersMirrorLegend_MirrorFace_Tooltip",
								                     "选择骨骼摆放的镜像面"))
							]
							+ SHorizontalBox::Slot()
							.Padding(0.0f, 0.0f, 10.0f, 0.0f)
							.AutoWidth()
							[
								SNew(SComboBox<FName>)
								.ToolTipText(LOCTEXT("SCollidersMirrorMappingWindow_MirrorFace_Tooltip", "选择骨骼摆放的镜像面"))
								.OptionsSource(&ResultFaceComboBoxItems)
								.OnGenerateWidget_Lambda([](FName InItem) -> TSharedRef<SWidget>
								{
									return SNew(STextBlock).Text(FText::FromName(InItem));
								})
								.OnSelectionChanged_Lambda([this](FName NewSelection, ESelectInfo::Type SelectInfo)
								{
									*ResultMirrorFace = ResultFaceComboBoxItems.Contains(NewSelection) ? NewSelection : FName("YZ");
								})
								.InitiallySelectedItem(FName(*ResultMirrorFace))
								[
									SNew(STextBlock).Text_Lambda([this]() -> FText
									{
										return FText::FromName(*ResultMirrorFace);
									})
								]
							]
						]
					]
				]
			]
		];
}

TSharedRef<ITableRow> SCollidersMirrorMappingWindow::OnGenerateRowForCollidersMirror(TSharedPtr<int32> InIndex, const TSharedRef<STableViewBase>& Table) const
{
	int32 Index = *InIndex;

	FLinearColor RowColor = UnMatchedColor;
	if ((*ResultColliderMirrorSettings)[Index].ResultColliderBone != FName("NoMirror"))
	{
		if ((*ResultColliderMirrorSettings)[Index].bIsResultSelfMatched)
			RowColor = SelfMatchedColor;
		else
			RowColor = CustomMatchedColor;
	}

	return SNew(STableRow<TSharedPtr<int32>>, Table)
		[
			SNew(SHorizontalBox)
			+ SHorizontalBox::Slot()
			.VAlign(VAlign_Center)
			.HAlign(HAlign_Left)
			.FillWidth(0.2f)
			.Padding(5.0f)
			[
				SNew(STextBlock)
				.Text(FText::AsNumber(Index + 1))
				.ColorAndOpacity(FSlateColor(RowColor))
			]
			+ SHorizontalBox::Slot()
			.VAlign(VAlign_Center)
			.HAlign(HAlign_Left)
			.FillWidth(0.4f)
			.Padding(5.0f)
			[
				SNew(STextBlock)
				.Text(FText::FromName((*SourceColliderBones)[Index]))
				.ColorAndOpacity(FSlateColor(RowColor))
			]
			+ SHorizontalBox::Slot()
			.VAlign(VAlign_Center)
			.HAlign(HAlign_Left)
			.FillWidth(0.4f)
			.Padding(5.0f)
			[
				SNew(SComboBox<FName>)
				.ToolTipText(LOCTEXT("SCollidersMirrorMappingWindow_CollidersMirrorSetting_Tooltip", "NoMirror表示不进行碰撞体的对称，Self_Mirror表示以原骨骼进行碰撞体对称，选择骨骼名称表示按照该对应关系进行碰撞体的对称。"))
				.OptionsSource(&ResultNameComboBoxItems)
				.ForegroundColor(FSlateColor(RowColor))
				.OnGenerateWidget_Lambda([](FName InItem) -> TSharedRef<SWidget>
				{
					return SNew(STextBlock).Text(FText::FromName(InItem));
				})
				.OnSelectionChanged_Lambda([Index,this](FName NewSelection, ESelectInfo::Type SelectInfo)
				{
					(*ResultColliderMirrorSettings)[Index].ResultColliderBone = NewSelection;
				})
				.InitiallySelectedItem(FName((*ResultColliderMirrorSettings)[Index].ResultColliderBone))
				[
					SNew(STextBlock).Text_Lambda([Index,this]() -> FText
					{
						return FText::FromName((*ResultColliderMirrorSettings)[Index].ResultColliderBone);
					})
				]
			]
		];
}

FReply SCollidersMirrorMappingWindow::OnCancel()
{
	if (WidgetWindow.IsValid())
	{
		WidgetWindow.Pin()->RequestDestroyWindow();
	}

	bCanCollidersMirror = false;

	return FReply::Handled();
}

FReply SCollidersMirrorMappingWindow::OnDone()
{
	if (WidgetWindow.IsValid())
	{
		WidgetWindow.Pin()->RequestDestroyWindow();
	}

	bCanCollidersMirror = true;

	return FReply::Handled();
}

#undef LOCTEXT_NAMESPACE
