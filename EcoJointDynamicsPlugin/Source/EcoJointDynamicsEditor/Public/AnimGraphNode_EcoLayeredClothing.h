/**
* Dynapex Character Physics System for Unreal
 * 
 * Created by neilfang, March 2024
 */

#pragma once

#include "CoreMinimal.h"
#include "AnimNode_EcoLayeredClothing.h"
#include "AnimGraphNode_EcoJointDynamics.h"
#include "AnimGraphNode_EcoLayeredClothing.generated.h"

struct FJointPairInformation
{
	int32 RootIndex{-1};
	int32 EndIndex{-1};
};

struct FMeshInformation
{
	int32 EdgeNum{0};
	int32 VertexNum{0};
	int32 TriangleNum{0};
	int32 DihedralBendingConstraintNum{0};
};

struct FClothConstraintInformation
{
	int32 BendingHorizontalConstraintNum{0};
	int32 BendingVerticalConstraintNum{0};
	int32 ShearConstraintNum{0};
	int32 StructuralHorizontalConstraintNum{0};
	int32 StructuralVerticalConstraintNum{0};
	void Init()
	{
		BendingHorizontalConstraintNum = 0;
		BendingVerticalConstraintNum = 0;
		ShearConstraintNum =0;
		StructuralHorizontalConstraintNum =0;
		StructuralVerticalConstraintNum =0;
	}
	int32 Sum() const
	{
		return BendingHorizontalConstraintNum+BendingVerticalConstraintNum+ShearConstraintNum+StructuralHorizontalConstraintNum+StructuralVerticalConstraintNum;
	}
};
/**
 * Anim Graph node for FAnimNode_EcoLayeredClothing
 */
UCLASS()
class ECOJOINTDYNAMICSEDITOR_API UAnimGraphNode_EcoLayeredClothing : public UAnimGraphNode_EcoJointDynamics
{
	GENERATED_BODY()

public:
	UPROPERTY(EditAnywhere, Category=Settings)
	FAnimNode_EcoLayeredClothing Node;

	UPROPERTY(EditAnywhere, Category=Debug)
	bool bDrawAngleLimit{true};

	UPROPERTY(EditAnywhere, Category=Debug)
	bool bDebugDrawStructuralHorizontalConstraints{true};

	UPROPERTY(EditAnywhere, Category=Debug)
	bool bDebugDrawStructuralVerticalConstraints{true};

	UPROPERTY(EditAnywhere, Category=Debug)
	bool bDebugDrawBendingHorizontalConstraints{true};

	UPROPERTY(EditAnywhere, Category=Debug)
	bool bDebugDrawBendingVerticalConstraints{true};

	UPROPERTY(EditAnywhere, Category=Debug)
	bool bDebugDrawShearConstraints{true};

	UPROPERTY(EditAnywhere, Category=Debug)
	bool bDrawLayeredClothingConnections{true};
	
	// 是否开启绘制多层布料的骨骼mesh
	UPROPERTY(EditAnywhere, Category=Debug)
	bool bDebugDrawLayerMesh{false};

	// 指定绘制某一层的骨骼mesh，如果不指定则全部绘制
	UPROPERTY(EditAnywhere, Category=Debug, meta=(EditCondition=bDebugDrawLayerMesh))
	FName DebugLayerName;

	// 指定绘制骨骼mesh的材质，如果不指定则使用默认材质
	UPROPERTY(EditAnywhere, Category=Debug, meta=(EditCondition=bDebugDrawLayerMesh))
	TObjectPtr<UMaterialInterface> DebugLayerMaterial;
	
	virtual FAnimNode_EcoJointDynamics* GetEcoJointDynamicsNode() override { return &Node; }
	virtual FText GetJointStructNames() const override;

	// UEdGraphNode interface
	virtual FText GetControllerDescription() const override;
	virtual FEditorModeID GetEditorMode() const override;
	virtual void ReconstructNode() override;
	virtual void PostEditChangeProperty(FPropertyChangedEvent& PropertyChangedEvent) override;
	// End of UEdGraphNode

	// UAnimGraphNode_SkeletalControlBase interface
	virtual void Draw(FPrimitiveDrawInterface* PDI, USkeletalMeshComponent* SkelMeshComp) const override;
	// End of UAnimGraphNode_SkeletalControlBase interface

	UFUNCTION(Category=PCS, CallInEditor)
	void GeneratePCS();

	/** 重置 PCS 数据 */
	UFUNCTION(Category=PCS, CallInEditor)
	void ResetPCS();

protected:
	// UAnimGraphNode_SkeletalControlBase protected interface
	virtual const FAnimNode_SkeletalControlBase* GetNode() const override { return &Node; }
	virtual void ValidateAnimNodeDuringCompilation(USkeleton* ForSkeleton, FCompilerResultsLog& MessageLog) override;
	// End of UAnimGraphNode_SkeletalControlBase protected interface

	static void DrawConstraint(FPrimitiveDrawInterface* PDI, const FEcoAnimJointMesh& ClothMesh, const TArray<FEcoAnimJointMesh::FEcoClothMeshConstraint>& Constraints,
	                           TSet<const FEcoAnimJoint*>& DrawnJoints);
	void DrawLayerSurface(FPrimitiveDrawInterface* PDI, const FEcoMeshSurface* LayerSurface) const;
	virtual class UEcoJointDynamicsPrefab* CreatePrefab(UPackage* Pkg, FName Name) const override;

	typedef FEcoDistanceConstraint<FAnimNode_EcoLayeredClothing> FDistanceConstraint;
	static void DrawDistanceConstraints_Lines(FPrimitiveDrawInterface* PDI, const TArray<FEcoAnimJointMesh>& AnimMeshes, const TArray<FEcoAnimJointChain>& AnimChains, const TArray<FDistanceConstraint>& DistanceConstraints);
	static bool GetAnimJoint(const TArray<FEcoAnimJointMesh>& AnimMeshes, const TArray<FEcoAnimJointChain>& AnimChains,  int32 MeshIndex, int32 ChainIndex, int32 JointIndex, FEcoAnimJoint& OutJoint);
};

