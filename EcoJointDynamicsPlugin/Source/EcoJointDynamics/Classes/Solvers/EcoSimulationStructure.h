/**
* Dynapex Character Physics System for Unreal
 * 
 * Created by neilfang, May 2024
 */
#pragma once
#include "EcoIntegrator.h"
#include "EcoAnimJoint.h"
#include "EcoJointDynamicsTypes.h"
#include "EcoConstraintRemoval.h"
#include "Constraints/EcoLRAConstraint.h"
#include "Constraints/EcoClothConstraint.h"
#include "Constraints/EcoDistanceConstraint.h"

struct FEcoRibbonsDef;
enum class EDummyBoneAxis : uint8;
struct FAnimNode_EcoJointDynamics;
struct FEcoClothDef;
struct FEcoJointPair;
struct FEcoAnimPhysicsContext;

/** 由 Joint 组成的动力学结构的基类
 * @remarks 单个结构需要由单一的 Parent Bone
 */
struct FEcoSimulationStructureBase
{
	FName Name;
	bool bForceBoneLengthLimit{false};
	float ForceBoneLengthLimitCoefficient{1.0f};

	int32 LODThreshold{INDEX_NONE};
	bool IsLODValid(const FEcoAnimPhysicsContext& PhysicsContext) const; //判断当前结构的LOD是否能够启用物理动画仿真

	bool bRootCollision{false};
	FEcoRotLimits RotLimits;

	// Output Pose
	TArray<FBoneTransform> BoneTransforms;
	EDummyBoneAxis DummyBoneForwardAxis;
	float DummyBoneLength{0};

	void UpdatePoseTransform(TArray<FEcoAnimJoint>& AnimJoints, FComponentSpacePoseContext& Output) const;

	void ApplySimulationResult(TArray<FEcoAnimJoint>& AnimJoints, FComponentSpacePoseContext& Output, float BlendWeight, bool bSort);

	static void RefreshBoneReference(TArray<FEcoAnimJoint>& AnimJoints, const FBoneContainer& RequiredBones);

	static bool CreateAnimJoints(const FEcoJointPair& JointPairDef, FComponentSpacePoseContext& PoseContext, float DummyBoneLength, EDummyBoneAxis DummyBoneForwardAxis,
	                             const TArray<FBoneReference>& ExcludeBones, TArray<FEcoAnimJoint>& OutAnimJoints);

	static void UpdatePhysicsSettings(TArray<FEcoAnimJoint>& AnimJoints, const FEcoPhysicsSettings* NodePhysicsSettings, const FAnimNode_EcoJointDynamics* Node);

	static float UpdateLengthRate(TArray<FEcoAnimJoint>& AnimJoints);
	static void UpdateAbsoluteLengthRate(TArray<FEcoAnimJoint>& AnimJoints, float AbsoluteLength);
};

template <typename Integrator>
struct FEcoSimulationStructure : public FEcoSimulationStructureBase
{
	static void PreSolve(TArray<FEcoAnimJoint>& AnimJoints, const FEcoAnimPhysicsContext& PhysicsContext, FComponentSpacePoseContext& Output, const bool& bUseBodyComponentSpace);
	static void PostSolve(TArray<FEcoAnimJoint>& AnimJoints, const FEcoAnimPhysicsContext& PhysicsContext, const bool& bUseBodyComponentSpace);
	static void UpdateWithoutPhysics(TArray<FEcoAnimJoint>& AnimJoints, const FEcoAnimPhysicsContext& PhysicsContext, const float DeltaTime);
};


/** 由 Joint 组成的链状结构，只具备 Joint 之间的距离约束 */
struct FEcoAnimJointChain : public FEcoSimulationStructure<FEcoEulerIntegrator>
{
	// 结构中的的每个 Joint 的运行时数据 
	TArray<FEcoAnimJoint> AnimJoints;

	// 骨骼链的整体长度
	float TotalLength{0};

	// 初始化内部数据
	void Initialize(const FEcoJointPair& InChainDef, const FEcoRibbonsDef& StructureDef, FComponentSpacePoseContext& PoseContext);
	void UpdatePoseTransform(FComponentSpacePoseContext& Output);
	void UpdatePhysicsSettings(const FAnimNode_EcoJointDynamics* Node);
	void UpdateAbsoluteLengthRate(float AbsoluteLength);

	void PreSolve(const FEcoAnimPhysicsContext& PhysicsContext, FComponentSpacePoseContext& PoseContext, const bool& bUseBodyComponentSpace);
	void SolveSkeletalLimits(FComponentSpacePoseContext& PoseContext);
	void PostSolve(const FEcoAnimPhysicsContext& PhysicsContext, const bool& bUseBodyComponentSpace);
	void UpdateWithoutPhysics(FComponentSpacePoseContext& PoseContext, const FEcoAnimPhysicsContext& PhysicsContext, const float DeltaTime);
	void ApplySimulationResult(FComponentSpacePoseContext& PoseContext, float BlendWeight);
	void RefreshBoneReference(const FBoneContainer& RequiredBones);

	static float GetAbsoluteLength(const TArray<FEcoAnimJointChain>& AnimChains);
};

/** 选用三角形是因为无法保证组成 Quad 的四个 Joints 在同一平面上*/
struct FEcoJointTriangle
{
	FEcoAnimJoint* Vert[3]{nullptr};

	FVector GetEdgeVector(int32 vi, int32 vj) const;
	void GetEdge(int32 ei, FVector& OutStart, FVector& OutEnd) const;
	void GetEdgeJoint(int32 ei, FEcoAnimJoint*& OutStart, FEcoAnimJoint*& OutEnd) const;
	const FVector& GetVertex(int32 ei) const;
	
	// 将函数定义移到头文件中
	bool IsLODValidToEvaluate() const
	{
		for (int32 i = 0; i < 3; i++)
		{
			if (!Vert[i] || !Vert[i]->IsLODValidToEvaluate)
				return false;
		}
		return true;
	}

	/** 计算三角形的外法线 */
	FVector GetOuterNormal() const
	{
		// 假设三角形顶点为顺时针方向, UE的坐标系是左手系
		const FVector BA = Vert[2]->Location - Vert[1]->Location;
		const FVector CB = Vert[0]->Location - Vert[1]->Location;
		return (BA ^ CB).GetSafeNormal();
	}
};

/** 由 Joint 组成的边，具备两个相邻三角形的索引 */
struct FEcoJointEdge
{
	FEcoAnimJoint* StartJoint{nullptr};
	FEcoAnimJoint* EndJoint{nullptr};
	
	// 最多两个相邻三角形的索引
	int32 AdjacentFaceIndices[2]{INDEX_NONE, INDEX_NONE};
	
	bool operator==(const FEcoJointEdge& Other) const
	{
		return (StartJoint == Other.StartJoint && EndJoint == Other.EndJoint) ||
			   (StartJoint == Other.EndJoint && EndJoint == Other.StartJoint);
	}
	
	// 添加相邻三角形
	void AddAdjacentFace(int32 FaceIndex)
	{
		if(AdjacentFaceIndices[0] == INDEX_NONE)
		{
			AdjacentFaceIndices[0] = FaceIndex;
		}
		else if(AdjacentFaceIndices[1] == INDEX_NONE && AdjacentFaceIndices[0] != FaceIndex)
		{
			AdjacentFaceIndices[1] = FaceIndex;
		}
	}
};

/** MeshSurface is a mesh structure consists of joints, edges and face that unifies cloth, ribbon and rope structures. A MeshSurface represents chains if it has no face data*/
struct FEcoMeshSurface
{
	TArray<FEcoAnimJoint*> Vertices;
	TArray<FEcoJointTriangle> Faces;
	TArray<FEcoJointEdge> Edges;

	int32 LODThreshold{INDEX_NONE};

	bool IsLODValid(const int32 SimulationLOD) const
	{
		return LODThreshold == INDEX_NONE || LODThreshold >= SimulationLOD;
	}
};

/** Thickness data for a mesh surface */
struct FEcoMeshSurfaceThicknessData
{
	TArray<float> VertexThickness;
	TArray<float> EdgeThickness;
	TArray<float> FaceThickness;
};


/** 由 Joint 组成的 Mesh 结构，可具备横向、纵向、斜向的距离约束 */
struct FEcoAnimJointMesh : public FEcoSimulationStructure<FEcoEulerIntegrator>
{
	typedef FEcoClothConstraint<FEcoAnimJointMesh> FEcoClothMeshConstraint;

	void Initialize(const FEcoClothDef& InClothDef, FComponentSpacePoseContext& PoseContext, const FEcoClothConstraintPower& ConstraintPowerSettings, const int32 Index);
	void UpdatePhysicsSettings(const FAnimNode_EcoJointDynamics* Node);
	void UpdatePoseTransform(FComponentSpacePoseContext& Output);

	void PreSolve(const FEcoAnimPhysicsContext& PhysicsContext, FComponentSpacePoseContext& Output, const bool& bUseBodyComponentSpace);
	void SolveClothConstraint(FAnimNode_EcoJointDynamics* InNode, const bool bStructuralHorizontalConstraint, const bool bStructuralVerticalConstraint,
	                          const bool bBendingHorizontalConstraint, const bool bBendingVerticalConstraint, const bool bShearConstraint);
	void SolveSkeletalLimits();
	void PostSolve(const FEcoAnimPhysicsContext& PhysicsContext, const bool& bUseBodyComponentSpace);
	void UpdateWithoutPhysics(FComponentSpacePoseContext& Output, const FEcoAnimPhysicsContext& PhysicsContext, const float DeltaTime);
	void ApplySimulationResult(FComponentSpacePoseContext& Output, float BlendWeight);
	void RefreshBoneReference(const FBoneContainer& RequiredBones);

	// 结构中的的每个 Joint 的运行时数据, Table 中每个 element 为一个 chain
	TArray<TArray<FEcoAnimJoint>> AnimJointsTable;

	/** 最长的链条才长度，用来做 AbsoluteLengthRate 插值计算 */
	float MaxChainLength{0};

	// Cloth constrains
	TArray<FEcoClothMeshConstraint> StructuralHorizontalConstraints;
	TArray<FEcoClothMeshConstraint> StructuralVerticalConstraints;
	TArray<FEcoClothMeshConstraint> BendingHorizontalConstraints;
	TArray<FEcoClothMeshConstraint> BendingVerticalConstraints;
	TArray<FEcoClothMeshConstraint> ShearConstraints;

	/** 布料的二面角弯曲约束 */
	TArray<FEcoDihedralBendingConstraint> DihedralBendingConstraints;
	
	void InitializeDihedralBendingConstraints();
	void UpdateDihedralBendingConstraints(const FRuntimeFloatCurve& InPowerCurve, const float InPowerScalar);
	void SolveDihedralBendingConstraints();

	int32 GetJointChainCount() const;
	void CreateConstraint_Vertical(TArray<FEcoClothMeshConstraint>& OutConstraints, bool bCollision, bool bBending);
	void CreateConstraint_Horizontal(TArray<FEcoClothMeshConstraint>& OutConstraints, bool bJointLoop, bool bCollision, bool bBending, const FEcoConstraintRemovalSet& RemovalSet);
	void CreateConstraint_Shear(TArray<FEcoClothMeshConstraint>& OutConstraints, bool bJointLoop, bool bCollision, const FEcoConstraintRemovalSet& RemovalSet);
	void UpdateConstraints(const FEcoClothDef& InClothDef, const FEcoClothConstraintPower& ConstraintPowerSettings);

	void UpdateConstraintPower(TArray<FEcoClothMeshConstraint>& InConstraints, const FRuntimeFloatCurve& InStretchPower,
	                           const FRuntimeFloatCurve& InShrinkPower, const float StretchPowerScalar, const float ShrinkPowerScalar) const
	{
		for (auto& Constraint : InConstraints)
			Constraint.UpdateConstraintPower(this, InStretchPower, InShrinkPower, StretchPowerScalar, ShrinkPowerScalar);
	}

	void UpdateAbsoluteLengthRate(float AbsoluteLength);

	FEcoAnimJoint* GetJoint(const FEcoMeshJointIndex& Index)
	{
		auto& Chain = AnimJointsTable[Index.ChainIndex];
		return &Chain[Index.JointIndex];
	}

	const FEcoAnimJoint* GetJoint_Const(const FEcoMeshJointIndex& Index) const
	{
		auto& Chain = AnimJointsTable[Index.ChainIndex];
		return &Chain[Index.JointIndex];
	}

	template <typename T>
	void SolveConstraints(TArray<T>& InConstraints)
	{
		for (auto& Constraint : InConstraints)
			Constraint.Solve(this);
	}

	static float GetAbsoluteLength(const TArray<FEcoAnimJointMesh>& AnimMeshes);

	int32 ClothDefIndex{0}; //该mesh对应的Clothdef的索引

	// 布料的表面点、线、面网格数据
	FEcoMeshSurface ClothSurface;

	// 初始化表面数据
    void InitializeSurfaceData(const FEcoClothDef& InClothDef);
	void InitializeSurfaceDataInternal(TArray<FEcoAnimJoint>& JointChainA, TArray<FEcoAnimJoint>& JointChainB);
};

/** 由点状骨骼定义的一组 Particles */
struct FEcoAnimParticles : public FEcoSimulationStructure<FEcoEulerIntegrator>
{
	typedef FEcoDistanceConstraint<FEcoAnimParticles> FEcoParticleDistanceConstraint;
	typedef FEcoRopeLRAConstraint<FEcoAnimParticles> FEcoParticleLRAConstraint;

	TArray<FEcoAnimJoint> AnimJoints;

	/** 每个 Segment 对应一个 Distance Constant */
	TArray<FEcoParticleDistanceConstraint> LocalConstraints;

	/** Long Range Attachment Constraints */
	FEcoAnimJoint StartJoint;
	FEcoAnimJoint EndJoint;
	TArray<FEcoParticleLRAConstraint> LRAConstraints;

	/** 是否开启末端旋转 */
	bool bEnableLastBoneRotate{true};
	
	bool IsEmpty() const { return AnimJoints.IsEmpty(); }
	void Initialize(const FEcoRopeDef& RopeDef, FComponentSpacePoseContext& PoseContext);
	void InitializeLRAConstraints(const FEcoRopeDef& RopeDef, FComponentSpacePoseContext& PoseContext);
	void UpdatePhysicsSettings(const FAnimNode_EcoJointDynamics* Node);
	void RefreshBoneReference(const FBoneContainer& RequiredBones);
	void UpdatePoseTransform(FComponentSpacePoseContext& Output);

	void UpdateWithoutPhysics(FComponentSpacePoseContext& Output, const FEcoAnimPhysicsContext& PhysicsContext, const float DeltaTime);
	void PreSolve(const FEcoAnimPhysicsContext& PhysicsContext, FComponentSpacePoseContext& Output, const bool& bUseBodyComponentSpace);
	void SolveConstraint(const bool& bEnableLRA);
	void SolveBendingConstraint(float& BendingStiffness);
	void PostSolve(const FEcoAnimPhysicsContext& PhysicsContext, const bool& bUseBodyComponentSpace);
	void ApplySimulationResult(FComponentSpacePoseContext& Output, float BlendWeight);
	void ApplySimulationResultToLastBone(FComponentSpacePoseContext& Output, float BlendWeight);

	bool HasLRAConstraints() const { return LRAConstraints.Num() > 0; }
	TArray<float> GetLengthFromEnd(const FVector& EndLocation) const;

	FEcoAnimJoint* GetJoint(const int32 MeshIndex, const int32 ChainIndex, const int32 JointIndex)
	{
		return &AnimJoints[JointIndex];
	}
	
	FEcoAnimJoint* GetJoint(const int32 JointIndex)
	{
		return &AnimJoints[JointIndex];
	}

	bool IsJointIndexValid(const int32 MeshIndex, const int32 ChainIndex, const int32 JointIndex) const
	{
		return AnimJoints.IsValidIndex(JointIndex);
	}
};

struct StrandSegmentState
{
	float LengthRest{0};
	// rotation of the segment represented by a quaternion
	FQuat Rotation;
	// rotation at previous time step
	FQuat RotationPrev;
	// angular velocity
	FVector AngularVelocity;
	// torque
	FVector Torque;
	// inverse mass
	float InverseMass;
	// inertia is a diagonal matrix with size 3*3, temply use one variable to represent it
	float Inertia = 1.0f;
};

struct StrandTripletState
{
	// darboux vector
	FVector DarbouxVector;
	// darboux vector at rest
	FVector DarbouxVectorRest;
};

struct StrandPointState // sates needed in rod hair or loacal-global shape comstraint hair
{
	FTransform RestLocalPose; // pose in parent bone's space at rest
	FTransform RestPoseCS; // pose in component space at rest
	FTransform GlobalPose; // pose in world space
	FVector GlobalLocation; // location in world space
	FVector GlobalVelocity{FVector::ZeroVector}; // velocity in world space
	FVector GlobalPrevLocation{FVector::ZeroVector}; // location in world space at previous time step
};

/** 发丝结构体的基类*/
struct FEcoAnimJointStrandBase : public FEcoSimulationStructure<FEcoEulerIntegrator>
{
	TArray<FEcoAnimJoint> AnimJoints;
	TArray<StrandPointState> PointStates;
	FEcoStrandPhysicsParams PhysicsParams;

	// only AnimJoints are initialized here
	void Initialize(const FEcoHairGroupDef& InHairGroupDef, const int32 StrandRootBoneId, FComponentSpacePoseContext& PoseContext);

	void StepAsRigidMotion(FComponentSpacePoseContext& Output);
	void TransferPointStatesToAnimJoints(FComponentSpacePoseContext& Output);
	void TransferAnimJointsToPointStates(FComponentSpacePoseContext& Output);

	void UpdatePhysicsSettings(const FAnimNode_EcoJointDynamics* Node);
	void UpdatePoseTransform(FComponentSpacePoseContext& Output);
	void UpdateWithoutPhysics(FComponentSpacePoseContext& Output, const FEcoAnimPhysicsContext& PhysicsContext, const float DeltaTime);
	void ApplySimulationResult(FComponentSpacePoseContext& Output, float BlendWeight);
	void UpdateAbsoluteLengthRate(float AbsoluteLength);

	bool IsEmpty() const { return AnimJoints.IsEmpty(); }
};

/** 基于Cosserat Rod 模型的发丝结构*/
struct FEcoAnimJointStrandCosseratRod : public FEcoAnimJointStrandBase
{
	TArray<StrandSegmentState> SegmentStates;
	TArray<StrandTripletState> TripletStates;

	void Initialize(const FEcoHairGroupDef& InHairGroupDef, const int32 StrandRootBoneId, FComponentSpacePoseContext& PoseContext);

	void Step(int32 ConstraintIteration, const FEcoAnimPhysicsContext& PhysicsContext, FComponentSpacePoseContext& Output);
	void SubStep(int32 TotalSubsteps, int32 CurrentSubstep, const FEcoAnimPhysicsContext& PhysicsContext, FComponentSpacePoseContext& Output);
	void PreSolve(int32 TotalSubsteps, int32 CurrentSubstep, const FEcoAnimPhysicsContext& PhysicsContext, FComponentSpacePoseContext& Output);
	void SolveStretchAndShearConstraint(); // 拉伸、剪切约束
	void SolveBendingAndTwistConstraint(); // 弯曲、扭转约束
	void SolveConstraint();
	void PostSolve(int32 TotalSubsteps, int32 CurrentSubstep, const FEcoAnimPhysicsContext& PhysicsContext);
};

/** 基于Local-Global形状约束的发丝结构*/
struct FEcoAnimJointStrandLocalGlobal : public FEcoAnimJointStrandBase
{
	void Initialize(const FEcoHairGroupDef& InHairGroupDef, const int32 StrandRootBoneId, FComponentSpacePoseContext& PoseContext);

	void PreSolve(FComponentSpacePoseContext& Output, const FEcoAnimPhysicsContext& PhysicsContext);
	void PostSolve(FComponentSpacePoseContext& Output, const FEcoAnimPhysicsContext& PhysicsContext);
	void SolveLocalConstraint(FComponentSpacePoseContext& Output);
	void SolveGlobalConstraint(FComponentSpacePoseContext& Output);
	void SolveStretchConstraint();
	void Step(int32 ConstraintIteration, const FEcoAnimPhysicsContext& PhysicsContext, FComponentSpacePoseContext& Output);
};
