// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Framework/Commands/Commands.h"
#include "ToolbarRegisterStyle.h"

class FToolbarRegisterCommands : public TCommands<FToolbarRegisterCommands>
{
public:

	FToolbarRegisterCommands()
		: TCommands<FToolbarRegisterCommands>(TEXT("ToolbarRegister"), NSLOCTEXT("Contexts", "ToolbarRegister", "ToolbarRegister Plugin"), NAME_None, FToolbarRegisterStyle::GetStyleSetName())
	{
	}

	/** Get the singleton instance of this set of commands. */
	static FToolbarRegisterCommands* Get_Internal()
	{
		return &*(Instance.Pin());
	}

	// TCommands<> interface
	virtual void RegisterCommands() override;
	
public:
	TSharedPtr< FUICommandInfo > PluginAction;
};
