// Copyright Epic Games, Inc. All Rights Reserved.

using UnrealBuildTool;

public class PreviewMap : ModuleRules
{
	public PreviewMap(ReadOnlyTargetRules Target) : base(Target)
	{
		PCHUsage = ModuleRules.PCHUsageMode.UseExplicitOrSharedPCHs;
		
		PublicIncludePaths.AddRange(
			new string[] {
				// ... add public include paths required here ...
			}
			);
				
		
		PrivateIncludePaths.AddRange(
			new string[] {
				// ... add other private include paths required here ...
			}
			);
			
		
		PublicDependencyModuleNames.AddRange(
			new string[]
			{
				"Core",
				"PreviewMapProcess",
				// ... add other public dependencies that you statically link with here ...
			}
			);
			
		
		PrivateDependencyModuleNames.AddRange(
			new string[]
			{
				"Core",
				"CoreUObject",
				"Engine",
				"UnrealEd",
				"Slate",
				"SlateCore",
				"EditorStyle",
				"LevelEditor",
				"PropertyEditor",
				"ContentBrowser",
				"AssetTools",
				"EditorFramework",
				"InputCore",
				"AdvancedPreviewScene",
				"ToolMenus",
				"Projects",
				"AssetDefinition",
				"AssetRegistry",
				"UMG",
				"Wardrobe",
				"DeveloperToolSettings",
				// ... add private dependencies that you statically link with here ...	
			}
			);
		
		
		DynamicallyLoadedModuleNames.AddRange(
			new string[]
			{
				// ... add any modules that your module loads dynamically here ...
			}
			);
	}
}
