<!-- This file was generated by UnrealBuildTool.ProjectFileGenerator.CreateRulesAssemblyProject() -->
<Project Sdk="Microsoft.NET.Sdk">
  <Import Project="..\..\..\..\Source\Programs\Shared\UnrealEngine.csproj.props" />
  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <Configurations>Debug;Release;Development</Configurations>
    <DefineConstants>$(DefineConstants);WITH_FORWARDED_MODULE_RULES_CTOR;WITH_FORWARDED_TARGET_RULES_CTOR;UE_4_17_OR_LATER;UE_4_18_OR_LATER;UE_4_19_OR_LATER;UE_4_20_OR_LATER;UE_4_21_OR_LATER;UE_4_22_OR_LATER;UE_4_23_OR_LATER;UE_4_24_OR_LATER;UE_4_25_OR_LATER;UE_4_26_OR_LATER;UE_4_27_OR_LATER;UE_4_28_OR_LATER;UE_4_29_OR_LATER;UE_4_30_OR_LATER;UE_5_0_OR_LATER;UE_5_1_OR_LATER;UE_5_2_OR_LATER;UE_5_3_OR_LATER;UE_5_4_OR_LATER</DefineConstants>
  </PropertyGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\..\..\Source\Programs\Shared\EpicGames.Build\EpicGames.Build.csproj"><Private>false</Private></ProjectReference>
    <ProjectReference Include="..\..\..\..\Source\Programs\UnrealBuildTool\UnrealBuildTool.csproj"><Private>false</Private></ProjectReference>
  </ItemGroup>
  <ItemGroup>
  <Compile Include="..\..\..\..\Platforms\VisionOS\Plugins\Compression\OodleNetwork\Source\OodleNetworkHandlerComponent_VisionOS.Build.cs"><Link>Platforms\VisionOS\Plugins\Compression\OodleNetwork\Source\OodleNetworkHandlerComponent_VisionOS.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Platforms\VisionOS\Plugins\Runtime\OpenXRVisionOS\Source\OXRVisionOSSettings\OXRVisionOSSettings.build.cs"><Link>Platforms\VisionOS\Plugins\Runtime\OpenXRVisionOS\Source\OXRVisionOSSettings\OXRVisionOSSettings.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Platforms\VisionOS\Plugins\Runtime\OpenXRVisionOS\Source\OXRVisionOS\OXRVisionOS.Build.cs"><Link>Platforms\VisionOS\Plugins\Runtime\OpenXRVisionOS\Source\OXRVisionOS\OXRVisionOS.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Platforms\VisionOS\Source\Developer\VisionOSPlatformEditor\VisionOSPlatformEditor.Build.cs"><Link>Platforms\VisionOS\Source\Developer\VisionOSPlatformEditor\VisionOSPlatformEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Platforms\VisionOS\Source\Developer\VisionOSTargetPlatform\VisionOSTargetPlatform.Build.cs"><Link>Platforms\VisionOS\Source\Developer\VisionOSTargetPlatform\VisionOSTargetPlatform.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Platforms\VisionOS\Source\Runtime\Launch\Launch_VisionOS.Build.cs"><Link>Platforms\VisionOS\Source\Runtime\Launch\Launch_VisionOS.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Platforms\VisionOS\Source\Runtime\OodleDataCompression\OodleDataCompression_VisionOS.Build.cs"><Link>Platforms\VisionOS\Source\Runtime\OodleDataCompression\OodleDataCompression_VisionOS.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Platforms\VisionOS\Source\Runtime\VisionOSRuntimeSettings\VisionOSRuntimeSettings.Build.cs"><Link>Platforms\VisionOS\Source\Runtime\VisionOSRuntimeSettings\VisionOSRuntimeSettings.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\2D\Paper2D\Source\Paper2DEditor\Paper2DEditor.Build.cs"><Link>Plugins\2D\Paper2D\Source\Paper2DEditor\Paper2DEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\2D\Paper2D\Source\Paper2D\Paper2D.Build.cs"><Link>Plugins\2D\Paper2D\Source\Paper2D\Paper2D.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\2D\Paper2D\Source\PaperSpriteSheetImporter\PaperSpriteSheetImporter.Build.cs"><Link>Plugins\2D\Paper2D\Source\PaperSpriteSheetImporter\PaperSpriteSheetImporter.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\2D\Paper2D\Source\PaperTiledImporter\PaperTiledImporter.Build.cs"><Link>Plugins\2D\Paper2D\Source\PaperTiledImporter\PaperTiledImporter.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\2D\Paper2D\Source\SmartSnapping\SmartSnapping.Build.cs"><Link>Plugins\2D\Paper2D\Source\SmartSnapping\SmartSnapping.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\2D\Paper2D\Source\SpriterImporter\SpriterImporter.Build.cs"><Link>Plugins\2D\Paper2D\Source\SpriterImporter\SpriterImporter.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\AI\AISupport\Source\AISupportModule\AISupportModule.Build.cs"><Link>Plugins\AI\AISupport\Source\AISupportModule\AISupportModule.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\AI\EnvironmentQueryEditor\Source\EnvironmentQueryEditor\EnvironmentQueryEditor.Build.cs"><Link>Plugins\AI\EnvironmentQueryEditor\Source\EnvironmentQueryEditor\EnvironmentQueryEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\AI\HTNPlanner\Source\HTNPlanner\HTNPlanner.Build.cs"><Link>Plugins\AI\HTNPlanner\Source\HTNPlanner\HTNPlanner.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\AI\HTNPlanner\Source\HTNTestSuite\HTNTestSuite.Build.cs"><Link>Plugins\AI\HTNPlanner\Source\HTNTestSuite\HTNTestSuite.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\AI\MLAdapter\Source\MLAdapterTestSuite\MLAdapterTestSuite.Build.cs"><Link>Plugins\AI\MLAdapter\Source\MLAdapterTestSuite\MLAdapterTestSuite.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\AI\MLAdapter\Source\MLAdapter\MLAdapter.Build.cs"><Link>Plugins\AI\MLAdapter\Source\MLAdapter\MLAdapter.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\AI\MassAI\Source\MassAIBehavior\MassAIBehavior.Build.cs"><Link>Plugins\AI\MassAI\Source\MassAIBehavior\MassAIBehavior.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\AI\MassAI\Source\MassAIDebug\MassAIDebug.Build.cs"><Link>Plugins\AI\MassAI\Source\MassAIDebug\MassAIDebug.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\AI\MassAI\Source\MassAIReplication\MassAIReplication.Build.cs"><Link>Plugins\AI\MassAI\Source\MassAIReplication\MassAIReplication.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\AI\MassAI\Source\MassAITestSuite\MassAITestSuite.Build.cs"><Link>Plugins\AI\MassAI\Source\MassAITestSuite\MassAITestSuite.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\AI\MassAI\Source\MassNavigationEditor\MassNavigationEditor.Build.cs"><Link>Plugins\AI\MassAI\Source\MassNavigationEditor\MassNavigationEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\AI\MassAI\Source\MassNavigation\MassNavigation.Build.cs"><Link>Plugins\AI\MassAI\Source\MassNavigation\MassNavigation.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\AI\MassAI\Source\MassZoneGraphNavigation\MassZoneGraphNavigation.Build.cs"><Link>Plugins\AI\MassAI\Source\MassZoneGraphNavigation\MassZoneGraphNavigation.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\AI\MassCrowd\Source\MassCrowd\MassCrowd.Build.cs"><Link>Plugins\AI\MassCrowd\Source\MassCrowd\MassCrowd.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Accessory\Source\AccessoryEditor\AccessoryEditor.Build.cs"><Link>Plugins\Accessory\Source\AccessoryEditor\AccessoryEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Accessory\Source\Accessory\Accessory.Build.cs"><Link>Plugins\Accessory\Source\Accessory\Accessory.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Animation\ACLPlugin\Source\ACLPluginEditor\ACLPluginEditor.Build.cs"><Link>Plugins\Animation\ACLPlugin\Source\ACLPluginEditor\ACLPluginEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Animation\ACLPlugin\Source\ACLPlugin\ACLPlugin.Build.cs"><Link>Plugins\Animation\ACLPlugin\Source\ACLPlugin\ACLPlugin.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Animation\AnimationData\Source\AnimationData\AnimationData.build.cs"><Link>Plugins\Animation\AnimationData\Source\AnimationData\AnimationData.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Animation\AnimationLocomotionLibrary\Source\Editor\AnimationLocomotionLibraryEditor.Build.cs"><Link>Plugins\Animation\AnimationLocomotionLibrary\Source\Editor\AnimationLocomotionLibraryEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Animation\AnimationLocomotionLibrary\Source\Runtime\AnimationLocomotionLibraryRuntime.Build.cs"><Link>Plugins\Animation\AnimationLocomotionLibrary\Source\Runtime\AnimationLocomotionLibraryRuntime.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Animation\AnimationModifierLibrary\Source\AnimationModifierLibrary\AnimationModifierLibrary.Build.cs"><Link>Plugins\Animation\AnimationModifierLibrary\Source\AnimationModifierLibrary\AnimationModifierLibrary.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Animation\AnimationWarping\Source\Editor\AnimationWarpingEditor.Build.cs"><Link>Plugins\Animation\AnimationWarping\Source\Editor\AnimationWarpingEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Animation\AnimationWarping\Source\Runtime\AnimationWarpingRuntime.Build.cs"><Link>Plugins\Animation\AnimationWarping\Source\Runtime\AnimationWarpingRuntime.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Animation\BlendSpaceMotionAnalysis\Source\BlendSpaceMotionAnalysis\BlendSpaceMotionAnalysis.Build.cs"><Link>Plugins\Animation\BlendSpaceMotionAnalysis\Source\BlendSpaceMotionAnalysis\BlendSpaceMotionAnalysis.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Animation\BlendStack\Source\Editor\BlendStackEditor.Build.cs"><Link>Plugins\Animation\BlendStack\Source\Editor\BlendStackEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Animation\BlendStack\Source\Runtime\BlendStack.Build.cs"><Link>Plugins\Animation\BlendStack\Source\Runtime\BlendStack.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Animation\ControlRigSpline\Source\ControlRigSpline\ControlRigSpline.build.cs"><Link>Plugins\Animation\ControlRigSpline\Source\ControlRigSpline\ControlRigSpline.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Animation\ControlRig\Source\ControlRigDeveloper\ControlRigDeveloper.Build.cs"><Link>Plugins\Animation\ControlRig\Source\ControlRigDeveloper\ControlRigDeveloper.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Animation\ControlRig\Source\ControlRigEditor\ControlRigEditor.Build.cs"><Link>Plugins\Animation\ControlRig\Source\ControlRigEditor\ControlRigEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Animation\ControlRig\Source\ControlRig\ControlRig.Build.cs"><Link>Plugins\Animation\ControlRig\Source\ControlRig\ControlRig.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Animation\DeformerGraph\Source\OptimusCore\OptimusCore.Build.cs"><Link>Plugins\Animation\DeformerGraph\Source\OptimusCore\OptimusCore.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Animation\DeformerGraph\Source\OptimusDeveloper\OptimusDeveloper.Build.cs"><Link>Plugins\Animation\DeformerGraph\Source\OptimusDeveloper\OptimusDeveloper.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Animation\DeformerGraph\Source\OptimusEditor\OptimusEditor.Build.cs"><Link>Plugins\Animation\DeformerGraph\Source\OptimusEditor\OptimusEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Animation\DeformerGraph\Source\OptimusSettings\OptimusSettings.Build.cs"><Link>Plugins\Animation\DeformerGraph\Source\OptimusSettings\OptimusSettings.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Animation\GameplayInsights\Source\GameplayInsightsEditor\GameplayInsightsEditor.Build.cs"><Link>Plugins\Animation\GameplayInsights\Source\GameplayInsightsEditor\GameplayInsightsEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Animation\GameplayInsights\Source\GameplayInsights\GameplayInsights.Build.cs"><Link>Plugins\Animation\GameplayInsights\Source\GameplayInsights\GameplayInsights.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Animation\GameplayInsights\Source\RewindDebuggerVLog\RewindDebuggerVLog.Build.cs"><Link>Plugins\Animation\GameplayInsights\Source\RewindDebuggerVLog\RewindDebuggerVLog.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Animation\GameplayInsights\Source\RewindDebugger\RewindDebugger.Build.cs"><Link>Plugins\Animation\GameplayInsights\Source\RewindDebugger\RewindDebugger.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Animation\IKRig\Source\IKRigDeveloper\IKRigDeveloper.Build.cs"><Link>Plugins\Animation\IKRig\Source\IKRigDeveloper\IKRigDeveloper.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Animation\IKRig\Source\IKRigEditor\IKRigEditor.Build.cs"><Link>Plugins\Animation\IKRig\Source\IKRigEditor\IKRigEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Animation\IKRig\Source\IKRig\IKRig.Build.cs"><Link>Plugins\Animation\IKRig\Source\IKRig\IKRig.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Animation\LiveLinkCurveDebugUI\Source\LiveLinkCurveDebugUI\LiveLinkCurveDebugUI.Build.cs"><Link>Plugins\Animation\LiveLinkCurveDebugUI\Source\LiveLinkCurveDebugUI\LiveLinkCurveDebugUI.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Animation\LiveLinkHub\Source\LiveLinkHubEditor\LiveLinkHubEditor.Build.cs"><Link>Plugins\Animation\LiveLinkHub\Source\LiveLinkHubEditor\LiveLinkHubEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Animation\LiveLinkHub\Source\LiveLinkHubMessaging\LiveLinkHubMessaging.build.cs"><Link>Plugins\Animation\LiveLinkHub\Source\LiveLinkHubMessaging\LiveLinkHubMessaging.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Animation\LiveLinkHub\Source\LiveLinkHub\LiveLinkHub.Build.cs"><Link>Plugins\Animation\LiveLinkHub\Source\LiveLinkHub\LiveLinkHub.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Animation\LiveLink\Source\LiveLinkComponents\LiveLinkComponents.Build.cs"><Link>Plugins\Animation\LiveLink\Source\LiveLinkComponents\LiveLinkComponents.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Animation\LiveLink\Source\LiveLinkEditor\LiveLinkEditor.Build.cs"><Link>Plugins\Animation\LiveLink\Source\LiveLinkEditor\LiveLinkEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Animation\LiveLink\Source\LiveLinkGraphNode\LiveLinkGraphNode.Build.cs"><Link>Plugins\Animation\LiveLink\Source\LiveLinkGraphNode\LiveLinkGraphNode.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Animation\LiveLink\Source\LiveLinkMovieScene\LiveLinkMovieScene.Build.cs"><Link>Plugins\Animation\LiveLink\Source\LiveLinkMovieScene\LiveLinkMovieScene.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Animation\LiveLink\Source\LiveLinkMultiUser\LiveLinkMultiUser.Build.cs"><Link>Plugins\Animation\LiveLink\Source\LiveLinkMultiUser\LiveLinkMultiUser.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Animation\LiveLink\Source\LiveLinkSequencer\LiveLinkSequencer.Build.cs"><Link>Plugins\Animation\LiveLink\Source\LiveLinkSequencer\LiveLinkSequencer.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Animation\LiveLink\Source\LiveLink\LiveLink.Build.cs"><Link>Plugins\Animation\LiveLink\Source\LiveLink\LiveLink.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Animation\MLDeformer\ChaosClothGenerator\Source\ChaosClothGenerator\ChaosClothGenerator.build.cs"><Link>Plugins\Animation\MLDeformer\ChaosClothGenerator\Source\ChaosClothGenerator\ChaosClothGenerator.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Animation\MLDeformer\MLDeformerFramework\Source\MLDeformerFrameworkEditor\MLDeformerFrameworkEditor.Build.cs"><Link>Plugins\Animation\MLDeformer\MLDeformerFramework\Source\MLDeformerFrameworkEditor\MLDeformerFrameworkEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Animation\MLDeformer\MLDeformerFramework\Source\MLDeformerFramework\MLDeformerFramework.Build.cs"><Link>Plugins\Animation\MLDeformer\MLDeformerFramework\Source\MLDeformerFramework\MLDeformerFramework.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Animation\MLDeformer\NearestNeighborModel\Source\NearestNeighborModelEditor\NearestNeighborModelEditor.Build.cs"><Link>Plugins\Animation\MLDeformer\NearestNeighborModel\Source\NearestNeighborModelEditor\NearestNeighborModelEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Animation\MLDeformer\NearestNeighborModel\Source\NearestNeighborModel\NearestNeighborModel.Build.cs"><Link>Plugins\Animation\MLDeformer\NearestNeighborModel\Source\NearestNeighborModel\NearestNeighborModel.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Animation\MLDeformer\NeuralMorphModel\Source\NeuralMorphModelEditor\NeuralMorphModelEditor.Build.cs"><Link>Plugins\Animation\MLDeformer\NeuralMorphModel\Source\NeuralMorphModelEditor\NeuralMorphModelEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Animation\MLDeformer\NeuralMorphModel\Source\NeuralMorphModel\NeuralMorphModel.Build.cs"><Link>Plugins\Animation\MLDeformer\NeuralMorphModel\Source\NeuralMorphModel\NeuralMorphModel.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Animation\MLDeformer\VertexDeltaModel\Source\VertexDeltaModelEditor\VertexDeltaModelEditor.Build.cs"><Link>Plugins\Animation\MLDeformer\VertexDeltaModel\Source\VertexDeltaModelEditor\VertexDeltaModelEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Animation\MLDeformer\VertexDeltaModel\Source\VertexDeltaModel\VertexDeltaModel.Build.cs"><Link>Plugins\Animation\MLDeformer\VertexDeltaModel\Source\VertexDeltaModel\VertexDeltaModel.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Animation\MotionWarping\Source\MotionWarping\MotionWarping.Build.cs"><Link>Plugins\Animation\MotionWarping\Source\MotionWarping\MotionWarping.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Animation\PerformanceCaptureCore\Source\PerformanceCaptureCore\PerformanceCaptureCore.Build.cs"><Link>Plugins\Animation\PerformanceCaptureCore\Source\PerformanceCaptureCore\PerformanceCaptureCore.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Animation\PoseSearch\Source\Editor\PoseSearchEditor.Build.cs"><Link>Plugins\Animation\PoseSearch\Source\Editor\PoseSearchEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Animation\PoseSearch\Source\Runtime\PoseSearch.Build.cs"><Link>Plugins\Animation\PoseSearch\Source\Runtime\PoseSearch.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Animation\RigLogic\Source\RigLogicDeveloper\RigLogicDeveloper.Build.cs"><Link>Plugins\Animation\RigLogic\Source\RigLogicDeveloper\RigLogicDeveloper.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Animation\RigLogic\Source\RigLogicEditor\RigLogicEditor.Build.cs"><Link>Plugins\Animation\RigLogic\Source\RigLogicEditor\RigLogicEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Animation\RigLogic\Source\RigLogicLibTest\RigLogicLibTest.Build.cs"><Link>Plugins\Animation\RigLogic\Source\RigLogicLibTest\RigLogicLibTest.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Animation\RigLogic\Source\RigLogicLib\RigLogicLib.Build.cs"><Link>Plugins\Animation\RigLogic\Source\RigLogicLib\RigLogicLib.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Animation\RigLogic\Source\RigLogicModule\RigLogicModule.Build.cs"><Link>Plugins\Animation\RigLogic\Source\RigLogicModule\RigLogicModule.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\AssetPipeline\Source\AssetPipelineEditor\AssetPipelineEditor.Build.cs"><Link>Plugins\AssetPipeline\Source\AssetPipelineEditor\AssetPipelineEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\AssetPipeline\Source\AssetPipeline\AssetPipeline.Build.cs"><Link>Plugins\AssetPipeline\Source\AssetPipeline\AssetPipeline.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\AudioGameplayVolume\Source\AudioGameplayVolumeEditor\AudioGameplayVolumeEditor.Build.cs"><Link>Plugins\AudioGameplayVolume\Source\AudioGameplayVolumeEditor\AudioGameplayVolumeEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\AudioGameplayVolume\Source\AudioGameplayVolume\AudioGameplayVolume.Build.cs"><Link>Plugins\AudioGameplayVolume\Source\AudioGameplayVolume\AudioGameplayVolume.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\AudioGameplay\Source\AudioGameplayTests\AudioGameplayTests.build.cs"><Link>Plugins\AudioGameplay\Source\AudioGameplayTests\AudioGameplayTests.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\AudioGameplay\Source\AudioGameplay\AudioGameplay.Build.cs"><Link>Plugins\AudioGameplay\Source\AudioGameplay\AudioGameplay.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\AudioInsights\Source\AudioInsights\AudioInsights.Build.cs"><Link>Plugins\AudioInsights\Source\AudioInsights\AudioInsights.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\AvatarUtils\Source\FbxUtils\FbxUtilsModule.Build.cs"><Link>Plugins\AvatarUtils\Source\FbxUtils\FbxUtilsModule.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\AvatarUtils\Source\SettingSorter\SettingSorter.Build.cs"><Link>Plugins\AvatarUtils\Source\SettingSorter\SettingSorter.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\BlueprintFileUtils\Source\BlueprintFileUtils\BlueprintFileUtils.Build.cs"><Link>Plugins\BlueprintFileUtils\Source\BlueprintFileUtils\BlueprintFileUtils.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\BlueprintMergeTexture\Source\BlueprintMergeTexture\BlueprintMergeTexture.Build.cs"><Link>Plugins\BlueprintMergeTexture\Source\BlueprintMergeTexture\BlueprintMergeTexture.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\BonesChainPhysics\Source\BonesChainPhysics\BonesChainPhysics.Build.cs"><Link>Plugins\BonesChainPhysics\Source\BonesChainPhysics\BonesChainPhysics.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\BonesChainPhysics\Source\KawaiiPhysicsEd\KawaiiPhysicsEd.Build.cs"><Link>Plugins\BonesChainPhysics\Source\KawaiiPhysicsEd\KawaiiPhysicsEd.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\BonesChainPhysics\Source\KawaiiPhysics\KawaiiPhysics.Build.cs"><Link>Plugins\BonesChainPhysics\Source\KawaiiPhysics\KawaiiPhysics.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\BonesChainPhysics\Source\SPCRJointDynamicsEd\SPCRJointDynamicsEd.Build.cs"><Link>Plugins\BonesChainPhysics\Source\SPCRJointDynamicsEd\SPCRJointDynamicsEd.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\BonesChainPhysics\Source\SPCRJointDynamics\SPCRJointDynamics.Build.cs"><Link>Plugins\BonesChainPhysics\Source\SPCRJointDynamics\SPCRJointDynamics.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Cameras\CameraShakePreviewer\Source\CameraShakePreviewer\CameraShakePreviewer.Build.cs"><Link>Plugins\Cameras\CameraShakePreviewer\Source\CameraShakePreviewer\CameraShakePreviewer.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Cameras\GameplayCameras\Source\GameplayCamerasEditor\GameplayCamerasEditor.Build.cs"><Link>Plugins\Cameras\GameplayCameras\Source\GameplayCamerasEditor\GameplayCamerasEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Cameras\GameplayCameras\Source\GameplayCameras\GameplayCameras.Build.cs"><Link>Plugins\Cameras\GameplayCameras\Source\GameplayCameras\GameplayCameras.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\ChaosClothAssetEditor\Source\ChaosClothAssetDataflowNodes\ChaosClothAssetDataflowNodes.Build.cs"><Link>Plugins\ChaosClothAssetEditor\Source\ChaosClothAssetDataflowNodes\ChaosClothAssetDataflowNodes.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\ChaosClothAssetEditor\Source\ChaosClothAssetEditorTools\ChaosClothAssetEditorTools.Build.cs"><Link>Plugins\ChaosClothAssetEditor\Source\ChaosClothAssetEditorTools\ChaosClothAssetEditorTools.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\ChaosClothAssetEditor\Source\ChaosClothAssetEditor\ChaosClothAssetEditor.Build.cs"><Link>Plugins\ChaosClothAssetEditor\Source\ChaosClothAssetEditor\ChaosClothAssetEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\ChaosClothAssetEditor\Source\ChaosClothAssetTools\ChaosClothAssetTools.Build.cs"><Link>Plugins\ChaosClothAssetEditor\Source\ChaosClothAssetTools\ChaosClothAssetTools.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\ChaosClothAsset\Source\ChaosClothAssetEngine\ChaosClothAssetEngine.Build.cs"><Link>Plugins\ChaosClothAsset\Source\ChaosClothAssetEngine\ChaosClothAssetEngine.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\ChaosClothAsset\Source\ChaosClothAsset\ChaosClothAsset.Build.cs"><Link>Plugins\ChaosClothAsset\Source\ChaosClothAsset\ChaosClothAsset.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\ChaosCloth\Source\ChaosClothEditor\ChaosClothEditor.Build.cs"><Link>Plugins\ChaosCloth\Source\ChaosClothEditor\ChaosClothEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\ChaosCloth\Source\ChaosCloth\ChaosCloth.Build.cs"><Link>Plugins\ChaosCloth\Source\ChaosCloth\ChaosCloth.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\ChaosVD\Source\ChaosVD\ChaosVD.Build.cs"><Link>Plugins\ChaosVD\Source\ChaosVD\ChaosVD.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\CmdLinkServer\Source\CmdLinkServer\CmdLinkServer.Build.cs"><Link>Plugins\CmdLinkServer\Source\CmdLinkServer\CmdLinkServer.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Compositing\Composure\Source\ComposureEditor\ComposureEditor.Build.cs"><Link>Plugins\Compositing\Composure\Source\ComposureEditor\ComposureEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Compositing\Composure\Source\ComposureLayersEditor\ComposureLayersEditor.Build.cs"><Link>Plugins\Compositing\Composure\Source\ComposureLayersEditor\ComposureLayersEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Compositing\Composure\Source\Composure\Composure.Build.cs"><Link>Plugins\Compositing\Composure\Source\Composure\Composure.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Compositing\LensDistortion\Source\LensDistortion\LensDistortion.Build.cs"><Link>Plugins\Compositing\LensDistortion\Source\LensDistortion\LensDistortion.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Compositing\OpenCVLensDistortion\Source\OpenCVLensCalibration\OpenCVLensCalibration.Build.cs"><Link>Plugins\Compositing\OpenCVLensDistortion\Source\OpenCVLensCalibration\OpenCVLensCalibration.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Compositing\OpenCVLensDistortion\Source\OpenCVLensDistortion\OpenCVLensDistortion.Build.cs"><Link>Plugins\Compositing\OpenCVLensDistortion\Source\OpenCVLensDistortion\OpenCVLensDistortion.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Compositing\OpenColorIO\Source\OpenColorIOEditor\OpenColorIOEditor.Build.cs"><Link>Plugins\Compositing\OpenColorIO\Source\OpenColorIOEditor\OpenColorIOEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Compositing\OpenColorIO\Source\OpenColorIO\OpenColorIO.Build.cs"><Link>Plugins\Compositing\OpenColorIO\Source\OpenColorIO\OpenColorIO.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Compression\OodleNetwork\Source\OodleNetworkHandlerComponent.Build.cs"><Link>Plugins\Compression\OodleNetwork\Source\OodleNetworkHandlerComponent.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\DawnSDK\DawnSDK\Source\CoordinatorUtilForDawn\CoordinatorUtilForDawn.Build.cs"><Link>Plugins\DawnSDK\DawnSDK\Source\CoordinatorUtilForDawn\CoordinatorUtilForDawn.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\DawnSDK\DawnSDK\Source\DawnDeamon\DawnDeamon.Build.cs"><Link>Plugins\DawnSDK\DawnSDK\Source\DawnDeamon\DawnDeamon.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\DawnSDK\DawnSDK\Source\DawnSDKCommon\DawnSDKCommon.Build.cs"><Link>Plugins\DawnSDK\DawnSDK\Source\DawnSDKCommon\DawnSDKCommon.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\DawnSDK\DawnSDK\Source\DawnUI\DawnUI.build.cs"><Link>Plugins\DawnSDK\DawnSDK\Source\DawnUI\DawnUI.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\DawnSDK\DawnSDK\Source\TLBSDawnSwarmInterface\TLBSDawnSwarmInterface.Build.cs"><Link>Plugins\DawnSDK\DawnSDK\Source\TLBSDawnSwarmInterface\TLBSDawnSwarmInterface.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Dawn\Source\DawnAssetScan\DawnAssetScan.Build.cs"><Link>Plugins\Dawn\Source\DawnAssetScan\DawnAssetScan.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Dawn\Source\DawnPRT\DawnPRT.Build.cs"><Link>Plugins\Dawn\Source\DawnPRT\DawnPRT.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Dawn\Source\TLBSBase\TLBSBase.Build.cs"><Link>Plugins\Dawn\Source\TLBSBase\TLBSBase.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Dawn\Source\TLBSEditor\TLBSEditor.Build.cs"><Link>Plugins\Dawn\Source\TLBSEditor\TLBSEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Dawn\Source\TLBSSwarmInterface\TLBSSwarmInterface.Build.cs"><Link>Plugins\Dawn\Source\TLBSSwarmInterface\TLBSSwarmInterface.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Developer\AnimationSharing\Source\AnimationSharingEd\AnimationSharingEd.Build.cs"><Link>Plugins\Developer\AnimationSharing\Source\AnimationSharingEd\AnimationSharingEd.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Developer\AnimationSharing\Source\AnimationSharing\AnimationSharing.Build.cs"><Link>Plugins\Developer\AnimationSharing\Source\AnimationSharing\AnimationSharing.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Developer\BlankPlugin\Source\BlankPlugin\BlankPlugin.Build.cs"><Link>Plugins\Developer\BlankPlugin\Source\BlankPlugin\BlankPlugin.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Developer\CLionSourceCodeAccess\Source\CLionSourceCodeAccess\CLionSourceCodeAccess.Build.cs"><Link>Plugins\Developer\CLionSourceCodeAccess\Source\CLionSourceCodeAccess\CLionSourceCodeAccess.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Developer\CodeLiteSourceCodeAccess\Source\CodeLiteSourceCodeAccess\CodeLiteSourceCodeAccess.Build.cs"><Link>Plugins\Developer\CodeLiteSourceCodeAccess\Source\CodeLiteSourceCodeAccess\CodeLiteSourceCodeAccess.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Developer\Concert\ConcertApp\DisasterRecoveryClient\Source\DisasterRecoveryClient\DisasterRecoveryClient.Build.cs"><Link>Plugins\Developer\Concert\ConcertApp\DisasterRecoveryClient\Source\DisasterRecoveryClient\DisasterRecoveryClient.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Developer\Concert\ConcertApp\MultiUserClient\Source\MultiUserClientLibrary\MultiUserClientLibrary.Build.cs"><Link>Plugins\Developer\Concert\ConcertApp\MultiUserClient\Source\MultiUserClientLibrary\MultiUserClientLibrary.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Developer\Concert\ConcertApp\MultiUserClient\Source\MultiUserClient\MultiUserClient.Build.cs"><Link>Plugins\Developer\Concert\ConcertApp\MultiUserClient\Source\MultiUserClient\MultiUserClient.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Developer\Concert\ConcertApp\MultiUserClient\Source\MultiUserReplicationEditor\MultiUserReplicationEditor.Build.cs"><Link>Plugins\Developer\Concert\ConcertApp\MultiUserClient\Source\MultiUserReplicationEditor\MultiUserReplicationEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Developer\Concert\ConcertApp\MultiUserServer\Source\MultiUserServer\MultiUserServer.build.cs"><Link>Plugins\Developer\Concert\ConcertApp\MultiUserServer\Source\MultiUserServer\MultiUserServer.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Developer\Concert\ConcertMain\Source\ConcertClient\ConcertClient.Build.cs"><Link>Plugins\Developer\Concert\ConcertMain\Source\ConcertClient\ConcertClient.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Developer\Concert\ConcertMain\Source\ConcertServer\ConcertServer.Build.cs"><Link>Plugins\Developer\Concert\ConcertMain\Source\ConcertServer\ConcertServer.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Developer\Concert\ConcertMain\Source\ConcertTransport\ConcertTransport.Build.cs"><Link>Plugins\Developer\Concert\ConcertMain\Source\ConcertTransport\ConcertTransport.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Developer\Concert\ConcertMain\Source\Concert\Concert.Build.cs"><Link>Plugins\Developer\Concert\ConcertMain\Source\Concert\Concert.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Developer\Concert\ConcertScripting\ConcertReplicationScripting\Source\ConcertReplicationScriptingEditor\ConcertReplicationScriptingEditor.build.cs"><Link>Plugins\Developer\Concert\ConcertScripting\ConcertReplicationScripting\Source\ConcertReplicationScriptingEditor\ConcertReplicationScriptingEditor.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Developer\Concert\ConcertScripting\ConcertReplicationScripting\Source\ConcertReplicationScripting\ConcertReplicationScripting.Build.cs"><Link>Plugins\Developer\Concert\ConcertScripting\ConcertReplicationScripting\Source\ConcertReplicationScripting\ConcertReplicationScripting.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Developer\Concert\ConcertSync\ConcertSyncClient\Source\ConcertSyncClient\ConcertSyncClient.Build.cs"><Link>Plugins\Developer\Concert\ConcertSync\ConcertSyncClient\Source\ConcertSyncClient\ConcertSyncClient.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Developer\Concert\ConcertSync\ConcertSyncCore\Source\ConcertSyncCore\ConcertSyncCore.Build.cs"><Link>Plugins\Developer\Concert\ConcertSync\ConcertSyncCore\Source\ConcertSyncCore\ConcertSyncCore.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Developer\Concert\ConcertSync\ConcertSyncServer\Source\ConcertSyncServer\ConcertSyncServer.Build.cs"><Link>Plugins\Developer\Concert\ConcertSync\ConcertSyncServer\Source\ConcertSyncServer\ConcertSyncServer.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Developer\Concert\ConcertSync\ConcertSyncTest\Source\ConcertSyncTest\ConcertSyncTest.Build.cs"><Link>Plugins\Developer\Concert\ConcertSync\ConcertSyncTest\Source\ConcertSyncTest\ConcertSyncTest.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Developer\Concert\ConcertUI\ConcertClientSharedSlate\Source\ConcertClientSharedSlate\ConcertClientSharedSlate.Build.cs"><Link>Plugins\Developer\Concert\ConcertUI\ConcertClientSharedSlate\Source\ConcertClientSharedSlate\ConcertClientSharedSlate.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Developer\Concert\ConcertUI\ConcertSharedSlate\Source\ConcertSharedSlate\ConcertSharedSlate.Build.cs"><Link>Plugins\Developer\Concert\ConcertUI\ConcertSharedSlate\Source\ConcertSharedSlate\ConcertSharedSlate.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Developer\DumpGPUServices\Source\DumpGPUServices\DumpGPUServices.Build.cs"><Link>Plugins\Developer\DumpGPUServices\Source\DumpGPUServices\DumpGPUServices.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Developer\GitSourceControl\Source\GitSourceControl\GitSourceControl.Build.cs"><Link>Plugins\Developer\GitSourceControl\Source\GitSourceControl\GitSourceControl.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Developer\KDevelopSourceCodeAccess\Source\KDevelopSourceCodeAccess\KDevelopSourceCodeAccess.Build.cs"><Link>Plugins\Developer\KDevelopSourceCodeAccess\Source\KDevelopSourceCodeAccess\KDevelopSourceCodeAccess.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Developer\N10XSourceCodeAccess\Source\N10XSourceCodeAccess\N10XSourceCodeAccess.Build.cs"><Link>Plugins\Developer\N10XSourceCodeAccess\Source\N10XSourceCodeAccess\N10XSourceCodeAccess.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Developer\NullSourceCodeAccess\Source\NullSourceCodeAccess\NullSourceCodeAccess.Build.cs"><Link>Plugins\Developer\NullSourceCodeAccess\Source\NullSourceCodeAccess\NullSourceCodeAccess.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Developer\OneSkyLocalizationService\Source\OneSkyLocalizationService\OneSkyLocalizationService.Build.cs"><Link>Plugins\Developer\OneSkyLocalizationService\Source\OneSkyLocalizationService\OneSkyLocalizationService.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Developer\PerforceSourceControl\Source\PerforceSourceControl\PerforceSourceControl.Build.cs"><Link>Plugins\Developer\PerforceSourceControl\Source\PerforceSourceControl\PerforceSourceControl.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Developer\PixWinPlugin\Source\PixWinPlugin\PixWinPlugin.Build.cs"><Link>Plugins\Developer\PixWinPlugin\Source\PixWinPlugin\PixWinPlugin.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Developer\PlasticSourceControl\Source\PlasticSourceControl\PlasticSourceControl.Build.cs"><Link>Plugins\Developer\PlasticSourceControl\Source\PlasticSourceControl\PlasticSourceControl.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Developer\PluginUtils\Source\PluginUtils\PluginUtils.Build.cs"><Link>Plugins\Developer\PluginUtils\Source\PluginUtils\PluginUtils.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Developer\PropertyAccessNode\Source\PropertyAccessNode\PropertyAccessNode.Build.cs"><Link>Plugins\Developer\PropertyAccessNode\Source\PropertyAccessNode\PropertyAccessNode.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Developer\RenderDocPlugin\Source\RenderDocPlugin\RenderDocPlugin.Build.cs"><Link>Plugins\Developer\RenderDocPlugin\Source\RenderDocPlugin\RenderDocPlugin.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Developer\RiderSourceCodeAccess\Source\RiderSourceCodeAccess\RiderSourceCodeAccess.Build.cs"><Link>Plugins\Developer\RiderSourceCodeAccess\Source\RiderSourceCodeAccess\RiderSourceCodeAccess.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Developer\SubversionSourceControl\Source\SubversionSourceControl\SubversionSourceControl.Build.cs"><Link>Plugins\Developer\SubversionSourceControl\Source\SubversionSourceControl\SubversionSourceControl.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Developer\TextureFormatOodle\Source\TextureFormatOodle.Build.cs"><Link>Plugins\Developer\TextureFormatOodle\Source\TextureFormatOodle.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Developer\TraceDataFiltering\Source\TraceDataFiltering\TraceDataFiltering.build.cs"><Link>Plugins\Developer\TraceDataFiltering\Source\TraceDataFiltering\TraceDataFiltering.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Developer\TraceSourceFiltering\Source\SourceFilteringCore\SourceFilteringCore.Build.cs"><Link>Plugins\Developer\TraceSourceFiltering\Source\SourceFilteringCore\SourceFilteringCore.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Developer\TraceSourceFiltering\Source\SourceFilteringEditor\SourceFilteringEditor.Build.cs"><Link>Plugins\Developer\TraceSourceFiltering\Source\SourceFilteringEditor\SourceFilteringEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Developer\TraceSourceFiltering\Source\SourceFilteringTrace\SourceFilteringTrace.Build.cs"><Link>Plugins\Developer\TraceSourceFiltering\Source\SourceFilteringTrace\SourceFilteringTrace.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Developer\UObjectPlugin\Source\UObjectPlugin\UObjectPlugin.Build.cs"><Link>Plugins\Developer\UObjectPlugin\Source\UObjectPlugin\UObjectPlugin.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Developer\VisualStudioCodeSourceCodeAccess\Source\VisualStudioCodeSourceCodeAccess\VisualStudioCodeSourceCodeAccess.Build.cs"><Link>Plugins\Developer\VisualStudioCodeSourceCodeAccess\Source\VisualStudioCodeSourceCodeAccess\VisualStudioCodeSourceCodeAccess.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Developer\VisualStudioSourceCodeAccess\Source\ThirdParty\VisualStudioSetup\VisualStudioSetup.Build.cs"><Link>Plugins\Developer\VisualStudioSourceCodeAccess\Source\ThirdParty\VisualStudioSetup\VisualStudioSetup.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Developer\VisualStudioSourceCodeAccess\Source\VisualStudioSourceCodeAccess\VisualStudioSourceCodeAccess.Build.cs"><Link>Plugins\Developer\VisualStudioSourceCodeAccess\Source\VisualStudioSourceCodeAccess\VisualStudioSourceCodeAccess.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Developer\XCodeSourceCodeAccess\Source\XCodeSourceCodeAccess\XCodeSourceCodeAccess.Build.cs"><Link>Plugins\Developer\XCodeSourceCodeAccess\Source\XCodeSourceCodeAccess\XCodeSourceCodeAccess.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Developer\XcodeGPUDebuggerPlugin\Source\XcodeGPUDebuggerPlugin\XcodeGPUDebuggerPlugin.Build.cs"><Link>Plugins\Developer\XcodeGPUDebuggerPlugin\Source\XcodeGPUDebuggerPlugin\XcodeGPUDebuggerPlugin.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\DirectExcel\Source\DirectExcel\DirectExcel.Build.cs"><Link>Plugins\DirectExcel\Source\DirectExcel\DirectExcel.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\DiscoveryBeaconReceiver\Source\DiscoveryBeaconReceiver\DiscoveryBeaconReceiver.build.cs"><Link>Plugins\DiscoveryBeaconReceiver\Source\DiscoveryBeaconReceiver\DiscoveryBeaconReceiver.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\EcoJointDynamicsPlugin\Source\EcoJointDynamicsEditor\EcoJointDynamicsEditor.Build.cs"><Link>Plugins\EcoJointDynamicsPlugin\Source\EcoJointDynamicsEditor\EcoJointDynamicsEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\EcoJointDynamicsPlugin\Source\EcoJointDynamics\EcoJointDynamics.Build.cs"><Link>Plugins\EcoJointDynamicsPlugin\Source\EcoJointDynamics\EcoJointDynamics.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Editor\AssetDetection\Source\AssetDetection\AssetDetection.Build.cs"><Link>Plugins\Editor\AssetDetection\Source\AssetDetection\AssetDetection.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Editor\AssetManagerEditor\Source\AssetManagerEditor\AssetManagerEditor.Build.cs"><Link>Plugins\Editor\AssetManagerEditor\Source\AssetManagerEditor\AssetManagerEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Editor\AssetReferenceRestrictions\Source\AssetReferenceRestrictions\AssetReferenceRestrictions.Build.cs"><Link>Plugins\Editor\AssetReferenceRestrictions\Source\AssetReferenceRestrictions\AssetReferenceRestrictions.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Editor\AssetRegistryExport\Source\AssetRegistryExport.Build.cs"><Link>Plugins\Editor\AssetRegistryExport\Source\AssetRegistryExport.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Editor\AssetSearch\Source\AssetSearch.Build.cs"><Link>Plugins\Editor\AssetSearch\Source\AssetSearch.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Editor\BlueprintHeaderView\Source\BlueprintHeaderView\BlueprintHeaderView.Build.cs"><Link>Plugins\Editor\BlueprintHeaderView\Source\BlueprintHeaderView\BlueprintHeaderView.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Editor\BlueprintMaterialTextureNodes\Source\BlueprintMaterialTextureNodes\BlueprintMaterialTextureNodes.Build.cs"><Link>Plugins\Editor\BlueprintMaterialTextureNodes\Source\BlueprintMaterialTextureNodes\BlueprintMaterialTextureNodes.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Editor\ChangelistReview\Source\ChangelistReview.Build.cs"><Link>Plugins\Editor\ChangelistReview\Source\ChangelistReview.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Editor\ConsoleVariablesEditor\Source\ConsoleVariablesEditorRuntime\ConsoleVariablesEditorRuntime.build.cs"><Link>Plugins\Editor\ConsoleVariablesEditor\Source\ConsoleVariablesEditorRuntime\ConsoleVariablesEditorRuntime.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Editor\ConsoleVariablesEditor\Source\ConsoleVariablesEditor\ConsoleVariablesEditor.build.cs"><Link>Plugins\Editor\ConsoleVariablesEditor\Source\ConsoleVariablesEditor\ConsoleVariablesEditor.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Editor\ContentBrowser\ContentBrowserAliasDataSoure\Source\ContentBrowserAliasDataSource\ContentBrowserAliasDataSource.Build.cs"><Link>Plugins\Editor\ContentBrowser\ContentBrowserAliasDataSoure\Source\ContentBrowserAliasDataSource\ContentBrowserAliasDataSource.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Editor\ContentBrowser\ContentBrowserAssetDataSource\Source\ContentBrowserAssetDataSource\ContentBrowserAssetDataSource.Build.cs"><Link>Plugins\Editor\ContentBrowser\ContentBrowserAssetDataSource\Source\ContentBrowserAssetDataSource\ContentBrowserAssetDataSource.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Editor\ContentBrowser\ContentBrowserClassDataSource\Source\ContentBrowserClassDataSource\ContentBrowserClassDataSource.Build.cs"><Link>Plugins\Editor\ContentBrowser\ContentBrowserClassDataSource\Source\ContentBrowserClassDataSource\ContentBrowserClassDataSource.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Editor\ContentBrowser\ContentBrowserFileDataSource\Source\ContentBrowserFileDataSource\ContentBrowserFileDataSource.Build.cs"><Link>Plugins\Editor\ContentBrowser\ContentBrowserFileDataSource\Source\ContentBrowserFileDataSource\ContentBrowserFileDataSource.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Editor\CryptoKeys\Source\CryptoKeysOpenSSL\CryptoKeysOpenSSL.Build.cs"><Link>Plugins\Editor\CryptoKeys\Source\CryptoKeysOpenSSL\CryptoKeysOpenSSL.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Editor\CryptoKeys\Source\CryptoKeys\CryptoKeys.Build.cs"><Link>Plugins\Editor\CryptoKeys\Source\CryptoKeys\CryptoKeys.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Editor\CurveEditorTools\Source\CurveEditorTools\CurveEditorTools.Build.cs"><Link>Plugins\Editor\CurveEditorTools\Source\CurveEditorTools\CurveEditorTools.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Editor\DataValidation\Source\DataValidation\DataValidation.Build.cs"><Link>Plugins\Editor\DataValidation\Source\DataValidation\DataValidation.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Editor\DisplayClusterLaunch\Source\DisplayClusterLaunchEditor\DisplayClusterLaunchEditor.build.cs"><Link>Plugins\Editor\DisplayClusterLaunch\Source\DisplayClusterLaunchEditor\DisplayClusterLaunchEditor.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Editor\EditorDebugTools\Source\EditorDebugTools\EditorDebugTools.Build.cs"><Link>Plugins\Editor\EditorDebugTools\Source\EditorDebugTools\EditorDebugTools.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Editor\EditorPerformanceModule\Source\EditorPerformanceModule\EditorPerformanceModule.Build.cs"><Link>Plugins\Editor\EditorPerformanceModule\Source\EditorPerformanceModule\EditorPerformanceModule.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Editor\EditorScriptingUtilities\Source\EditorScriptingUtilities\EditorScriptingUtilities.Build.cs"><Link>Plugins\Editor\EditorScriptingUtilities\Source\EditorScriptingUtilities\EditorScriptingUtilities.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Editor\EditorSysConfigAssistant\Source\EditorSysConfigAssistant\EditorSysConfigAssistant.Build.cs"><Link>Plugins\Editor\EditorSysConfigAssistant\Source\EditorSysConfigAssistant\EditorSysConfigAssistant.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Editor\EngineAssetDefinitions\Source\EngineAssetDefinitions.Build.cs"><Link>Plugins\Editor\EngineAssetDefinitions\Source\EngineAssetDefinitions.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Editor\FacialAnimation\Source\FacialAnimationEditor\FacialAnimationEditor.Build.cs"><Link>Plugins\Editor\FacialAnimation\Source\FacialAnimationEditor\FacialAnimationEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Editor\FacialAnimation\Source\FacialAnimation\FacialAnimation.Build.cs"><Link>Plugins\Editor\FacialAnimation\Source\FacialAnimation\FacialAnimation.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Editor\GameplayTagsEditor\Source\GameplayTagsEditor\GameplayTagsEditor.Build.cs"><Link>Plugins\Editor\GameplayTagsEditor\Source\GameplayTagsEditor\GameplayTagsEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Editor\GeometryMode\Source\BspMode\BspMode.Build.cs"><Link>Plugins\Editor\GeometryMode\Source\BspMode\BspMode.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Editor\GeometryMode\Source\GeometryMode\GeometryMode.Build.cs"><Link>Plugins\Editor\GeometryMode\Source\GeometryMode\GeometryMode.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Editor\GeometryMode\Source\TextureAlignMode\TextureAlignMode.Build.cs"><Link>Plugins\Editor\GeometryMode\Source\TextureAlignMode\TextureAlignMode.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Editor\GuidedTutorials\Source\IntroTutorials\IntroTutorials.Build.cs"><Link>Plugins\Editor\GuidedTutorials\Source\IntroTutorials\IntroTutorials.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Editor\LiveUpdateForSlate\Source\LiveUpdateForSlate\LiveUpdateForSlate.Build.cs"><Link>Plugins\Editor\LiveUpdateForSlate\Source\LiveUpdateForSlate\LiveUpdateForSlate.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Editor\Localization\PortableObjectFileDataSource\Source\PortableObjectFileDataSource\PortableObjectFileDataSource.Build.cs"><Link>Plugins\Editor\Localization\PortableObjectFileDataSource\Source\PortableObjectFileDataSource\PortableObjectFileDataSource.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Editor\MacGraphicsSwitching\Source\MacGraphicsSwitching\MacGraphicsSwitching.Build.cs"><Link>Plugins\Editor\MacGraphicsSwitching\Source\MacGraphicsSwitching\MacGraphicsSwitching.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Editor\MaterialAnalyzer\Source\MaterialAnalyzer.Build.cs"><Link>Plugins\Editor\MaterialAnalyzer\Source\MaterialAnalyzer.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Editor\MeshLODToolset\Source\MeshLODToolset\MeshLODToolset.Build.cs"><Link>Plugins\Editor\MeshLODToolset\Source\MeshLODToolset\MeshLODToolset.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Editor\MobileLauncherProfileWizard\Source\MobileLauncherProfileWizard\MobileLauncherProfileWizard.Build.cs"><Link>Plugins\Editor\MobileLauncherProfileWizard\Source\MobileLauncherProfileWizard\MobileLauncherProfileWizard.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Editor\ModelingToolsEditorMode\Source\ModelingToolsEditorMode\ModelingToolsEditorMode.Build.cs"><Link>Plugins\Editor\ModelingToolsEditorMode\Source\ModelingToolsEditorMode\ModelingToolsEditorMode.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Editor\ObjectMixer\LightMixer\Source\LightMixer\LightMixer.build.cs"><Link>Plugins\Editor\ObjectMixer\LightMixer\Source\LightMixer\LightMixer.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Editor\ObjectMixer\ObjectMixer\Source\ObjectMixer\ObjectMixerEditor.build.cs"><Link>Plugins\Editor\ObjectMixer\ObjectMixer\Source\ObjectMixer\ObjectMixerEditor.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Editor\PluginBrowser\Source\PluginBrowser\PluginBrowser.Build.cs"><Link>Plugins\Editor\PluginBrowser\Source\PluginBrowser\PluginBrowser.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Editor\ProxyLODPlugin\Source\ProxyLOD\ProxyLODMeshReduction.Build.cs"><Link>Plugins\Editor\ProxyLODPlugin\Source\ProxyLOD\ProxyLODMeshReduction.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Editor\ProxyLODPlugin\Source\ThirdParty\DirectXMesh.Build.cs"><Link>Plugins\Editor\ProxyLODPlugin\Source\ThirdParty\DirectXMesh.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Editor\ProxyLODPlugin\Source\ThirdParty\UVAtlas.Build.cs"><Link>Plugins\Editor\ProxyLODPlugin\Source\ThirdParty\UVAtlas.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Editor\SampleToolsEditorMode\Source\SampleToolsEditorMode.Build.cs"><Link>Plugins\Editor\SampleToolsEditorMode\Source\SampleToolsEditorMode.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Editor\ScriptableToolsEditorMode\Source\ScriptableToolsEditorMode\ScriptableToolsEditorMode.Build.cs"><Link>Plugins\Editor\ScriptableToolsEditorMode\Source\ScriptableToolsEditorMode\ScriptableToolsEditorMode.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Editor\SequencerAnimTools\Source\SequencerAnimTools\SequencerAnimTools.Build.cs"><Link>Plugins\Editor\SequencerAnimTools\Source\SequencerAnimTools\SequencerAnimTools.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Editor\SpeedTreeImporter\Source\SpeedTreeImporter\SpeedTreeImporter.Build.cs"><Link>Plugins\Editor\SpeedTreeImporter\Source\SpeedTreeImporter\SpeedTreeImporter.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Editor\StaticMeshEditorModeling\Source\StaticMeshEditorModeling\StaticMeshEditorModeling.Build.cs"><Link>Plugins\Editor\StaticMeshEditorModeling\Source\StaticMeshEditorModeling\StaticMeshEditorModeling.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Editor\StylusInput\Source\StylusInput\StylusInput.Build.cs"><Link>Plugins\Editor\StylusInput\Source\StylusInput\StylusInput.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Editor\UVEditor\Source\UVEditorToolsEditorOnly\UVEditorToolsEditorOnly.Build.cs"><Link>Plugins\Editor\UVEditor\Source\UVEditorToolsEditorOnly\UVEditorToolsEditorOnly.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Editor\UVEditor\Source\UVEditorTools\UVEditorTools.Build.cs"><Link>Plugins\Editor\UVEditor\Source\UVEditorTools\UVEditorTools.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Editor\UVEditor\Source\UVEditor\UVEditor.Build.cs"><Link>Plugins\Editor\UVEditor\Source\UVEditor\UVEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Editor\WaveformEditor\Source\WaveformEditorWidgets\WaveformEditorWidgets.build.cs"><Link>Plugins\Editor\WaveformEditor\Source\WaveformEditorWidgets\WaveformEditorWidgets.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Editor\WaveformEditor\Source\WaveformEditor\WaveformEditor.build.cs"><Link>Plugins\Editor\WaveformEditor\Source\WaveformEditor\WaveformEditor.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Editor\WaveformEditor\Source\WaveformTransformationsWidgets\WaveformTransformationsWidgets.build.cs"><Link>Plugins\Editor\WaveformEditor\Source\WaveformTransformationsWidgets\WaveformTransformationsWidgets.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Editor\WaveformEditor\Source\WaveformTransformations\WaveformTransformations.build.cs"><Link>Plugins\Editor\WaveformEditor\Source\WaveformTransformations\WaveformTransformations.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Editor\WorldPartitionHLODUtilities\Source\WorldPartitionHLODUtilities.Build.cs"><Link>Plugins\Editor\WorldPartitionHLODUtilities\Source\WorldPartitionHLODUtilities.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\EnhanceProductivityEditor\Source\ClothEditor\ClothEditor.Build.cs"><Link>Plugins\EnhanceProductivityEditor\Source\ClothEditor\ClothEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\EnhanceProductivityEditor\Source\WidgetExtent\WidgetExtent.Build.cs"><Link>Plugins\EnhanceProductivityEditor\Source\WidgetExtent\WidgetExtent.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\EnhancedInput\Source\EnhancedInput\EnhancedInput.Build.cs"><Link>Plugins\EnhancedInput\Source\EnhancedInput\EnhancedInput.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\EnhancedInput\Source\InputBlueprintNodes\InputBlueprintNodes.Build.cs"><Link>Plugins\EnhancedInput\Source\InputBlueprintNodes\InputBlueprintNodes.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\EnhancedInput\Source\InputEditor\InputEditor.Build.cs"><Link>Plugins\EnhancedInput\Source\InputEditor\InputEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Enterprise\AxFImporter\Source\AxFImporter\AxFImporter.Build.cs"><Link>Plugins\Enterprise\AxFImporter\Source\AxFImporter\AxFImporter.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Enterprise\DataprepEditor\Source\DataprepCore\DataprepCore.Build.cs"><Link>Plugins\Enterprise\DataprepEditor\Source\DataprepCore\DataprepCore.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Enterprise\DataprepEditor\Source\DataprepEditorScriptingUtilities\DataprepEditorScriptingUtilities.Build.cs"><Link>Plugins\Enterprise\DataprepEditor\Source\DataprepEditorScriptingUtilities\DataprepEditorScriptingUtilities.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Enterprise\DataprepEditor\Source\DataprepEditor\DataprepEditor.Build.cs"><Link>Plugins\Enterprise\DataprepEditor\Source\DataprepEditor\DataprepEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Enterprise\DataprepEditor\Source\DataprepLibraries\DataprepLibraries.Build.cs"><Link>Plugins\Enterprise\DataprepEditor\Source\DataprepLibraries\DataprepLibraries.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Enterprise\DatasmithC4DImporter\Source\DatasmithC4DTranslator\DatasmithC4DTranslator.Build.cs"><Link>Plugins\Enterprise\DatasmithC4DImporter\Source\DatasmithC4DTranslator\DatasmithC4DTranslator.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Enterprise\DatasmithCADImporter\Source\CADInterfaces\CADInterfaces.Build.cs"><Link>Plugins\Enterprise\DatasmithCADImporter\Source\CADInterfaces\CADInterfaces.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Enterprise\DatasmithCADImporter\Source\CADKernelSurface\CADKernelSurface.Build.cs"><Link>Plugins\Enterprise\DatasmithCADImporter\Source\CADKernelSurface\CADKernelSurface.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Enterprise\DatasmithCADImporter\Source\CADLibrary\CADLibrary.Build.cs"><Link>Plugins\Enterprise\DatasmithCADImporter\Source\CADLibrary\CADLibrary.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Enterprise\DatasmithCADImporter\Source\CADTools\CADTools.Build.cs"><Link>Plugins\Enterprise\DatasmithCADImporter\Source\CADTools\CADTools.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Enterprise\DatasmithCADImporter\Source\DatasmithCADTranslator\DatasmithCADTranslator.Build.cs"><Link>Plugins\Enterprise\DatasmithCADImporter\Source\DatasmithCADTranslator\DatasmithCADTranslator.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Enterprise\DatasmithCADImporter\Source\DatasmithDispatcher\DatasmithDispatcher.Build.cs"><Link>Plugins\Enterprise\DatasmithCADImporter\Source\DatasmithDispatcher\DatasmithDispatcher.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Enterprise\DatasmithCADImporter\Source\DatasmithOpenNurbsTranslator\DatasmithOpenNurbsTranslator.Build.cs"><Link>Plugins\Enterprise\DatasmithCADImporter\Source\DatasmithOpenNurbsTranslator\DatasmithOpenNurbsTranslator.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Enterprise\DatasmithCADImporter\Source\DatasmithPLMXMLTranslator\DatasmithPLMXMLTranslator.Build.cs"><Link>Plugins\Enterprise\DatasmithCADImporter\Source\DatasmithPLMXMLTranslator\DatasmithPLMXMLTranslator.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Enterprise\DatasmithCADImporter\Source\DatasmithWireTranslator\DatasmithWireTranslator2020.Build.cs"><Link>Plugins\Enterprise\DatasmithCADImporter\Source\DatasmithWireTranslator\DatasmithWireTranslator2020.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Enterprise\DatasmithCADImporter\Source\DatasmithWireTranslator\DatasmithWireTranslator2021_3.Build.cs"><Link>Plugins\Enterprise\DatasmithCADImporter\Source\DatasmithWireTranslator\DatasmithWireTranslator2021_3.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Enterprise\DatasmithCADImporter\Source\DatasmithWireTranslator\DatasmithWireTranslator2022.Build.cs"><Link>Plugins\Enterprise\DatasmithCADImporter\Source\DatasmithWireTranslator\DatasmithWireTranslator2022.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Enterprise\DatasmithCADImporter\Source\DatasmithWireTranslator\DatasmithWireTranslator2022_1.Build.cs"><Link>Plugins\Enterprise\DatasmithCADImporter\Source\DatasmithWireTranslator\DatasmithWireTranslator2022_1.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Enterprise\DatasmithCADImporter\Source\DatasmithWireTranslator\DatasmithWireTranslator2022_2.Build.cs"><Link>Plugins\Enterprise\DatasmithCADImporter\Source\DatasmithWireTranslator\DatasmithWireTranslator2022_2.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Enterprise\DatasmithCADImporter\Source\DatasmithWireTranslator\DatasmithWireTranslator2023_0.Build.cs"><Link>Plugins\Enterprise\DatasmithCADImporter\Source\DatasmithWireTranslator\DatasmithWireTranslator2023_0.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Enterprise\DatasmithCADImporter\Source\DatasmithWireTranslator\DatasmithWireTranslator2023_1.Build.cs"><Link>Plugins\Enterprise\DatasmithCADImporter\Source\DatasmithWireTranslator\DatasmithWireTranslator2023_1.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Enterprise\DatasmithCADImporter\Source\ParametricSurfaceExtension\ParametricSurfaceExtension.Build.cs"><Link>Plugins\Enterprise\DatasmithCADImporter\Source\ParametricSurfaceExtension\ParametricSurfaceExtension.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Enterprise\DatasmithCADImporter\Source\ParametricSurface\ParametricSurface.Build.cs"><Link>Plugins\Enterprise\DatasmithCADImporter\Source\ParametricSurface\ParametricSurface.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Enterprise\DatasmithContent\Source\DatasmithContentEditor\DatasmithContentEditor.Build.cs"><Link>Plugins\Enterprise\DatasmithContent\Source\DatasmithContentEditor\DatasmithContentEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Enterprise\DatasmithContent\Source\DatasmithContent\DatasmithContent.Build.cs"><Link>Plugins\Enterprise\DatasmithContent\Source\DatasmithContent\DatasmithContent.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Enterprise\DatasmithFBXImporter\Source\DatasmithDeltaGenTranslator\DatasmithDeltaGenTranslator.Build.cs"><Link>Plugins\Enterprise\DatasmithFBXImporter\Source\DatasmithDeltaGenTranslator\DatasmithDeltaGenTranslator.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Enterprise\DatasmithFBXImporter\Source\DatasmithFBXTranslator\DatasmithFBXTranslator.Build.cs"><Link>Plugins\Enterprise\DatasmithFBXImporter\Source\DatasmithFBXTranslator\DatasmithFBXTranslator.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Enterprise\DatasmithFBXImporter\Source\DatasmithVREDTranslator\DatasmithVREDTranslator.Build.cs"><Link>Plugins\Enterprise\DatasmithFBXImporter\Source\DatasmithVREDTranslator\DatasmithVREDTranslator.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Enterprise\DatasmithImporter\Source\DatasmithExternalSource\DatasmithExternalSource.Build.cs"><Link>Plugins\Enterprise\DatasmithImporter\Source\DatasmithExternalSource\DatasmithExternalSource.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Enterprise\DatasmithImporter\Source\DatasmithImporter\DatasmithImporter.Build.cs"><Link>Plugins\Enterprise\DatasmithImporter\Source\DatasmithImporter\DatasmithImporter.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Enterprise\DatasmithImporter\Source\DatasmithNativeTranslator\DatasmithNativeTranslator.Build.cs"><Link>Plugins\Enterprise\DatasmithImporter\Source\DatasmithNativeTranslator\DatasmithNativeTranslator.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Enterprise\DatasmithImporter\Source\DatasmithTranslator\DatasmithTranslator.Build.cs"><Link>Plugins\Enterprise\DatasmithImporter\Source\DatasmithTranslator\DatasmithTranslator.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Enterprise\DatasmithImporter\Source\DirectLinkExtensionEditor\DirectLinkExtensionEditor.Build.cs"><Link>Plugins\Enterprise\DatasmithImporter\Source\DirectLinkExtensionEditor\DirectLinkExtensionEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Enterprise\DatasmithImporter\Source\DirectLinkExtension\DirectLinkExtension.Build.cs"><Link>Plugins\Enterprise\DatasmithImporter\Source\DirectLinkExtension\DirectLinkExtension.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Enterprise\DatasmithImporter\Source\DirectLinkTest\DirectLinkTest.Build.cs"><Link>Plugins\Enterprise\DatasmithImporter\Source\DirectLinkTest\DirectLinkTest.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Enterprise\DatasmithImporter\Source\ExternalSource\ExternalSource.build.cs"><Link>Plugins\Enterprise\DatasmithImporter\Source\ExternalSource\ExternalSource.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Enterprise\GLTFExporter\Source\GLTFExporter\GLTFExporter.Build.cs"><Link>Plugins\Enterprise\GLTFExporter\Source\GLTFExporter\GLTFExporter.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Enterprise\LidarPointCloud\Source\LidarPointCloudEditor\LidarPointCloudEditor.Build.cs"><Link>Plugins\Enterprise\LidarPointCloud\Source\LidarPointCloudEditor\LidarPointCloudEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Enterprise\LidarPointCloud\Source\LidarPointCloudRuntime\LidarPointCloudRuntime.Build.cs"><Link>Plugins\Enterprise\LidarPointCloud\Source\LidarPointCloudRuntime\LidarPointCloudRuntime.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Enterprise\MDLImporter\Source\MDLImporter\MDLImporter.Build.cs"><Link>Plugins\Enterprise\MDLImporter\Source\MDLImporter\MDLImporter.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Enterprise\VariantManagerContent\Source\VariantManagerContentEditor\VariantManagerContentEditor.Build.cs"><Link>Plugins\Enterprise\VariantManagerContent\Source\VariantManagerContentEditor\VariantManagerContentEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Enterprise\VariantManagerContent\Source\VariantManagerContent\VariantManagerContent.Build.cs"><Link>Plugins\Enterprise\VariantManagerContent\Source\VariantManagerContent\VariantManagerContent.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Enterprise\VariantManager\Source\VariantManager\VariantManager.Build.cs"><Link>Plugins\Enterprise\VariantManager\Source\VariantManager\VariantManager.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\EnvironmentInteraction\Source\EnvironmentInteraction\EnvironmentInteraction.Build.cs"><Link>Plugins\EnvironmentInteraction\Source\EnvironmentInteraction\EnvironmentInteraction.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\AVCodecs\AMFCodecs\Source\AMFCodecsRHI\AMFCodecsRHI.Build.cs"><Link>Plugins\Experimental\AVCodecs\AMFCodecs\Source\AMFCodecsRHI\AMFCodecsRHI.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\AVCodecs\AMFCodecs\Source\AMFCodecs\AMFCodecs.Build.cs"><Link>Plugins\Experimental\AVCodecs\AMFCodecs\Source\AMFCodecs\AMFCodecs.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\AVCodecs\AVCodecsCore\Source\AVCodecsCoreRHI\AVCodecsCoreRHI.Build.cs"><Link>Plugins\Experimental\AVCodecs\AVCodecsCore\Source\AVCodecsCoreRHI\AVCodecsCoreRHI.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\AVCodecs\AVCodecsCore\Source\AVCodecsCore\AVCodecsCore.Build.cs"><Link>Plugins\Experimental\AVCodecs\AVCodecsCore\Source\AVCodecsCore\AVCodecsCore.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\AVCodecs\NVCodecs\Source\NVCodecsRHI\NVCodecsRHI.Build.cs"><Link>Plugins\Experimental\AVCodecs\NVCodecs\Source\NVCodecsRHI\NVCodecsRHI.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\AVCodecs\NVCodecs\Source\NVCodecs\NVCodecs.Build.cs"><Link>Plugins\Experimental\AVCodecs\NVCodecs\Source\NVCodecs\NVCodecs.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\AVCodecs\NVCodecs\Source\NVDEC\NVDEC.Build.cs"><Link>Plugins\Experimental\AVCodecs\NVCodecs\Source\NVDEC\NVDEC.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\AVCodecs\NVCodecs\Source\NVENC\NVENC.Build.cs"><Link>Plugins\Experimental\AVCodecs\NVCodecs\Source\NVENC\NVENC.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\AVCodecs\VTCodecs\Source\VTCodecsRHI\VTCodecsRHI.build.cs"><Link>Plugins\Experimental\AVCodecs\VTCodecs\Source\VTCodecsRHI\VTCodecsRHI.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\AVCodecs\VTCodecs\Source\VTCodecs\VTCodecs.build.cs"><Link>Plugins\Experimental\AVCodecs\VTCodecs\Source\VTCodecs\VTCodecs.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\AVCodecs\WMFCodecs\Source\WMFCodecs\WMFCodecs.Build.cs"><Link>Plugins\Experimental\AVCodecs\WMFCodecs\Source\WMFCodecs\WMFCodecs.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\AbilitySystemGameFeatureActions\Source\AbilitySystemGameFeatureActions\AbilitySystemGameFeatureActions.Build.cs"><Link>Plugins\Experimental\AbilitySystemGameFeatureActions\Source\AbilitySystemGameFeatureActions\AbilitySystemGameFeatureActions.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\ActorModifierCore\Source\ActorModifierCoreEditor\ActorModifierCoreEditor.Build.cs"><Link>Plugins\Experimental\ActorModifierCore\Source\ActorModifierCoreEditor\ActorModifierCoreEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\ActorModifierCore\Source\ActorModifierCore\ActorModifierCore.Build.cs"><Link>Plugins\Experimental\ActorModifierCore\Source\ActorModifierCore\ActorModifierCore.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\ActorPalette\Source\ActorPalette\ActorPalette.Build.cs"><Link>Plugins\Experimental\ActorPalette\Source\ActorPalette\ActorPalette.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\AdvancedRenamer\Source\AdvancedRenamer\AdvancedRenamer.Build.cs"><Link>Plugins\Experimental\AdvancedRenamer\Source\AdvancedRenamer\AdvancedRenamer.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\AnimNext\Source\AnimNextEditor\AnimNextEditor.Build.cs"><Link>Plugins\Experimental\AnimNext\Source\AnimNextEditor\AnimNextEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\AnimNext\Source\AnimNextTestSuite\AnimNextTestSuite.Build.cs"><Link>Plugins\Experimental\AnimNext\Source\AnimNextTestSuite\AnimNextTestSuite.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\AnimNext\Source\AnimNextUncookedOnly\AnimNextUncookedOnly.Build.cs"><Link>Plugins\Experimental\AnimNext\Source\AnimNextUncookedOnly\AnimNextUncookedOnly.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\AnimNext\Source\AnimNext\AnimNext.Build.cs"><Link>Plugins\Experimental\AnimNext\Source\AnimNext\AnimNext.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\AnimToTexture\Source\AnimToTextureEditor\AnimToTextureEditor.Build.cs"><Link>Plugins\Experimental\AnimToTexture\Source\AnimToTextureEditor\AnimToTextureEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\AnimToTexture\Source\AnimToTexture\AnimToTexture.Build.cs"><Link>Plugins\Experimental\AnimToTexture\Source\AnimToTexture\AnimToTexture.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Animation\ContextualAnimation\Source\ContextualAnimationEditor\ContextualAnimationEditor.Build.cs"><Link>Plugins\Experimental\Animation\ContextualAnimation\Source\ContextualAnimationEditor\ContextualAnimationEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Animation\ContextualAnimation\Source\ContextualAnimation\ContextualAnimation.Build.cs"><Link>Plugins\Experimental\Animation\ContextualAnimation\Source\ContextualAnimation\ContextualAnimation.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Animation\CurveExpression\Source\Editor\CurveExpressionEditor.Build.cs"><Link>Plugins\Experimental\Animation\CurveExpression\Source\Editor\CurveExpressionEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Animation\CurveExpression\Source\Runtime\CurveExpression.Build.cs"><Link>Plugins\Experimental\Animation\CurveExpression\Source\Runtime\CurveExpression.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Animation\MotionTrajectory\Source\MotionTrajectory\MotionTrajectory.Build.cs"><Link>Plugins\Experimental\Animation\MotionTrajectory\Source\MotionTrajectory\MotionTrajectory.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Animation\SkeletalMeshModelingTools\Source\SkeletalMeshModelingTools\SkeletalMeshModelingTools.Build.cs"><Link>Plugins\Experimental\Animation\SkeletalMeshModelingTools\Source\SkeletalMeshModelingTools\SkeletalMeshModelingTools.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\AppleVision\Source\AppleVisionBlueprintSupport\AppleVisionBlueprintSupport.Build.cs"><Link>Plugins\Experimental\AppleVision\Source\AppleVisionBlueprintSupport\AppleVisionBlueprintSupport.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\AppleVision\Source\AppleVision\AppleVision.Build.cs"><Link>Plugins\Experimental\AppleVision\Source\AppleVision\AppleVision.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\AutomationUtils\Source\AutomationUtilsEditor\AutomationUtilsEditor.Build.cs"><Link>Plugins\Experimental\AutomationUtils\Source\AutomationUtilsEditor\AutomationUtilsEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\AutomationUtils\Source\AutomationUtils\AutomationUtils.Build.cs"><Link>Plugins\Experimental\AutomationUtils\Source\AutomationUtils\AutomationUtils.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Avalanche\Source\AvalancheAttributeEditor\AvalancheAttributeEditor.Build.cs"><Link>Plugins\Experimental\Avalanche\Source\AvalancheAttributeEditor\AvalancheAttributeEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Avalanche\Source\AvalancheAttribute\AvalancheAttribute.Build.cs"><Link>Plugins\Experimental\Avalanche\Source\AvalancheAttribute\AvalancheAttribute.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Avalanche\Source\AvalancheComponentVisualizers\AvalancheComponentVisualizers.Build.cs"><Link>Plugins\Experimental\Avalanche\Source\AvalancheComponentVisualizers\AvalancheComponentVisualizers.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Avalanche\Source\AvalancheCore\AvalancheCore.Build.cs"><Link>Plugins\Experimental\Avalanche\Source\AvalancheCore\AvalancheCore.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Avalanche\Source\AvalancheEditorCore\AvalancheEditorCore.Build.cs"><Link>Plugins\Experimental\Avalanche\Source\AvalancheEditorCore\AvalancheEditorCore.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Avalanche\Source\AvalancheEditor\AvalancheEditor.Build.cs"><Link>Plugins\Experimental\Avalanche\Source\AvalancheEditor\AvalancheEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Avalanche\Source\AvalancheEffectorsEditor\AvalancheEffectorsEditor.Build.cs"><Link>Plugins\Experimental\Avalanche\Source\AvalancheEffectorsEditor\AvalancheEffectorsEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Avalanche\Source\AvalancheEffectors\AvalancheEffectors.Build.cs"><Link>Plugins\Experimental\Avalanche\Source\AvalancheEffectors\AvalancheEffectors.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Avalanche\Source\AvalancheInteractiveToolsRuntime\AvalancheInteractiveToolsRuntime.Build.cs"><Link>Plugins\Experimental\Avalanche\Source\AvalancheInteractiveToolsRuntime\AvalancheInteractiveToolsRuntime.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Avalanche\Source\AvalancheInteractiveTools\AvalancheInteractiveTools.Build.cs"><Link>Plugins\Experimental\Avalanche\Source\AvalancheInteractiveTools\AvalancheInteractiveTools.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Avalanche\Source\AvalancheLevelViewport\AvalancheLevelViewport.Build.cs"><Link>Plugins\Experimental\Avalanche\Source\AvalancheLevelViewport\AvalancheLevelViewport.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Avalanche\Source\AvalancheMRQEditor\AvalancheMRQEditor.Build.cs"><Link>Plugins\Experimental\Avalanche\Source\AvalancheMRQEditor\AvalancheMRQEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Avalanche\Source\AvalancheMRQ\AvalancheMRQ.Build.cs"><Link>Plugins\Experimental\Avalanche\Source\AvalancheMRQ\AvalancheMRQ.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Avalanche\Source\AvalancheMaskEditor\AvalancheMaskEditor.Build.cs"><Link>Plugins\Experimental\Avalanche\Source\AvalancheMaskEditor\AvalancheMaskEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Avalanche\Source\AvalancheMask\AvalancheMask.Build.cs"><Link>Plugins\Experimental\Avalanche\Source\AvalancheMask\AvalancheMask.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Avalanche\Source\AvalancheMediaEditor\AvalancheMediaEditor.Build.cs"><Link>Plugins\Experimental\Avalanche\Source\AvalancheMediaEditor\AvalancheMediaEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Avalanche\Source\AvalancheMedia\AvalancheMedia.Build.cs"><Link>Plugins\Experimental\Avalanche\Source\AvalancheMedia\AvalancheMedia.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Avalanche\Source\AvalancheModifiersEditor\AvalancheModifiersEditor.Build.cs"><Link>Plugins\Experimental\Avalanche\Source\AvalancheModifiersEditor\AvalancheModifiersEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Avalanche\Source\AvalancheModifiers\AvalancheModifiers.Build.cs"><Link>Plugins\Experimental\Avalanche\Source\AvalancheModifiers\AvalancheModifiers.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Avalanche\Source\AvalancheOutliner\AvalancheOutliner.Build.cs"><Link>Plugins\Experimental\Avalanche\Source\AvalancheOutliner\AvalancheOutliner.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Avalanche\Source\AvalanchePropertyAnimatorEditor\AvalanchePropertyAnimatorEditor.Build.cs"><Link>Plugins\Experimental\Avalanche\Source\AvalanchePropertyAnimatorEditor\AvalanchePropertyAnimatorEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Avalanche\Source\AvalanchePropertyAnimator\AvalanchePropertyAnimator.Build.cs"><Link>Plugins\Experimental\Avalanche\Source\AvalanchePropertyAnimator\AvalanchePropertyAnimator.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Avalanche\Source\AvalancheRemoteControlEditor\AvalancheRemoteControlEditor.Build.cs"><Link>Plugins\Experimental\Avalanche\Source\AvalancheRemoteControlEditor\AvalancheRemoteControlEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Avalanche\Source\AvalancheRemoteControl\AvalancheRemoteControl.Build.cs"><Link>Plugins\Experimental\Avalanche\Source\AvalancheRemoteControl\AvalancheRemoteControl.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Avalanche\Source\AvalancheSVGEditor\AvalancheSVGEditor.Build.cs"><Link>Plugins\Experimental\Avalanche\Source\AvalancheSVGEditor\AvalancheSVGEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Avalanche\Source\AvalancheSceneTree\AvalancheSceneTree.Build.cs"><Link>Plugins\Experimental\Avalanche\Source\AvalancheSceneTree\AvalancheSceneTree.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Avalanche\Source\AvalancheSequence\AvalancheSequence.Build.cs"><Link>Plugins\Experimental\Avalanche\Source\AvalancheSequence\AvalancheSequence.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Avalanche\Source\AvalancheSequencer\AvalancheSequencer.Build.cs"><Link>Plugins\Experimental\Avalanche\Source\AvalancheSequencer\AvalancheSequencer.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Avalanche\Source\AvalancheShapesEditor\AvalancheShapesEditor.Build.cs"><Link>Plugins\Experimental\Avalanche\Source\AvalancheShapesEditor\AvalancheShapesEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Avalanche\Source\AvalancheShapes\AvalancheShapes.Build.cs"><Link>Plugins\Experimental\Avalanche\Source\AvalancheShapes\AvalancheShapes.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Avalanche\Source\AvalancheTagEditor\AvalancheTagEditor.Build.cs"><Link>Plugins\Experimental\Avalanche\Source\AvalancheTagEditor\AvalancheTagEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Avalanche\Source\AvalancheTag\AvalancheTag.Build.cs"><Link>Plugins\Experimental\Avalanche\Source\AvalancheTag\AvalancheTag.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Avalanche\Source\AvalancheTextEditor\AvalancheTextEditor.Build.cs"><Link>Plugins\Experimental\Avalanche\Source\AvalancheTextEditor\AvalancheTextEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Avalanche\Source\AvalancheText\AvalancheText.Build.cs"><Link>Plugins\Experimental\Avalanche\Source\AvalancheText\AvalancheText.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Avalanche\Source\AvalancheTransitionEditor\AvalancheTransitionEditor.Build.cs"><Link>Plugins\Experimental\Avalanche\Source\AvalancheTransitionEditor\AvalancheTransitionEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Avalanche\Source\AvalancheTransition\AvalancheTransition.Build.cs"><Link>Plugins\Experimental\Avalanche\Source\AvalancheTransition\AvalancheTransition.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Avalanche\Source\AvalancheViewport\AvalancheViewport.Build.cs"><Link>Plugins\Experimental\Avalanche\Source\AvalancheViewport\AvalancheViewport.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Avalanche\Source\Avalanche\Avalanche.Build.cs"><Link>Plugins\Experimental\Avalanche\Source\Avalanche\Avalanche.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\BackChannel\Source\BackChannel\BackChannel.Build.cs"><Link>Plugins\Experimental\BackChannel\Source\BackChannel\BackChannel.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\BlueprintSnapNodes\Source\BlueprintSnapNodes\BlueprintSnapNodes.Build.cs"><Link>Plugins\Experimental\BlueprintSnapNodes\Source\BlueprintSnapNodes\BlueprintSnapNodes.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\BlueprintStats\Source\BlueprintStats\BlueprintStats.Build.cs"><Link>Plugins\Experimental\BlueprintStats\Source\BlueprintStats\BlueprintStats.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Buoyancy\Source\Runtime\Buoyancy.Build.cs"><Link>Plugins\Experimental\Buoyancy\Source\Runtime\Buoyancy.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\ChaosCachingUSD\Source\ChaosCachingUSD\ChaosCachingUSD.build.cs"><Link>Plugins\Experimental\ChaosCachingUSD\Source\ChaosCachingUSD\ChaosCachingUSD.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\ChaosCaching\Source\ChaosCachingEditor\ChaosCachingEditor.Build.cs"><Link>Plugins\Experimental\ChaosCaching\Source\ChaosCachingEditor\ChaosCachingEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\ChaosCaching\Source\ChaosCaching\ChaosCaching.Build.cs"><Link>Plugins\Experimental\ChaosCaching\Source\ChaosCaching\ChaosCaching.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\ChaosEditor\Source\FractureEditor\FractureEditor.Build.cs"><Link>Plugins\Experimental\ChaosEditor\Source\FractureEditor\FractureEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\ChaosFlesh\Source\ChaosFleshEditor\ChaosFleshEditor.Build.cs"><Link>Plugins\Experimental\ChaosFlesh\Source\ChaosFleshEditor\ChaosFleshEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\ChaosFlesh\Source\ChaosFleshEngine\ChaosFleshEngine.Build.cs"><Link>Plugins\Experimental\ChaosFlesh\Source\ChaosFleshEngine\ChaosFleshEngine.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\ChaosFlesh\Source\ChaosFleshNodes\ChaosFleshNodes.Build.cs"><Link>Plugins\Experimental\ChaosFlesh\Source\ChaosFleshNodes\ChaosFleshNodes.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\ChaosFlesh\Source\ChaosFlesh\ChaosFlesh.Build.cs"><Link>Plugins\Experimental\ChaosFlesh\Source\ChaosFlesh\ChaosFlesh.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\ChaosModularVehicle\Source\ChaosModularVehicleEngine\ChaosModularVehicleEngine.Build.cs"><Link>Plugins\Experimental\ChaosModularVehicle\Source\ChaosModularVehicleEngine\ChaosModularVehicleEngine.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\ChaosModularVehicle\Source\ChaosModularVehicle\ChaosModularVehicle.Build.cs"><Link>Plugins\Experimental\ChaosModularVehicle\Source\ChaosModularVehicle\ChaosModularVehicle.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\ChaosNiagara\Source\ChaosNiagara\ChaosNiagara.Build.cs"><Link>Plugins\Experimental\ChaosNiagara\Source\ChaosNiagara\ChaosNiagara.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\ChaosSolverPlugin\Source\ChaosSolverEditor\ChaosSolverEditor.Build.cs"><Link>Plugins\Experimental\ChaosSolverPlugin\Source\ChaosSolverEditor\ChaosSolverEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\ChaosUserDataPT\Source\ChaosUserDataPT\ChaosUserDataPT.Build.cs"><Link>Plugins\Experimental\ChaosUserDataPT\Source\ChaosUserDataPT\ChaosUserDataPT.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\ChaosVehiclesPlugin\Source\ChaosVehiclesEditor\ChaosVehiclesEditor.Build.cs"><Link>Plugins\Experimental\ChaosVehiclesPlugin\Source\ChaosVehiclesEditor\ChaosVehiclesEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\ChaosVehiclesPlugin\Source\ChaosVehicles\ChaosVehicles.Build.cs"><Link>Plugins\Experimental\ChaosVehiclesPlugin\Source\ChaosVehicles\ChaosVehicles.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\CharacterAI\Source\CharacterAI\CharacterAI.Build.cs"><Link>Plugins\Experimental\CharacterAI\Source\CharacterAI\CharacterAI.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\CharacterFXEditor\BaseCharacterFXEditor\Source\BaseCharacterFXEditor\BaseCharacterFXEditor.Build.cs"><Link>Plugins\Experimental\CharacterFXEditor\BaseCharacterFXEditor\Source\BaseCharacterFXEditor\BaseCharacterFXEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\CharacterFXEditor\ExampleCharacterFXEditor\Source\ExampleCharacterFXEditor\ExampleCharacterFXEditor.Build.cs"><Link>Plugins\Experimental\CharacterFXEditor\ExampleCharacterFXEditor\Source\ExampleCharacterFXEditor\ExampleCharacterFXEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Chooser\Source\ChooserEditor\ChooserEditor.Build.cs"><Link>Plugins\Experimental\Chooser\Source\ChooserEditor\ChooserEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Chooser\Source\ChooserUncooked\ChooserUncooked.Build.cs"><Link>Plugins\Experimental\Chooser\Source\ChooserUncooked\ChooserUncooked.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Chooser\Source\Chooser\Chooser.Build.cs"><Link>Plugins\Experimental\Chooser\Source\Chooser\Chooser.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Chooser\Source\ProxyTableEditor\ProxyTableEditor.Build.cs"><Link>Plugins\Experimental\Chooser\Source\ProxyTableEditor\ProxyTableEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Chooser\Source\ProxyTableUncooked\ProxyTableUncooked.Build.cs"><Link>Plugins\Experimental\Chooser\Source\ProxyTableUncooked\ProxyTableUncooked.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Chooser\Source\ProxyTable\ProxyTable.Build.cs"><Link>Plugins\Experimental\Chooser\Source\ProxyTable\ProxyTable.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\CineCameraRigs\Source\CineCameraRigsEditor\CineCameraRigsEditor.Build.cs"><Link>Plugins\Experimental\CineCameraRigs\Source\CineCameraRigsEditor\CineCameraRigsEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\CineCameraRigs\Source\CineCameraRigs\CineCameraRigs.Build.cs"><Link>Plugins\Experimental\CineCameraRigs\Source\CineCameraRigs\CineCameraRigs.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\CineCameraSceneCapture\Source\CineCameraSceneCapture\CineCameraSceneCapture.Build.cs"><Link>Plugins\Experimental\CineCameraSceneCapture\Source\CineCameraSceneCapture\CineCameraSceneCapture.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\CinematicPrestreaming\Source\CinematicPrestreamingEditor\CinematicPrestreamingEditor.Build.cs"><Link>Plugins\Experimental\CinematicPrestreaming\Source\CinematicPrestreamingEditor\CinematicPrestreamingEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\CinematicPrestreaming\Source\CinematicPrestreaming\CinematicPrestreaming.Build.cs"><Link>Plugins\Experimental\CinematicPrestreaming\Source\CinematicPrestreaming\CinematicPrestreaming.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\ClonerEffector\Source\ClonerEffectorEditor\ClonerEffectorEditor.Build.cs"><Link>Plugins\Experimental\ClonerEffector\Source\ClonerEffectorEditor\ClonerEffectorEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\ClonerEffector\Source\ClonerEffector\ClonerEffector.Build.cs"><Link>Plugins\Experimental\ClonerEffector\Source\ClonerEffector\ClonerEffector.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\CodeEditor\Source\CodeEditor\CodeEditor.Build.cs"><Link>Plugins\Experimental\CodeEditor\Source\CodeEditor\CodeEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\CodeView\Source\CodeView\CodeView.Build.cs"><Link>Plugins\Experimental\CodeView\Source\CodeView\CodeView.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\ColorCorrectRegions\Source\ColorCorrectRegionsEditor\ColorCorrectRegionsEditor.Build.cs"><Link>Plugins\Experimental\ColorCorrectRegions\Source\ColorCorrectRegionsEditor\ColorCorrectRegionsEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\ColorCorrectRegions\Source\ColorCorrectRegions\ColorCorrectRegions.Build.cs"><Link>Plugins\Experimental\ColorCorrectRegions\Source\ColorCorrectRegions\ColorCorrectRegions.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\CommonConversation\Source\CommonConversationEditor\CommonConversationEditor.Build.cs"><Link>Plugins\Experimental\CommonConversation\Source\CommonConversationEditor\CommonConversationEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\CommonConversation\Source\CommonConversationGraph\CommonConversationGraph.Build.cs"><Link>Plugins\Experimental\CommonConversation\Source\CommonConversationGraph\CommonConversationGraph.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\CommonConversation\Source\CommonConversationRuntime\CommonConversationRuntime.Build.cs"><Link>Plugins\Experimental\CommonConversation\Source\CommonConversationRuntime\CommonConversationRuntime.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\ControlFlows\Source\ControlFlows\ControlFlows.Build.cs"><Link>Plugins\Experimental\ControlFlows\Source\ControlFlows\ControlFlows.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\CustomDetailsView\Source\CustomDetailsView\CustomDetailsView.Build.cs"><Link>Plugins\Experimental\CustomDetailsView\Source\CustomDetailsView\CustomDetailsView.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Dataflow\Source\DataflowAssetTools\DataflowAssetTools.Build.cs"><Link>Plugins\Experimental\Dataflow\Source\DataflowAssetTools\DataflowAssetTools.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Dataflow\Source\DataflowEditor\DataflowEditor.Build.cs"><Link>Plugins\Experimental\Dataflow\Source\DataflowEditor\DataflowEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Dataflow\Source\DataflowEnginePlugin\DataflowEnginePlugin.Build.cs"><Link>Plugins\Experimental\Dataflow\Source\DataflowEnginePlugin\DataflowEnginePlugin.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Dataflow\Source\DataflowNodes\DataflowNodes.Build.cs"><Link>Plugins\Experimental\Dataflow\Source\DataflowNodes\DataflowNodes.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\DatasmithCloTranslator\Source\DatasmithCloTranslator\DatasmithCloTranslator.Build.cs"><Link>Plugins\Experimental\DatasmithCloTranslator\Source\DatasmithCloTranslator\DatasmithCloTranslator.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\DefaultInstallBundleManager\Source\DefaultInstallBundleManager.Build.cs"><Link>Plugins\Experimental\DefaultInstallBundleManager\Source\DefaultInstallBundleManager.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\DynamicMaterial\Source\DynamicMaterialEditor\DynamicMaterialEditor.Build.cs"><Link>Plugins\Experimental\DynamicMaterial\Source\DynamicMaterialEditor\DynamicMaterialEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\DynamicMaterial\Source\DynamicMaterial\DynamicMaterial.Build.cs"><Link>Plugins\Experimental\DynamicMaterial\Source\DynamicMaterial\DynamicMaterial.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Enterprise\DataprepGeometryOperations\Source\DataprepGeometryOperations.Build.cs"><Link>Plugins\Experimental\Enterprise\DataprepGeometryOperations\Source\DataprepGeometryOperations.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Enterprise\DatasmithInterchange\Source\DatasmithInterchange\DatasmithInterchange.Build.cs"><Link>Plugins\Experimental\Enterprise\DatasmithInterchange\Source\DatasmithInterchange\DatasmithInterchange.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Enterprise\DatasmithRuntime\Source\DatasmithRuntime.Build.cs"><Link>Plugins\Experimental\Enterprise\DatasmithRuntime\Source\DatasmithRuntime.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\FieldSystemPlugin\Source\FieldSyStemEditor\FieldSystemEditor.Build.cs"><Link>Plugins\Experimental\FieldSystemPlugin\Source\FieldSyStemEditor\FieldSystemEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\FloatingProperties\Source\FloatingProperties\FloatingProperties.Build.cs"><Link>Plugins\Experimental\FloatingProperties\Source\FloatingProperties\FloatingProperties.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Fracture\Source\FractureEngine\FractureEngine.Build.cs"><Link>Plugins\Experimental\Fracture\Source\FractureEngine\FractureEngine.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\FullBodyIK\Source\FullBodyIK\FullBodyIK.Build.cs"><Link>Plugins\Experimental\FullBodyIK\Source\FullBodyIK\FullBodyIK.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\FullBodyIK\Source\PBIK\PBIK.Build.cs"><Link>Plugins\Experimental\FullBodyIK\Source\PBIK\PBIK.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\GPULightmass\Source\GPULightmassEditor\GPULightmassEditor.Build.cs"><Link>Plugins\Experimental\GPULightmass\Source\GPULightmassEditor\GPULightmassEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\GPULightmass\Source\GPULightmass\GPULightmass.Build.cs"><Link>Plugins\Experimental\GPULightmass\Source\GPULightmass\GPULightmass.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\GameInputWindows\Source\GameInputWindows\GameInputWindows.build.cs"><Link>Plugins\Experimental\GameInputWindows\Source\GameInputWindows\GameInputWindows.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\GameplayBehaviors\Source\GameplayBehaviorsEditorModule\GameplayBehaviorsEditorModule.Build.cs"><Link>Plugins\Experimental\GameplayBehaviors\Source\GameplayBehaviorsEditorModule\GameplayBehaviorsEditorModule.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\GameplayBehaviors\Source\GameplayBehaviorsModule\GameplayBehaviorsModule.Build.cs"><Link>Plugins\Experimental\GameplayBehaviors\Source\GameplayBehaviorsModule\GameplayBehaviorsModule.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\GameplayBehaviors\Source\GameplayBehaviorsTestSuite\GameplayBehaviorsTestSuite.Build.cs"><Link>Plugins\Experimental\GameplayBehaviors\Source\GameplayBehaviorsTestSuite\GameplayBehaviorsTestSuite.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\GameplayGraph\Source\GameplayGraph\GameplayGraph.Build.cs"><Link>Plugins\Experimental\GameplayGraph\Source\GameplayGraph\GameplayGraph.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\GameplayTargetingSystem\Source\GameplayTargetingSystem\TargetingSystem.Build.cs"><Link>Plugins\Experimental\GameplayTargetingSystem\Source\GameplayTargetingSystem\TargetingSystem.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Gauntlet\Source\Gauntlet\Gauntlet.Build.cs"><Link>Plugins\Experimental\Gauntlet\Source\Gauntlet\Gauntlet.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\GeometryCacheAbcFile\Source\GeometryCacheAbcFile\GeometryCacheAbcFile.Build.cs"><Link>Plugins\Experimental\GeometryCacheAbcFile\Source\GeometryCacheAbcFile\GeometryCacheAbcFile.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\GeometryCollectionPlugin\Source\GeometryCollectionEditor\GeometryCollectionEditor.Build.cs"><Link>Plugins\Experimental\GeometryCollectionPlugin\Source\GeometryCollectionEditor\GeometryCollectionEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\GeometryCollectionPlugin\Source\GeometryCollectionNodes\GeometryCollectionNodes.Build.cs"><Link>Plugins\Experimental\GeometryCollectionPlugin\Source\GeometryCollectionNodes\GeometryCollectionNodes.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\GeometryCollectionPlugin\Source\GeometryCollectionSequencer\GeometryCollectionSequencer.Build.cs"><Link>Plugins\Experimental\GeometryCollectionPlugin\Source\GeometryCollectionSequencer\GeometryCollectionSequencer.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\GeometryCollectionPlugin\Source\GeometryCollectionTracks\GeometryCollectionTracks.Build.cs"><Link>Plugins\Experimental\GeometryCollectionPlugin\Source\GeometryCollectionTracks\GeometryCollectionTracks.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\GeometryFlow\Source\GeometryFlowCore\GeometryFlowCore.Build.cs"><Link>Plugins\Experimental\GeometryFlow\Source\GeometryFlowCore\GeometryFlowCore.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\GeometryFlow\Source\GeometryFlowMeshProcessingEditor\GeometryFlowMeshProcessingEditor.Build.cs"><Link>Plugins\Experimental\GeometryFlow\Source\GeometryFlowMeshProcessingEditor\GeometryFlowMeshProcessingEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\GeometryFlow\Source\GeometryFlowMeshProcessing\GeometryFlowMeshProcessing.Build.cs"><Link>Plugins\Experimental\GeometryFlow\Source\GeometryFlowMeshProcessing\GeometryFlowMeshProcessing.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\GeometryMask\Source\GeometryMaskEditor\GeometryMaskEditor.Build.cs"><Link>Plugins\Experimental\GeometryMask\Source\GeometryMaskEditor\GeometryMaskEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\GeometryMask\Source\GeometryMask\GeometryMask.Build.cs"><Link>Plugins\Experimental\GeometryMask\Source\GeometryMask\GeometryMask.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\GizmoEdMode\Source\GizmoEdMode\GizmoEdMode.Build.cs"><Link>Plugins\Experimental\GizmoEdMode\Source\GizmoEdMode\GizmoEdMode.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\GizmoEdMode\Source\LightGizmos\LightGizmos.Build.cs"><Link>Plugins\Experimental\GizmoEdMode\Source\LightGizmos\LightGizmos.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\GizmoFramework\Source\GizmoSettings\GizmoSettings.Build.cs"><Link>Plugins\Experimental\GizmoFramework\Source\GizmoSettings\GizmoSettings.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\HairCardGenerator\Source\HairCardGeneratorEditor\HairCardGeneratorEditor.Build.cs"><Link>Plugins\Experimental\HairCardGenerator\Source\HairCardGeneratorEditor\HairCardGeneratorEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\HairModelingToolset\Source\HairModelingToolset\HairModelingToolset.Build.cs"><Link>Plugins\Experimental\HairModelingToolset\Source\HairModelingToolset\HairModelingToolset.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\ImagePlate\Source\ImagePlateEditor\ImagePlateEditor.Build.cs"><Link>Plugins\Experimental\ImagePlate\Source\ImagePlateEditor\ImagePlateEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\ImagePlate\Source\ImagePlate\ImagePlate.Build.cs"><Link>Plugins\Experimental\ImagePlate\Source\ImagePlate\ImagePlate.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\InstanceDataObjectFixupTool\Source\InstanceDataObjectFixupTool.Build.cs"><Link>Plugins\Experimental\InstanceDataObjectFixupTool\Source\InstanceDataObjectFixupTool.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Iris\Source\Iris\Iris.Build.cs"><Link>Plugins\Experimental\Iris\Source\Iris\Iris.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\JWT\Source\JWT\JWT.Build.cs"><Link>Plugins\Experimental\JWT\Source\JWT\JWT.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Landmass\Source\Editor\LandmassEditor.Build.cs"><Link>Plugins\Experimental\Landmass\Source\Editor\LandmassEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Landmass\Source\Runtime\Landmass.Build.cs"><Link>Plugins\Experimental\Landmass\Source\Runtime\Landmass.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\LandscapePatch\Source\LandscapePatchEditorOnly\LandscapePatchEditorOnly.Build.cs"><Link>Plugins\Experimental\LandscapePatch\Source\LandscapePatchEditorOnly\LandscapePatchEditorOnly.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\LandscapePatch\Source\LandscapePatch\LandscapePatch.Build.cs"><Link>Plugins\Experimental\LandscapePatch\Source\LandscapePatch\LandscapePatch.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\LearningAgents\Source\LearningAgentsTraining\LearningAgentsTraining.Build.cs"><Link>Plugins\Experimental\LearningAgents\Source\LearningAgentsTraining\LearningAgentsTraining.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\LearningAgents\Source\LearningAgents\LearningAgents.Build.cs"><Link>Plugins\Experimental\LearningAgents\Source\LearningAgents\LearningAgents.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\LearningAgents\Source\LearningTraining\LearningTraining.Build.cs"><Link>Plugins\Experimental\LearningAgents\Source\LearningTraining\LearningTraining.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\LearningAgents\Source\Learning\Learning.Build.cs"><Link>Plugins\Experimental\LearningAgents\Source\Learning\Learning.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\LiveLinkControlRig\Source\LiveLinkControlRig\LiveLinkControlRig.build.cs"><Link>Plugins\Experimental\LiveLinkControlRig\Source\LiveLinkControlRig\LiveLinkControlRig.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\LiveLinkFaceImporter\Source\LiveLinkFaceImporter\LiveLinkFaceImporter.Build.cs"><Link>Plugins\Experimental\LiveLinkFaceImporter\Source\LiveLinkFaceImporter\LiveLinkFaceImporter.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\LocalizableMessage\Source\LocalizableMessageBlueprint\LocalizableMessageBlueprint.Build.cs"><Link>Plugins\Experimental\LocalizableMessage\Source\LocalizableMessageBlueprint\LocalizableMessageBlueprint.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\LocalizableMessage\Source\LocalizableMessage\LocalizableMessage.Build.cs"><Link>Plugins\Experimental\LocalizableMessage\Source\LocalizableMessage\LocalizableMessage.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\MeshModelingToolsetExp\Source\GeometryProcessingAdapters\GeometryProcessingAdapters.Build.cs"><Link>Plugins\Experimental\MeshModelingToolsetExp\Source\GeometryProcessingAdapters\GeometryProcessingAdapters.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\MeshModelingToolsetExp\Source\MeshModelingToolsEditorOnlyExp\MeshModelingToolsEditorOnlyExp.Build.cs"><Link>Plugins\Experimental\MeshModelingToolsetExp\Source\MeshModelingToolsEditorOnlyExp\MeshModelingToolsEditorOnlyExp.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\MeshModelingToolsetExp\Source\MeshModelingToolsExp\MeshModelingToolsExp.Build.cs"><Link>Plugins\Experimental\MeshModelingToolsetExp\Source\MeshModelingToolsExp\MeshModelingToolsExp.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\MeshModelingToolsetExp\Source\ModelingEditorUI\ModelingEditorUI.build.cs"><Link>Plugins\Experimental\MeshModelingToolsetExp\Source\ModelingEditorUI\ModelingEditorUI.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\MeshModelingToolsetExp\Source\SkeletalMeshModifiers\SkeletalMeshModifiers.build.cs"><Link>Plugins\Experimental\MeshModelingToolsetExp\Source\SkeletalMeshModifiers\SkeletalMeshModifiers.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\MetaHuman\MetaHumanProjectUtilities\Source\MetaHumanProjectUtilities\MetaHumanProjectUtilities.Build.cs"><Link>Plugins\Experimental\MetaHuman\MetaHumanProjectUtilities\Source\MetaHumanProjectUtilities\MetaHumanProjectUtilities.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\MotoSynth\Source\MotoSynthEditor\MotoSynthEditor.Build.cs"><Link>Plugins\Experimental\MotoSynth\Source\MotoSynthEditor\MotoSynthEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\MotoSynth\Source\MotoSynth\MotoSynth.Build.cs"><Link>Plugins\Experimental\MotoSynth\Source\MotoSynth\MotoSynth.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\MotorSimOutputMotoSynth\Source\MotorSimOutputMotoSynth\MotorSimOutputMotoSynth.build.cs"><Link>Plugins\Experimental\MotorSimOutputMotoSynth\Source\MotorSimOutputMotoSynth\MotorSimOutputMotoSynth.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\MoverExamples\Source\MoverExamples\MoverExamples.Build.cs"><Link>Plugins\Experimental\MoverExamples\Source\MoverExamples\MoverExamples.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\MoverTests\Source\MoverTests\MoverTests.Build.cs"><Link>Plugins\Experimental\MoverTests\Source\MoverTests\MoverTests.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Mover\Source\MoverEditor\MoverEditor.Build.cs"><Link>Plugins\Experimental\Mover\Source\MoverEditor\MoverEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Mover\Source\Mover\Mover.Build.cs"><Link>Plugins\Experimental\Mover\Source\Mover\Mover.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Mutable\Source\CustomizableObjectEditor\CustomizableObjectEditor.Build.cs"><Link>Plugins\Experimental\Mutable\Source\CustomizableObjectEditor\CustomizableObjectEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Mutable\Source\CustomizableObjectPopulationEditor\CustomizableObjectPopulationEditor.Build.cs"><Link>Plugins\Experimental\Mutable\Source\CustomizableObjectPopulationEditor\CustomizableObjectPopulationEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Mutable\Source\CustomizableObjectPopulation\CustomizableObjectPopulation.Build.cs"><Link>Plugins\Experimental\Mutable\Source\CustomizableObjectPopulation\CustomizableObjectPopulation.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Mutable\Source\CustomizableObject\CustomizableObject.Build.cs"><Link>Plugins\Experimental\Mutable\Source\CustomizableObject\CustomizableObject.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Mutable\Source\MutableRuntime\MutableRuntime.Build.cs"><Link>Plugins\Experimental\Mutable\Source\MutableRuntime\MutableRuntime.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Mutable\Source\MutableTools\MutableTools.Build.cs"><Link>Plugins\Experimental\Mutable\Source\MutableTools\MutableTools.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Mutable\Source\MutableValidation\MutableValidation.Build.cs"><Link>Plugins\Experimental\Mutable\Source\MutableValidation\MutableValidation.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\NNEDenoiser\Source\NNEDenoiserShaders\NNEDenoiserShaders.Build.cs"><Link>Plugins\Experimental\NNEDenoiser\Source\NNEDenoiserShaders\NNEDenoiserShaders.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\NNEDenoiser\Source\NNEDenoiser\NNEDenoiser.Build.cs"><Link>Plugins\Experimental\NNEDenoiser\Source\NNEDenoiser\NNEDenoiser.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\NNERuntimeBasicCpu\Source\NNERuntimeBasicCpu\NNERuntimeBasicCpu.Build.cs"><Link>Plugins\Experimental\NNERuntimeBasicCpu\Source\NNERuntimeBasicCpu\NNERuntimeBasicCpu.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\NNERuntimeIREE\Source\NNERuntimeIREEEditor\NNERuntimeIREEEditor.Build.cs"><Link>Plugins\Experimental\NNERuntimeIREE\Source\NNERuntimeIREEEditor\NNERuntimeIREEEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\NNERuntimeIREE\Source\NNERuntimeIREE\NNERuntimeIREE.Build.cs"><Link>Plugins\Experimental\NNERuntimeIREE\Source\NNERuntimeIREE\NNERuntimeIREE.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\NNERuntimeIREE\Source\ThirdParty\IREE\IREE.Build.cs"><Link>Plugins\Experimental\NNERuntimeIREE\Source\ThirdParty\IREE\IREE.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\NNERuntimeRDG\Source\NNEHlslShaders\NNEHlslShaders.Build.cs"><Link>Plugins\Experimental\NNERuntimeRDG\Source\NNEHlslShaders\NNEHlslShaders.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\NNERuntimeRDG\Source\NNERuntimeRDG\NNERuntimeRDG.Build.cs"><Link>Plugins\Experimental\NNERuntimeRDG\Source\NNERuntimeRDG\NNERuntimeRDG.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\NaniteDisplacedMesh\Source\NaniteDisplacedMeshEditor\NaniteDisplacedMeshEditor.Build.cs"><Link>Plugins\Experimental\NaniteDisplacedMesh\Source\NaniteDisplacedMeshEditor\NaniteDisplacedMeshEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\NaniteDisplacedMesh\Source\NaniteDisplacedMesh\NaniteDisplacedMesh.Build.cs"><Link>Plugins\Experimental\NaniteDisplacedMesh\Source\NaniteDisplacedMesh\NaniteDisplacedMesh.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\NeuralRendering\Source\NeuralPostProcessing\NeuralPostProcessing.Build.cs"><Link>Plugins\Experimental\NeuralRendering\Source\NeuralPostProcessing\NeuralPostProcessing.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\OpenImageDenoise\Source\OpenImageDenoise\OpenImageDenoise.Build.cs"><Link>Plugins\Experimental\OpenImageDenoise\Source\OpenImageDenoise\OpenImageDenoise.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\OperatorStack\Source\OperatorStack\OperatorStackEditor.Build.cs"><Link>Plugins\Experimental\OperatorStack\Source\OperatorStack\OperatorStackEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\OptiXDenoise\Source\OptiXDenoise\OptixDenoise.Build.cs"><Link>Plugins\Experimental\OptiXDenoise\Source\OptiXDenoise\OptixDenoise.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\OptiXDenoise\Source\ThirdParty\OptiXDenoiseBase\OptiXDenoiseBase.Build.cs"><Link>Plugins\Experimental\OptiXDenoise\Source\ThirdParty\OptiXDenoiseBase\OptiXDenoiseBase.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\PCGBiomeCore\Source\PCGBiomeCore\PCGBiomeCore.Build.cs"><Link>Plugins\Experimental\PCGBiomeCore\Source\PCGBiomeCore\PCGBiomeCore.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\PCGWaterInterop\Source\PCGWaterInterop\PCGWaterInterop.Build.cs"><Link>Plugins\Experimental\PCGWaterInterop\Source\PCGWaterInterop\PCGWaterInterop.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\PanoramicCapture\Source\PanoramicCapture\PanoramicCapture.Build.cs"><Link>Plugins\Experimental\PanoramicCapture\Source\PanoramicCapture\PanoramicCapture.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\PhysicsControl\Source\PhysicsControlEditor\PhysicsControlEditor.Build.cs"><Link>Plugins\Experimental\PhysicsControl\Source\PhysicsControlEditor\PhysicsControlEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\PhysicsControl\Source\PhysicsControlUncookedOnly\PhysicsControlUncookedOnly.Build.cs"><Link>Plugins\Experimental\PhysicsControl\Source\PhysicsControlUncookedOnly\PhysicsControlUncookedOnly.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\PhysicsControl\Source\PhysicsControl\PhysicsControl.Build.cs"><Link>Plugins\Experimental\PhysicsControl\Source\PhysicsControl\PhysicsControl.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\PixelStreamingPlayer\Source\PixelStreamingPlayerEditor\PixelStreamingPlayerEditor.Build.cs"><Link>Plugins\Experimental\PixelStreamingPlayer\Source\PixelStreamingPlayerEditor\PixelStreamingPlayerEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\PixelStreamingPlayer\Source\PixelStreamingPlayer\PixelStreamingPlayer.Build.cs"><Link>Plugins\Experimental\PixelStreamingPlayer\Source\PixelStreamingPlayer\PixelStreamingPlayer.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\PlanarCutPlugin\Source\PlanarCut\PlanarCut.Build.cs"><Link>Plugins\Experimental\PlanarCutPlugin\Source\PlanarCut\PlanarCut.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\PlatformCrypto\Source\PlatformCryptoOpenSSL\PlatformCryptoOpenSSL.Build.cs"><Link>Plugins\Experimental\PlatformCrypto\Source\PlatformCryptoOpenSSL\PlatformCryptoOpenSSL.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\PlatformCrypto\Source\PlatformCryptoTypes\PlatformCryptoTypes.Build.cs"><Link>Plugins\Experimental\PlatformCrypto\Source\PlatformCryptoTypes\PlatformCryptoTypes.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\PlatformCrypto\Source\PlatformCrypto\PlatformCrypto.Build.cs"><Link>Plugins\Experimental\PlatformCrypto\Source\PlatformCrypto\PlatformCrypto.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\PluginAudit\Source\PluginAudit.Build.cs"><Link>Plugins\Experimental\PluginAudit\Source\PluginAudit.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\PluginReferenceViewer\Source\PluginReferenceViewer\PluginReferenceViewer.Build.cs"><Link>Plugins\Experimental\PluginReferenceViewer\Source\PluginReferenceViewer\PluginReferenceViewer.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\PluginTemplateTool\Source\PluginTemplateTool.Build.cs"><Link>Plugins\Experimental\PluginTemplateTool\Source\PluginTemplateTool.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\PostProcessMaterialChainGraph\Source\PostProcessMaterialChainGraphEditor\PPMChainGraphEditor.Build.cs"><Link>Plugins\Experimental\PostProcessMaterialChainGraph\Source\PostProcessMaterialChainGraphEditor\PPMChainGraphEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\PostProcessMaterialChainGraph\Source\PostProcessMaterialChainGraph\PPMChainGraph.Build.cs"><Link>Plugins\Experimental\PostProcessMaterialChainGraph\Source\PostProcessMaterialChainGraph\PPMChainGraph.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\PropertyAnimatorCore\Source\PropertyAnimatorCoreEditor\PropertyAnimatorCoreEditor.Build.cs"><Link>Plugins\Experimental\PropertyAnimatorCore\Source\PropertyAnimatorCoreEditor\PropertyAnimatorCoreEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\PropertyAnimatorCore\Source\PropertyAnimatorCore\PropertyAnimatorCore.Build.cs"><Link>Plugins\Experimental\PropertyAnimatorCore\Source\PropertyAnimatorCore\PropertyAnimatorCore.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\PropertyAnimator\Source\PropertyAnimatorEditor\PropertyAnimatorEditor.Build.cs"><Link>Plugins\Experimental\PropertyAnimator\Source\PropertyAnimatorEditor\PropertyAnimatorEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\PropertyAnimator\Source\PropertyAnimator\PropertyAnimator.Build.cs"><Link>Plugins\Experimental\PropertyAnimator\Source\PropertyAnimator\PropertyAnimator.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\PythonScriptPlugin\Source\PythonScriptPluginPreload\PythonScriptPluginPreload.Build.cs"><Link>Plugins\Experimental\PythonScriptPlugin\Source\PythonScriptPluginPreload\PythonScriptPluginPreload.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\PythonScriptPlugin\Source\PythonScriptPlugin\PythonScriptPlugin.Build.cs"><Link>Plugins\Experimental\PythonScriptPlugin\Source\PythonScriptPlugin\PythonScriptPlugin.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\QuicMessaging\Source\QuicMessagingTransport\QuicMessagingTransport.Build.cs"><Link>Plugins\Experimental\QuicMessaging\Source\QuicMessagingTransport\QuicMessagingTransport.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\QuicMessaging\Source\QuicMessaging\QuicMessaging.Build.cs"><Link>Plugins\Experimental\QuicMessaging\Source\QuicMessaging\QuicMessaging.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\RawInput\Source\RawInput\RawInput.Build.cs"><Link>Plugins\Experimental\RawInput\Source\RawInput\RawInput.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\RemoteControlComponents\Source\RemoteControlComponentsEditor\RemoteControlComponentsEditor.Build.cs"><Link>Plugins\Experimental\RemoteControlComponents\Source\RemoteControlComponentsEditor\RemoteControlComponentsEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\RemoteControlComponents\Source\RemoteControlComponents\RemoteControlComponents.Build.cs"><Link>Plugins\Experimental\RemoteControlComponents\Source\RemoteControlComponents\RemoteControlComponents.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\RemoteSession\Source\RemoteSessionEditor\RemoteSessionEditor.Build.cs"><Link>Plugins\Experimental\RemoteSession\Source\RemoteSessionEditor\RemoteSessionEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\RemoteSession\Source\RemoteSession\RemoteSession.Build.cs"><Link>Plugins\Experimental\RemoteSession\Source\RemoteSession\RemoteSession.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\RenderGrid\Source\RenderGridDeveloper\RenderGridDeveloper.Build.cs"><Link>Plugins\Experimental\RenderGrid\Source\RenderGridDeveloper\RenderGridDeveloper.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\RenderGrid\Source\RenderGridEditor\RenderGridEditor.Build.cs"><Link>Plugins\Experimental\RenderGrid\Source\RenderGridEditor\RenderGridEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\RenderGrid\Source\RenderGrid\RenderGrid.Build.cs"><Link>Plugins\Experimental\RenderGrid\Source\RenderGrid\RenderGrid.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\SVGImporter\Source\SVGImporterEditor\SVGImporterEditor.Build.cs"><Link>Plugins\Experimental\SVGImporter\Source\SVGImporterEditor\SVGImporterEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\SVGImporter\Source\SVGImporter\SVGImporter.Build.cs"><Link>Plugins\Experimental\SVGImporter\Source\SVGImporter\SVGImporter.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\ScreenReader\Source\ScreenReader\ScreenReader.Build.cs"><Link>Plugins\Experimental\ScreenReader\Source\ScreenReader\ScreenReader.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\SecuritySandbox\Source\SecuritySandbox\SecuritySandbox.Build.cs"><Link>Plugins\Experimental\SecuritySandbox\Source\SecuritySandbox\SecuritySandbox.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\SerializationUtils\Source\JsonSerialization\JsonSerialization.Build.cs"><Link>Plugins\Experimental\SerializationUtils\Source\JsonSerialization\JsonSerialization.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\SerializationUtils\Source\XmlSerialization\XmlSerialization.Build.cs"><Link>Plugins\Experimental\SerializationUtils\Source\XmlSerialization\XmlSerialization.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Shotgrid\Source\Shotgrid\Shotgrid.Build.cs"><Link>Plugins\Experimental\Shotgrid\Source\Shotgrid\Shotgrid.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\SimpleHMD\Source\SimpleHMD\SimpleHMD.Build.cs"><Link>Plugins\Experimental\SimpleHMD\Source\SimpleHMD\SimpleHMD.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\SkeletalReduction\Source\SkeletalMeshReduction.Build.cs"><Link>Plugins\Experimental\SkeletalReduction\Source\SkeletalMeshReduction.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\SlateModelViewViewModel\Source\SlateMVVM\SlateMVVM.Build.cs"><Link>Plugins\Experimental\SlateModelViewViewModel\Source\SlateMVVM\SlateMVVM.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\SlateScreenReader\Source\SlateScreenReader\SlateScreenReader.Build.cs"><Link>Plugins\Experimental\SlateScreenReader\Source\SlateScreenReader\SlateScreenReader.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\StateGraph\Source\StateGraphManager\StateGraphManager.Build.cs"><Link>Plugins\Experimental\StateGraph\Source\StateGraphManager\StateGraphManager.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\StateGraph\Source\StateGraph\StateGraph.Build.cs"><Link>Plugins\Experimental\StateGraph\Source\StateGraph\StateGraph.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\StormSyncAvalancheBridge\Source\StormSyncAvaBridgeEditor\StormSyncAvaBridgeEditor.Build.cs"><Link>Plugins\Experimental\StormSyncAvalancheBridge\Source\StormSyncAvaBridgeEditor\StormSyncAvaBridgeEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\StormSyncAvalancheBridge\Source\StormSyncAvaBridge\StormSyncAvaBridge.Build.cs"><Link>Plugins\Experimental\StormSyncAvalancheBridge\Source\StormSyncAvaBridge\StormSyncAvaBridge.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\StormSync\Source\StormSyncCore\StormSyncCore.Build.cs"><Link>Plugins\Experimental\StormSync\Source\StormSyncCore\StormSyncCore.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\StormSync\Source\StormSyncDrives\StormSyncDrives.Build.cs"><Link>Plugins\Experimental\StormSync\Source\StormSyncDrives\StormSyncDrives.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\StormSync\Source\StormSyncEditor\StormSyncEditor.Build.cs"><Link>Plugins\Experimental\StormSync\Source\StormSyncEditor\StormSyncEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\StormSync\Source\StormSyncImport\StormSyncImport.Build.cs"><Link>Plugins\Experimental\StormSync\Source\StormSyncImport\StormSyncImport.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\StormSync\Source\StormSyncTests\StormSyncTests.Build.cs"><Link>Plugins\Experimental\StormSync\Source\StormSyncTests\StormSyncTests.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\StormSync\Source\StormSyncTransportClient\StormSyncTransportClient.Build.cs"><Link>Plugins\Experimental\StormSync\Source\StormSyncTransportClient\StormSyncTransportClient.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\StormSync\Source\StormSyncTransportCore\StormSyncTransportCore.Build.cs"><Link>Plugins\Experimental\StormSync\Source\StormSyncTransportCore\StormSyncTransportCore.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\StormSync\Source\StormSyncTransportServer\StormSyncTransportServer.Build.cs"><Link>Plugins\Experimental\StormSync\Source\StormSyncTransportServer\StormSyncTransportServer.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\StructUtils\Source\StructUtilsEditor\StructUtilsEditor.Build.cs"><Link>Plugins\Experimental\StructUtils\Source\StructUtilsEditor\StructUtilsEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\StructUtils\Source\StructUtilsEngine\StructUtilsEngine.Build.cs"><Link>Plugins\Experimental\StructUtils\Source\StructUtilsEngine\StructUtilsEngine.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\StructUtils\Source\StructUtilsNodes\StructUtilsNodes.Build.cs"><Link>Plugins\Experimental\StructUtils\Source\StructUtilsNodes\StructUtilsNodes.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\StructUtils\Source\StructUtilsTestSuite\StructUtilsTestSuite.Build.cs"><Link>Plugins\Experimental\StructUtils\Source\StructUtilsTestSuite\StructUtilsTestSuite.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\StructUtils\Source\StructUtils\StructUtils.Build.cs"><Link>Plugins\Experimental\StructUtils\Source\StructUtils\StructUtils.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\StudioTelemetry\Source\AnalyticsHorde\AnalyticsHorde.Build.cs"><Link>Plugins\Experimental\StudioTelemetry\Source\AnalyticsHorde\AnalyticsHorde.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\StudioTelemetry\Source\AnalyticsLog\AnalyticsLog.Build.cs"><Link>Plugins\Experimental\StudioTelemetry\Source\AnalyticsLog\AnalyticsLog.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\StudioTelemetry\Source\StudioTelemetry\StudioTelemetry.build.cs"><Link>Plugins\Experimental\StudioTelemetry\Source\StudioTelemetry\StudioTelemetry.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\SurfaceEffects\Source\SurfaceEffects\SurfaceEffects.Build.cs"><Link>Plugins\Experimental\SurfaceEffects\Source\SurfaceEffects\SurfaceEffects.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\TargetDeviceServicesScripting\Source\TargetDeviceServicesScripting\TargetDeviceServicesScripting.Build.cs"><Link>Plugins\Experimental\TargetDeviceServicesScripting\Source\TargetDeviceServicesScripting\TargetDeviceServicesScripting.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\TetMeshing\Source\TetMeshing\TetMeshing.Build.cs"><Link>Plugins\Experimental\TetMeshing\Source\TetMeshing\TetMeshing.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Text3D\Source\Text3DEditor\Text3DEditor.Build.cs"><Link>Plugins\Experimental\Text3D\Source\Text3DEditor\Text3DEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Text3D\Source\Text3D\Text3D.Build.cs"><Link>Plugins\Experimental\Text3D\Source\Text3D\Text3D.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\TextToSpeech\Source\TextToSpeech\TextToSpeech.Build.cs"><Link>Plugins\Experimental\TextToSpeech\Source\TextToSpeech\TextToSpeech.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\TextToSpeech\Source\ThirdParty\Flite\Flite.Build.cs"><Link>Plugins\Experimental\TextToSpeech\Source\ThirdParty\Flite\Flite.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\TextureGraph\Source\TextureGraphEditor\TextureGraphEditor.Build.cs"><Link>Plugins\Experimental\TextureGraph\Source\TextureGraphEditor\TextureGraphEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\TextureGraph\Source\TextureGraphEngine\TextureGraphEngine.Build.cs"><Link>Plugins\Experimental\TextureGraph\Source\TextureGraphEngine\TextureGraphEngine.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\TextureGraph\Source\TextureGraphInsightEditor\TextureGraphInsightEditor.Build.cs"><Link>Plugins\Experimental\TextureGraph\Source\TextureGraphInsightEditor\TextureGraphInsightEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\TextureGraph\Source\TextureGraphInsight\TextureGraphInsight.Build.cs"><Link>Plugins\Experimental\TextureGraph\Source\TextureGraphInsight\TextureGraphInsight.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\TextureGraph\Source\TextureGraph\TextureGraph.Build.cs"><Link>Plugins\Experimental\TextureGraph\Source\TextureGraph\TextureGraph.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\TextureGraph\Source\ThirdParty\Continuable.Build.cs"><Link>Plugins\Experimental\TextureGraph\Source\ThirdParty\Continuable.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\TextureGraph\Source\ThirdParty\Function2.Build.cs"><Link>Plugins\Experimental\TextureGraph\Source\ThirdParty\Function2.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\TextureMediaPlayer\Source\TextureMediaPlayerFactory\TextureMediaPlayerFactory.Build.cs"><Link>Plugins\Experimental\TextureMediaPlayer\Source\TextureMediaPlayerFactory\TextureMediaPlayerFactory.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\TextureMediaPlayer\Source\TextureMediaPlayer\TextureMediaPlayer.Build.cs"><Link>Plugins\Experimental\TextureMediaPlayer\Source\TextureMediaPlayer\TextureMediaPlayer.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\ToolPresets\Source\ToolPresetAsset\ToolPresetAsset.Build.cs"><Link>Plugins\Experimental\ToolPresets\Source\ToolPresetAsset\ToolPresetAsset.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\ToolPresets\Source\ToolPresetEditor\ToolPresetEditor.Build.cs"><Link>Plugins\Experimental\ToolPresets\Source\ToolPresetEditor\ToolPresetEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\TypedElementDataStorageEditorUI\Source\TedsOutliner\TedsOutliner.Build.cs"><Link>Plugins\Experimental\TypedElementDataStorageEditorUI\Source\TedsOutliner\TedsOutliner.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\TypedElementDataStorageEditorUI\Source\TedsPropertyEditor\TedsPropertyEditor.Build.cs"><Link>Plugins\Experimental\TypedElementDataStorageEditorUI\Source\TedsPropertyEditor\TedsPropertyEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\TypedElementsDataStorage\Source\TypedElementsDataStorageRevisionControl\TypedElementsDataStorageRevisionControl.Build.CS"><Link>Plugins\Experimental\TypedElementsDataStorage\Source\TypedElementsDataStorageRevisionControl\TypedElementsDataStorageRevisionControl.Build.CS</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\TypedElementsDataStorage\Source\TypedElementsDataStorageUI\TypedElementsDataStorageUI.Build.cs"><Link>Plugins\Experimental\TypedElementsDataStorage\Source\TypedElementsDataStorageUI\TypedElementsDataStorageUI.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\TypedElementsDataStorage\Source\TypedElementsDataStorage\TypedElementsDataStorage.Build.cs"><Link>Plugins\Experimental\TypedElementsDataStorage\Source\TypedElementsDataStorage\TypedElementsDataStorage.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\UIFramework\Source\UIFramework.Build.cs"><Link>Plugins\Experimental\UIFramework\Source\UIFramework.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\UserToolBoxBasicCommand\Source\UserToolBoxBasicCommand\UserToolBoxBasicCommand.Build.cs"><Link>Plugins\Experimental\UserToolBoxBasicCommand\Source\UserToolBoxBasicCommand\UserToolBoxBasicCommand.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\UserToolBoxCore\Source\UserToolBoxCore\UserToolBoxCore.Build.cs"><Link>Plugins\Experimental\UserToolBoxCore\Source\UserToolBoxCore\UserToolBoxCore.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\VirtualHeightfieldMesh\Source\VirtualHeightfieldMeshEditor\VirtualHeightfieldMeshEditor.Build.cs"><Link>Plugins\Experimental\VirtualHeightfieldMesh\Source\VirtualHeightfieldMeshEditor\VirtualHeightfieldMeshEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\VirtualHeightfieldMesh\Source\VirtualHeightfieldMesh\VirtualHeightfieldMesh.Build.cs"><Link>Plugins\Experimental\VirtualHeightfieldMesh\Source\VirtualHeightfieldMesh\VirtualHeightfieldMesh.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\VirtualProductionUtilities\Source\VPBookmarkEditor\VPBookmarkEditor.Build.cs"><Link>Plugins\Experimental\VirtualProductionUtilities\Source\VPBookmarkEditor\VPBookmarkEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\VirtualProductionUtilities\Source\VPBookmark\VPBookmark.Build.cs"><Link>Plugins\Experimental\VirtualProductionUtilities\Source\VPBookmark\VPBookmark.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\VirtualProductionUtilities\Source\VPMaterialsEditor\VPMaterialsEditor.Build.cs"><Link>Plugins\Experimental\VirtualProductionUtilities\Source\VPMaterialsEditor\VPMaterialsEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\VirtualProductionUtilities\Source\VPUtilitiesEditor\VPUtilitiesEditor.Build.cs"><Link>Plugins\Experimental\VirtualProductionUtilities\Source\VPUtilitiesEditor\VPUtilitiesEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\VirtualProductionUtilities\Source\VPUtilities\VPUtilities.Build.cs"><Link>Plugins\Experimental\VirtualProductionUtilities\Source\VPUtilities\VPUtilities.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\VirtualProduction\LedWallCalibration\Source\LedWallCalibrationEditor\LedWallCalibrationEditor.Build.cs"><Link>Plugins\Experimental\VirtualProduction\LedWallCalibration\Source\LedWallCalibrationEditor\LedWallCalibrationEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\VirtualProduction\LedWallCalibration\Source\LedWallCalibration\LedWallCalibration.Build.cs"><Link>Plugins\Experimental\VirtualProduction\LedWallCalibration\Source\LedWallCalibration\LedWallCalibration.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\VirtualProduction\VPRoles\Source\VPRolesEditor\VPRolesEditor.Build.cs"><Link>Plugins\Experimental\VirtualProduction\VPRoles\Source\VPRolesEditor\VPRolesEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\VirtualProduction\VPRoles\Source\VPRoles\VPRoles.Build.cs"><Link>Plugins\Experimental\VirtualProduction\VPRoles\Source\VPRoles\VPRoles.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\VirtualProduction\VPSettings\Source\VPSettings.Build.cs"><Link>Plugins\Experimental\VirtualProduction\VPSettings\Source\VPSettings.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\VirtualScouting\Source\VirtualScoutingEditor\VirtualScoutingEditor.Build.cs"><Link>Plugins\Experimental\VirtualScouting\Source\VirtualScoutingEditor\VirtualScoutingEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\VirtualScouting\Source\VirtualScoutingOpenXR\VirtualScoutingOpenXR.Build.cs"><Link>Plugins\Experimental\VirtualScouting\Source\VirtualScoutingOpenXR\VirtualScoutingOpenXR.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\VirtualScouting\Source\VirtualScouting\VirtualScouting.Build.cs"><Link>Plugins\Experimental\VirtualScouting\Source\VirtualScouting\VirtualScouting.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Water\Source\Editor\WaterEditor.Build.cs"><Link>Plugins\Experimental\Water\Source\Editor\WaterEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Water\Source\Runtime\Water.Build.cs"><Link>Plugins\Experimental\Water\Source\Runtime\Water.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\WaveFunctionCollapse\Source\WaveFunctionCollapse\WaveFunctionCollapse.Build.cs"><Link>Plugins\Experimental\WaveFunctionCollapse\Source\WaveFunctionCollapse\WaveFunctionCollapse.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\WebSocketMessaging\Source\WebSocketMessaging\WebSocketMessaging.Build.cs"><Link>Plugins\Experimental\WebSocketMessaging\Source\WebSocketMessaging\WebSocketMessaging.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\WebSocketNetworking\Source\WebSocketNetworking\WebSocketNetworking.Build.cs"><Link>Plugins\Experimental\WebSocketNetworking\Source\WebSocketNetworking\WebSocketNetworking.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Web\WebAPI\Source\WebAPIBlueprintGraph\WebAPIBlueprintGraph.Build.cs"><Link>Plugins\Experimental\Web\WebAPI\Source\WebAPIBlueprintGraph\WebAPIBlueprintGraph.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Web\WebAPI\Source\WebAPIEditor\WebAPIEditor.build.cs"><Link>Plugins\Experimental\Web\WebAPI\Source\WebAPIEditor\WebAPIEditor.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Web\WebAPI\Source\WebAPILiquidJS\WebAPILiquidJS.Build.cs"><Link>Plugins\Experimental\Web\WebAPI\Source\WebAPILiquidJS\WebAPILiquidJS.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Web\WebAPI\Source\WebAPIOpenAPI\WebAPIOpenAPI.build.cs"><Link>Plugins\Experimental\Web\WebAPI\Source\WebAPIOpenAPI\WebAPIOpenAPI.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\Web\WebAPI\Source\WebAPI\WebAPI.Build.cs"><Link>Plugins\Experimental\Web\WebAPI\Source\WebAPI\WebAPI.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\WidgetEditorToolPalette\Source\WidgetEditorToolPalette\WidgetEditorToolPalette.Build.cs"><Link>Plugins\Experimental\WidgetEditorToolPalette\Source\WidgetEditorToolPalette\WidgetEditorToolPalette.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\XRCreativeFramework\Source\XRCreativeEditor\XRCreativeEditor.Build.cs"><Link>Plugins\Experimental\XRCreativeFramework\Source\XRCreativeEditor\XRCreativeEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Experimental\XRCreativeFramework\Source\XRCreative\XRCreative.Build.cs"><Link>Plugins\Experimental\XRCreativeFramework\Source\XRCreative\XRCreative.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\ExportBPToLevelInstance\Source\ExportBPToLevelInstance\ExportBPToLevelInstance.Build.cs"><Link>Plugins\ExportBPToLevelInstance\Source\ExportBPToLevelInstance\ExportBPToLevelInstance.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\FX\CascadeToNiagaraConverter\Source\CascadeToNiagaraConverter\CascadeToNiagaraConverter.Build.cs"><Link>Plugins\FX\CascadeToNiagaraConverter\Source\CascadeToNiagaraConverter\CascadeToNiagaraConverter.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\FX\ExampleCustomDataInterface\Source\ExampleCustomDataInterface\ExampleCustomDataInterface.Build.cs"><Link>Plugins\FX\ExampleCustomDataInterface\Source\ExampleCustomDataInterface\ExampleCustomDataInterface.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\FX\NiagaraFluids\Source\NiagaraFluids\NiagaraFluids.Build.cs"><Link>Plugins\FX\NiagaraFluids\Source\NiagaraFluids\NiagaraFluids.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\FX\NiagaraMRQ\Source\NiagaraMRQ\NiagaraMRQ.Build.cs"><Link>Plugins\FX\NiagaraMRQ\Source\NiagaraMRQ\NiagaraMRQ.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\FX\NiagaraSimCaching\Source\NiagaraSimCachingEditor\NiagaraSimCachingEditor.Build.cs"><Link>Plugins\FX\NiagaraSimCaching\Source\NiagaraSimCachingEditor\NiagaraSimCachingEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\FX\NiagaraSimCaching\Source\NiagaraSimCaching\NiagaraSimCaching.Build.cs"><Link>Plugins\FX\NiagaraSimCaching\Source\NiagaraSimCaching\NiagaraSimCaching.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\FX\Niagara\Source\NiagaraAnimNotifies\NiagaraAnimNotifies.Build.cs"><Link>Plugins\FX\Niagara\Source\NiagaraAnimNotifies\NiagaraAnimNotifies.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\FX\Niagara\Source\NiagaraBlueprintNodes\NiagaraBlueprintNodes.build.cs"><Link>Plugins\FX\Niagara\Source\NiagaraBlueprintNodes\NiagaraBlueprintNodes.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\FX\Niagara\Source\NiagaraCore\NiagaraCore.Build.cs"><Link>Plugins\FX\Niagara\Source\NiagaraCore\NiagaraCore.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\FX\Niagara\Source\NiagaraEditorWidgets\NiagaraEditorWidgets.Build.cs"><Link>Plugins\FX\Niagara\Source\NiagaraEditorWidgets\NiagaraEditorWidgets.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\FX\Niagara\Source\NiagaraEditor\NiagaraEditor.Build.cs"><Link>Plugins\FX\Niagara\Source\NiagaraEditor\NiagaraEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\FX\Niagara\Source\NiagaraShader\NiagaraShader.Build.cs"><Link>Plugins\FX\Niagara\Source\NiagaraShader\NiagaraShader.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\FX\Niagara\Source\NiagaraVertexFactories\NiagaraVertexFactories.Build.cs"><Link>Plugins\FX\Niagara\Source\NiagaraVertexFactories\NiagaraVertexFactories.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\FX\Niagara\Source\Niagara\Niagara.Build.cs"><Link>Plugins\FX\Niagara\Source\Niagara\Niagara.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\FastBuildController\Source\FastbuildController.Build.cs"><Link>Plugins\FastBuildController\Source\FastbuildController.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\GSR\Source\GSRTUModule\GSRTUModule.Build.cs"><Link>Plugins\GSR\Source\GSRTUModule\GSRTUModule.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\GSR\Source\GSR\GSR.Build.cs"><Link>Plugins\GSR\Source\GSR\GSR.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\GSR\Source\src\src.Build.cs"><Link>Plugins\GSR\Source\src\src.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Garment\Source\GarmentEditor\GarmentEditor.Build.cs"><Link>Plugins\Garment\Source\GarmentEditor\GarmentEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Garment\Source\Garment\Garment.Build.cs"><Link>Plugins\Garment\Source\Garment\Garment.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Garment\Source\WardrobeAnimGraph\WardrobeAnimGraph.Build.cs"><Link>Plugins\Garment\Source\WardrobeAnimGraph\WardrobeAnimGraph.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Garment\Source\WardrobeEditor\WardrobeEditor.Build.cs"><Link>Plugins\Garment\Source\WardrobeEditor\WardrobeEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Garment\Source\Wardrobe\Wardrobe.Build.cs"><Link>Plugins\Garment\Source\Wardrobe\Wardrobe.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\H3DPlugins\Avatar\Source\AvatarPod\AvatarPod.Build.cs"><Link>Plugins\H3DPlugins\Avatar\Source\AvatarPod\AvatarPod.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\H3DPlugins\Avatar\Source\Avatar\Avatar.Build.cs"><Link>Plugins\H3DPlugins\Avatar\Source\Avatar\Avatar.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Importers\AlembicHairImporter\Source\AlembicHairTranslator\AlembicHairTranslatorModule.Build.cs"><Link>Plugins\Importers\AlembicHairImporter\Source\AlembicHairTranslator\AlembicHairTranslatorModule.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Importers\AlembicImporter\Source\AlembicImporter\AlembicImporter.Build.cs"><Link>Plugins\Importers\AlembicImporter\Source\AlembicImporter\AlembicImporter.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Importers\AlembicImporter\Source\AlembicLibrary\AlembicLibrary.Build.cs"><Link>Plugins\Importers\AlembicImporter\Source\AlembicLibrary\AlembicLibrary.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Importers\USDImporter\Source\GeometryCacheUSD\GeometryCacheUSD.build.cs"><Link>Plugins\Importers\USDImporter\Source\GeometryCacheUSD\GeometryCacheUSD.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Importers\USDImporter\Source\USDClassesEditor\USDClassesEditor.Build.cs"><Link>Plugins\Importers\USDImporter\Source\USDClassesEditor\USDClassesEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Importers\USDImporter\Source\USDClasses\USDClasses.Build.cs"><Link>Plugins\Importers\USDImporter\Source\USDClasses\USDClasses.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Importers\USDImporter\Source\USDExporter\USDExporter.Build.cs"><Link>Plugins\Importers\USDImporter\Source\USDExporter\USDExporter.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Importers\USDImporter\Source\USDSchemas\USDSchemas.Build.cs"><Link>Plugins\Importers\USDImporter\Source\USDSchemas\USDSchemas.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Importers\USDImporter\Source\USDStageEditorViewModels\USDStageEditorViewModels.Build.cs"><Link>Plugins\Importers\USDImporter\Source\USDStageEditorViewModels\USDStageEditorViewModels.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Importers\USDImporter\Source\USDStageEditor\USDStageEditor.Build.cs"><Link>Plugins\Importers\USDImporter\Source\USDStageEditor\USDStageEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Importers\USDImporter\Source\USDStageImporter\USDStageImporter.Build.cs"><Link>Plugins\Importers\USDImporter\Source\USDStageImporter\USDStageImporter.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Importers\USDImporter\Source\USDStage\USDStage.Build.cs"><Link>Plugins\Importers\USDImporter\Source\USDStage\USDStage.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Importers\USDImporter\Source\USDTests\USDTests.Build.cs"><Link>Plugins\Importers\USDImporter\Source\USDTests\USDTests.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Importers\USDImporter\Source\USDUtilities\USDUtilities.Build.cs"><Link>Plugins\Importers\USDImporter\Source\USDUtilities\USDUtilities.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Importers\USDImporter\Source\UnrealUSDWrapper\UnrealUSDWrapper.Build.cs"><Link>Plugins\Importers\USDImporter\Source\UnrealUSDWrapper\UnrealUSDWrapper.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Importers\USDMultiUser\Source\USDMultiUser\USDMultiUser.Build.cs"><Link>Plugins\Importers\USDMultiUser\Source\USDMultiUser\USDMultiUser.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Interchange\Editor\Source\InterchangeEditor\InterchangeEditor.Build.cs"><Link>Plugins\Interchange\Editor\Source\InterchangeEditor\InterchangeEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Interchange\Editor\Source\Pipelines\InterchangeEditorPipelines.Build.cs"><Link>Plugins\Interchange\Editor\Source\Pipelines\InterchangeEditorPipelines.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Interchange\Editor\Source\Utilities\InterchangeEditorUtilities.Build.cs"><Link>Plugins\Interchange\Editor\Source\Utilities\InterchangeEditorUtilities.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Interchange\Runtime\Source\Common\InterchangeCommon.Build.cs"><Link>Plugins\Interchange\Runtime\Source\Common\InterchangeCommon.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Interchange\Runtime\Source\Dispatcher\InterchangeDispatcher.Build.cs"><Link>Plugins\Interchange\Runtime\Source\Dispatcher\InterchangeDispatcher.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Interchange\Runtime\Source\Export\InterchangeExport.Build.cs"><Link>Plugins\Interchange\Runtime\Source\Export\InterchangeExport.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Interchange\Runtime\Source\FactoryNodes\InterchangeFactoryNodes.Build.cs"><Link>Plugins\Interchange\Runtime\Source\FactoryNodes\InterchangeFactoryNodes.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Interchange\Runtime\Source\Import\InterchangeImport.Build.cs"><Link>Plugins\Interchange\Runtime\Source\Import\InterchangeImport.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Interchange\Runtime\Source\Messages\InterchangeMessages.Build.cs"><Link>Plugins\Interchange\Runtime\Source\Messages\InterchangeMessages.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Interchange\Runtime\Source\Nodes\InterchangeNodes.Build.cs"><Link>Plugins\Interchange\Runtime\Source\Nodes\InterchangeNodes.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Interchange\Runtime\Source\Parsers\CommonParser\InterchangeCommonParser.Build.cs"><Link>Plugins\Interchange\Runtime\Source\Parsers\CommonParser\InterchangeCommonParser.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Interchange\Runtime\Source\Parsers\Fbx\InterchangeFbxParser.Build.cs"><Link>Plugins\Interchange\Runtime\Source\Parsers\Fbx\InterchangeFbxParser.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Interchange\Runtime\Source\Parsers\GLTFCore\GLTFCore.Build.cs"><Link>Plugins\Interchange\Runtime\Source\Parsers\GLTFCore\GLTFCore.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Interchange\Runtime\Source\Pipelines\InterchangePipelines.Build.cs"><Link>Plugins\Interchange\Runtime\Source\Pipelines\InterchangePipelines.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Interchange\Runtime\Source\ThirdParty\Draco\Draco.Build.cs"><Link>Plugins\Interchange\Runtime\Source\ThirdParty\Draco\Draco.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\JsonBlueprintUtilities\Source\JsonBlueprintGraph\JsonBlueprintGraph.Build.cs"><Link>Plugins\JsonBlueprintUtilities\Source\JsonBlueprintGraph\JsonBlueprintGraph.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\JsonBlueprintUtilities\Source\JsonBlueprintUtilities\JsonBlueprintUtilities.Build.cs"><Link>Plugins\JsonBlueprintUtilities\Source\JsonBlueprintUtilities\JsonBlueprintUtilities.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\LevelBaker\Source\LevelBaker\LevelBaker.Build.cs"><Link>Plugins\LevelBaker\Source\LevelBaker\LevelBaker.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\LevelBaker\Source\ThirdParty\CUDATookit\CUDATookit.Build.cs"><Link>Plugins\LevelBaker\Source\ThirdParty\CUDATookit\CUDATookit.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\LevelBaker\Source\ThirdParty\Optix\Optix.Build.cs"><Link>Plugins\LevelBaker\Source\ThirdParty\Optix\Optix.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\LightWeightInstancesEditor\Source\LightWeightInstancesEditor\LightWeightInstancesEditor.Build.cs"><Link>Plugins\LightWeightInstancesEditor\Source\LightWeightInstancesEditor\LightWeightInstancesEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\AjaMedia\Source\AjaMediaEditor\AjaMediaEditor.Build.cs"><Link>Plugins\Media\AjaMedia\Source\AjaMediaEditor\AjaMediaEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\AjaMedia\Source\AjaMediaFactory\AjaMediaFactory.Build.cs"><Link>Plugins\Media\AjaMedia\Source\AjaMediaFactory\AjaMediaFactory.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\AjaMedia\Source\AjaMediaOutput\AjaMediaOutput.Build.cs"><Link>Plugins\Media\AjaMedia\Source\AjaMediaOutput\AjaMediaOutput.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\AjaMedia\Source\AjaMedia\AjaMedia.Build.cs"><Link>Plugins\Media\AjaMedia\Source\AjaMedia\AjaMedia.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\AjaMedia\Source\Aja\AjaCore.Build.cs"><Link>Plugins\Media\AjaMedia\Source\Aja\AjaCore.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\AndroidCamera\Source\AndroidCameraEditor\AndroidCameraEditor.Build.cs"><Link>Plugins\Media\AndroidCamera\Source\AndroidCameraEditor\AndroidCameraEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\AndroidCamera\Source\AndroidCameraFactory\AndroidCameraFactory.Build.cs"><Link>Plugins\Media\AndroidCamera\Source\AndroidCameraFactory\AndroidCameraFactory.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\AndroidCamera\Source\AndroidCamera\AndroidCamera.Build.cs"><Link>Plugins\Media\AndroidCamera\Source\AndroidCamera\AndroidCamera.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\AndroidMedia\Source\AndroidMediaEditor\AndroidMediaEditor.Build.cs"><Link>Plugins\Media\AndroidMedia\Source\AndroidMediaEditor\AndroidMediaEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\AndroidMedia\Source\AndroidMediaFactory\AndroidMediaFactory.Build.cs"><Link>Plugins\Media\AndroidMedia\Source\AndroidMediaFactory\AndroidMediaFactory.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\AndroidMedia\Source\AndroidMedia\AndroidMedia.Build.cs"><Link>Plugins\Media\AndroidMedia\Source\AndroidMedia\AndroidMedia.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\AppleProResDecoderElectra\Source\AppleProResDecoderElectra\AppleProResDecoderElectra.Build.cs"><Link>Plugins\Media\AppleProResDecoderElectra\Source\AppleProResDecoderElectra\AppleProResDecoderElectra.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\AppleProResMedia\Source\AppleProResMedia\AppleProResMedia.Build.cs"><Link>Plugins\Media\AppleProResMedia\Source\AppleProResMedia\AppleProResMedia.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\AppleProResMedia\Source\ThirdParty\ProResToolbox\ProResToolbox.Build.cs"><Link>Plugins\Media\AppleProResMedia\Source\ThirdParty\ProResToolbox\ProResToolbox.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\AudioCaptureTimecodeProvider\Source\AudioCaptureTimecodeProvider\AudioCaptureTimecodeProvider.Build.cs"><Link>Plugins\Media\AudioCaptureTimecodeProvider\Source\AudioCaptureTimecodeProvider\AudioCaptureTimecodeProvider.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\AvfMedia\Source\AvfMediaCapture\AvfMediaCapture.Build.cs"><Link>Plugins\Media\AvfMedia\Source\AvfMediaCapture\AvfMediaCapture.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\AvfMedia\Source\AvfMediaEditor\AvfMediaEditor.Build.cs"><Link>Plugins\Media\AvfMedia\Source\AvfMediaEditor\AvfMediaEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\AvfMedia\Source\AvfMediaFactory\AvfMediaFactory.Build.cs"><Link>Plugins\Media\AvfMedia\Source\AvfMediaFactory\AvfMediaFactory.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\AvfMedia\Source\AvfMedia\AvfMedia.Build.cs"><Link>Plugins\Media\AvfMedia\Source\AvfMedia\AvfMedia.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\AvidDNxHDDecoderElectra\Source\AvidDNxHDDecoderElectra.Build.cs"><Link>Plugins\Media\AvidDNxHDDecoderElectra\Source\AvidDNxHDDecoderElectra.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\AvidDNxMedia\Source\Source\AvidDNxMedia.Build.cs"><Link>Plugins\Media\AvidDNxMedia\Source\Source\AvidDNxMedia.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\BinkMedia\Source\BinkMediaPlayerEditor\BinkMediaPlayerEditor.Build.cs"><Link>Plugins\Media\BinkMedia\Source\BinkMediaPlayerEditor\BinkMediaPlayerEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\BinkMedia\Source\BinkMediaPlayerSDK\BinkMediaPlayerSDK.Build.cs"><Link>Plugins\Media\BinkMedia\Source\BinkMediaPlayerSDK\BinkMediaPlayerSDK.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\BinkMedia\Source\BinkMediaPlayer\BinkMediaPlayer.Build.cs"><Link>Plugins\Media\BinkMedia\Source\BinkMediaPlayer\BinkMediaPlayer.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\BlackmagicMedia\Source\BlackmagicCore\BlackmagicCore.build.cs"><Link>Plugins\Media\BlackmagicMedia\Source\BlackmagicCore\BlackmagicCore.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\BlackmagicMedia\Source\BlackmagicMediaEditor\BlackmagicMediaEditor.Build.cs"><Link>Plugins\Media\BlackmagicMedia\Source\BlackmagicMediaEditor\BlackmagicMediaEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\BlackmagicMedia\Source\BlackmagicMediaFactory\BlackmagicMediaFactory.Build.cs"><Link>Plugins\Media\BlackmagicMedia\Source\BlackmagicMediaFactory\BlackmagicMediaFactory.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\BlackmagicMedia\Source\BlackmagicMediaOutput\BlackmagicMediaOutput.Build.cs"><Link>Plugins\Media\BlackmagicMedia\Source\BlackmagicMediaOutput\BlackmagicMediaOutput.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\BlackmagicMedia\Source\BlackmagicMedia\BlackmagicMedia.Build.cs"><Link>Plugins\Media\BlackmagicMedia\Source\BlackmagicMedia\BlackmagicMedia.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\BlackmagicMedia\Source\ThirdParty\BlackmagicLib\BlackmagicSDK.build.cs"><Link>Plugins\Media\BlackmagicMedia\Source\ThirdParty\BlackmagicLib\BlackmagicSDK.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\ElectraCDM\Source\ElectraCDM\ElectraCDM.Build.cs"><Link>Plugins\Media\ElectraCDM\Source\ElectraCDM\ElectraCDM.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\ElectraCodecs\Source\ElectraCodecFactory\ElectraCodecFactory.Build.cs"><Link>Plugins\Media\ElectraCodecs\Source\ElectraCodecFactory\ElectraCodecFactory.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\ElectraCodecs\Source\ElectraDecoders\ElectraDecoders.Build.cs"><Link>Plugins\Media\ElectraCodecs\Source\ElectraDecoders\ElectraDecoders.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\ElectraPlayer\Source\ElectraPlayerFactory\ElectraPlayerFactory.Build.cs"><Link>Plugins\Media\ElectraPlayer\Source\ElectraPlayerFactory\ElectraPlayerFactory.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\ElectraPlayer\Source\ElectraPlayerPluginHandler\ElectraPlayerPluginHandler.Build.cs"><Link>Plugins\Media\ElectraPlayer\Source\ElectraPlayerPluginHandler\ElectraPlayerPluginHandler.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\ElectraPlayer\Source\ElectraPlayerPlugin\ElectraPlayerPlugin.Build.cs"><Link>Plugins\Media\ElectraPlayer\Source\ElectraPlayerPlugin\ElectraPlayerPlugin.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\ElectraPlayer\Source\ElectraPlayerRuntime\ElectraPlayerRuntime.Build.cs"><Link>Plugins\Media\ElectraPlayer\Source\ElectraPlayerRuntime\ElectraPlayerRuntime.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\ElectraSubtitles\Source\ElectraSubtitles\ElectraSubtitles.Build.cs"><Link>Plugins\Media\ElectraSubtitles\Source\ElectraSubtitles\ElectraSubtitles.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\ElectraUtil\Source\ElectraBase\ElectraBase.Build.cs"><Link>Plugins\Media\ElectraUtil\Source\ElectraBase\ElectraBase.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\ElectraUtil\Source\ElectraHTTPStream\ElectraHTTPStream.Build.cs"><Link>Plugins\Media\ElectraUtil\Source\ElectraHTTPStream\ElectraHTTPStream.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\ElectraUtil\Source\ElectraSamples\ElectraSamples.Build.cs"><Link>Plugins\Media\ElectraUtil\Source\ElectraSamples\ElectraSamples.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\HAPDecoderElectra\Source\HAPDecoderElectra\HAPDecoderElectra.Build.cs"><Link>Plugins\Media\HAPDecoderElectra\Source\HAPDecoderElectra\HAPDecoderElectra.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\HAPMedia\Source\HAPMedia\HAPMedia.Build.cs"><Link>Plugins\Media\HAPMedia\Source\HAPMedia\HAPMedia.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\HardwareEncoders\Source\EncoderAMF\EncoderAMF.Build.cs"><Link>Plugins\Media\HardwareEncoders\Source\EncoderAMF\EncoderAMF.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\HardwareEncoders\Source\EncoderNVENC\EncoderNVENC.Build.cs"><Link>Plugins\Media\HardwareEncoders\Source\EncoderNVENC\EncoderNVENC.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\ImgMedia\Source\ExrReaderGpu\ExrReaderGpu.Build.cs"><Link>Plugins\Media\ImgMedia\Source\ExrReaderGpu\ExrReaderGpu.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\ImgMedia\Source\ImgMediaEditor\ImgMediaEditor.Build.cs"><Link>Plugins\Media\ImgMedia\Source\ImgMediaEditor\ImgMediaEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\ImgMedia\Source\ImgMediaEngine\ImgMediaEngine.Build.cs"><Link>Plugins\Media\ImgMedia\Source\ImgMediaEngine\ImgMediaEngine.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\ImgMedia\Source\ImgMediaFactory\ImgMediaFactory.Build.cs"><Link>Plugins\Media\ImgMedia\Source\ImgMediaFactory\ImgMediaFactory.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\ImgMedia\Source\ImgMedia\ImgMedia.Build.cs"><Link>Plugins\Media\ImgMedia\Source\ImgMedia\ImgMedia.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\ImgMedia\Source\OpenExrWrapper\OpenExrWrapper.Build.cs"><Link>Plugins\Media\ImgMedia\Source\OpenExrWrapper\OpenExrWrapper.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\LinearTimecode\Source\LinearTimecode\LinearTimecode.Build.cs"><Link>Plugins\Media\LinearTimecode\Source\LinearTimecode\LinearTimecode.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\MediaCompositing\Source\MediaCompositingEditor\MediaCompositingEditor.Build.cs"><Link>Plugins\Media\MediaCompositing\Source\MediaCompositingEditor\MediaCompositingEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\MediaCompositing\Source\MediaCompositing\MediaCompositing.Build.cs"><Link>Plugins\Media\MediaCompositing\Source\MediaCompositing\MediaCompositing.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\MediaFrameworkUtilities\Source\MediaFrameworkUtilitiesEditor\MediaFrameworkUtilitiesEditor.Build.cs"><Link>Plugins\Media\MediaFrameworkUtilities\Source\MediaFrameworkUtilitiesEditor\MediaFrameworkUtilitiesEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\MediaFrameworkUtilities\Source\MediaFrameworkUtilities\MediaFrameworkUtilities.Build.cs"><Link>Plugins\Media\MediaFrameworkUtilities\Source\MediaFrameworkUtilities\MediaFrameworkUtilities.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\MediaIOFramework\Source\GPUTextureTransfer\GPUTextureTransfer.Build.cs"><Link>Plugins\Media\MediaIOFramework\Source\GPUTextureTransfer\GPUTextureTransfer.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\MediaIOFramework\Source\MediaIOCore\MediaIOCore.Build.cs"><Link>Plugins\Media\MediaIOFramework\Source\MediaIOCore\MediaIOCore.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\MediaIOFramework\Source\MediaIOEditor\MediaIOEditor.Build.cs"><Link>Plugins\Media\MediaIOFramework\Source\MediaIOEditor\MediaIOEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\MediaMovieStreamer\Source\MediaMovieStreamer\MediaMovieStreamer.Build.cs"><Link>Plugins\Media\MediaMovieStreamer\Source\MediaMovieStreamer\MediaMovieStreamer.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\MediaPlate\Source\MediaPlateEditor\MediaPlateEditor.Build.cs"><Link>Plugins\Media\MediaPlate\Source\MediaPlateEditor\MediaPlateEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\MediaPlate\Source\MediaPlate\MediaPlate.Build.cs"><Link>Plugins\Media\MediaPlate\Source\MediaPlate\MediaPlate.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\MediaPlayerEditor\Source\MediaPlayerEditor\MediaPlayerEditor.Build.cs"><Link>Plugins\Media\MediaPlayerEditor\Source\MediaPlayerEditor\MediaPlayerEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\MfMedia\Source\MfMediaEditor\MfMediaEditor.Build.cs"><Link>Plugins\Media\MfMedia\Source\MfMediaEditor\MfMediaEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\MfMedia\Source\MfMediaFactory\MfMediaFactory.Build.cs"><Link>Plugins\Media\MfMedia\Source\MfMediaFactory\MfMediaFactory.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\MfMedia\Source\MfMedia\MfMedia.Build.cs"><Link>Plugins\Media\MfMedia\Source\MfMedia\MfMedia.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\OpusDecoderElectra\Source\OpusDecoderElectra\OpusDecoderElectra.Build.cs"><Link>Plugins\Media\OpusDecoderElectra\Source\OpusDecoderElectra\OpusDecoderElectra.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\PixelCapture\Source\PixelCaptureShaders\PixelCaptureShaders.build.cs"><Link>Plugins\Media\PixelCapture\Source\PixelCaptureShaders\PixelCaptureShaders.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\PixelCapture\Source\PixelCapture\PixelCapture.build.cs"><Link>Plugins\Media\PixelCapture\Source\PixelCapture\PixelCapture.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\PixelStreaming\Source\PixelStreamingBlueprintEditor\PixelStreamingBlueprintEditor.Build.cs"><Link>Plugins\Media\PixelStreaming\Source\PixelStreamingBlueprintEditor\PixelStreamingBlueprintEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\PixelStreaming\Source\PixelStreamingBlueprint\PixelStreamingBlueprint.Build.cs"><Link>Plugins\Media\PixelStreaming\Source\PixelStreamingBlueprint\PixelStreamingBlueprint.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\PixelStreaming\Source\PixelStreamingEditor\PixelStreamingEditor.build.cs"><Link>Plugins\Media\PixelStreaming\Source\PixelStreamingEditor\PixelStreamingEditor.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\PixelStreaming\Source\PixelStreamingHMD\PixelStreamingHMD.build.cs"><Link>Plugins\Media\PixelStreaming\Source\PixelStreamingHMD\PixelStreamingHMD.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\PixelStreaming\Source\PixelStreamingInput\PixelStreamingInput.build.cs"><Link>Plugins\Media\PixelStreaming\Source\PixelStreamingInput\PixelStreamingInput.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\PixelStreaming\Source\PixelStreamingServers\PixelStreamingServers.build.cs"><Link>Plugins\Media\PixelStreaming\Source\PixelStreamingServers\PixelStreamingServers.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\PixelStreaming\Source\PixelStreaming\PixelStreaming.Build.cs"><Link>Plugins\Media\PixelStreaming\Source\PixelStreaming\PixelStreaming.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\TimecodeSynchronizer\Source\TimecodeSynchronizerEditor\TimecodeSynchronizerEditor.build.cs"><Link>Plugins\Media\TimecodeSynchronizer\Source\TimecodeSynchronizerEditor\TimecodeSynchronizerEditor.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\TimecodeSynchronizer\Source\TimecodeSynchronizer\TimecodeSynchronizer.Build.cs"><Link>Plugins\Media\TimecodeSynchronizer\Source\TimecodeSynchronizer\TimecodeSynchronizer.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\VPxDecoderElectra\Source\VPxDecoderElectra\VPxDecoderElectra.Build.cs"><Link>Plugins\Media\VPxDecoderElectra\Source\VPxDecoderElectra\VPxDecoderElectra.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\WebMMedia\Source\ThirdParty\webm\libwebm.Build.cs"><Link>Plugins\Media\WebMMedia\Source\ThirdParty\webm\libwebm.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\WebMMedia\Source\WebMMediaEditor\WebMMediaEditor.Build.cs"><Link>Plugins\Media\WebMMedia\Source\WebMMediaEditor\WebMMediaEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\WebMMedia\Source\WebMMediaFactory\WebMMediaFactory.Build.cs"><Link>Plugins\Media\WebMMedia\Source\WebMMediaFactory\WebMMediaFactory.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\WebMMedia\Source\WebMMedia\WebMMedia.Build.cs"><Link>Plugins\Media\WebMMedia\Source\WebMMedia\WebMMedia.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\WmfMedia\Source\WmfMediaEditor\WmfMediaEditor.Build.cs"><Link>Plugins\Media\WmfMedia\Source\WmfMediaEditor\WmfMediaEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\WmfMedia\Source\WmfMediaFactory\WmfMediaFactory.Build.cs"><Link>Plugins\Media\WmfMedia\Source\WmfMediaFactory\WmfMediaFactory.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Media\WmfMedia\Source\WmfMedia\WmfMedia.Build.cs"><Link>Plugins\Media\WmfMedia\Source\WmfMedia\WmfMedia.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\MemoryUsageQueries\Source\MemoryUsageQueries\MemoryUsageQueries.Build.cs"><Link>Plugins\MemoryUsageQueries\Source\MemoryUsageQueries\MemoryUsageQueries.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\MergeActorsToolModule\Source\MergeActorsToolModule\MergeActorsToolModule.Build.cs"><Link>Plugins\MergeActorsToolModule\Source\MergeActorsToolModule\MergeActorsToolModule.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\MeshPainting\Source\MeshPaintEditorMode\MeshPaintEditorMode.Build.cs"><Link>Plugins\MeshPainting\Source\MeshPaintEditorMode\MeshPaintEditorMode.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\MeshPainting\Source\MeshPaintingToolset\MeshPaintingToolset.Build.cs"><Link>Plugins\MeshPainting\Source\MeshPaintingToolset\MeshPaintingToolset.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Messaging\MessagingDebugger\Source\MessagingDebugger\MessagingDebugger.Build.cs"><Link>Plugins\Messaging\MessagingDebugger\Source\MessagingDebugger\MessagingDebugger.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Messaging\TcpMessaging\Source\TcpMessaging\TcpMessaging.Build.cs"><Link>Plugins\Messaging\TcpMessaging\Source\TcpMessaging\TcpMessaging.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Messaging\UdpMessaging\Source\UdpMessaging\UdpMessaging.Build.cs"><Link>Plugins\Messaging\UdpMessaging\Source\UdpMessaging\UdpMessaging.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\MovieScene\ActorSequence\Source\ActorSequenceEditor\ActorSequenceEditor.Build.cs"><Link>Plugins\MovieScene\ActorSequence\Source\ActorSequenceEditor\ActorSequenceEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\MovieScene\ActorSequence\Source\ActorSequence\ActorSequence.Build.cs"><Link>Plugins\MovieScene\ActorSequence\Source\ActorSequence\ActorSequence.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\MovieScene\CustomizableSequencerTracks\Source\CustomizableSequencerTracksEditor\CustomizableSequencerTracksEditor.Build.cs"><Link>Plugins\MovieScene\CustomizableSequencerTracks\Source\CustomizableSequencerTracksEditor\CustomizableSequencerTracksEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\MovieScene\CustomizableSequencerTracks\Source\CustomizableSequencerTracks\CustomizableSequencerTracks.Build.cs"><Link>Plugins\MovieScene\CustomizableSequencerTracks\Source\CustomizableSequencerTracks\CustomizableSequencerTracks.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\MovieScene\LevelSequenceEditor\Source\LevelSequenceEditor\LevelSequenceEditor.Build.cs"><Link>Plugins\MovieScene\LevelSequenceEditor\Source\LevelSequenceEditor\LevelSequenceEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\MovieScene\MoviePipelineMaskRenderPass\Source\MoviePipelineMaskRenderPass\MoviePipelineMaskRenderPass.Build.cs"><Link>Plugins\MovieScene\MoviePipelineMaskRenderPass\Source\MoviePipelineMaskRenderPass\MoviePipelineMaskRenderPass.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\MovieScene\MovieRenderPipeline\Source\MovieRenderPipelineCore\MovieRenderPipelineCore.Build.cs"><Link>Plugins\MovieScene\MovieRenderPipeline\Source\MovieRenderPipelineCore\MovieRenderPipelineCore.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\MovieScene\MovieRenderPipeline\Source\MovieRenderPipelineEditor\MovieRenderPipelineEditor.Build.cs"><Link>Plugins\MovieScene\MovieRenderPipeline\Source\MovieRenderPipelineEditor\MovieRenderPipelineEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\MovieScene\MovieRenderPipeline\Source\MovieRenderPipelineRenderPasses\MovieRenderPipelineRenderPasses.Build.cs"><Link>Plugins\MovieScene\MovieRenderPipeline\Source\MovieRenderPipelineRenderPasses\MovieRenderPipelineRenderPasses.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\MovieScene\MovieRenderPipeline\Source\MovieRenderPipelineSettings\MovieRenderPipelineSettings.Build.cs"><Link>Plugins\MovieScene\MovieRenderPipeline\Source\MovieRenderPipelineSettings\MovieRenderPipelineSettings.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\MovieScene\MovieRenderPipeline\Source\openexrRTTI\UEOpenExrRTTI.Build.cs"><Link>Plugins\MovieScene\MovieRenderPipeline\Source\openexrRTTI\UEOpenExrRTTI.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\MovieScene\MovieSceneTextTrack\Source\MovieSceneTextTrackEditor\MovieSceneTextTrackEditor.Build.cs"><Link>Plugins\MovieScene\MovieSceneTextTrack\Source\MovieSceneTextTrackEditor\MovieSceneTextTrackEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\MovieScene\MovieSceneTextTrack\Source\MovieSceneTextTrack\MovieSceneTextTrack.Build.cs"><Link>Plugins\MovieScene\MovieSceneTextTrack\Source\MovieSceneTextTrack\MovieSceneTextTrack.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\MovieScene\ReplayTracks\Source\ReplayTracksEditor\ReplayTracksEditor.Build.cs"><Link>Plugins\MovieScene\ReplayTracks\Source\ReplayTracksEditor\ReplayTracksEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\MovieScene\ReplayTracks\Source\ReplayTracks\ReplayTracks.Build.cs"><Link>Plugins\MovieScene\ReplayTracks\Source\ReplayTracks\ReplayTracks.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\MovieScene\SequencerScripting\Source\SequencerScriptingEditor\SequencerScriptingEditor.Build.cs"><Link>Plugins\MovieScene\SequencerScripting\Source\SequencerScriptingEditor\SequencerScriptingEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\MovieScene\SequencerScripting\Source\SequencerScripting\SequencerScripting.Build.cs"><Link>Plugins\MovieScene\SequencerScripting\Source\SequencerScripting\SequencerScripting.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\MovieScene\TemplateSequence\Source\TemplateSequenceEditor\TemplateSequenceEditor.Build.cs"><Link>Plugins\MovieScene\TemplateSequence\Source\TemplateSequenceEditor\TemplateSequenceEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\MovieScene\TemplateSequence\Source\TemplateSequence\TemplateSequence.Build.cs"><Link>Plugins\MovieScene\TemplateSequence\Source\TemplateSequence\TemplateSequence.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\NNE\NNERuntimeORT\Source\NNERuntimeORT\NNERuntimeORT.Build.cs"><Link>Plugins\NNE\NNERuntimeORT\Source\NNERuntimeORT\NNERuntimeORT.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\NNE\NNERuntimeORT\Source\NNEUtilities\NNEUtilities.Build.cs"><Link>Plugins\NNE\NNERuntimeORT\Source\NNEUtilities\NNEUtilities.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\NNE\NNERuntimeORT\Source\ThirdParty\OnnxEditor\NNEOnnxEditor.Build.cs"><Link>Plugins\NNE\NNERuntimeORT\Source\ThirdParty\OnnxEditor\NNEOnnxEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\NNE\NNERuntimeORT\Source\ThirdParty\OnnxruntimeEditor\NNEOnnxruntimeEditor.build.cs"><Link>Plugins\NNE\NNERuntimeORT\Source\ThirdParty\OnnxruntimeEditor\NNEOnnxruntimeEditor.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\NNE\NNERuntimeORT\Source\ThirdParty\ProtobufEditor\NNEProtobufEditor.Build.cs"><Link>Plugins\NNE\NNERuntimeORT\Source\ThirdParty\ProtobufEditor\NNEProtobufEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\NetcodeUnitTest\NUTUnrealEngine\Source\NUTUnrealEngine\NUTUnrealEngine.Build.cs"><Link>Plugins\NetcodeUnitTest\NUTUnrealEngine\Source\NUTUnrealEngine\NUTUnrealEngine.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\NetcodeUnitTest\NetcodeUnitTest\Source\NetcodeUnitTest\NetcodeUnitTest.Build.cs"><Link>Plugins\NetcodeUnitTest\NetcodeUnitTest\Source\NetcodeUnitTest\NetcodeUnitTest.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Online\Android\AndroidFetchBackgroundDownload\Source\AndroidFetchBackgroundDownload\AndroidFetchBackgroundDownload.Build.cs"><Link>Plugins\Online\Android\AndroidFetchBackgroundDownload\Source\AndroidFetchBackgroundDownload\AndroidFetchBackgroundDownload.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Online\Android\OnlineSubsystemGooglePlay\Source\OnlineSubsystemGooglePlay.Build.cs"><Link>Plugins\Online\Android\OnlineSubsystemGooglePlay\Source\OnlineSubsystemGooglePlay.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Online\EOSOverlayInputProvider\Source\EOSOverlayInputProvider\EOSOverlayInputProvider.Build.cs"><Link>Plugins\Online\EOSOverlayInputProvider\Source\EOSOverlayInputProvider\EOSOverlayInputProvider.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Online\EOSShared\Source\EOSShared\EOSShared.Build.cs"><Link>Plugins\Online\EOSShared\Source\EOSShared\EOSShared.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Online\IOS\OnlineSubsystemIOS\Source\OnlineSubsystemIOS.Build.cs"><Link>Plugins\Online\IOS\OnlineSubsystemIOS\Source\OnlineSubsystemIOS.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Online\OnlineBase\Source\OnlineBase.Build.cs"><Link>Plugins\Online\OnlineBase\Source\OnlineBase.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Online\OnlineFramework\Source\Hotfix\Hotfix.Build.cs"><Link>Plugins\Online\OnlineFramework\Source\Hotfix\Hotfix.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Online\OnlineFramework\Source\Lobby\Lobby.Build.cs"><Link>Plugins\Online\OnlineFramework\Source\Lobby\Lobby.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Online\OnlineFramework\Source\LoginFlow\LoginFlow.Build.cs"><Link>Plugins\Online\OnlineFramework\Source\LoginFlow\LoginFlow.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Online\OnlineFramework\Source\Party\Party.Build.cs"><Link>Plugins\Online\OnlineFramework\Source\Party\Party.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Online\OnlineFramework\Source\PatchCheck\PatchCheck.build.cs"><Link>Plugins\Online\OnlineFramework\Source\PatchCheck\PatchCheck.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Online\OnlineFramework\Source\PlayTimeLimit\PlayTimeLimit.Build.cs"><Link>Plugins\Online\OnlineFramework\Source\PlayTimeLimit\PlayTimeLimit.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Online\OnlineFramework\Source\Qos\Qos.Build.cs"><Link>Plugins\Online\OnlineFramework\Source\Qos\Qos.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Online\OnlineFramework\Source\Rejoin\Rejoin.Build.cs"><Link>Plugins\Online\OnlineFramework\Source\Rejoin\Rejoin.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Online\OnlineServicesEOSGS\Source\OnlineServicesEOSGS.Build.cs"><Link>Plugins\Online\OnlineServicesEOSGS\Source\OnlineServicesEOSGS.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Online\OnlineServicesEOS\Source\OnlineServicesEOS.Build.cs"><Link>Plugins\Online\OnlineServicesEOS\Source\OnlineServicesEOS.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Online\OnlineServicesNull\Source\OnlineServicesNull.Build.cs"><Link>Plugins\Online\OnlineServicesNull\Source\OnlineServicesNull.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Online\OnlineServicesOSSAdapter\Source\OnlineServicesOSSAdapter.Build.cs"><Link>Plugins\Online\OnlineServicesOSSAdapter\Source\OnlineServicesOSSAdapter.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Online\OnlineServices\Source\OnlineServicesCommonEngineUtils\OnlineServicesCommonEngineUtils.Build.cs"><Link>Plugins\Online\OnlineServices\Source\OnlineServicesCommonEngineUtils\OnlineServicesCommonEngineUtils.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Online\OnlineServices\Source\OnlineServicesCommon\OnlineServicesCommon.Build.cs"><Link>Plugins\Online\OnlineServices\Source\OnlineServicesCommon\OnlineServicesCommon.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Online\OnlineServices\Source\OnlineServicesInterface\OnlineServicesInterface.Build.cs"><Link>Plugins\Online\OnlineServices\Source\OnlineServicesInterface\OnlineServicesInterface.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Online\OnlineSubsystemAmazon\Source\OnlineSubsystemAmazon.Build.cs"><Link>Plugins\Online\OnlineSubsystemAmazon\Source\OnlineSubsystemAmazon.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Online\OnlineSubsystemApple\Source\OnlineSubsystemApple.Build.cs"><Link>Plugins\Online\OnlineSubsystemApple\Source\OnlineSubsystemApple.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Online\OnlineSubsystemEOS\Source\OnlineSubsystemEOSPlus\OnlineSubsystemEOSPlus.Build.cs"><Link>Plugins\Online\OnlineSubsystemEOS\Source\OnlineSubsystemEOSPlus\OnlineSubsystemEOSPlus.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Online\OnlineSubsystemEOS\Source\OnlineSubsystemEOS\OnlineSubsystemEOS.Build.cs"><Link>Plugins\Online\OnlineSubsystemEOS\Source\OnlineSubsystemEOS\OnlineSubsystemEOS.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Online\OnlineSubsystemFacebook\Source\OnlineSubsystemFacebook.Build.cs"><Link>Plugins\Online\OnlineSubsystemFacebook\Source\OnlineSubsystemFacebook.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Online\OnlineSubsystemGoogle\Source\OnlineSubsystemGoogle.Build.cs"><Link>Plugins\Online\OnlineSubsystemGoogle\Source\OnlineSubsystemGoogle.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Online\OnlineSubsystemNull\Source\OnlineSubsystemNull.Build.cs"><Link>Plugins\Online\OnlineSubsystemNull\Source\OnlineSubsystemNull.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Online\OnlineSubsystemOculus\Source\OnlineSubsystemOculus.Build.cs"><Link>Plugins\Online\OnlineSubsystemOculus\Source\OnlineSubsystemOculus.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Online\OnlineSubsystemSteam\Source\OnlineSubsystemSteam.Build.cs"><Link>Plugins\Online\OnlineSubsystemSteam\Source\OnlineSubsystemSteam.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Online\OnlineSubsystemTencent\Source\OnlineSubsystemTencent.Build.cs"><Link>Plugins\Online\OnlineSubsystemTencent\Source\OnlineSubsystemTencent.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Online\OnlineSubsystemUtils\Source\OnlineBlueprintSupport\OnlineBlueprintSupport.Build.cs"><Link>Plugins\Online\OnlineSubsystemUtils\Source\OnlineBlueprintSupport\OnlineBlueprintSupport.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Online\OnlineSubsystemUtils\Source\OnlineSubsystemUtils\OnlineSubsystemUtils.Build.cs"><Link>Plugins\Online\OnlineSubsystemUtils\Source\OnlineSubsystemUtils\OnlineSubsystemUtils.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Online\OnlineSubsystem\Source\OnlineSubsystem.Build.cs"><Link>Plugins\Online\OnlineSubsystem\Source\OnlineSubsystem.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Online\SocketSubsystemEOS\Source\SocketSubsystemEOS\SocketSubsystemEOS.Build.cs"><Link>Plugins\Online\SocketSubsystemEOS\Source\SocketSubsystemEOS\SocketSubsystemEOS.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Online\VoiceChat\EOSVoiceChat\Source\EOSVoiceChat\EOSVoiceChat.Build.cs"><Link>Plugins\Online\VoiceChat\EOSVoiceChat\Source\EOSVoiceChat\EOSVoiceChat.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Online\VoiceChat\VoiceChat\Source\VoiceChat.Build.cs"><Link>Plugins\Online\VoiceChat\VoiceChat\Source\VoiceChat.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Online\WebAuth\Source\WebAuth.Build.cs"><Link>Plugins\Online\WebAuth\Source\WebAuth.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\PCGExternalDataInterop\Source\PCGExternalDataInteropEditor\PCGExternalDataInteropEditor.Build.cs"><Link>Plugins\PCGExternalDataInterop\Source\PCGExternalDataInteropEditor\PCGExternalDataInteropEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\PCGExternalDataInterop\Source\PCGExternalDataInterop\PCGExternalDataInterop.Build.cs"><Link>Plugins\PCGExternalDataInterop\Source\PCGExternalDataInterop\PCGExternalDataInterop.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\PCGGeometryScriptInterop\Source\PCGGeometryScriptInterop\PCGGeometryScriptInterop.Build.cs"><Link>Plugins\PCGGeometryScriptInterop\Source\PCGGeometryScriptInterop\PCGGeometryScriptInterop.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\PCG\Source\PCGCompute\PCGCompute.Build.cs"><Link>Plugins\PCG\Source\PCGCompute\PCGCompute.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\PCG\Source\PCGEditor\PCGEditor.Build.cs"><Link>Plugins\PCG\Source\PCGEditor\PCGEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\PCG\Source\PCG\PCG.Build.cs"><Link>Plugins\PCG\Source\PCG\PCG.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Performance\PerformanceMonitor\Source\PerformanceMonitor\PerformanceMonitor.Build.cs"><Link>Plugins\Performance\PerformanceMonitor\Source\PerformanceMonitor\PerformanceMonitor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\PlatformsMapSync\Source\PlatformsMapSync\PlatformsMapSync.Build.cs"><Link>Plugins\PlatformsMapSync\Source\PlatformsMapSync\PlatformsMapSync.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Portal\LauncherChunkInstaller\Source\LauncherChunkInstaller\LauncherChunkInstaller.Build.cs"><Link>Plugins\Portal\LauncherChunkInstaller\Source\LauncherChunkInstaller\LauncherChunkInstaller.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Portrait\Source\PortraitAnimGraph\PortraitAnimGraph.Build.cs"><Link>Plugins\Portrait\Source\PortraitAnimGraph\PortraitAnimGraph.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Portrait\Source\PortraitEditor\PortraitEditor.Build.cs"><Link>Plugins\Portrait\Source\PortraitEditor\PortraitEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Portrait\Source\Portrait\Portrait.Build.cs"><Link>Plugins\Portrait\Source\Portrait\Portrait.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\PoseDriverConnect\Source\PoseDriverConnect\PoseDriverConnect.Build.cs"><Link>Plugins\PoseDriverConnect\Source\PoseDriverConnect\PoseDriverConnect.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\PreviewMap\Source\PreviewMapProcess\PreviewMapProcess.Build.cs"><Link>Plugins\PreviewMap\Source\PreviewMapProcess\PreviewMapProcess.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\PreviewMap\Source\PreviewMap\PreviewMap.Build.cs"><Link>Plugins\PreviewMap\Source\PreviewMap\PreviewMap.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Protocols\MQTT\Source\MQTTCoreEditor\MQTTCoreEditor.Build.cs"><Link>Plugins\Protocols\MQTT\Source\MQTTCoreEditor\MQTTCoreEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Protocols\MQTT\Source\MQTTCore\MQTTCore.Build.cs"><Link>Plugins\Protocols\MQTT\Source\MQTTCore\MQTTCore.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\RenderGraphInsights\Source\RenderGraphInsights\RenderGraphInsights.Build.cs"><Link>Plugins\RenderGraphInsights\Source\RenderGraphInsights\RenderGraphInsights.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\RootBaker\Source\RootBaker\RootBaker.Build.cs"><Link>Plugins\RootBaker\Source\RootBaker\RootBaker.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\AR\ARUtilities\Source\ARUtilities\ARUtilities.Build.cs"><Link>Plugins\Runtime\AR\ARUtilities\Source\ARUtilities\ARUtilities.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\AR\AppleAR\AppleARKitFaceSupport\Source\AppleARKitFaceSupport\AppleARKitFaceSupport.Build.cs"><Link>Plugins\Runtime\AR\AppleAR\AppleARKitFaceSupport\Source\AppleARKitFaceSupport\AppleARKitFaceSupport.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\AR\AppleAR\AppleARKit\Source\AppleARKitPoseTrackingLiveLink\AppleARKitPoseTrackingLiveLink.build.cs"><Link>Plugins\Runtime\AR\AppleAR\AppleARKit\Source\AppleARKitPoseTrackingLiveLink\AppleARKitPoseTrackingLiveLink.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\AR\AppleAR\AppleARKit\Source\AppleARKit\AppleARKit.Build.cs"><Link>Plugins\Runtime\AR\AppleAR\AppleARKit\Source\AppleARKit\AppleARKit.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\AR\AzureSpatialAnchorsForARCore\Source\AzureSpatialAnchorsForARCore\AzureSpatialAnchorsForARCore.Build.cs"><Link>Plugins\Runtime\AR\AzureSpatialAnchorsForARCore\Source\AzureSpatialAnchorsForARCore\AzureSpatialAnchorsForARCore.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\AR\AzureSpatialAnchorsForARKit\Source\AzureSpatialAnchorsForARKit\AzureSpatialAnchorsForARKit.Build.cs"><Link>Plugins\Runtime\AR\AzureSpatialAnchorsForARKit\Source\AzureSpatialAnchorsForARKit\AzureSpatialAnchorsForARKit.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\AR\AzureSpatialAnchors\Source\AzureSpatialAnchors\AzureSpatialAnchors.Build.cs"><Link>Plugins\Runtime\AR\AzureSpatialAnchors\Source\AzureSpatialAnchors\AzureSpatialAnchors.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\AR\Google\GoogleARCoreServices\Source\GoogleARCoreServices\GoogleARCoreServices.Build.cs"><Link>Plugins\Runtime\AR\Google\GoogleARCoreServices\Source\GoogleARCoreServices\GoogleARCoreServices.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\AR\Google\GoogleARCore\Source\GoogleARCoreBase\GoogleARCoreBase.Build.cs"><Link>Plugins\Runtime\AR\Google\GoogleARCore\Source\GoogleARCoreBase\GoogleARCoreBase.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\AR\Google\GoogleARCore\Source\GoogleARCoreRendering\GoogleARCoreRendering.Build.cs"><Link>Plugins\Runtime\AR\Google\GoogleARCore\Source\GoogleARCoreRendering\GoogleARCoreRendering.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\ActorLayerUtilities\Source\ActorLayerUtilitiesEditor\ActorLayerUtilitiesEditor.Build.cs"><Link>Plugins\Runtime\ActorLayerUtilities\Source\ActorLayerUtilitiesEditor\ActorLayerUtilitiesEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\ActorLayerUtilities\Source\ActorLayerUtilities\ActorLayerUtilities.Build.cs"><Link>Plugins\Runtime\ActorLayerUtilities\Source\ActorLayerUtilities\ActorLayerUtilities.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\Advertising\IOSTapJoy\Source\IOSTapJoy\IOSTapJoy.Build.cs"><Link>Plugins\Runtime\Advertising\IOSTapJoy\Source\IOSTapJoy\IOSTapJoy.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\Analytics\Adjust\Source\AdjustEditor\AdjustEditor.Build.cs"><Link>Plugins\Runtime\Analytics\Adjust\Source\AdjustEditor\AdjustEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\Analytics\Adjust\Source\AndroidAdjust\AndroidAdjust.Build.cs"><Link>Plugins\Runtime\Analytics\Adjust\Source\AndroidAdjust\AndroidAdjust.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\Analytics\Adjust\Source\IOSAdjust\IOSAdjust.Build.cs"><Link>Plugins\Runtime\Analytics\Adjust\Source\IOSAdjust\IOSAdjust.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\Analytics\AnalyticsBlueprintLibrary\Source\AnalyticsBlueprintLibrary\AnalyticsBlueprintLibrary.Build.cs"><Link>Plugins\Runtime\Analytics\AnalyticsBlueprintLibrary\Source\AnalyticsBlueprintLibrary\AnalyticsBlueprintLibrary.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\Analytics\AnalyticsMulticast\Source\AnaltyicsMulticastEditor\AnalyticsMulticastEditor.Build.cs"><Link>Plugins\Runtime\Analytics\AnalyticsMulticast\Source\AnaltyicsMulticastEditor\AnalyticsMulticastEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\Analytics\AnalyticsMulticast\Source\AnalyticsMulticast\AnalyticsMulticast.Build.cs"><Link>Plugins\Runtime\Analytics\AnalyticsMulticast\Source\AnalyticsMulticast\AnalyticsMulticast.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\Analytics\FileLogging\Source\FileLogging\FileLogging.Build.cs"><Link>Plugins\Runtime\Analytics\FileLogging\Source\FileLogging\FileLogging.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\Analytics\Flurry\Source\FlurryEditor\FlurryEditor.Build.cs"><Link>Plugins\Runtime\Analytics\Flurry\Source\FlurryEditor\FlurryEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\Analytics\Flurry\Source\IOSFlurry\Source\IOSFlurry\IOSFlurry.Build.cs"><Link>Plugins\Runtime\Analytics\Flurry\Source\IOSFlurry\Source\IOSFlurry\IOSFlurry.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\AndroidBackgroundService\Source\AndroidBackgroundService\AndroidBackgroundService.Build.cs"><Link>Plugins\Runtime\AndroidBackgroundService\Source\AndroidBackgroundService\AndroidBackgroundService.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\AndroidDeviceProfileSelector\Source\AndroidDeviceProfileCommandlets\AndroidDeviceProfileCommandlets.Build.cs"><Link>Plugins\Runtime\AndroidDeviceProfileSelector\Source\AndroidDeviceProfileCommandlets\AndroidDeviceProfileCommandlets.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\AndroidDeviceProfileSelector\Source\AndroidDeviceProfileSelectorRuntime\AndroidDeviceProfileSelectorRuntime.Build.cs"><Link>Plugins\Runtime\AndroidDeviceProfileSelector\Source\AndroidDeviceProfileSelectorRuntime\AndroidDeviceProfileSelectorRuntime.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\AndroidDeviceProfileSelector\Source\AndroidDeviceProfileSelector\AndroidDeviceProfileSelector.Build.cs"><Link>Plugins\Runtime\AndroidDeviceProfileSelector\Source\AndroidDeviceProfileSelector\AndroidDeviceProfileSelector.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\AndroidFileServer\Source\AndroidFileServerEditor\AndroidFileServerEditor.Build.cs"><Link>Plugins\Runtime\AndroidFileServer\Source\AndroidFileServerEditor\AndroidFileServerEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\AndroidFileServer\Source\AndroidFileServer\AndroidFileServer.Build.cs"><Link>Plugins\Runtime\AndroidFileServer\Source\AndroidFileServer\AndroidFileServer.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\AndroidMoviePlayer\Source\AndroidMoviePlayer\AndroidMoviePlayer.Build.cs"><Link>Plugins\Runtime\AndroidMoviePlayer\Source\AndroidMoviePlayer\AndroidMoviePlayer.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\AndroidPermission\Source\AndroidPermission\AndroidPermission.Build.cs"><Link>Plugins\Runtime\AndroidPermission\Source\AndroidPermission\AndroidPermission.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\AnimationBudgetAllocator\Source\AnimationBudgetAllocator\AnimationBudgetAllocator.Build.cs"><Link>Plugins\Runtime\AnimationBudgetAllocator\Source\AnimationBudgetAllocator\AnimationBudgetAllocator.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\ApexDestruction\Source\ApexDestructionEditor\ApexDestructionEditor.Build.cs"><Link>Plugins\Runtime\ApexDestruction\Source\ApexDestructionEditor\ApexDestructionEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\ApexDestruction\Source\ApexDestruction\ApexDestruction.Build.cs"><Link>Plugins\Runtime\ApexDestruction\Source\ApexDestruction\ApexDestruction.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\AppleImageUtils\Source\AppleImageUtilsBlueprintSupport\AppleImageUtilsBlueprintSupport.Build.cs"><Link>Plugins\Runtime\AppleImageUtils\Source\AppleImageUtilsBlueprintSupport\AppleImageUtilsBlueprintSupport.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\AppleImageUtils\Source\AppleImageUtils\AppleImageUtils.Build.cs"><Link>Plugins\Runtime\AppleImageUtils\Source\AppleImageUtils\AppleImageUtils.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\AppleMoviePlayer\Source\AppleMoviePlayer\AppleMoviePlayer.Build.cs"><Link>Plugins\Runtime\AppleMoviePlayer\Source\AppleMoviePlayer\AppleMoviePlayer.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\ArchVisCharacter\Source\ArchVisCharacter\ArchVisCharacter.Build.cs"><Link>Plugins\Runtime\ArchVisCharacter\Source\ArchVisCharacter\ArchVisCharacter.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\AssetTags\Source\AssetTags\AssetTags.Build.cs"><Link>Plugins\Runtime\AssetTags\Source\AssetTags\AssetTags.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\AudioCapture\Source\AudioCaptureEditor\AudioCaptureEditor.build.cs"><Link>Plugins\Runtime\AudioCapture\Source\AudioCaptureEditor\AudioCaptureEditor.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\AudioCapture\Source\AudioCapture\AudioCapture.Build.cs"><Link>Plugins\Runtime\AudioCapture\Source\AudioCapture\AudioCapture.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\AudioModulation\Source\AudioModulationEditor\AudioModulationEditor.build.cs"><Link>Plugins\Runtime\AudioModulation\Source\AudioModulationEditor\AudioModulationEditor.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\AudioModulation\Source\AudioModulation\AudioModulation.Build.cs"><Link>Plugins\Runtime\AudioModulation\Source\AudioModulation\AudioModulation.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\AudioMotorSim\Source\AudioMotorSimStandardComponents\AudioMotorSimStandardComponents.Build.cs"><Link>Plugins\Runtime\AudioMotorSim\Source\AudioMotorSimStandardComponents\AudioMotorSimStandardComponents.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\AudioMotorSim\Source\AudioMotorSim\AudioMotorSim.Build.cs"><Link>Plugins\Runtime\AudioMotorSim\Source\AudioMotorSim\AudioMotorSim.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\AudioSynesthesia\Source\AudioSynesthesiaCore\AudioSynesthesiaCore.Build.cs"><Link>Plugins\Runtime\AudioSynesthesia\Source\AudioSynesthesiaCore\AudioSynesthesiaCore.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\AudioSynesthesia\Source\AudioSynesthesiaEditor\AudioSynesthesiaEditor.Build.cs"><Link>Plugins\Runtime\AudioSynesthesia\Source\AudioSynesthesiaEditor\AudioSynesthesiaEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\AudioSynesthesia\Source\AudioSynesthesia\AudioSynesthesia.Build.cs"><Link>Plugins\Runtime\AudioSynesthesia\Source\AudioSynesthesia\AudioSynesthesia.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\AudioWidgets\Source\AudioWidgets\AudioWidgets.Build.cs"><Link>Plugins\Runtime\AudioWidgets\Source\AudioWidgets\AudioWidgets.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\CableComponent\Source\CableComponent\CableComponent.Build.cs"><Link>Plugins\Runtime\CableComponent\Source\CableComponent\CableComponent.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\ChunkDownloader\Source\ChunkDownloader.Build.cs"><Link>Plugins\Runtime\ChunkDownloader\Source\ChunkDownloader.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\CommonUI\Source\CommonInput\CommonInput.Build.cs"><Link>Plugins\Runtime\CommonUI\Source\CommonInput\CommonInput.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\CommonUI\Source\CommonUIEditor\CommonUIEditor.Build.cs"><Link>Plugins\Runtime\CommonUI\Source\CommonUIEditor\CommonUIEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\CommonUI\Source\CommonUI\CommonUI.Build.cs"><Link>Plugins\Runtime\CommonUI\Source\CommonUI\CommonUI.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\ComputeFramework\Source\ComputeFrameworkEditor\ComputeFrameworkEditor.Build.cs"><Link>Plugins\Runtime\ComputeFramework\Source\ComputeFrameworkEditor\ComputeFrameworkEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\ComputeFramework\Source\ComputeFramework\ComputeFramework.Build.cs"><Link>Plugins\Runtime\ComputeFramework\Source\ComputeFramework\ComputeFramework.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\CustomMeshComponent\Source\CustomMeshComponent\CustomMeshComponent.Build.cs"><Link>Plugins\Runtime\CustomMeshComponent\Source\CustomMeshComponent\CustomMeshComponent.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\DataRegistry\Source\DataRegistryEditor\DataRegistryEditor.Build.cs"><Link>Plugins\Runtime\DataRegistry\Source\DataRegistryEditor\DataRegistryEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\DataRegistry\Source\DataRegistry\DataRegistry.Build.cs"><Link>Plugins\Runtime\DataRegistry\Source\DataRegistry\DataRegistry.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\Database\ADOSupport\Source\ADOSupport\ADOSupport.Build.cs"><Link>Plugins\Runtime\Database\ADOSupport\Source\ADOSupport\ADOSupport.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\Database\DatabaseSupport\Source\DatabaseSupport\DatabaseSupport.Build.cs"><Link>Plugins\Runtime\Database\DatabaseSupport\Source\DatabaseSupport\DatabaseSupport.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\Database\RemoteDatabaseSupport\Source\RemoteDatabaseSupport\RemoteDatabaseSupport.Build.cs"><Link>Plugins\Runtime\Database\RemoteDatabaseSupport\Source\RemoteDatabaseSupport\RemoteDatabaseSupport.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\Database\SQLiteCore\Source\SQLiteCore\SQLiteCore.Build.cs"><Link>Plugins\Runtime\Database\SQLiteCore\Source\SQLiteCore\SQLiteCore.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\Database\SQLiteSupport\Source\SQLiteSupport\SQLiteSupport.Build.cs"><Link>Plugins\Runtime\Database\SQLiteSupport\Source\SQLiteSupport\SQLiteSupport.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\ExampleDeviceProfileSelector\Source\ExampleDeviceProfileSelector\ExampleDeviceProfileSelector.Build.cs"><Link>Plugins\Runtime\ExampleDeviceProfileSelector\Source\ExampleDeviceProfileSelector\ExampleDeviceProfileSelector.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\Firebase\Source\Firebase.Build.cs"><Link>Plugins\Runtime\Firebase\Source\Firebase.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\GameFeatures\Source\GameFeaturesEditor\GameFeaturesEditor.Build.cs"><Link>Plugins\Runtime\GameFeatures\Source\GameFeaturesEditor\GameFeaturesEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\GameFeatures\Source\GameFeatures\GameFeatures.Build.cs"><Link>Plugins\Runtime\GameFeatures\Source\GameFeatures\GameFeatures.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\GameInput\Source\GameInputBaseEditor\GameInputBaseEditor.build.cs"><Link>Plugins\Runtime\GameInput\Source\GameInputBaseEditor\GameInputBaseEditor.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\GameInput\Source\GameInputBase\GameInputBase.build.cs"><Link>Plugins\Runtime\GameInput\Source\GameInputBase\GameInputBase.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\GameplayAbilities\Source\GameplayAbilitiesEditor\GameplayAbilitiesEditor.Build.cs"><Link>Plugins\Runtime\GameplayAbilities\Source\GameplayAbilitiesEditor\GameplayAbilitiesEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\GameplayAbilities\Source\GameplayAbilities\GameplayAbilities.Build.cs"><Link>Plugins\Runtime\GameplayAbilities\Source\GameplayAbilities\GameplayAbilities.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\GameplayBehaviorSmartObjects\Source\GameplayBehaviorSmartObjectsModule\GameplayBehaviorSmartObjectsModule.Build.cs"><Link>Plugins\Runtime\GameplayBehaviorSmartObjects\Source\GameplayBehaviorSmartObjectsModule\GameplayBehaviorSmartObjectsModule.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\GameplayInteractions\Source\GameplayInteractionsModule\GameplayInteractionsModule.Build.cs"><Link>Plugins\Runtime\GameplayInteractions\Source\GameplayInteractionsModule\GameplayInteractionsModule.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\GameplayStateTree\Source\GameplayStateTreeModule\GameplayStateTreeModule.Build.cs"><Link>Plugins\Runtime\GameplayStateTree\Source\GameplayStateTreeModule\GameplayStateTreeModule.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\GeoReferencing\Source\GeoReferencingEditor\GeoReferencingEditor.Build.cs"><Link>Plugins\Runtime\GeoReferencing\Source\GeoReferencingEditor\GeoReferencingEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\GeoReferencing\Source\GeoReferencing\GeoReferencing.Build.cs"><Link>Plugins\Runtime\GeoReferencing\Source\GeoReferencing\GeoReferencing.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\GeoReferencing\Source\ThirdParty\Proj.Build.cs"><Link>Plugins\Runtime\GeoReferencing\Source\ThirdParty\Proj.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\GeometryCache\Source\GeometryCacheEd\GeometryCacheEd.Build.cs"><Link>Plugins\Runtime\GeometryCache\Source\GeometryCacheEd\GeometryCacheEd.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\GeometryCache\Source\GeometryCacheSequencer\GeometryCacheSequencer.Build.cs"><Link>Plugins\Runtime\GeometryCache\Source\GeometryCacheSequencer\GeometryCacheSequencer.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\GeometryCache\Source\GeometryCacheStreamer\GeometryCacheStreamer.Build.cs"><Link>Plugins\Runtime\GeometryCache\Source\GeometryCacheStreamer\GeometryCacheStreamer.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\GeometryCache\Source\GeometryCacheTracks\GeometryCacheTracks.Build.cs"><Link>Plugins\Runtime\GeometryCache\Source\GeometryCacheTracks\GeometryCacheTracks.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\GeometryCache\Source\GeometryCache\GeometryCache.Build.cs"><Link>Plugins\Runtime\GeometryCache\Source\GeometryCache\GeometryCache.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\GeometryProcessing\Source\DynamicMesh\DynamicMesh.Build.cs"><Link>Plugins\Runtime\GeometryProcessing\Source\DynamicMesh\DynamicMesh.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\GeometryProcessing\Source\GeometryAlgorithms\GeometryAlgorithms.Build.cs"><Link>Plugins\Runtime\GeometryProcessing\Source\GeometryAlgorithms\GeometryAlgorithms.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\GeometryScripting\Source\GeometryScriptingCore\GeometryScriptingCore.Build.cs"><Link>Plugins\Runtime\GeometryScripting\Source\GeometryScriptingCore\GeometryScriptingCore.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\GeometryScripting\Source\GeometryScriptingEditor\GeometryScriptingEditor.Build.cs"><Link>Plugins\Runtime\GeometryScripting\Source\GeometryScriptingEditor\GeometryScriptingEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\GoogleCloudMessaging\Source\GoogleCloudMessaging\GoogleCloudMessaging.Build.cs"><Link>Plugins\Runtime\GoogleCloudMessaging\Source\GoogleCloudMessaging\GoogleCloudMessaging.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\GooglePAD\Source\GooglePADEditor\GooglePADEditor.Build.cs"><Link>Plugins\Runtime\GooglePAD\Source\GooglePADEditor\GooglePADEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\GooglePAD\Source\GooglePAD\GooglePAD.Build.cs"><Link>Plugins\Runtime\GooglePAD\Source\GooglePAD\GooglePAD.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\HDRIBackdrop\Source\HDRIBackdrop\HDRIBackdrop.Build.cs"><Link>Plugins\Runtime\HDRIBackdrop\Source\HDRIBackdrop\HDRIBackdrop.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\HPMotionController\Source\HPMotionController\HPMotionController.Build.cs"><Link>Plugins\Runtime\HPMotionController\Source\HPMotionController\HPMotionController.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\HTTPChunkInstaller\Source\HTTPChunkInstaller.Build.cs"><Link>Plugins\Runtime\HTTPChunkInstaller\Source\HTTPChunkInstaller.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\HairStrands\Source\HairCardGeneratorFramework\HairCardGeneratorFramework.Build.cs"><Link>Plugins\Runtime\HairStrands\Source\HairCardGeneratorFramework\HairCardGeneratorFramework.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\HairStrands\Source\HairStrandsCore\HairStrandsCore.Build.cs"><Link>Plugins\Runtime\HairStrands\Source\HairStrandsCore\HairStrandsCore.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\HairStrands\Source\HairStrandsDeformer\HairStrandsDeformer.Build.cs"><Link>Plugins\Runtime\HairStrands\Source\HairStrandsDeformer\HairStrandsDeformer.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\HairStrands\Source\HairStrandsEditor\HairStrandsEditor.Build.cs"><Link>Plugins\Runtime\HairStrands\Source\HairStrandsEditor\HairStrandsEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\HairStrands\Source\HairStrandsRuntime\HairStrandsRuntime.Build.cs"><Link>Plugins\Runtime\HairStrands\Source\HairStrandsRuntime\HairStrandsRuntime.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\Harmonix\Source\HarmonixDspEditor\HarmonixDspEditor.Build.cs"><Link>Plugins\Runtime\Harmonix\Source\HarmonixDspEditor\HarmonixDspEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\Harmonix\Source\HarmonixDspTests\HarmonixDspTests.Build.cs"><Link>Plugins\Runtime\Harmonix\Source\HarmonixDspTests\HarmonixDspTests.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\Harmonix\Source\HarmonixDsp\HarmonixDsp.Build.cs"><Link>Plugins\Runtime\Harmonix\Source\HarmonixDsp\HarmonixDsp.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\Harmonix\Source\HarmonixEditor\HarmonixEditor.Build.cs"><Link>Plugins\Runtime\Harmonix\Source\HarmonixEditor\HarmonixEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\Harmonix\Source\HarmonixMetasoundEditor\HarmonixMetasoundEditor.Build.cs"><Link>Plugins\Runtime\Harmonix\Source\HarmonixMetasoundEditor\HarmonixMetasoundEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\Harmonix\Source\HarmonixMetasoundTests\HarmonixMetasoundTests.Build.cs"><Link>Plugins\Runtime\Harmonix\Source\HarmonixMetasoundTests\HarmonixMetasoundTests.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\Harmonix\Source\HarmonixMetasound\HarmonixMetasound.Build.cs"><Link>Plugins\Runtime\Harmonix\Source\HarmonixMetasound\HarmonixMetasound.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\Harmonix\Source\HarmonixMidiEditor\HarmonixMidiEditor.Build.cs"><Link>Plugins\Runtime\Harmonix\Source\HarmonixMidiEditor\HarmonixMidiEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\Harmonix\Source\HarmonixMidiTests\HarmonixMidiTests.Build.cs"><Link>Plugins\Runtime\Harmonix\Source\HarmonixMidiTests\HarmonixMidiTests.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\Harmonix\Source\HarmonixMidi\HarmonixMidi.Build.cs"><Link>Plugins\Runtime\Harmonix\Source\HarmonixMidi\HarmonixMidi.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\Harmonix\Source\Harmonix\Harmonix.Build.cs"><Link>Plugins\Runtime\Harmonix\Source\Harmonix\Harmonix.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\IOSDeviceProfileSelector\Source\IOSDeviceProfileSelector\IOSDeviceProfileSelector.Build.cs"><Link>Plugins\Runtime\IOSDeviceProfileSelector\Source\IOSDeviceProfileSelector\IOSDeviceProfileSelector.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\IOSReplayKit\Source\IOSReplayKit\IOSReplayKit.Build.cs"><Link>Plugins\Runtime\IOSReplayKit\Source\IOSReplayKit\IOSReplayKit.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\InputDebugging\Source\InputDebuggingEditor\InputDebuggingEditor.Build.cs"><Link>Plugins\Runtime\InputDebugging\Source\InputDebuggingEditor\InputDebuggingEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\InputDebugging\Source\InputDebugging\InputDebugging.Build.cs"><Link>Plugins\Runtime\InputDebugging\Source\InputDebugging\InputDebugging.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\InstancedActors\Source\InstancedActorsTestSuite\InstancedActorsTestSuite.Build.cs"><Link>Plugins\Runtime\InstancedActors\Source\InstancedActorsTestSuite\InstancedActorsTestSuite.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\InstancedActors\Source\InstancedActors\InstancedActors.Build.cs"><Link>Plugins\Runtime\InstancedActors\Source\InstancedActors\InstancedActors.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\LevelStreamingPersistence\Source\LevelStreamingPersistence\LevelStreamingPersistence.Build.cs"><Link>Plugins\Runtime\LevelStreamingPersistence\Source\LevelStreamingPersistence\LevelStreamingPersistence.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\LinuxDeviceProfileSelector\Source\LinuxDeviceProfileSelector\LinuxDeviceProfileSelector.Build.cs"><Link>Plugins\Runtime\LinuxDeviceProfileSelector\Source\LinuxDeviceProfileSelector\LinuxDeviceProfileSelector.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\LiveLinkOvernDisplay\Source\LiveLinkOverNDisplay\LiveLinkOverNDisplay.Build.cs"><Link>Plugins\Runtime\LiveLinkOvernDisplay\Source\LiveLinkOverNDisplay\LiveLinkOverNDisplay.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\LocationServicesAndroidImpl\Source\LocationServicesAndroidEditor\LocationServicesAndroidEditor.Build.cs"><Link>Plugins\Runtime\LocationServicesAndroidImpl\Source\LocationServicesAndroidEditor\LocationServicesAndroidEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\LocationServicesAndroidImpl\Source\LocationServicesAndroidImpl\LocationServicesAndroidImpl.Build.cs"><Link>Plugins\Runtime\LocationServicesAndroidImpl\Source\LocationServicesAndroidImpl\LocationServicesAndroidImpl.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\LocationServicesBPLibrary\Source\LocationServicesBPLibrary\LocationServicesBPLibrary.Build.cs"><Link>Plugins\Runtime\LocationServicesBPLibrary\Source\LocationServicesBPLibrary\LocationServicesBPLibrary.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\LocationServicesIOSImpl\Source\LocationServicesIOSEditor\LocationServicesIOSEditor.Build.cs"><Link>Plugins\Runtime\LocationServicesIOSImpl\Source\LocationServicesIOSEditor\LocationServicesIOSEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\LocationServicesIOSImpl\Source\LocationServicesIOSImpl\LocationServicesIOSImpl.Build.cs"><Link>Plugins\Runtime\LocationServicesIOSImpl\Source\LocationServicesIOSImpl\LocationServicesIOSImpl.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\MIDIDevice\Source\MIDIDevice\MIDIDevice.Build.cs"><Link>Plugins\Runtime\MIDIDevice\Source\MIDIDevice\MIDIDevice.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\MassEntity\Source\MassEntityDebugger\MassEntityDebugger.Build.cs"><Link>Plugins\Runtime\MassEntity\Source\MassEntityDebugger\MassEntityDebugger.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\MassEntity\Source\MassEntityEditor\MassEntityEditor.Build.cs"><Link>Plugins\Runtime\MassEntity\Source\MassEntityEditor\MassEntityEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\MassEntity\Source\MassEntityTestSuite\MassEntityTestSuite.Build.cs"><Link>Plugins\Runtime\MassEntity\Source\MassEntityTestSuite\MassEntityTestSuite.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\MassEntity\Source\MassEntity\MassEntity.Build.cs"><Link>Plugins\Runtime\MassEntity\Source\MassEntity\MassEntity.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\MassGameplay\Source\MassActors\MassActors.Build.cs"><Link>Plugins\Runtime\MassGameplay\Source\MassActors\MassActors.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\MassGameplay\Source\MassCommon\MassCommon.Build.cs"><Link>Plugins\Runtime\MassGameplay\Source\MassCommon\MassCommon.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\MassGameplay\Source\MassGameplayDebug\MassGameplayDebug.Build.cs"><Link>Plugins\Runtime\MassGameplay\Source\MassGameplayDebug\MassGameplayDebug.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\MassGameplay\Source\MassGameplayEditor\MassGameplayEditor.Build.cs"><Link>Plugins\Runtime\MassGameplay\Source\MassGameplayEditor\MassGameplayEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\MassGameplay\Source\MassGameplayExternalTraits\MassGameplayExternalTraits.Build.cs"><Link>Plugins\Runtime\MassGameplay\Source\MassGameplayExternalTraits\MassGameplayExternalTraits.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\MassGameplay\Source\MassGameplayTestSuite\MassGameplayTestSuite.Build.cs"><Link>Plugins\Runtime\MassGameplay\Source\MassGameplayTestSuite\MassGameplayTestSuite.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\MassGameplay\Source\MassLOD\MassLOD.Build.cs"><Link>Plugins\Runtime\MassGameplay\Source\MassLOD\MassLOD.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\MassGameplay\Source\MassMovementEditor\MassMovementEditor.Build.cs"><Link>Plugins\Runtime\MassGameplay\Source\MassMovementEditor\MassMovementEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\MassGameplay\Source\MassMovement\MassMovement.Build.cs"><Link>Plugins\Runtime\MassGameplay\Source\MassMovement\MassMovement.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\MassGameplay\Source\MassReplication\MassReplication.Build.cs"><Link>Plugins\Runtime\MassGameplay\Source\MassReplication\MassReplication.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\MassGameplay\Source\MassRepresentation\MassRepresentation.Build.cs"><Link>Plugins\Runtime\MassGameplay\Source\MassRepresentation\MassRepresentation.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\MassGameplay\Source\MassSignals\MassSignals.Build.cs"><Link>Plugins\Runtime\MassGameplay\Source\MassSignals\MassSignals.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\MassGameplay\Source\MassSimulation\MassSimulation.Build.cs"><Link>Plugins\Runtime\MassGameplay\Source\MassSimulation\MassSimulation.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\MassGameplay\Source\MassSmartObjects\MassSmartObjects.Build.cs"><Link>Plugins\Runtime\MassGameplay\Source\MassSmartObjects\MassSmartObjects.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\MassGameplay\Source\MassSpawner\MassSpawner.Build.cs"><Link>Plugins\Runtime\MassGameplay\Source\MassSpawner\MassSpawner.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\MeshModelingToolset\Source\MeshModelingToolsEditorOnly\MeshModelingToolsEditorOnly.Build.cs"><Link>Plugins\Runtime\MeshModelingToolset\Source\MeshModelingToolsEditorOnly\MeshModelingToolsEditorOnly.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\MeshModelingToolset\Source\MeshModelingTools\MeshModelingTools.Build.cs"><Link>Plugins\Runtime\MeshModelingToolset\Source\MeshModelingTools\MeshModelingTools.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\MeshModelingToolset\Source\ModelingComponentsEditorOnly\ModelingComponentsEditorOnly.Build.cs"><Link>Plugins\Runtime\MeshModelingToolset\Source\ModelingComponentsEditorOnly\ModelingComponentsEditorOnly.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\MeshModelingToolset\Source\ModelingComponents\ModelingComponents.Build.cs"><Link>Plugins\Runtime\MeshModelingToolset\Source\ModelingComponents\ModelingComponents.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\MeshModelingToolset\Source\ModelingOperatorsEditorOnly\ModelingOperatorsEditorOnly.Build.cs"><Link>Plugins\Runtime\MeshModelingToolset\Source\ModelingOperatorsEditorOnly\ModelingOperatorsEditorOnly.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\MeshModelingToolset\Source\ModelingOperators\ModelingOperators.Build.cs"><Link>Plugins\Runtime\MeshModelingToolset\Source\ModelingOperators\ModelingOperators.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\MeshReconstruction\DummyMeshReconstructor\Source\DummyMeshReconstructor\DummyMeshReconstructor.Build.cs"><Link>Plugins\Runtime\MeshReconstruction\DummyMeshReconstructor\Source\DummyMeshReconstructor\DummyMeshReconstructor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\Metasound\Source\MetasoundEditor\MetasoundEditor.Build.cs"><Link>Plugins\Runtime\Metasound\Source\MetasoundEditor\MetasoundEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\Metasound\Source\MetasoundEngineTest\MetasoundEngineTest.Build.cs"><Link>Plugins\Runtime\Metasound\Source\MetasoundEngineTest\MetasoundEngineTest.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\Metasound\Source\MetasoundEngine\MetasoundEngine.Build.cs"><Link>Plugins\Runtime\Metasound\Source\MetasoundEngine\MetasoundEngine.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\Metasound\Source\MetasoundFrontend\MetasoundFrontend.Build.cs"><Link>Plugins\Runtime\Metasound\Source\MetasoundFrontend\MetasoundFrontend.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\Metasound\Source\MetasoundGenerator\MetasoundGenerator.Build.cs"><Link>Plugins\Runtime\Metasound\Source\MetasoundGenerator\MetasoundGenerator.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\Metasound\Source\MetasoundGraphCore\MetasoundGraphCore.Build.cs"><Link>Plugins\Runtime\Metasound\Source\MetasoundGraphCore\MetasoundGraphCore.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\Metasound\Source\MetasoundStandardNodes\MetasoundStandardNodes.Build.cs"><Link>Plugins\Runtime\Metasound\Source\MetasoundStandardNodes\MetasoundStandardNodes.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\MicrosoftSpatialAudio\Source\MicrosoftSpatialSound\MicrosoftSpatialSound.Build.cs"><Link>Plugins\Runtime\MicrosoftSpatialAudio\Source\MicrosoftSpatialSound\MicrosoftSpatialSound.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\MixedRealityCaptureFramework\Source\MixedRealityCaptureFramework\MixedRealityCaptureFramework.Build.cs"><Link>Plugins\Runtime\MixedRealityCaptureFramework\Source\MixedRealityCaptureFramework\MixedRealityCaptureFramework.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\MobileFSR\Source\MobileFSR\MobileFSR.Build.cs"><Link>Plugins\Runtime\MobileFSR\Source\MobileFSR\MobileFSR.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\MobilePatchingUtils\Source\MobilePatchingUtils\MobilePatchingUtils.Build.cs"><Link>Plugins\Runtime\MobilePatchingUtils\Source\MobilePatchingUtils\MobilePatchingUtils.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\ModelViewViewModel\Source\ModelViewViewModelAssetSearch\ModelViewViewModelAssetSearch.Build.cs"><Link>Plugins\Runtime\ModelViewViewModel\Source\ModelViewViewModelAssetSearch\ModelViewViewModelAssetSearch.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\ModelViewViewModel\Source\ModelViewViewModelBlueprint\ModelViewViewModelBlueprint.Build.cs"><Link>Plugins\Runtime\ModelViewViewModel\Source\ModelViewViewModelBlueprint\ModelViewViewModelBlueprint.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\ModelViewViewModel\Source\ModelViewViewModelDebuggerEditor\ModelViewViewModelDebuggerEditor.Build.cs"><Link>Plugins\Runtime\ModelViewViewModel\Source\ModelViewViewModelDebuggerEditor\ModelViewViewModelDebuggerEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\ModelViewViewModel\Source\ModelViewViewModelDebugger\ModelViewViewModelDebugger.Build.cs"><Link>Plugins\Runtime\ModelViewViewModel\Source\ModelViewViewModelDebugger\ModelViewViewModelDebugger.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\ModelViewViewModel\Source\ModelViewViewModelEditor\ModelViewViewModelEditor.Build.cs"><Link>Plugins\Runtime\ModelViewViewModel\Source\ModelViewViewModelEditor\ModelViewViewModelEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\ModelViewViewModel\Source\ModelViewViewModel\ModelViewViewModel.Build.cs"><Link>Plugins\Runtime\ModelViewViewModel\Source\ModelViewViewModel\ModelViewViewModel.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\ModularGameplay\Source\ModularGameplay\ModularGameplay.Build.cs"><Link>Plugins\Runtime\ModularGameplay\Source\ModularGameplay\ModularGameplay.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\MsQuic\Source\MsQuicRuntime.Build.cs"><Link>Plugins\Runtime\MsQuic\Source\MsQuicRuntime.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\NavCorridor\Source\NavCorridor\NavCorridor.Build.cs"><Link>Plugins\Runtime\NavCorridor\Source\NavCorridor\NavCorridor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\NetworkPredictionExtras\Source\NetworkPredictionExtrasLatentLoad\NetworkPredictionExtrasLatentLoad.Build.cs"><Link>Plugins\Runtime\NetworkPredictionExtras\Source\NetworkPredictionExtrasLatentLoad\NetworkPredictionExtrasLatentLoad.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\NetworkPredictionExtras\Source\NetworkPredictionExtras\NetworkPredictionExtras.Build.cs"><Link>Plugins\Runtime\NetworkPredictionExtras\Source\NetworkPredictionExtras\NetworkPredictionExtras.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\NetworkPredictionInsights\Source\NetworkPredictionInsights\NetworkPredictionInsights.Build.cs"><Link>Plugins\Runtime\NetworkPredictionInsights\Source\NetworkPredictionInsights\NetworkPredictionInsights.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\NetworkPrediction\Source\NetworkPrediction\NetworkPrediction.Build.cs"><Link>Plugins\Runtime\NetworkPrediction\Source\NetworkPrediction\NetworkPrediction.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\Nvidia\GeForceNOWWrapper\Source\GeForceNOWWrapper.Build.cs"><Link>Plugins\Runtime\Nvidia\GeForceNOWWrapper\Source\GeForceNOWWrapper.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\Nvidia\Reflex\Source\Reflex.Build.cs"><Link>Plugins\Runtime\Nvidia\Reflex\Source\Reflex.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\OSCModulationMixing\Source\OSCModulationMixing\OSCModulationMixing.Build.cs"><Link>Plugins\Runtime\OSCModulationMixing\Source\OSCModulationMixing\OSCModulationMixing.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\OSC\Source\OSC\OSC.Build.cs"><Link>Plugins\Runtime\OSC\Source\OSC\OSC.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\OpenCV\Source\OpenCVHelper\OpenCVHelper.Build.cs"><Link>Plugins\Runtime\OpenCV\Source\OpenCVHelper\OpenCVHelper.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\OpenCV\Source\ThirdParty\OpenCV\OpenCV.Build.cs"><Link>Plugins\Runtime\OpenCV\Source\ThirdParty\OpenCV\OpenCV.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\OpenXREyeTracker\Source\OpenXREyeTracker\OpenXREyeTracker.Build.cs"><Link>Plugins\Runtime\OpenXREyeTracker\Source\OpenXREyeTracker\OpenXREyeTracker.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\OpenXRHandTracking\Source\OpenXRHandTrackingEditor\OpenXRHandTrackingEditor.Build.cs"><Link>Plugins\Runtime\OpenXRHandTracking\Source\OpenXRHandTrackingEditor\OpenXRHandTrackingEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\OpenXRHandTracking\Source\OpenXRHandTracking\OpenXRHandTracking.Build.cs"><Link>Plugins\Runtime\OpenXRHandTracking\Source\OpenXRHandTracking\OpenXRHandTracking.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\OpenXRMsftHandInteraction\Source\OpenXRMsftHandInteraction\OpenXRMsftHandInteraction.Build.cs"><Link>Plugins\Runtime\OpenXRMsftHandInteraction\Source\OpenXRMsftHandInteraction\OpenXRMsftHandInteraction.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\OpenXRViveTracker\Source\OpenXRViveTracker\OpenXRViveTracker.Build.cs"><Link>Plugins\Runtime\OpenXRViveTracker\Source\OpenXRViveTracker\OpenXRViveTracker.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\OpenXR\Source\OpenXRAR\OpenXRAR.Build.cs"><Link>Plugins\Runtime\OpenXR\Source\OpenXRAR\OpenXRAR.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\OpenXR\Source\OpenXREditor\OpenXREditor.Build.cs"><Link>Plugins\Runtime\OpenXR\Source\OpenXREditor\OpenXREditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\OpenXR\Source\OpenXRHMD\OpenXRHMD.Build.cs"><Link>Plugins\Runtime\OpenXR\Source\OpenXRHMD\OpenXRHMD.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\OpenXR\Source\OpenXRInput\OpenXRInput.Build.cs"><Link>Plugins\Runtime\OpenXR\Source\OpenXRInput\OpenXRInput.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\OptionalMobileFeaturesBPLibrary\Source\OptionalMobileFeaturesBPLibrary\OptionalMobileFeaturesBPLibrary.Build.cs"><Link>Plugins\Runtime\OptionalMobileFeaturesBPLibrary\Source\OptionalMobileFeaturesBPLibrary\OptionalMobileFeaturesBPLibrary.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\PacketHandlers\AESGCMHandlerComponent\Source\AESGCMHandlerComponent.Build.cs"><Link>Plugins\Runtime\PacketHandlers\AESGCMHandlerComponent\Source\AESGCMHandlerComponent.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\PacketHandlers\AESHandlerComponent\Source\AESHandlerComponent.Build.cs"><Link>Plugins\Runtime\PacketHandlers\AESHandlerComponent\Source\AESHandlerComponent.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\PacketHandlers\DTLSHandlerComponent\Source\DTLSHandlerComponent.Build.cs"><Link>Plugins\Runtime\PacketHandlers\DTLSHandlerComponent\Source\DTLSHandlerComponent.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\PreLoadScreenMoviePlayer\Source\PreLoadScreenMoviePlayer\PreLoadScreenMoviePlayer.Build.cs"><Link>Plugins\Runtime\PreLoadScreenMoviePlayer\Source\PreLoadScreenMoviePlayer\PreLoadScreenMoviePlayer.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\ProceduralMeshComponentEx\Source\ProceduralMeshComponentExEditor\ProceduralMeshComponentExEditor.Build.cs"><Link>Plugins\Runtime\ProceduralMeshComponentEx\Source\ProceduralMeshComponentExEditor\ProceduralMeshComponentExEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\ProceduralMeshComponentEx\Source\ProceduralMeshComponentEx\ProceduralMeshComponentEx.Build.cs"><Link>Plugins\Runtime\ProceduralMeshComponentEx\Source\ProceduralMeshComponentEx\ProceduralMeshComponentEx.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\ProceduralMeshComponent\Source\ProceduralMeshComponentEditor\ProceduralMeshComponentEditor.Build.cs"><Link>Plugins\Runtime\ProceduralMeshComponent\Source\ProceduralMeshComponentEditor\ProceduralMeshComponentEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\ProceduralMeshComponent\Source\ProceduralMeshComponent\ProceduralMeshComponent.Build.cs"><Link>Plugins\Runtime\ProceduralMeshComponent\Source\ProceduralMeshComponent\ProceduralMeshComponent.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\PropertyAccess\Source\PropertyAccessEditor\PropertyAccessEditor.Build.cs"><Link>Plugins\Runtime\PropertyAccess\Source\PropertyAccessEditor\PropertyAccessEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\PropertyBindingUtils\Source\PropertyBindingUtilsTestSuite\PropertyBindingUtilsTestSuite.Build.cs"><Link>Plugins\Runtime\PropertyBindingUtils\Source\PropertyBindingUtilsTestSuite\PropertyBindingUtilsTestSuite.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\PropertyBindingUtils\Source\PropertyBindingUtils\PropertyBindingUtils.Build.cs"><Link>Plugins\Runtime\PropertyBindingUtils\Source\PropertyBindingUtils\PropertyBindingUtils.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\RenderTrace\Source\RenderTrace.Build.cs"><Link>Plugins\Runtime\RenderTrace\Source\RenderTrace.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\ReplicationGraph\Source\ReplicationGraph.Build.cs"><Link>Plugins\Runtime\ReplicationGraph\Source\ReplicationGraph.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\ReplicationSystemTestPlugin\Source\ReplicationSystemTestPlugin.Build.cs"><Link>Plugins\Runtime\ReplicationSystemTestPlugin\Source\ReplicationSystemTestPlugin.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\ResonanceAudio\Source\ResonanceAudioEditor\ResonanceAudioEditor.Build.cs"><Link>Plugins\Runtime\ResonanceAudio\Source\ResonanceAudioEditor\ResonanceAudioEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\ResonanceAudio\Source\ResonanceAudio\ResonanceAudio.Build.cs"><Link>Plugins\Runtime\ResonanceAudio\Source\ResonanceAudio\ResonanceAudio.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\RigVM\Source\RigVMDeveloper\RigVMDeveloper.Build.cs"><Link>Plugins\Runtime\RigVM\Source\RigVMDeveloper\RigVMDeveloper.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\RigVM\Source\RigVMEditor\RigVMEditor.Build.cs"><Link>Plugins\Runtime\RigVM\Source\RigVMEditor\RigVMEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\RigVM\Source\RigVM\RigVM.Build.cs"><Link>Plugins\Runtime\RigVM\Source\RigVM\RigVM.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\ScriptableToolsFramework\Source\EditorScriptableToolsFramework\EditorScriptableToolsFramework.Build.cs"><Link>Plugins\Runtime\ScriptableToolsFramework\Source\EditorScriptableToolsFramework\EditorScriptableToolsFramework.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\ScriptableToolsFramework\Source\ScriptableToolsFramework\ScriptableToolsFramework.Build.cs"><Link>Plugins\Runtime\ScriptableToolsFramework\Source\ScriptableToolsFramework\ScriptableToolsFramework.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\SignificanceManager\Source\SignificanceManager\SignificanceManager.Build.cs"><Link>Plugins\Runtime\SignificanceManager\Source\SignificanceManager\SignificanceManager.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\SkeletalMerging\Source\SkeletalMerging\SkeletalMerging.build.cs"><Link>Plugins\Runtime\SkeletalMerging\Source\SkeletalMerging\SkeletalMerging.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\SmartObjects\Source\SmartObjectsEditorModule\SmartObjectsEditorModule.Build.cs"><Link>Plugins\Runtime\SmartObjects\Source\SmartObjectsEditorModule\SmartObjectsEditorModule.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\SmartObjects\Source\SmartObjectsModule\SmartObjectsModule.Build.cs"><Link>Plugins\Runtime\SmartObjects\Source\SmartObjectsModule\SmartObjectsModule.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\SmartObjects\Source\SmartObjectsTestSuite\SmartObjectsTestSuite.Build.cs"><Link>Plugins\Runtime\SmartObjects\Source\SmartObjectsTestSuite\SmartObjectsTestSuite.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\SoundCueTemplates\Source\SoundCueTemplatesEditor\SoundCueTemplatesEditor.Build.cs"><Link>Plugins\Runtime\SoundCueTemplates\Source\SoundCueTemplatesEditor\SoundCueTemplatesEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\SoundCueTemplates\Source\SoundCueTemplates\SoundCueTemplates.Build.cs"><Link>Plugins\Runtime\SoundCueTemplates\Source\SoundCueTemplates\SoundCueTemplates.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\SoundFields\Source\SoundFields\SoundFields.Build.cs"><Link>Plugins\Runtime\SoundFields\Source\SoundFields\SoundFields.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\SoundMod\Source\SoundModImporter\SoundModImporter.Build.cs"><Link>Plugins\Runtime\SoundMod\Source\SoundModImporter\SoundModImporter.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\SoundMod\Source\SoundMod\SoundMod.Build.cs"><Link>Plugins\Runtime\SoundMod\Source\SoundMod\SoundMod.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\SoundUtilities\Source\SoundUtilitiesEditor\SoundUtilitiesEditor.Build.cs"><Link>Plugins\Runtime\SoundUtilities\Source\SoundUtilitiesEditor\SoundUtilitiesEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\SoundUtilities\Source\SoundUtilities\SoundUtilities.Build.cs"><Link>Plugins\Runtime\SoundUtilities\Source\SoundUtilities\SoundUtilities.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\Soundscape\Source\SoundScapeEditor\SoundScapeEditor.Build.cs"><Link>Plugins\Runtime\Soundscape\Source\SoundScapeEditor\SoundScapeEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\Soundscape\Source\SoundScape\SoundScape.Build.cs"><Link>Plugins\Runtime\Soundscape\Source\SoundScape\SoundScape.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\Spatialization\Source\SpatializationEditor\SpatializationEditor.Build.cs"><Link>Plugins\Runtime\Spatialization\Source\SpatializationEditor\SpatializationEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\Spatialization\Source\Spatialization\Spatialization.Build.cs"><Link>Plugins\Runtime\Spatialization\Source\Spatialization\Spatialization.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\StateTree\Source\StateTreeEditorModule\StateTreeEditorModule.Build.cs"><Link>Plugins\Runtime\StateTree\Source\StateTreeEditorModule\StateTreeEditorModule.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\StateTree\Source\StateTreeModule\StateTreeModule.Build.cs"><Link>Plugins\Runtime\StateTree\Source\StateTreeModule\StateTreeModule.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\StateTree\Source\StateTreeTestSuite\StateTreeTestSuite.Build.cs"><Link>Plugins\Runtime\StateTree\Source\StateTreeTestSuite\StateTreeTestSuite.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\Steam\SteamAudio\Source\SteamAudio\SteamAudio.Build.cs"><Link>Plugins\Runtime\Steam\SteamAudio\Source\SteamAudio\SteamAudio.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\Steam\SteamController\Source\SteamController\SteamController.Build.cs"><Link>Plugins\Runtime\Steam\SteamController\Source\SteamController\SteamController.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\Steam\SteamShared\Source\SteamShared\SteamShared.Build.cs"><Link>Plugins\Runtime\Steam\SteamShared\Source\SteamShared\SteamShared.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\Steam\SteamSockets\Source\SteamSockets\SteamSockets.Build.cs"><Link>Plugins\Runtime\Steam\SteamSockets\Source\SteamSockets\SteamSockets.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\SunPosition\Source\SunPosition\SunPosition.build.cs"><Link>Plugins\Runtime\SunPosition\Source\SunPosition\SunPosition.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\Synthesis\Source\SynthesisEditor\SynthesisEditor.Build.cs"><Link>Plugins\Runtime\Synthesis\Source\SynthesisEditor\SynthesisEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\Synthesis\Source\Synthesis\Synthesis.Build.cs"><Link>Plugins\Runtime\Synthesis\Source\Synthesis\Synthesis.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\WarpUtils\Source\PFMExporter\PFMExporter.Build.cs"><Link>Plugins\Runtime\WarpUtils\Source\PFMExporter\PFMExporter.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\WarpUtils\Source\WarpUtils\WarpUtils.Build.cs"><Link>Plugins\Runtime\WarpUtils\Source\WarpUtils\WarpUtils.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\WaveTable\Source\WaveTableEditor\WaveTableEditor.Build.cs"><Link>Plugins\Runtime\WaveTable\Source\WaveTableEditor\WaveTableEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\WaveTable\Source\WaveTable\WaveTable.Build.cs"><Link>Plugins\Runtime\WaveTable\Source\WaveTable\WaveTable.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\WebBrowserNativeProxy\Source\WebBrowserNativeProxy\WebBrowserNativeProxy.build.cs"><Link>Plugins\Runtime\WebBrowserNativeProxy\Source\WebBrowserNativeProxy\WebBrowserNativeProxy.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\WebBrowserWidget\Source\WebBrowserWidget\WebBrowserWidget.build.cs"><Link>Plugins\Runtime\WebBrowserWidget\Source\WebBrowserWidget\WebBrowserWidget.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\WebMMoviePlayer\Source\WebMMoviePlayer\WebMMoviePlayer.Build.cs"><Link>Plugins\Runtime\WebMMoviePlayer\Source\WebMMoviePlayer\WebMMoviePlayer.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\WindowsDeviceProfileSelector\Source\WindowsDeviceProfileSelector\WindowsDeviceProfileSelector.Build.cs"><Link>Plugins\Runtime\WindowsDeviceProfileSelector\Source\WindowsDeviceProfileSelector\WindowsDeviceProfileSelector.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\WindowsMoviePlayer\Source\WindowsMoviePlayer\WindowsMoviePlayer.Build.cs"><Link>Plugins\Runtime\WindowsMoviePlayer\Source\WindowsMoviePlayer\WindowsMoviePlayer.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\Windows\WinDualShock\Source\WinDualShock\WinDualShock.Build.cs"><Link>Plugins\Runtime\Windows\WinDualShock\Source\WinDualShock\WinDualShock.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\Windows\XInputDevice\Source\XInputDevice\XInputDevice.build.cs"><Link>Plugins\Runtime\Windows\XInputDevice\Source\XInputDevice\XInputDevice.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\WorldConditions\Source\WorldConditionsEditor\WorldConditionsEditor.Build.cs"><Link>Plugins\Runtime\WorldConditions\Source\WorldConditionsEditor\WorldConditionsEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\WorldConditions\Source\WorldConditionsTestSuite\WorldConditionsTestSuite.Build.cs"><Link>Plugins\Runtime\WorldConditions\Source\WorldConditionsTestSuite\WorldConditionsTestSuite.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\WorldConditions\Source\WorldConditions\WorldConditions.Build.cs"><Link>Plugins\Runtime\WorldConditions\Source\WorldConditions\WorldConditions.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\XRBase\Source\XRBaseEditor\XRBaseEditor.Build.cs"><Link>Plugins\Runtime\XRBase\Source\XRBaseEditor\XRBaseEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\XRBase\Source\XRBase\XRBase.Build.cs"><Link>Plugins\Runtime\XRBase\Source\XRBase\XRBase.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\XR\XRScribe\Source\XRScribe\XRScribe.Build.cs"><Link>Plugins\Runtime\XR\XRScribe\Source\XRScribe\XRScribe.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\XR\XRVisualization\Source\XRVisualization\XRVisualization.Build.cs"><Link>Plugins\Runtime\XR\XRVisualization\Source\XRVisualization\XRVisualization.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\ZoneGraphAnnotations\Source\ZoneGraphAnnotations\ZoneGraphAnnotations.Build.cs"><Link>Plugins\Runtime\ZoneGraphAnnotations\Source\ZoneGraphAnnotations\ZoneGraphAnnotations.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\ZoneGraph\Source\ZoneGraphDebug\ZoneGraphDebug.Build.cs"><Link>Plugins\Runtime\ZoneGraph\Source\ZoneGraphDebug\ZoneGraphDebug.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\ZoneGraph\Source\ZoneGraphEditor\ZoneGraphEditor.Build.cs"><Link>Plugins\Runtime\ZoneGraph\Source\ZoneGraphEditor\ZoneGraphEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\ZoneGraph\Source\ZoneGraphTestSuite\ZoneGraphTestSuite.Build.cs"><Link>Plugins\Runtime\ZoneGraph\Source\ZoneGraphTestSuite\ZoneGraphTestSuite.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\ZoneGraph\Source\ZoneGraph\ZoneGraph.Build.cs"><Link>Plugins\Runtime\ZoneGraph\Source\ZoneGraph\ZoneGraph.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\nDisplayModularFeatures\Source\DisplayClusterLightCardExtender\DisplayClusterLightCardExtender.build.cs"><Link>Plugins\Runtime\nDisplayModularFeatures\Source\DisplayClusterLightCardExtender\DisplayClusterLightCardExtender.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\nDisplayModularFeatures\Source\DisplayClusterModularFeaturesEditor\DisplayClusterModularFeaturesEditor.build.cs"><Link>Plugins\Runtime\nDisplayModularFeatures\Source\DisplayClusterModularFeaturesEditor\DisplayClusterModularFeaturesEditor.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\nDisplay\Source\DisplayClusterColorGrading\DisplayClusterColorGrading.Build.cs"><Link>Plugins\Runtime\nDisplay\Source\DisplayClusterColorGrading\DisplayClusterColorGrading.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\nDisplay\Source\DisplayClusterConfiguration\DisplayClusterConfiguration.Build.cs"><Link>Plugins\Runtime\nDisplay\Source\DisplayClusterConfiguration\DisplayClusterConfiguration.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\nDisplay\Source\DisplayClusterConfigurator\DisplayClusterConfigurator.Build.cs"><Link>Plugins\Runtime\nDisplay\Source\DisplayClusterConfigurator\DisplayClusterConfigurator.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\nDisplay\Source\DisplayClusterEditor\DisplayClusterEditor.Build.cs"><Link>Plugins\Runtime\nDisplay\Source\DisplayClusterEditor\DisplayClusterEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\nDisplay\Source\DisplayClusterFillDerivedDataCache\DisplayClusterFillDerivedDataCache.build.cs"><Link>Plugins\Runtime\nDisplay\Source\DisplayClusterFillDerivedDataCache\DisplayClusterFillDerivedDataCache.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\nDisplay\Source\DisplayClusterLightCardEditorShaders\DisplayClusterLightCardEditorShaders.Build.cs"><Link>Plugins\Runtime\nDisplay\Source\DisplayClusterLightCardEditorShaders\DisplayClusterLightCardEditorShaders.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\nDisplay\Source\DisplayClusterLightCardEditor\DisplayClusterLightCardEditor.Build.cs"><Link>Plugins\Runtime\nDisplay\Source\DisplayClusterLightCardEditor\DisplayClusterLightCardEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\nDisplay\Source\DisplayClusterMediaEditor\DisplayClusterMediaEditor.Build.cs"><Link>Plugins\Runtime\nDisplay\Source\DisplayClusterMediaEditor\DisplayClusterMediaEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\nDisplay\Source\DisplayClusterMedia\DisplayClusterMedia.Build.cs"><Link>Plugins\Runtime\nDisplay\Source\DisplayClusterMedia\DisplayClusterMedia.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\nDisplay\Source\DisplayClusterMessageInterceptor\DisplayClusterMessageInterception.Build.cs"><Link>Plugins\Runtime\nDisplay\Source\DisplayClusterMessageInterceptor\DisplayClusterMessageInterception.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\nDisplay\Source\DisplayClusterMoviePipelineEditor\DisplayClusterMoviePipelineEditor.Build.cs"><Link>Plugins\Runtime\nDisplay\Source\DisplayClusterMoviePipelineEditor\DisplayClusterMoviePipelineEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\nDisplay\Source\DisplayClusterMoviePipeline\DisplayClusterMoviePipeline.Build.cs"><Link>Plugins\Runtime\nDisplay\Source\DisplayClusterMoviePipeline\DisplayClusterMoviePipeline.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\nDisplay\Source\DisplayClusterMultiUser\DisplayClusterMultiUser.Build.cs"><Link>Plugins\Runtime\nDisplay\Source\DisplayClusterMultiUser\DisplayClusterMultiUser.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\nDisplay\Source\DisplayClusterOperator\DisplayClusterOperator.Build.cs"><Link>Plugins\Runtime\nDisplay\Source\DisplayClusterOperator\DisplayClusterOperator.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\nDisplay\Source\DisplayClusterProjection\DisplayClusterProjection.Build.cs"><Link>Plugins\Runtime\nDisplay\Source\DisplayClusterProjection\DisplayClusterProjection.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\nDisplay\Source\DisplayClusterRemoteControlInterceptor\DisplayClusterRemoteControlInterceptor.Build.cs"><Link>Plugins\Runtime\nDisplay\Source\DisplayClusterRemoteControlInterceptor\DisplayClusterRemoteControlInterceptor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\nDisplay\Source\DisplayClusterReplication\DisplayClusterReplication.Build.cs"><Link>Plugins\Runtime\nDisplay\Source\DisplayClusterReplication\DisplayClusterReplication.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\nDisplay\Source\DisplayClusterScenePreview\DisplayClusterScenePreview.Build.cs"><Link>Plugins\Runtime\nDisplay\Source\DisplayClusterScenePreview\DisplayClusterScenePreview.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\nDisplay\Source\DisplayClusterShaders\DisplayClusterShaders.Build.cs"><Link>Plugins\Runtime\nDisplay\Source\DisplayClusterShaders\DisplayClusterShaders.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\nDisplay\Source\DisplayClusterStageMonitoring\DisplayClusterStageMonitoring.Build.cs"><Link>Plugins\Runtime\nDisplay\Source\DisplayClusterStageMonitoring\DisplayClusterStageMonitoring.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\nDisplay\Source\DisplayClusterTests\DisplayClusterTests.Build.cs"><Link>Plugins\Runtime\nDisplay\Source\DisplayClusterTests\DisplayClusterTests.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\nDisplay\Source\DisplayClusterWarp\DisplayClusterWarp.Build.cs"><Link>Plugins\Runtime\nDisplay\Source\DisplayClusterWarp\DisplayClusterWarp.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\nDisplay\Source\DisplayCluster\DisplayCluster.Build.cs"><Link>Plugins\Runtime\nDisplay\Source\DisplayCluster\DisplayCluster.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\nDisplay\Source\SharedMemoryMediaEditor\SharedMemoryMediaEditor.Build.cs"><Link>Plugins\Runtime\nDisplay\Source\SharedMemoryMediaEditor\SharedMemoryMediaEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Runtime\nDisplay\Source\SharedMemoryMedia\SharedMemoryMedia.Build.cs"><Link>Plugins\Runtime\nDisplay\Source\SharedMemoryMedia\SharedMemoryMedia.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\SceneCapture\Source\SceneCapture\SceneCapture.Build.cs"><Link>Plugins\SceneCapture\Source\SceneCapture\SceneCapture.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\ScriptPlugin\Source\ScriptEditorPlugin\ScriptEditorPlugin.Build.cs"><Link>Plugins\ScriptPlugin\Source\ScriptEditorPlugin\ScriptEditorPlugin.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\ScriptPlugin\Source\ScriptPlugin\ScriptPlugin.Build.cs"><Link>Plugins\ScriptPlugin\Source\ScriptPlugin\ScriptPlugin.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Slate\SlateInsights\Source\SlateInsights\SlateInsights.Build.cs"><Link>Plugins\Slate\SlateInsights\Source\SlateInsights\SlateInsights.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Slate\SlateScripting\Source\SlateScriptingCommands\SlateScriptingCommands.Build.cs"><Link>Plugins\Slate\SlateScripting\Source\SlateScriptingCommands\SlateScriptingCommands.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\SmartGI\Source\AdaptiveProbes\SmartDDGI.Build.cs"><Link>Plugins\SmartGI\Source\AdaptiveProbes\SmartDDGI.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\SmartGI\Source\Bricks\SmartBrickGI.Build.cs"><Link>Plugins\SmartGI\Source\Bricks\SmartBrickGI.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\SmartGI\Source\Core\SmartCore.Build.cs"><Link>Plugins\SmartGI\Source\Core\SmartCore.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\SmartGI\Source\LightingCache\SmartLightingCache.Build.cs"><Link>Plugins\SmartGI\Source\LightingCache\SmartLightingCache.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\SmartGI\Source\Reflections\SmartReflections.Build.cs"><Link>Plugins\SmartGI\Source\Reflections\SmartReflections.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\SmartGI\Source\SmartGI\SmartGI.Build.cs"><Link>Plugins\SmartGI\Source\SmartGI\SmartGI.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\SmartGI\Source\Surfels\SmartSurfelGI.Build.cs"><Link>Plugins\SmartGI\Source\Surfels\SmartSurfelGI.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\SmartGI\Source\VolumeLighting\SmartVolumeLighting.Build.cs"><Link>Plugins\SmartGI\Source\VolumeLighting\SmartVolumeLighting.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\SmartGI\Source\Voxels\SmartVoxelGI.Build.cs"><Link>Plugins\SmartGI\Source\Voxels\SmartVoxelGI.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Tests\AutomationDriverTests\Source\AutomationDriverTests\AutomationDriverTests.Build.cs"><Link>Plugins\Tests\AutomationDriverTests\Source\AutomationDriverTests\AutomationDriverTests.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Tests\CQTest\Source\CQTestTests\CQTestTests.Build.cs"><Link>Plugins\Tests\CQTest\Source\CQTestTests\CQTestTests.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Tests\CQTest\Source\CQTest\CQTest.Build.cs"><Link>Plugins\Tests\CQTest\Source\CQTest\CQTest.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Tests\EditorTests\Source\EditorTests\EditorTests.Build.cs"><Link>Plugins\Tests\EditorTests\Source\EditorTests\EditorTests.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Tests\FbxAutomationTestBuilder\Source\FbxAutomationTestBuilder\FbxAutomationTestBuilder.Build.cs"><Link>Plugins\Tests\FbxAutomationTestBuilder\Source\FbxAutomationTestBuilder\FbxAutomationTestBuilder.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Tests\FunctionalTestingEditor\Source\FunctionalTestingEditor.Build.cs"><Link>Plugins\Tests\FunctionalTestingEditor\Source\FunctionalTestingEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Tests\InterchangeTests\Source\InterchangeTestEditor\InterchangeTestEditor.Build.cs"><Link>Plugins\Tests\InterchangeTests\Source\InterchangeTestEditor\InterchangeTestEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Tests\InterchangeTests\Source\InterchangeTests\InterchangeTests.Build.cs"><Link>Plugins\Tests\InterchangeTests\Source\InterchangeTests\InterchangeTests.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Tests\PythonAutomationTest\Source\PythonAutomationTest\PythonAutomationTest.Build.cs"><Link>Plugins\Tests\PythonAutomationTest\Source\PythonAutomationTest\PythonAutomationTest.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Tests\RHITests\Source\RHITests\RHITests.Build.cs"><Link>Plugins\Tests\RHITests\Source\RHITests\RHITests.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Tests\RuntimeTests\Source\RuntimeTests\RuntimeTests.Build.cs"><Link>Plugins\Tests\RuntimeTests\Source\RuntimeTests\RuntimeTests.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Tests\TestFramework\Source\TestFramework\TestFramework.Build.cs"><Link>Plugins\Tests\TestFramework\Source\TestFramework\TestFramework.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Tests\TestSamples\Source\TestSamples\TestSamples.Build.cs"><Link>Plugins\Tests\TestSamples\Source\TestSamples\TestSamples.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Tests\WidgetAutomationTests\Source\WidgetAutomationTests\WidgetAutomationTests.Build.cs"><Link>Plugins\Tests\WidgetAutomationTests\Source\WidgetAutomationTests\WidgetAutomationTests.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\TextureProcessing\Source\TextureProcessingEditor\TextureProcessingEditor.Build.cs"><Link>Plugins\TextureProcessing\Source\TextureProcessingEditor\TextureProcessingEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\TextureProcessing\Source\TextureProcessing\TextureProcessing.Build.cs"><Link>Plugins\TextureProcessing\Source\TextureProcessing\TextureProcessing.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\ToolbarRegister\Source\ToolbarRegister\ToolbarRegister.Build.cs"><Link>Plugins\ToolbarRegister\Source\ToolbarRegister\ToolbarRegister.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\TraceUtilities\Source\EditorTraceUtilities\EditorTraceUtilities.Build.cs"><Link>Plugins\TraceUtilities\Source\EditorTraceUtilities\EditorTraceUtilities.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\TraceUtilities\Source\TraceUtilities\TraceUtilities.Build.cs"><Link>Plugins\TraceUtilities\Source\TraceUtilities\TraceUtilities.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\UGC\Source\UGCBaseEditor\UGCBaseEditor.Build.cs"><Link>Plugins\UGC\Source\UGCBaseEditor\UGCBaseEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\UGC\Source\UGCBase\UGCBase.Build.cs"><Link>Plugins\UGC\Source\UGCBase\UGCBase.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\UGC\Source\UGCProfileEditor\UGCProfileEditor.Build.cs"><Link>Plugins\UGC\Source\UGCProfileEditor\UGCProfileEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\UGC\Source\UGCProfile\UGCProfile.Build.cs"><Link>Plugins\UGC\Source\UGCProfile\UGCProfile.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\UbaController\Source\UbaController\UbaController.build.cs"><Link>Plugins\UbaController\Source\UbaController\UbaController.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\CameraCalibrationCore\Source\CameraCalibrationCoreEditor\CameraCalibrationCoreEditor.Build.cs"><Link>Plugins\VirtualProduction\CameraCalibrationCore\Source\CameraCalibrationCoreEditor\CameraCalibrationCoreEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\CameraCalibrationCore\Source\CameraCalibrationCore\CameraCalibrationCore.Build.cs"><Link>Plugins\VirtualProduction\CameraCalibrationCore\Source\CameraCalibrationCore\CameraCalibrationCore.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\CameraCalibration\Source\CameraCalibrationEditor\CameraCalibrationEditor.Build.cs"><Link>Plugins\VirtualProduction\CameraCalibration\Source\CameraCalibrationEditor\CameraCalibrationEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\CompositePlane\Source\CompositePlane\CompositePlane.Build.cs"><Link>Plugins\VirtualProduction\CompositePlane\Source\CompositePlane\CompositePlane.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\DMX\DMXControlConsole\Source\DMXControlConsoleEditor\DMXControlConsoleEditor.Build.cs"><Link>Plugins\VirtualProduction\DMX\DMXControlConsole\Source\DMXControlConsoleEditor\DMXControlConsoleEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\DMX\DMXControlConsole\Source\DMXControlConsole\DMXControlConsole.Build.cs"><Link>Plugins\VirtualProduction\DMX\DMXControlConsole\Source\DMXControlConsole\DMXControlConsole.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\DMX\DMXDisplayCluster\Source\DMXDisplayClusterLightCard\DMXDisplayClusterLightCard.Build.cs"><Link>Plugins\VirtualProduction\DMX\DMXDisplayCluster\Source\DMXDisplayClusterLightCard\DMXDisplayClusterLightCard.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\DMX\DMXDisplayCluster\Source\DMXDisplayCluster\DMXDisplayCluster.Build.cs"><Link>Plugins\VirtualProduction\DMX\DMXDisplayCluster\Source\DMXDisplayCluster\DMXDisplayCluster.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\DMX\DMXEngine\Source\DMXBlueprintGraph\DMXBlueprintGraph.Build.cs"><Link>Plugins\VirtualProduction\DMX\DMXEngine\Source\DMXBlueprintGraph\DMXBlueprintGraph.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\DMX\DMXEngine\Source\DMXEditor\DMXEditor.Build.cs"><Link>Plugins\VirtualProduction\DMX\DMXEngine\Source\DMXEditor\DMXEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\DMX\DMXEngine\Source\DMXRuntime\DMXRuntime.Build.cs"><Link>Plugins\VirtualProduction\DMX\DMXEngine\Source\DMXRuntime\DMXRuntime.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\DMX\DMXFixtures\Source\DMXFixtures\DMXFixtures.Build.cs"><Link>Plugins\VirtualProduction\DMX\DMXFixtures\Source\DMXFixtures\DMXFixtures.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\DMX\DMXModularFeatures\Source\DMXFixtureActorInterface\DMXFixtureActorInterface.build.cs"><Link>Plugins\VirtualProduction\DMX\DMXModularFeatures\Source\DMXFixtureActorInterface\DMXFixtureActorInterface.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\DMX\DMXPixelMapping\Source\DMXPixelMappingBlueprintGraph\DMXPixelMappingBlueprintGraph.Build.cs"><Link>Plugins\VirtualProduction\DMX\DMXPixelMapping\Source\DMXPixelMappingBlueprintGraph\DMXPixelMappingBlueprintGraph.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\DMX\DMXPixelMapping\Source\DMXPixelMappingCore\DMXPixelMappingCore.build.cs"><Link>Plugins\VirtualProduction\DMX\DMXPixelMapping\Source\DMXPixelMappingCore\DMXPixelMappingCore.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\DMX\DMXPixelMapping\Source\DMXPixelMappingEditorWidgets\DMXPixelMappingEditorWidgets.Build.cs"><Link>Plugins\VirtualProduction\DMX\DMXPixelMapping\Source\DMXPixelMappingEditorWidgets\DMXPixelMappingEditorWidgets.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\DMX\DMXPixelMapping\Source\DMXPixelMappingEditor\DMXPixelMappingEditor.Build.cs"><Link>Plugins\VirtualProduction\DMX\DMXPixelMapping\Source\DMXPixelMappingEditor\DMXPixelMappingEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\DMX\DMXPixelMapping\Source\DMXPixelMappingRenderer\DMXPixelMappingRenderer.Build.cs"><Link>Plugins\VirtualProduction\DMX\DMXPixelMapping\Source\DMXPixelMappingRenderer\DMXPixelMappingRenderer.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\DMX\DMXPixelMapping\Source\DMXPixelMappingRuntime\DMXPixelMappingRuntime.build.cs"><Link>Plugins\VirtualProduction\DMX\DMXPixelMapping\Source\DMXPixelMappingRuntime\DMXPixelMappingRuntime.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\DMX\DMXProtocol\Source\DMXProtocolArtNet\DMXProtocolArtNet.Build.cs"><Link>Plugins\VirtualProduction\DMX\DMXProtocol\Source\DMXProtocolArtNet\DMXProtocolArtNet.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\DMX\DMXProtocol\Source\DMXProtocolBlueprintGraph\DMXProtocolBlueprintGraph.Build.cs"><Link>Plugins\VirtualProduction\DMX\DMXProtocol\Source\DMXProtocolBlueprintGraph\DMXProtocolBlueprintGraph.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\DMX\DMXProtocol\Source\DMXProtocolEditor\DMXProtocolEditor.Build.cs"><Link>Plugins\VirtualProduction\DMX\DMXProtocol\Source\DMXProtocolEditor\DMXProtocolEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\DMX\DMXProtocol\Source\DMXProtocolSACN\DMXProtocolsACN.Build.cs"><Link>Plugins\VirtualProduction\DMX\DMXProtocol\Source\DMXProtocolSACN\DMXProtocolsACN.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\DMX\DMXProtocol\Source\DMXProtocol\DMXProtocol.Build.cs"><Link>Plugins\VirtualProduction\DMX\DMXProtocol\Source\DMXProtocol\DMXProtocol.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\DMX\DatasmithMVR\Source\DatasmithMVRTranslator\DatasmithMVRTranslator.Build.cs"><Link>Plugins\VirtualProduction\DMX\DatasmithMVR\Source\DatasmithMVRTranslator\DatasmithMVRTranslator.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\DataCharts\Source\DataChartsEditor\DataChartsEditor.Build.cs"><Link>Plugins\VirtualProduction\DataCharts\Source\DataChartsEditor\DataChartsEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\DataCharts\Source\DataCharts\DataCharts.Build.cs"><Link>Plugins\VirtualProduction\DataCharts\Source\DataCharts\DataCharts.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\EpicStageApp\Source\EpicStageApp\EpicStageApp.build.cs"><Link>Plugins\VirtualProduction\EpicStageApp\Source\EpicStageApp\EpicStageApp.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\ICVFXTesting\Source\ICVFXTesting\ICVFXTesting.build.cs"><Link>Plugins\VirtualProduction\ICVFXTesting\Source\ICVFXTesting\ICVFXTesting.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\LensComponent\Source\LensComponentEditor\LensComponentEditor.build.cs"><Link>Plugins\VirtualProduction\LensComponent\Source\LensComponentEditor\LensComponentEditor.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\LensComponent\Source\LensComponent\LensComponent.build.cs"><Link>Plugins\VirtualProduction\LensComponent\Source\LensComponent\LensComponent.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\LevelSnapshots\Source\FoliageSupport\FoliageSupport.Build.cs"><Link>Plugins\VirtualProduction\LevelSnapshots\Source\FoliageSupport\FoliageSupport.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\LevelSnapshots\Source\LevelShapshotFilters\LevelSnapshotFilters.Build.cs"><Link>Plugins\VirtualProduction\LevelSnapshots\Source\LevelShapshotFilters\LevelSnapshotFilters.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\LevelSnapshots\Source\LevelSnapshotsEditor\LevelSnapshotsEditor.build.cs"><Link>Plugins\VirtualProduction\LevelSnapshots\Source\LevelSnapshotsEditor\LevelSnapshotsEditor.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\LevelSnapshots\Source\LevelSnapshots\LevelSnapshots.Build.cs"><Link>Plugins\VirtualProduction\LevelSnapshots\Source\LevelSnapshots\LevelSnapshots.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\LevelSnapshots\Source\nDisplaySupport\nDisplaySupport.Build.cs"><Link>Plugins\VirtualProduction\LevelSnapshots\Source\nDisplaySupport\nDisplaySupport.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\LiveLinkCamera\Source\LiveLinkCameraEditor\LiveLinkCameraEditor.build.cs"><Link>Plugins\VirtualProduction\LiveLinkCamera\Source\LiveLinkCameraEditor\LiveLinkCameraEditor.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\LiveLinkCamera\Source\LiveLinkCameraRecording\LiveLinkCameraRecording.build.cs"><Link>Plugins\VirtualProduction\LiveLinkCamera\Source\LiveLinkCameraRecording\LiveLinkCameraRecording.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\LiveLinkCamera\Source\LiveLinkCamera\LiveLinkCamera.build.cs"><Link>Plugins\VirtualProduction\LiveLinkCamera\Source\LiveLinkCamera\LiveLinkCamera.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\LiveLinkFreeD\Source\LiveLinkFreeD\LiveLinkFreeD.Build.cs"><Link>Plugins\VirtualProduction\LiveLinkFreeD\Source\LiveLinkFreeD\LiveLinkFreeD.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\LiveLinkInputDevice\Source\LiveLinkInputDevice\LiveLinkInputDevice.Build.cs"><Link>Plugins\VirtualProduction\LiveLinkInputDevice\Source\LiveLinkInputDevice\LiveLinkInputDevice.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\LiveLinkLens\Source\LiveLinkLens\LiveLinkLens.Build.cs"><Link>Plugins\VirtualProduction\LiveLinkLens\Source\LiveLinkLens\LiveLinkLens.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\LiveLinkMasterLockit\Source\LiveLinkMasterLockitEditor\LiveLinkMasterLockitEditor.Build.cs"><Link>Plugins\VirtualProduction\LiveLinkMasterLockit\Source\LiveLinkMasterLockitEditor\LiveLinkMasterLockitEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\LiveLinkMasterLockit\Source\LiveLinkMasterLockit\LiveLinkMasterLockit.Build.cs"><Link>Plugins\VirtualProduction\LiveLinkMasterLockit\Source\LiveLinkMasterLockit\LiveLinkMasterLockit.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\LiveLinkPrestonMDR\Source\LiveLinkPrestonMDREditor\LiveLinkPrestonMDREditor.Build.cs"><Link>Plugins\VirtualProduction\LiveLinkPrestonMDR\Source\LiveLinkPrestonMDREditor\LiveLinkPrestonMDREditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\LiveLinkPrestonMDR\Source\LiveLinkPrestonMDR\LiveLinkPrestonMDR.Build.cs"><Link>Plugins\VirtualProduction\LiveLinkPrestonMDR\Source\LiveLinkPrestonMDR\LiveLinkPrestonMDR.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\LiveLinkVRPN\Source\LiveLinkVRPN\LiveLinkVRPN.Build.cs"><Link>Plugins\VirtualProduction\LiveLinkVRPN\Source\LiveLinkVRPN\LiveLinkVRPN.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\LiveLinkXR\Source\LiveLinkXROpenXRExt\LiveLinkXROpenXRExt.Build.cs"><Link>Plugins\VirtualProduction\LiveLinkXR\Source\LiveLinkXROpenXRExt\LiveLinkXROpenXRExt.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\LiveLinkXR\Source\LiveLinkXR\LiveLinkXR.Build.cs"><Link>Plugins\VirtualProduction\LiveLinkXR\Source\LiveLinkXR\LiveLinkXR.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\MultiUserTakes\Source\ConcertTakeRecorder\ConcertTakeRecorder.build.cs"><Link>Plugins\VirtualProduction\MultiUserTakes\Source\ConcertTakeRecorder\ConcertTakeRecorder.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\RemoteControlInterception\Source\RemoteControlInterception\RemoteControlInterception.Build.cs"><Link>Plugins\VirtualProduction\RemoteControlInterception\Source\RemoteControlInterception\RemoteControlInterception.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\RemoteControlProtocolDMX\Source\RemoteControlProtocolDMXEditor\RemoteControlProtocolDMXEditor.Build.cs"><Link>Plugins\VirtualProduction\RemoteControlProtocolDMX\Source\RemoteControlProtocolDMXEditor\RemoteControlProtocolDMXEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\RemoteControlProtocolDMX\Source\RemoteControlProtocolDMX\RemoteControlProtocolDMX.Build.cs"><Link>Plugins\VirtualProduction\RemoteControlProtocolDMX\Source\RemoteControlProtocolDMX\RemoteControlProtocolDMX.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\RemoteControlProtocolMIDI\Source\RemoteControlProtocolMIDIEditor\RemoteControlProtocolMIDIEditor.Build.cs"><Link>Plugins\VirtualProduction\RemoteControlProtocolMIDI\Source\RemoteControlProtocolMIDIEditor\RemoteControlProtocolMIDIEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\RemoteControlProtocolMIDI\Source\RemoteControlProtocolMIDI\RemoteControlProtocolMIDI.Build.cs"><Link>Plugins\VirtualProduction\RemoteControlProtocolMIDI\Source\RemoteControlProtocolMIDI\RemoteControlProtocolMIDI.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\RemoteControlProtocolOSC\Source\RemoteControlProtocolOSC\RemoteControlProtocolOSC.Build.cs"><Link>Plugins\VirtualProduction\RemoteControlProtocolOSC\Source\RemoteControlProtocolOSC\RemoteControlProtocolOSC.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\RemoteControlWebInterface\Source\RemoteControlWebInterface\RemoteControlWebInterface.Build.cs"><Link>Plugins\VirtualProduction\RemoteControlWebInterface\Source\RemoteControlWebInterface\RemoteControlWebInterface.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\RemoteControl\Source\RemoteControlCommon\RemoteControlCommon.Build.cs"><Link>Plugins\VirtualProduction\RemoteControl\Source\RemoteControlCommon\RemoteControlCommon.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\RemoteControl\Source\RemoteControlLogic\RemoteControlLogic.Build.cs"><Link>Plugins\VirtualProduction\RemoteControl\Source\RemoteControlLogic\RemoteControlLogic.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\RemoteControl\Source\RemoteControlMultiUser\RemoteControlMultiUser.Build.cs"><Link>Plugins\VirtualProduction\RemoteControl\Source\RemoteControlMultiUser\RemoteControlMultiUser.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\RemoteControl\Source\RemoteControlProtocolWidgets\RemoteControlProtocolWidgets.Build.cs"><Link>Plugins\VirtualProduction\RemoteControl\Source\RemoteControlProtocolWidgets\RemoteControlProtocolWidgets.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\RemoteControl\Source\RemoteControlProtocol\RemoteControlProtocol.Build.cs"><Link>Plugins\VirtualProduction\RemoteControl\Source\RemoteControlProtocol\RemoteControlProtocol.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\RemoteControl\Source\RemoteControlUI\RemoteControlUI.Build.cs"><Link>Plugins\VirtualProduction\RemoteControl\Source\RemoteControlUI\RemoteControlUI.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\RemoteControl\Source\RemoteControl\RemoteControl.Build.cs"><Link>Plugins\VirtualProduction\RemoteControl\Source\RemoteControl\RemoteControl.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\RemoteControl\Source\WebRemoteControl\WebRemoteControl.Build.cs"><Link>Plugins\VirtualProduction\RemoteControl\Source\WebRemoteControl\WebRemoteControl.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\Rivermax\RivermaxCore\Source\RivermaxCore\RivermaxCore.build.cs"><Link>Plugins\VirtualProduction\Rivermax\RivermaxCore\Source\RivermaxCore\RivermaxCore.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\Rivermax\RivermaxCore\Source\RivermaxEditor\RivermaxEditor.build.cs"><Link>Plugins\VirtualProduction\Rivermax\RivermaxCore\Source\RivermaxEditor\RivermaxEditor.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\Rivermax\RivermaxCore\Source\RivermaxRendering\RivermaxRendering.build.cs"><Link>Plugins\VirtualProduction\Rivermax\RivermaxCore\Source\RivermaxRendering\RivermaxRendering.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\Rivermax\RivermaxMedia\Source\RivermaxMediaEditor\RivermaxMediaEditor.build.cs"><Link>Plugins\VirtualProduction\Rivermax\RivermaxMedia\Source\RivermaxMediaEditor\RivermaxMediaEditor.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\Rivermax\RivermaxMedia\Source\RivermaxMediaFactory\RivermaxMediaFactory.Build.cs"><Link>Plugins\VirtualProduction\Rivermax\RivermaxMedia\Source\RivermaxMediaFactory\RivermaxMediaFactory.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\Rivermax\RivermaxMedia\Source\RivermaxMedia\RivermaxMedia.build.cs"><Link>Plugins\VirtualProduction\Rivermax\RivermaxMedia\Source\RivermaxMedia\RivermaxMedia.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\Rivermax\RivermaxSync\Source\RivermaxSyncEditor\RivermaxSyncEditor.Build.cs"><Link>Plugins\VirtualProduction\Rivermax\RivermaxSync\Source\RivermaxSyncEditor\RivermaxSyncEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\Rivermax\RivermaxSync\Source\RivermaxSync\RivermaxSync.Build.cs"><Link>Plugins\VirtualProduction\Rivermax\RivermaxSync\Source\RivermaxSync\RivermaxSync.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\SequencerPlaylists\Source\SequencerPlaylists\SequencerPlaylists.Build.cs"><Link>Plugins\VirtualProduction\SequencerPlaylists\Source\SequencerPlaylists\SequencerPlaylists.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\StageMonitoring\Source\StageDataProvider\StageDataProvider.build.cs"><Link>Plugins\VirtualProduction\StageMonitoring\Source\StageDataProvider\StageDataProvider.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\StageMonitoring\Source\StageMonitorCommon\StageMonitorCommon.build.cs"><Link>Plugins\VirtualProduction\StageMonitoring\Source\StageMonitorCommon\StageMonitorCommon.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\StageMonitoring\Source\StageMonitorEditor\StageMonitorEditor.Build.cs"><Link>Plugins\VirtualProduction\StageMonitoring\Source\StageMonitorEditor\StageMonitorEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\StageMonitoring\Source\StageMonitor\StageMonitor.build.cs"><Link>Plugins\VirtualProduction\StageMonitoring\Source\StageMonitor\StageMonitor.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\Switchboard\Source\SwitchboardCommon\SwitchboardCommon.build.cs"><Link>Plugins\VirtualProduction\Switchboard\Source\SwitchboardCommon\SwitchboardCommon.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\Switchboard\Source\SwitchboardEditor\SwitchboardEditor.build.cs"><Link>Plugins\VirtualProduction\Switchboard\Source\SwitchboardEditor\SwitchboardEditor.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\Takes\Source\CacheTrackRecorder\CacheTrackRecorder.Build.cs"><Link>Plugins\VirtualProduction\Takes\Source\CacheTrackRecorder\CacheTrackRecorder.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\Takes\Source\TakeMovieScene\TakeMovieScene.Build.cs"><Link>Plugins\VirtualProduction\Takes\Source\TakeMovieScene\TakeMovieScene.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\Takes\Source\TakeRecorderSources\TakeRecorderSources.Build.cs"><Link>Plugins\VirtualProduction\Takes\Source\TakeRecorderSources\TakeRecorderSources.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\Takes\Source\TakeRecorder\TakeRecorder.Build.cs"><Link>Plugins\VirtualProduction\Takes\Source\TakeRecorder\TakeRecorder.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\Takes\Source\TakeSequencer\TakeSequencer.Build.cs"><Link>Plugins\VirtualProduction\Takes\Source\TakeSequencer\TakeSequencer.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\Takes\Source\TakeTrackRecorders\TakeTrackRecorders.Build.cs"><Link>Plugins\VirtualProduction\Takes\Source\TakeTrackRecorders\TakeTrackRecorders.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\Takes\Source\TakesCore\TakesCore.Build.cs"><Link>Plugins\VirtualProduction\Takes\Source\TakesCore\TakesCore.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\TextureShare\Source\TextureShareCore\TextureShareCore.Build.cs"><Link>Plugins\VirtualProduction\TextureShare\Source\TextureShareCore\TextureShareCore.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\TextureShare\Source\TextureShareDisplayCluster\TextureShareDisplayCluster.Build.cs"><Link>Plugins\VirtualProduction\TextureShare\Source\TextureShareDisplayCluster\TextureShareDisplayCluster.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\TextureShare\Source\TextureShare\TextureShare.Build.cs"><Link>Plugins\VirtualProduction\TextureShare\Source\TextureShare\TextureShare.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\TimedDataMonitor\Source\TimedDataMonitorEditor\TimedDataMonitorEditor.build.cs"><Link>Plugins\VirtualProduction\TimedDataMonitor\Source\TimedDataMonitorEditor\TimedDataMonitorEditor.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\TimedDataMonitor\Source\TimedDataMonitor\TimedDataMonitor.Build.cs"><Link>Plugins\VirtualProduction\TimedDataMonitor\Source\TimedDataMonitor\TimedDataMonitor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\VirtualCameraCore\Source\DecoupledOutputProvider\DecoupledOutputProvider.build.cs"><Link>Plugins\VirtualProduction\VirtualCameraCore\Source\DecoupledOutputProvider\DecoupledOutputProvider.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\VirtualCameraCore\Source\PixelStreamingVCam\PixelStreamingVCam.Build.cs"><Link>Plugins\VirtualProduction\VirtualCameraCore\Source\PixelStreamingVCam\PixelStreamingVCam.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\VirtualCameraCore\Source\VCamBlueprintNodes\VCamBlueprintNodes.build.cs"><Link>Plugins\VirtualProduction\VirtualCameraCore\Source\VCamBlueprintNodes\VCamBlueprintNodes.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\VirtualCameraCore\Source\VCamCoreEditor\VCamCoreEditor.build.cs"><Link>Plugins\VirtualProduction\VirtualCameraCore\Source\VCamCoreEditor\VCamCoreEditor.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\VirtualCameraCore\Source\VCamCore\VCamCore.Build.cs"><Link>Plugins\VirtualProduction\VirtualCameraCore\Source\VCamCore\VCamCore.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\VirtualCamera\Source\VCamExtensionsEditor\VCamExtensionsEditor.build.cs"><Link>Plugins\VirtualProduction\VirtualCamera\Source\VCamExtensionsEditor\VCamExtensionsEditor.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\VirtualCamera\Source\VCamExtensions\VCamExtensions.build.cs"><Link>Plugins\VirtualProduction\VirtualCamera\Source\VCamExtensions\VCamExtensions.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\VirtualCamera\Source\VirtualCameraEditor\VirtualCameraEditor.Build.cs"><Link>Plugins\VirtualProduction\VirtualCamera\Source\VirtualCameraEditor\VirtualCameraEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\VirtualProduction\VirtualCamera\Source\VirtualCamera\VirtualCamera.Build.cs"><Link>Plugins\VirtualProduction\VirtualCamera\Source\VirtualCamera\VirtualCamera.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Web\HttpBlueprint\Source\HttpBlueprintGraph\HttpBlueprintGraph.Build.cs"><Link>Plugins\Web\HttpBlueprint\Source\HttpBlueprintGraph\HttpBlueprintGraph.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Web\HttpBlueprint\Source\HttpBlueprint\HttpBlueprint.Build.cs"><Link>Plugins\Web\HttpBlueprint\Source\HttpBlueprint\HttpBlueprint.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\WorldMetrics\Source\CsvMetrics\CsvMetrics.Build.cs"><Link>Plugins\WorldMetrics\Source\CsvMetrics\CsvMetrics.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\WorldMetrics\Source\WorldMetricsCore\WorldMetricsCore.Build.cs"><Link>Plugins\WorldMetrics\Source\WorldMetricsCore\WorldMetricsCore.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\WorldMetrics\Source\WorldMetricsTest\WorldMetricsTest.Build.cs"><Link>Plugins\WorldMetrics\Source\WorldMetricsTest\WorldMetricsTest.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\XGEController\Source\XGEController.Build.cs"><Link>Plugins\XGEController\Source\XGEController.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\gFurPro\Source\GFurEditor\GFurEditor.Build.cs"><Link>Plugins\gFurPro\Source\GFurEditor\GFurEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\gFurPro\Source\GFurFlowMapEditor\GFurFlowMapEditor.Build.cs"><Link>Plugins\gFurPro\Source\GFurFlowMapEditor\GFurFlowMapEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\gFurPro\Source\GFur\GFur.Build.cs"><Link>Plugins\gFurPro\Source\GFur\GFur.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Shaders\Shaders.Build.cs"><Link>Shaders\Shaders.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\AITestSuite\AITestSuite.Build.cs"><Link>Source\Developer\AITestSuite\AITestSuite.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\Android\AndroidDeviceDetection\AndroidDeviceDetection.Build.cs"><Link>Source\Developer\Android\AndroidDeviceDetection\AndroidDeviceDetection.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\Android\AndroidPlatformEditor\AndroidPlatformEditor.Build.cs"><Link>Source\Developer\Android\AndroidPlatformEditor\AndroidPlatformEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\Android\AndroidTargetPlatformControls\AndroidTargetPlatformControls.Build.cs"><Link>Source\Developer\Android\AndroidTargetPlatformControls\AndroidTargetPlatformControls.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\Android\AndroidTargetPlatformSettings\AndroidTargetPlatformSettings.Build.cs"><Link>Source\Developer\Android\AndroidTargetPlatformSettings\AndroidTargetPlatformSettings.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\Android\AndroidTargetPlatform\AndroidTargetPlatform.Build.cs"><Link>Source\Developer\Android\AndroidTargetPlatform\AndroidTargetPlatform.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\AnimationDataController\AnimationDataController.build.cs"><Link>Source\Developer\AnimationDataController\AnimationDataController.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\AnimationWidgets\AnimationWidgets.Build.cs"><Link>Source\Developer\AnimationWidgets\AnimationWidgets.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\Apple\MetalShaderFormat\MetalShaderFormat.Build.cs"><Link>Source\Developer\Apple\MetalShaderFormat\MetalShaderFormat.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\AssetTools\AssetTools.Build.cs"><Link>Source\Developer\AssetTools\AssetTools.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\AudioFormatADPCM\AudioFormatADPCM.Build.cs"><Link>Source\Developer\AudioFormatADPCM\AudioFormatADPCM.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\AudioFormatBink\AudioFormatBink.Build.cs"><Link>Source\Developer\AudioFormatBink\AudioFormatBink.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\AudioFormatOgg\AudioFormatOgg.Build.cs"><Link>Source\Developer\AudioFormatOgg\AudioFormatOgg.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\AudioFormatOpus\AudioFormatOpus.Build.cs"><Link>Source\Developer\AudioFormatOpus\AudioFormatOpus.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\AudioFormatRad\AudioFormatRad.Build.cs"><Link>Source\Developer\AudioFormatRad\AudioFormatRad.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\AudioSettingsEditor\AudioSettingsEditor.Build.cs"><Link>Source\Developer\AudioSettingsEditor\AudioSettingsEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\AutomationController\AutomationController.Build.cs"><Link>Source\Developer\AutomationController\AutomationController.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\AutomationDriver\AutomationDriver.Build.cs"><Link>Source\Developer\AutomationDriver\AutomationDriver.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\AutomationWindow\AutomationWindow.Build.cs"><Link>Source\Developer\AutomationWindow\AutomationWindow.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\BSPUtils\BSPUtils.Build.cs"><Link>Source\Developer\BSPUtils\BSPUtils.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\BlankModule\BlankModule.Build.cs"><Link>Source\Developer\BlankModule\BlankModule.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\CollectionManager\CollectionManager.Build.cs"><Link>Source\Developer\CollectionManager\CollectionManager.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\CollisionAnalyzer\CollisionAnalyzer.Build.cs"><Link>Source\Developer\CollisionAnalyzer\CollisionAnalyzer.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\CookMetadata\CookMetadata.Build.cs"><Link>Source\Developer\CookMetadata\CookMetadata.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\CookOnTheFlyNetServer\CookOnTheFlyNetServer.Build.cs"><Link>Source\Developer\CookOnTheFlyNetServer\CookOnTheFlyNetServer.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\CookedEditor\CookedEditor.Build.cs"><Link>Source\Developer\CookedEditor\CookedEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\CrashDebugHelper\CrashDebugHelper.Build.cs"><Link>Source\Developer\CrashDebugHelper\CrashDebugHelper.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\Datasmith\DatasmithExporterUI\DatasmithExporterUI.Build.cs"><Link>Source\Developer\Datasmith\DatasmithExporterUI\DatasmithExporterUI.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\Datasmith\DatasmithExporter\DatasmithExporter.Build.cs"><Link>Source\Developer\Datasmith\DatasmithExporter\DatasmithExporter.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\Datasmith\DatasmithFacade\DatasmithFacade.Build.cs"><Link>Source\Developer\Datasmith\DatasmithFacade\DatasmithFacade.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\DerivedDataCache\DerivedDataCache.Build.cs"><Link>Source\Developer\DerivedDataCache\DerivedDataCache.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\DesktopPlatform\DesktopPlatform.Build.cs"><Link>Source\Developer\DesktopPlatform\DesktopPlatform.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\DesktopWidgets\DesktopWidgets.Build.cs"><Link>Source\Developer\DesktopWidgets\DesktopWidgets.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\DeveloperToolSettings\DeveloperToolSettings.Build.cs"><Link>Source\Developer\DeveloperToolSettings\DeveloperToolSettings.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\DeviceManager\DeviceManager.Build.cs"><Link>Source\Developer\DeviceManager\DeviceManager.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\DirectoryWatcher\DirectoryWatcher.Build.cs"><Link>Source\Developer\DirectoryWatcher\DirectoryWatcher.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\DistributedBuildInterface\DistributedBuildInterface.build.cs"><Link>Source\Developer\DistributedBuildInterface\DistributedBuildInterface.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\DrawPrimitiveDebugger\DrawPrimitiveDebugger.Build.cs"><Link>Source\Developer\DrawPrimitiveDebugger\DrawPrimitiveDebugger.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\EditorAnalyticsSession\EditorAnalyticsSession.Build.cs"><Link>Source\Developer\EditorAnalyticsSession\EditorAnalyticsSession.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\ExternalImagePicker\ExternalImagePicker.Build.cs"><Link>Source\Developer\ExternalImagePicker\ExternalImagePicker.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\FileUtilities\FileUtilities.Build.cs"><Link>Source\Developer\FileUtilities\FileUtilities.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\FunctionalTesting\FunctionalTesting.Build.cs"><Link>Source\Developer\FunctionalTesting\FunctionalTesting.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\GeometryProcessingInterfaces\GeometryProcessingInterfaces.Build.cs"><Link>Source\Developer\GeometryProcessingInterfaces\GeometryProcessingInterfaces.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\GraphColor\GraphColor.Build.cs"><Link>Source\Developer\GraphColor\GraphColor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\HierarchicalLODUtilities\HierarchicalLODUtilities.Build.cs"><Link>Source\Developer\HierarchicalLODUtilities\HierarchicalLODUtilities.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\Horde\Horde.Build.cs"><Link>Source\Developer\Horde\Horde.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\HotReload\HotReload.Build.cs"><Link>Source\Developer\HotReload\HotReload.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\IOS\IOSPlatformEditor\IOSPlatformEditor.Build.cs"><Link>Source\Developer\IOS\IOSPlatformEditor\IOSPlatformEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\IOS\IOSTargetPlatform\IOSTargetPlatform.Build.cs"><Link>Source\Developer\IOS\IOSTargetPlatform\IOSTargetPlatform.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\IOS\TVOSTargetPlatform\TVOSTargetPlatform.Build.cs"><Link>Source\Developer\IOS\TVOSTargetPlatform\TVOSTargetPlatform.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\IoStoreUtilities\IoStoreUtilities.Build.cs"><Link>Source\Developer\IoStoreUtilities\IoStoreUtilities.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\LauncherServices\LauncherServices.Build.cs"><Link>Source\Developer\LauncherServices\LauncherServices.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\Linux\LinuxArm64TargetPlatform\LinuxArm64TargetPlatform.Build.cs"><Link>Source\Developer\Linux\LinuxArm64TargetPlatform\LinuxArm64TargetPlatform.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\Linux\LinuxPlatformEditor\LinuxPlatformEditor.Build.cs"><Link>Source\Developer\Linux\LinuxPlatformEditor\LinuxPlatformEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\Linux\LinuxTargetPlatform\LinuxTargetPlatform.Build.cs"><Link>Source\Developer\Linux\LinuxTargetPlatform\LinuxTargetPlatform.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\LocalizationService\LocalizationService.Build.cs"><Link>Source\Developer\LocalizationService\LocalizationService.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\Localization\Localization.Build.cs"><Link>Source\Developer\Localization\Localization.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\LogVisualizer\LogVisualizer.Build.cs"><Link>Source\Developer\LogVisualizer\LogVisualizer.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\LowLevelTestsRunner\LowLevelTestsRunner.Build.cs"><Link>Source\Developer\LowLevelTestsRunner\LowLevelTestsRunner.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\Mac\MacPlatformEditor\MacPlatformEditor.Build.cs"><Link>Source\Developer\Mac\MacPlatformEditor\MacPlatformEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\Mac\MacTargetPlatform\MacTargetPlatform.Build.cs"><Link>Source\Developer\Mac\MacTargetPlatform\MacTargetPlatform.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\MaterialBaking\MaterialBaking.Build.cs"><Link>Source\Developer\MaterialBaking\MaterialBaking.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\MaterialUtilities\MaterialUtilities.Build.cs"><Link>Source\Developer\MaterialUtilities\MaterialUtilities.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\Merge\Merge.Build.cs"><Link>Source\Developer\Merge\Merge.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\MeshBoneReduction\MeshBoneReduction.Build.cs"><Link>Source\Developer\MeshBoneReduction\MeshBoneReduction.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\MeshBuilderCommon\MeshBuilderCommon.Build.cs"><Link>Source\Developer\MeshBuilderCommon\MeshBuilderCommon.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\MeshBuilder\MeshBuilder.Build.cs"><Link>Source\Developer\MeshBuilder\MeshBuilder.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\MeshDescriptionOperations\MeshDescriptionOperations.Build.cs"><Link>Source\Developer\MeshDescriptionOperations\MeshDescriptionOperations.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\MeshMergeUtilities\MeshMergeUtilities.Build.cs"><Link>Source\Developer\MeshMergeUtilities\MeshMergeUtilities.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\MeshReductionInterface\MeshReductionInterface.Build.cs"><Link>Source\Developer\MeshReductionInterface\MeshReductionInterface.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\MeshSimplifier\QuadricMeshReduction.Build.cs"><Link>Source\Developer\MeshSimplifier\QuadricMeshReduction.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\MeshUtilitiesEngine\MeshUtilitiesEngine.Build.cs"><Link>Source\Developer\MeshUtilitiesEngine\MeshUtilitiesEngine.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\MeshUtilities\MeshUtilities.Build.cs"><Link>Source\Developer\MeshUtilities\MeshUtilities.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\MessageLog\MessageLog.Build.cs"><Link>Source\Developer\MessageLog\MessageLog.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\NaniteBuilder\NaniteBuilder.Build.cs"><Link>Source\Developer\NaniteBuilder\NaniteBuilder.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\NaniteUtilities\NaniteUtilities.Build.cs"><Link>Source\Developer\NaniteUtilities\NaniteUtilities.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\OutputLog\OutputLog.Build.cs"><Link>Source\Developer\OutputLog\OutputLog.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\PakFileUtilities\PakFileUtilities.Build.cs"><Link>Source\Developer\PakFileUtilities\PakFileUtilities.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\PhysicsUtilities\PhysicsUtilities.Build.cs"><Link>Source\Developer\PhysicsUtilities\PhysicsUtilities.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\ProfileVisualizer\ProfileVisualizer.Build.cs"><Link>Source\Developer\ProfileVisualizer\ProfileVisualizer.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\ProfilerClient\ProfilerClient.Build.cs"><Link>Source\Developer\ProfilerClient\ProfilerClient.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\ProfilerMessages\ProfilerMessages.Build.cs"><Link>Source\Developer\ProfilerMessages\ProfilerMessages.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\ProfilerService\ProfilerService.Build.cs"><Link>Source\Developer\ProfilerService\ProfilerService.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\Profiler\Profiler.Build.cs"><Link>Source\Developer\Profiler\Profiler.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\ProjectLauncher\ProjectLauncher.Build.cs"><Link>Source\Developer\ProjectLauncher\ProjectLauncher.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\RealtimeProfiler\RealtimeProfiler.Build.cs"><Link>Source\Developer\RealtimeProfiler\RealtimeProfiler.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\S3Client\S3Client.Build.cs"><Link>Source\Developer\S3Client\S3Client.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\ScreenShotComparisonTools\ScreenShotComparisonTools.Build.cs"><Link>Source\Developer\ScreenShotComparisonTools\ScreenShotComparisonTools.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\ScreenShotComparison\ScreenShotComparison.Build.cs"><Link>Source\Developer\ScreenShotComparison\ScreenShotComparison.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\ScriptDisassembler\ScriptDisassembler.Build.cs"><Link>Source\Developer\ScriptDisassembler\ScriptDisassembler.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\SessionFrontend\SessionFrontend.Build.cs"><Link>Source\Developer\SessionFrontend\SessionFrontend.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\SettingsEditor\SettingsEditor.Build.cs"><Link>Source\Developer\SettingsEditor\SettingsEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\Settings\Settings.Build.cs"><Link>Source\Developer\Settings\Settings.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\ShaderCompilerCommon\ShaderCompilerCommon.Build.cs"><Link>Source\Developer\ShaderCompilerCommon\ShaderCompilerCommon.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\ShaderFormatOpenGL\ShaderFormatOpenGL.Build.cs"><Link>Source\Developer\ShaderFormatOpenGL\ShaderFormatOpenGL.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\ShaderFormatVectorVM\ShaderFormatVectorVM.Build.cs"><Link>Source\Developer\ShaderFormatVectorVM\ShaderFormatVectorVM.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\ShaderPreprocessor\ShaderPreprocessor.Build.cs"><Link>Source\Developer\ShaderPreprocessor\ShaderPreprocessor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\SharedSettingsWidgets\SharedSettingsWidgets.Build.cs"><Link>Source\Developer\SharedSettingsWidgets\SharedSettingsWidgets.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\SkeletalMeshUtilitiesCommon\SkeletalMeshUtilitiesCommon.Build.cs"><Link>Source\Developer\SkeletalMeshUtilitiesCommon\SkeletalMeshUtilitiesCommon.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\SlackIntegrations\SlackIntegrations.Build.cs"><Link>Source\Developer\SlackIntegrations\SlackIntegrations.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\SlateFileDialogs\SlateFileDialogs.Build.cs"><Link>Source\Developer\SlateFileDialogs\SlateFileDialogs.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\SlateFontDialog\SlateFontDialog.Build.cs"><Link>Source\Developer\SlateFontDialog\SlateFontDialog.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\SlateReflector\SlateReflector.Build.cs"><Link>Source\Developer\SlateReflector\SlateReflector.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\SourceCodeAccess\SourceCodeAccess.Build.cs"><Link>Source\Developer\SourceCodeAccess\SourceCodeAccess.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\SourceControlCheckInPrompt\SourceControlCheckInPrompt.Build.cs"><Link>Source\Developer\SourceControlCheckInPrompt\SourceControlCheckInPrompt.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\SourceControlViewport\SourceControlViewport.Build.cs"><Link>Source\Developer\SourceControlViewport\SourceControlViewport.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\SourceControl\SourceControl.Build.cs"><Link>Source\Developer\SourceControl\SourceControl.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\StandaloneRenderer\StandaloneRenderer.Build.cs"><Link>Source\Developer\StandaloneRenderer\StandaloneRenderer.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\TargetDeviceServices\TargetDeviceServices.Build.cs"><Link>Source\Developer\TargetDeviceServices\TargetDeviceServices.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\TargetPlatform\TargetPlatform.Build.cs"><Link>Source\Developer\TargetPlatform\TargetPlatform.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\TextureBuildUtilities\TextureBuildUtilities.Build.cs"><Link>Source\Developer\TextureBuildUtilities\TextureBuildUtilities.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\TextureBuild\TextureBuild.Build.cs"><Link>Source\Developer\TextureBuild\TextureBuild.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\TextureCompressor\TextureCompressor.Build.cs"><Link>Source\Developer\TextureCompressor\TextureCompressor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\TextureFormatASTC\TextureFormatASTC.Build.cs"><Link>Source\Developer\TextureFormatASTC\TextureFormatASTC.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\TextureFormatDXT\TextureFormatDXT.Build.cs"><Link>Source\Developer\TextureFormatDXT\TextureFormatDXT.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\TextureFormatETC2\TextureFormatETC2.Build.cs"><Link>Source\Developer\TextureFormatETC2\TextureFormatETC2.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\TextureFormatIntelISPCTexComp\TextureFormatIntelISPCTexComp.Build.cs"><Link>Source\Developer\TextureFormatIntelISPCTexComp\TextureFormatIntelISPCTexComp.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\TextureFormatUncompressed\TextureFormatUncompressed.Build.cs"><Link>Source\Developer\TextureFormatUncompressed\TextureFormatUncompressed.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\TextureFormat\TextureFormat.Build.cs"><Link>Source\Developer\TextureFormat\TextureFormat.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\ToolMenus\ToolMenus.Build.cs"><Link>Source\Developer\ToolMenus\ToolMenus.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\ToolWidgets\ToolWidgets.Build.cs"><Link>Source\Developer\ToolWidgets\ToolWidgets.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\TraceAnalysis\TraceAnalysis.Build.cs"><Link>Source\Developer\TraceAnalysis\TraceAnalysis.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\TraceInsights\TraceInsights.Build.cs"><Link>Source\Developer\TraceInsights\TraceInsights.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\TraceServices\TraceServices.Build.cs"><Link>Source\Developer\TraceServices\TraceServices.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\TreeMap\TreeMap.Build.cs"><Link>Source\Developer\TreeMap\TreeMap.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\TurnkeyIO\TurnkeyIO.Build.cs"><Link>Source\Developer\TurnkeyIO\TurnkeyIO.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\UncontrolledChangelists\UncontrolledChangelists.Build.cs"><Link>Source\Developer\UncontrolledChangelists\UncontrolledChangelists.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\UndoHistory\UndoHistory.Build.cs"><Link>Source\Developer\UndoHistory\UndoHistory.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\UnsavedAssetsTracker\Source\UnsavedAssetsTracker.Build.cs"><Link>Source\Developer\UnsavedAssetsTracker\Source\UnsavedAssetsTracker.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\Virtualization\Virtualization.Build.cs"><Link>Source\Developer\Virtualization\Virtualization.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\VisualGraphUtils\VisualGraphUtils.Build.cs"><Link>Source\Developer\VisualGraphUtils\VisualGraphUtils.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\VulkanShaderFormat\VulkanShaderFormat.Build.cs"><Link>Source\Developer\VulkanShaderFormat\VulkanShaderFormat.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\WidgetRegistration\WidgetRegistration.Build.cs"><Link>Source\Developer\WidgetRegistration\WidgetRegistration.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\Windows\LiveCodingServer\LiveCodingServer.Build.cs"><Link>Source\Developer\Windows\LiveCodingServer\LiveCodingServer.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\Windows\LiveCoding\LiveCoding.Build.cs"><Link>Source\Developer\Windows\LiveCoding\LiveCoding.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\Windows\ShaderFormatD3D\ShaderFormatD3D.Build.cs"><Link>Source\Developer\Windows\ShaderFormatD3D\ShaderFormatD3D.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\Windows\WindowsPlatformEditor\WindowsPlatformEditor.Build.cs"><Link>Source\Developer\Windows\WindowsPlatformEditor\WindowsPlatformEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\Windows\WindowsTargetPlatform\WindowsTargetPlatform.Build.cs"><Link>Source\Developer\Windows\WindowsTargetPlatform\WindowsTargetPlatform.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Developer\Zen\Zen.Build.cs"><Link>Source\Developer\Zen\Zen.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\AIGraph\AIGraph.Build.cs"><Link>Source\Editor\AIGraph\AIGraph.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\ActionableMessage\ActionableMessage.build.cs"><Link>Source\Editor\ActionableMessage\ActionableMessage.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\ActorPickerMode\ActorPickerMode.Build.cs"><Link>Source\Editor\ActorPickerMode\ActorPickerMode.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\AddContentDialog\AddContentDialog.Build.cs"><Link>Source\Editor\AddContentDialog\AddContentDialog.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\AdvancedPreviewScene\AdvancedPreviewScene.Build.cs"><Link>Source\Editor\AdvancedPreviewScene\AdvancedPreviewScene.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\AnimGraph\AnimGraph.Build.cs"><Link>Source\Editor\AnimGraph\AnimGraph.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\AnimationBlueprintEditor\AnimationBlueprintEditor.Build.cs"><Link>Source\Editor\AnimationBlueprintEditor\AnimationBlueprintEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\AnimationBlueprintLibrary\AnimationBlueprintLibrary.build.cs"><Link>Source\Editor\AnimationBlueprintLibrary\AnimationBlueprintLibrary.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\AnimationEditMode\AnimationEditMode.Build.cs"><Link>Source\Editor\AnimationEditMode\AnimationEditMode.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\AnimationEditorWidgets\AnimationEditorWidgets.Build.cs"><Link>Source\Editor\AnimationEditorWidgets\AnimationEditorWidgets.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\AnimationEditor\AnimationEditor.Build.cs"><Link>Source\Editor\AnimationEditor\AnimationEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\AnimationModifiers\AnimationModifiers.Build.cs"><Link>Source\Editor\AnimationModifiers\AnimationModifiers.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\AnimationSettings\AnimationSettings.Build.cs"><Link>Source\Editor\AnimationSettings\AnimationSettings.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\AssetDefinition\AssetDefinition.Build.cs"><Link>Source\Editor\AssetDefinition\AssetDefinition.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\AssetTagsEditor\AssetTagsEditor.Build.cs"><Link>Source\Editor\AssetTagsEditor\AssetTagsEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\AudioEditor\AudioEditor.Build.cs"><Link>Source\Editor\AudioEditor\AudioEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\BehaviorTreeEditor\BehaviorTreeEditor.Build.cs"><Link>Source\Editor\BehaviorTreeEditor\BehaviorTreeEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\BlueprintEditorLibrary\BlueprintEditorLibrary.Build.cs"><Link>Source\Editor\BlueprintEditorLibrary\BlueprintEditorLibrary.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\BlueprintGraph\BlueprintGraph.Build.cs"><Link>Source\Editor\BlueprintGraph\BlueprintGraph.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\Blutility\Blutility.Build.cs"><Link>Source\Editor\Blutility\Blutility.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\CSVtoSVG\CSVtoSVG.Build.cs"><Link>Source\Editor\CSVtoSVG\CSVtoSVG.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\Cascade\Cascade.Build.cs"><Link>Source\Editor\Cascade\Cascade.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\ClassViewer\ClassViewer.Build.cs"><Link>Source\Editor\ClassViewer\ClassViewer.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\ClothPainter\ClothPainter.Build.cs"><Link>Source\Editor\ClothPainter\ClothPainter.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\ClothingSystemEditorInterface\ClothingSystemEditorInterface.Build.cs"><Link>Source\Editor\ClothingSystemEditorInterface\ClothingSystemEditorInterface.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\ClothingSystemEditor\ClothingSystemEditor.Build.cs"><Link>Source\Editor\ClothingSystemEditor\ClothingSystemEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\CommonMenuExtensions\CommonMenuExtensions.Build.cs"><Link>Source\Editor\CommonMenuExtensions\CommonMenuExtensions.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\ComponentVisualizers\ComponentVisualizers.Build.cs"><Link>Source\Editor\ComponentVisualizers\ComponentVisualizers.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\ConfigEditor\ConfigEditor.Build.cs"><Link>Source\Editor\ConfigEditor\ConfigEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\ContentBrowserData\ContentBrowserData.Build.cs"><Link>Source\Editor\ContentBrowserData\ContentBrowserData.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\ContentBrowser\ContentBrowser.Build.cs"><Link>Source\Editor\ContentBrowser\ContentBrowser.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\CurveAssetEditor\CurveAssetEditor.Build.cs"><Link>Source\Editor\CurveAssetEditor\CurveAssetEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\CurveEditor\CurveEditor.Build.cs"><Link>Source\Editor\CurveEditor\CurveEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\CurveTableEditor\CurveTableEditor.Build.cs"><Link>Source\Editor\CurveTableEditor\CurveTableEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\DataLayerEditor\DataLayerEditor.Build.cs"><Link>Source\Editor\DataLayerEditor\DataLayerEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\DataTableEditor\DataTableEditor.Build.cs"><Link>Source\Editor\DataTableEditor\DataTableEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\DerivedDataEditor\DerivedDataEditor.Build.cs"><Link>Source\Editor\DerivedDataEditor\DerivedDataEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\DetailCustomizations\DetailCustomizations.Build.cs"><Link>Source\Editor\DetailCustomizations\DetailCustomizations.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\DeviceProfileEditor\DeviceProfileEditor.Build.cs"><Link>Source\Editor\DeviceProfileEditor\DeviceProfileEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\DeviceProfileServices\DeviceProfileServices.Build.cs"><Link>Source\Editor\DeviceProfileServices\DeviceProfileServices.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\DistCurveEditor\DistCurveEditor.Build.cs"><Link>Source\Editor\DistCurveEditor\DistCurveEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\Documentation\Documentation.Build.cs"><Link>Source\Editor\Documentation\Documentation.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\EditorConfig\EditorConfig.Build.cs"><Link>Source\Editor\EditorConfig\EditorConfig.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\EditorFramework\EditorFramework.Build.cs"><Link>Source\Editor\EditorFramework\EditorFramework.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\EditorSettingsViewer\EditorSettingsViewer.Build.cs"><Link>Source\Editor\EditorSettingsViewer\EditorSettingsViewer.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\EditorStyle\EditorStyle.Build.cs"><Link>Source\Editor\EditorStyle\EditorStyle.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\EditorSubsystem\EditorSubsystem.Build.cs"><Link>Source\Editor\EditorSubsystem\EditorSubsystem.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\EditorWidgets\EditorWidgets.Build.cs"><Link>Source\Editor\EditorWidgets\EditorWidgets.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\EnvironmentLightingViewer\EnvironmentLightingViewer.Build.cs"><Link>Source\Editor\EnvironmentLightingViewer\EnvironmentLightingViewer.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\Experimental\EditorInteractiveToolsFramework\EditorInteractiveToolsFramework.Build.cs"><Link>Source\Editor\Experimental\EditorInteractiveToolsFramework\EditorInteractiveToolsFramework.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\FoliageEdit\FoliageEdit.build.cs"><Link>Source\Editor\FoliageEdit\FoliageEdit.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\FontEditor\FontEditor.Build.cs"><Link>Source\Editor\FontEditor\FontEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\GameProjectGeneration\GameProjectGeneration.Build.cs"><Link>Source\Editor\GameProjectGeneration\GameProjectGeneration.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\GameplayDebugger\GameplayDebuggerEditor.Build.cs"><Link>Source\Editor\GameplayDebugger\GameplayDebuggerEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\GameplayTasksEditor\GameplayTasksEditor.Build.cs"><Link>Source\Editor\GameplayTasksEditor\GameplayTasksEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\GraphEditor\GraphEditor.Build.cs"><Link>Source\Editor\GraphEditor\GraphEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\HardwareTargeting\HardwareTargeting.Build.cs"><Link>Source\Editor\HardwareTargeting\HardwareTargeting.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\HierarchicalLODOutliner\HierarchicalLODOutliner.Build.cs"><Link>Source\Editor\HierarchicalLODOutliner\HierarchicalLODOutliner.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\InputBindingEditor\InputBindingEditor.Build.cs"><Link>Source\Editor\InputBindingEditor\InputBindingEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\InternationalizationSettings\InternationalizationSettings.Build.cs"><Link>Source\Editor\InternationalizationSettings\InternationalizationSettings.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\KismetCompiler\KismetCompiler.Build.cs"><Link>Source\Editor\KismetCompiler\KismetCompiler.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\KismetWidgets\KismetWidgets.Build.cs"><Link>Source\Editor\KismetWidgets\KismetWidgets.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\Kismet\Kismet.Build.cs"><Link>Source\Editor\Kismet\Kismet.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\LandscapeEditorUtilities\LandscapeEditorUtilities.Build.cs"><Link>Source\Editor\LandscapeEditorUtilities\LandscapeEditorUtilities.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\LandscapeEditor\LandscapeEditor.Build.cs"><Link>Source\Editor\LandscapeEditor\LandscapeEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\Layers\Layers.Build.cs"><Link>Source\Editor\Layers\Layers.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\LevelEditor\LevelEditor.Build.cs"><Link>Source\Editor\LevelEditor\LevelEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\LevelInstanceEditor\LevelInstanceEditor.Build.cs"><Link>Source\Editor\LevelInstanceEditor\LevelInstanceEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\LocalizationCommandletExecution\LocalizationCommandletExecution.Build.cs"><Link>Source\Editor\LocalizationCommandletExecution\LocalizationCommandletExecution.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\LocalizationDashboard\LocalizationDashboard.Build.cs"><Link>Source\Editor\LocalizationDashboard\LocalizationDashboard.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\MainFrame\MainFrame.Build.cs"><Link>Source\Editor\MainFrame\MainFrame.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\MaterialEditor\MaterialEditor.Build.cs"><Link>Source\Editor\MaterialEditor\MaterialEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\MergeActors\MergeActors.Build.cs"><Link>Source\Editor\MergeActors\MergeActors.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\MeshPaint\MeshPaint.Build.cs"><Link>Source\Editor\MeshPaint\MeshPaint.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\MovieSceneCaptureDialog\MovieSceneCaptureDialog.Build.cs"><Link>Source\Editor\MovieSceneCaptureDialog\MovieSceneCaptureDialog.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\MovieSceneTools\MovieSceneTools.Build.cs"><Link>Source\Editor\MovieSceneTools\MovieSceneTools.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\NNEEditor\NNEEditor.Build.cs"><Link>Source\Editor\NNEEditor\NNEEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\NewLevelDialog\NewLevelDialog.Build.cs"><Link>Source\Editor\NewLevelDialog\NewLevelDialog.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\OverlayEditor\OverlayEditor.Build.cs"><Link>Source\Editor\OverlayEditor\OverlayEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\PIEPreviewDeviceProfileSelector\PIEPreviewDeviceProfileSelector.Build.cs"><Link>Source\Editor\PIEPreviewDeviceProfileSelector\PIEPreviewDeviceProfileSelector.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\PIEPreviewDeviceSpecification\PIEPreviewDeviceSpecification.Build.cs"><Link>Source\Editor\PIEPreviewDeviceSpecification\PIEPreviewDeviceSpecification.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\PListEditor\PListEditor.Build.cs"><Link>Source\Editor\PListEditor\PListEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\PackagesDialog\PackagesDialog.Build.cs"><Link>Source\Editor\PackagesDialog\PackagesDialog.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\Persona\Persona.Build.cs"><Link>Source\Editor\Persona\Persona.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\PhysicsAssetEditor\PhysicsAssetEditor.Build.cs"><Link>Source\Editor\PhysicsAssetEditor\PhysicsAssetEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\PinnedCommandList\PinnedCommandList.Build.cs"><Link>Source\Editor\PinnedCommandList\PinnedCommandList.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\PixelInspector\PixelInspectorModule.Build.cs"><Link>Source\Editor\PixelInspector\PixelInspectorModule.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\PlacementMode\PlacementMode.Build.cs"><Link>Source\Editor\PlacementMode\PlacementMode.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\PluginWarden\PluginWarden.Build.cs"><Link>Source\Editor\PluginWarden\PluginWarden.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\ProjectSettingsViewer\ProjectSettingsViewer.Build.cs"><Link>Source\Editor\ProjectSettingsViewer\ProjectSettingsViewer.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\ProjectTargetPlatformEditor\ProjectTargetPlatformEditor.Build.cs"><Link>Source\Editor\ProjectTargetPlatformEditor\ProjectTargetPlatformEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\PropertyEditor\PropertyEditor.Build.cs"><Link>Source\Editor\PropertyEditor\PropertyEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\RenderResourceViewer\RenderResourceViewer.Build.cs"><Link>Source\Editor\RenderResourceViewer\RenderResourceViewer.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\RewindDebuggerInterface\RewindDebuggerInterface.Build.cs"><Link>Source\Editor\RewindDebuggerInterface\RewindDebuggerInterface.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\SceneDepthPickerMode\SceneDepthPickerMode.Build.cs"><Link>Source\Editor\SceneDepthPickerMode\SceneDepthPickerMode.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\SceneOutliner\SceneOutliner.Build.cs"><Link>Source\Editor\SceneOutliner\SceneOutliner.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\ScriptableEditorWidgets\ScriptableEditorWidgets.Build.cs"><Link>Source\Editor\ScriptableEditorWidgets\ScriptableEditorWidgets.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\SequenceRecorderSections\SequenceRecorderSections.Build.cs"><Link>Source\Editor\SequenceRecorderSections\SequenceRecorderSections.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\SequenceRecorder\SequenceRecorder.Build.cs"><Link>Source\Editor\SequenceRecorder\SequenceRecorder.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\SequencerCore\SequencerCore.Build.cs"><Link>Source\Editor\SequencerCore\SequencerCore.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\SequencerWidgets\SequencerWidgets.Build.cs"><Link>Source\Editor\SequencerWidgets\SequencerWidgets.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\Sequencer\Sequencer.Build.cs"><Link>Source\Editor\Sequencer\Sequencer.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\SerializedRecorderInterface\SerializedRecorderInterface.Build.cs"><Link>Source\Editor\SerializedRecorderInterface\SerializedRecorderInterface.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\SkeletalMeshEditor\SkeletalMeshEditor.Build.cs"><Link>Source\Editor\SkeletalMeshEditor\SkeletalMeshEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\SkeletonEditor\SkeletonEditor.Build.cs"><Link>Source\Editor\SkeletonEditor\SkeletonEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\SourceControlWindowExtender\SourceControlWindowExtender.Build.cs"><Link>Source\Editor\SourceControlWindowExtender\SourceControlWindowExtender.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\SourceControlWindows\SourceControlWindows.Build.cs"><Link>Source\Editor\SourceControlWindows\SourceControlWindows.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\SparseVolumeTexture\SparseVolumeTexture.Build.cs"><Link>Source\Editor\SparseVolumeTexture\SparseVolumeTexture.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\StaticMeshEditor\StaticMeshEditor.Build.cs"><Link>Source\Editor\StaticMeshEditor\StaticMeshEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\StatsViewer\StatsViewer.Build.cs"><Link>Source\Editor\StatsViewer\StatsViewer.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\StatusBar\StatusBar.Build.cs"><Link>Source\Editor\StatusBar\StatusBar.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\StringTableEditor\StringTableEditor.Build.cs"><Link>Source\Editor\StringTableEditor\StringTableEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\StructViewer\StructViewer.Build.cs"><Link>Source\Editor\StructViewer\StructViewer.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\SubobjectDataInterface\SubobjectDataInterface.Build.cs"><Link>Source\Editor\SubobjectDataInterface\SubobjectDataInterface.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\SubobjectEditor\SubobjectEditor.Build.cs"><Link>Source\Editor\SubobjectEditor\SubobjectEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\SwarmInterface\SwarmInterface.Build.cs"><Link>Source\Editor\SwarmInterface\SwarmInterface.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\TextureEditor\TextureEditor.Build.cs"><Link>Source\Editor\TextureEditor\TextureEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\ToolMenusEditor\ToolMenusEditor.Build.cs"><Link>Source\Editor\ToolMenusEditor\ToolMenusEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\TranslationEditor\TranslationEditor.Build.cs"><Link>Source\Editor\TranslationEditor\TranslationEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\TurnkeySupport\TurnkeySupport.Build.cs"><Link>Source\Editor\TurnkeySupport\TurnkeySupport.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\UATHelper\UATHelper.Build.cs"><Link>Source\Editor\UATHelper\UATHelper.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\UMGEditor\UMGEditor.Build.cs"><Link>Source\Editor\UMGEditor\UMGEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\UndoHistoryEditor\UndoHistoryEditor.Build.cs"><Link>Source\Editor\UndoHistoryEditor\UndoHistoryEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\UniversalObjectLocatorEditor\UniversalObjectLocatorEditor.Build.cs"><Link>Source\Editor\UniversalObjectLocatorEditor\UniversalObjectLocatorEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\UnrealEdMessages\UnrealEdMessages.Build.cs"><Link>Source\Editor\UnrealEdMessages\UnrealEdMessages.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\UnrealEd\UnrealEd.Build.cs"><Link>Source\Editor\UnrealEd\UnrealEd.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\VREditor\VREditor.Build.cs"><Link>Source\Editor\VREditor\VREditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\ViewportInteraction\ViewportInteraction.Build.cs"><Link>Source\Editor\ViewportInteraction\ViewportInteraction.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\ViewportSnapping\ViewportSnapping.Build.cs"><Link>Source\Editor\ViewportSnapping\ViewportSnapping.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\VirtualTexturingEditor\VirtualTexturingEditor.Build.cs"><Link>Source\Editor\VirtualTexturingEditor\VirtualTexturingEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\VirtualizationEditor\VirtualizationEditor.Build.cs"><Link>Source\Editor\VirtualizationEditor\VirtualizationEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\WorkspaceMenuStructure\WorkspaceMenuStructure.Build.cs"><Link>Source\Editor\WorkspaceMenuStructure\WorkspaceMenuStructure.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\WorldBrowser\WorldBrowser.Build.cs"><Link>Source\Editor\WorldBrowser\WorldBrowser.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Editor\WorldPartitionEditor\WorldPartitionEditor.Build.cs"><Link>Source\Editor\WorldPartitionEditor\WorldPartitionEditor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\AIModule\AIModule.Build.cs"><Link>Source\Runtime\AIModule\AIModule.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\AVEncoder\AVEncoder.Build.cs"><Link>Source\Runtime\AVEncoder\AVEncoder.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\AVIWriter\AVIWriter.Build.cs"><Link>Source\Runtime\AVIWriter\AVIWriter.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\AdpcmAudioDecoder\Module\AdpcmAudioDecoder.Build.cs"><Link>Source\Runtime\AdpcmAudioDecoder\Module\AdpcmAudioDecoder.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\AdvancedWidgets\AdvancedWidgets.Build.cs"><Link>Source\Runtime\AdvancedWidgets\AdvancedWidgets.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Advertising\Advertising\Advertising.Build.cs"><Link>Source\Runtime\Advertising\Advertising\Advertising.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Advertising\Android\AndroidAdvertising\AndroidAdvertising.Build.cs"><Link>Source\Runtime\Advertising\Android\AndroidAdvertising\AndroidAdvertising.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Advertising\IOS\IOSAdvertising\IOSAdvertising.Build.cs"><Link>Source\Runtime\Advertising\IOS\IOSAdvertising\IOSAdvertising.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Analytics\AnalyticsET\AnalyticsET.Build.cs"><Link>Source\Runtime\Analytics\AnalyticsET\AnalyticsET.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Analytics\AnalyticsSwrve\AnalyticsSwrve.Build.cs"><Link>Source\Runtime\Analytics\AnalyticsSwrve\AnalyticsSwrve.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Analytics\AnalyticsVisualEditing\AnalyticsVisualEditing.Build.cs"><Link>Source\Runtime\Analytics\AnalyticsVisualEditing\AnalyticsVisualEditing.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Analytics\Analytics\Analytics.Build.cs"><Link>Source\Runtime\Analytics\Analytics\Analytics.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Analytics\TelemetryUtils\TelemetryUtils.Build.cs"><Link>Source\Runtime\Analytics\TelemetryUtils\TelemetryUtils.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Android\AndroidLocalNotification\AndroidLocalNotification.Build.cs"><Link>Source\Runtime\Android\AndroidLocalNotification\AndroidLocalNotification.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Android\AndroidRuntimeSettings\AndroidRuntimeSettings.Build.cs"><Link>Source\Runtime\Android\AndroidRuntimeSettings\AndroidRuntimeSettings.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Android\AudioMixerAndroid\AudioMixerAndroid.Build.cs"><Link>Source\Runtime\Android\AudioMixerAndroid\AudioMixerAndroid.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\AnimGraphRuntime\AnimGraphRuntime.Build.cs"><Link>Source\Runtime\AnimGraphRuntime\AnimGraphRuntime.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\AnimationCore\AnimationCore.Build.cs"><Link>Source\Runtime\AnimationCore\AnimationCore.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\AppFramework\AppFramework.Build.cs"><Link>Source\Runtime\AppFramework\AppFramework.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Apple\AudioMixerAudioUnit\AudioMixerAudioUnit.Build.cs"><Link>Source\Runtime\Apple\AudioMixerAudioUnit\AudioMixerAudioUnit.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Apple\AudioMixerCoreAudio\AudioMixerCoreAudio.Build.cs"><Link>Source\Runtime\Apple\AudioMixerCoreAudio\AudioMixerCoreAudio.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Apple\MetalRHI\MetalRHI.Build.cs"><Link>Source\Runtime\Apple\MetalRHI\MetalRHI.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\ApplicationCore\ApplicationCore.Build.cs"><Link>Source\Runtime\ApplicationCore\ApplicationCore.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\AssetRegistry\AssetRegistry.Build.cs"><Link>Source\Runtime\AssetRegistry\AssetRegistry.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\AudioAnalyzer\AudioAnalyzer.Build.cs"><Link>Source\Runtime\AudioAnalyzer\AudioAnalyzer.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\AudioCaptureCore\AudioCaptureCore.Build.cs"><Link>Source\Runtime\AudioCaptureCore\AudioCaptureCore.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\AudioCaptureImplementations\Android\AudioCaptureAndroid\AudioCaptureAndroid.Build.cs"><Link>Source\Runtime\AudioCaptureImplementations\Android\AudioCaptureAndroid\AudioCaptureAndroid.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\AudioCaptureImplementations\AudioCaptureRtAudio\AudioCaptureRtAudio.Build.cs"><Link>Source\Runtime\AudioCaptureImplementations\AudioCaptureRtAudio\AudioCaptureRtAudio.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\AudioCaptureImplementations\IOS\AudioCaptureAudioUnit\AudioCaptureAudioUnit.Build.cs"><Link>Source\Runtime\AudioCaptureImplementations\IOS\AudioCaptureAudioUnit\AudioCaptureAudioUnit.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\AudioCaptureImplementations\Windows\AudioCaputureWasapi\AudioCaptureWasapi.Build.cs"><Link>Source\Runtime\AudioCaptureImplementations\Windows\AudioCaputureWasapi\AudioCaptureWasapi.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\AudioExtensions\AudioExtensions.Build.cs"><Link>Source\Runtime\AudioExtensions\AudioExtensions.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\AudioLink\AudioLinkCore\AudioLinkCore.Build.cs"><Link>Source\Runtime\AudioLink\AudioLinkCore\AudioLinkCore.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\AudioLink\AudioLinkEngine\AudioLinkEngine.Build.cs"><Link>Source\Runtime\AudioLink\AudioLinkEngine\AudioLinkEngine.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\AudioLink\AudioMixerPlatformAudioLink\AudioMixerPlatformAudioLink.Build.cs"><Link>Source\Runtime\AudioLink\AudioMixerPlatformAudioLink\AudioMixerPlatformAudioLink.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\AudioMixerCore\AudioMixerCore.Build.cs"><Link>Source\Runtime\AudioMixerCore\AudioMixerCore.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\AudioMixer\AudioMixer.Build.cs"><Link>Source\Runtime\AudioMixer\AudioMixer.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\AudioPlatformConfiguration\AudioPlatformConfiguration.Build.cs"><Link>Source\Runtime\AudioPlatformConfiguration\AudioPlatformConfiguration.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\AugmentedReality\AugmentedReality.build.cs"><Link>Source\Runtime\AugmentedReality\AugmentedReality.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\AutomationMessages\AutomationMessages.Build.cs"><Link>Source\Runtime\AutomationMessages\AutomationMessages.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\AutomationTest\AutomationTest.Build.cs"><Link>Source\Runtime\AutomationTest\AutomationTest.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\AutomationWorker\AutomationWorker.Build.cs"><Link>Source\Runtime\AutomationWorker\AutomationWorker.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\BinkAudioDecoder\Module\BinkAudioDecoder.Build.cs"><Link>Source\Runtime\BinkAudioDecoder\Module\BinkAudioDecoder.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\BlueprintRuntime\BlueprintRuntime.Build.cs"><Link>Source\Runtime\BlueprintRuntime\BlueprintRuntime.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\BuildSettings\BuildSettings.Build.cs"><Link>Source\Runtime\BuildSettings\BuildSettings.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\CEF3Utils\CEF3Utils.Build.cs"><Link>Source\Runtime\CEF3Utils\CEF3Utils.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\CUDA\Source\CUDA.Build.cs"><Link>Source\Runtime\CUDA\Source\CUDA.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Cbor\Cbor.Build.cs"><Link>Source\Runtime\Cbor\Cbor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\CinematicCamera\CinematicCamera.Build.cs"><Link>Source\Runtime\CinematicCamera\CinematicCamera.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\ClientPilot\ClientPilot.Build.cs"><Link>Source\Runtime\ClientPilot\ClientPilot.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\ClothingSystemRuntimeCommon\ClothingSystemRuntimeCommon.Build.cs"><Link>Source\Runtime\ClothingSystemRuntimeCommon\ClothingSystemRuntimeCommon.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\ClothingSystemRuntimeInterface\ClothingSystemRuntimeInterface.Build.cs"><Link>Source\Runtime\ClothingSystemRuntimeInterface\ClothingSystemRuntimeInterface.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\ClothingSystemRuntimeNv\ClothingSystemRuntimeNv.Build.cs"><Link>Source\Runtime\ClothingSystemRuntimeNv\ClothingSystemRuntimeNv.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\ColorManagement\ColorManagement.Build.cs"><Link>Source\Runtime\ColorManagement\ColorManagement.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\CookOnTheFly\CookOnTheFly.Build.cs"><Link>Source\Runtime\CookOnTheFly\CookOnTheFly.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\CoreOnline\CoreOnline.Build.cs"><Link>Source\Runtime\CoreOnline\CoreOnline.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\CoreUObject\CoreUObject.Build.cs"><Link>Source\Runtime\CoreUObject\CoreUObject.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\CoreVerseVM\CoreVerseVM.build.cs"><Link>Source\Runtime\CoreVerseVM\CoreVerseVM.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Core\Core.Build.cs"><Link>Source\Runtime\Core\Core.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\CrashReportCore\CrashReportCore.Build.cs"><Link>Source\Runtime\CrashReportCore\CrashReportCore.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\D3D12RHI\D3D12RHI.Build.cs"><Link>Source\Runtime\D3D12RHI\D3D12RHI.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Datasmith\CADKernel\CADKernel.Build.cs"><Link>Source\Runtime\Datasmith\CADKernel\CADKernel.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Datasmith\DatasmithCore\DatasmithCore.Build.cs"><Link>Source\Runtime\Datasmith\DatasmithCore\DatasmithCore.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Datasmith\DirectLink\DirectLink.Build.cs"><Link>Source\Runtime\Datasmith\DirectLink\DirectLink.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\DeveloperSettings\DeveloperSettings.Build.cs"><Link>Source\Runtime\DeveloperSettings\DeveloperSettings.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\EngineMessages\EngineMessages.Build.cs"><Link>Source\Runtime\EngineMessages\EngineMessages.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\EngineSettings\EngineSettings.Build.cs"><Link>Source\Runtime\EngineSettings\EngineSettings.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Engine\Engine.Build.cs"><Link>Source\Runtime\Engine\Engine.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Experimental\Animation\Constraints\Constraints.Build.cs"><Link>Source\Runtime\Experimental\Animation\Constraints\Constraints.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Experimental\ChaosCore\ChaosCore.Build.cs"><Link>Source\Runtime\Experimental\ChaosCore\ChaosCore.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Experimental\ChaosSolverEngine\ChaosSolverEngine.Build.cs"><Link>Source\Runtime\Experimental\ChaosSolverEngine\ChaosSolverEngine.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Experimental\ChaosVDData\ChaosVDData.build.cs"><Link>Source\Runtime\Experimental\ChaosVDData\ChaosVDData.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Experimental\ChaosVehicles\ChaosVehiclesCore\ChaosVehiclesCore.Build.cs"><Link>Source\Runtime\Experimental\ChaosVehicles\ChaosVehiclesCore\ChaosVehiclesCore.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Experimental\ChaosVehicles\ChaosVehiclesEngine\ChaosVehiclesEngine.Build.cs"><Link>Source\Runtime\Experimental\ChaosVehicles\ChaosVehiclesEngine\ChaosVehiclesEngine.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Experimental\ChaosVisualDebugger\ChaosVDRuntime.Build.cs"><Link>Source\Runtime\Experimental\ChaosVisualDebugger\ChaosVDRuntime.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Experimental\Chaos\Chaos.Build.cs"><Link>Source\Runtime\Experimental\Chaos\Chaos.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Experimental\Dataflow\Core\DataflowCore.Build.cs"><Link>Source\Runtime\Experimental\Dataflow\Core\DataflowCore.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Experimental\Dataflow\Engine\DataflowEngine.Build.cs"><Link>Source\Runtime\Experimental\Dataflow\Engine\DataflowEngine.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Experimental\FieldSystem\Source\FieldSystemEngine\FieldSystemEngine.Build.cs"><Link>Source\Runtime\Experimental\FieldSystem\Source\FieldSystemEngine\FieldSystemEngine.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Experimental\GeometryCollectionEngine\GeometryCollectionEngine.Build.cs"><Link>Source\Runtime\Experimental\GeometryCollectionEngine\GeometryCollectionEngine.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Experimental\IoStoreOnDemand\IoStoreOnDemand.Build.cs"><Link>Source\Runtime\Experimental\IoStoreOnDemand\IoStoreOnDemand.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Experimental\Iris\Core\IrisCore.Build.cs"><Link>Source\Runtime\Experimental\Iris\Core\IrisCore.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Experimental\Iris\Stub\IrisStub.Build.cs"><Link>Source\Runtime\Experimental\Iris\Stub\IrisStub.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Experimental\Voronoi\Voronoi.Build.cs"><Link>Source\Runtime\Experimental\Voronoi\Voronoi.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\ExternalRPCRegistry\ExternalRpcRegistry.Build.cs"><Link>Source\Runtime\ExternalRPCRegistry\ExternalRpcRegistry.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\EyeTracker\EyeTracker.Build.cs"><Link>Source\Runtime\EyeTracker\EyeTracker.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\FieldNotification\FieldNotification.Build.cs"><Link>Source\Runtime\FieldNotification\FieldNotification.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Foliage\Foliage.Build.cs"><Link>Source\Runtime\Foliage\Foliage.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\FriendsAndChat\FriendsAndChat.Build.cs"><Link>Source\Runtime\FriendsAndChat\FriendsAndChat.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\GameMenuBuilder\GameMenuBuilder.Build.cs"><Link>Source\Runtime\GameMenuBuilder\GameMenuBuilder.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\GameplayDebugger\GameplayDebugger.Build.cs"><Link>Source\Runtime\GameplayDebugger\GameplayDebugger.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\GameplayMediaEncoder\GameplayMediaEncoder.Build.cs"><Link>Source\Runtime\GameplayMediaEncoder\GameplayMediaEncoder.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\GameplayTags\GameplayTags.Build.cs"><Link>Source\Runtime\GameplayTags\GameplayTags.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\GameplayTasks\GameplayTasks.Build.cs"><Link>Source\Runtime\GameplayTasks\GameplayTasks.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\GeometryCore\GeometryCore.Build.cs"><Link>Source\Runtime\GeometryCore\GeometryCore.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\GeometryFramework\GeometryFramework.Build.cs"><Link>Source\Runtime\GeometryFramework\GeometryFramework.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\HardwareSurvey\HardwareSurvey.Build.cs"><Link>Source\Runtime\HardwareSurvey\HardwareSurvey.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\HeadMountedDisplay\HeadMountedDisplay.Build.cs"><Link>Source\Runtime\HeadMountedDisplay\HeadMountedDisplay.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\IESFile\IESFile.Build.cs"><Link>Source\Runtime\IESFile\IESFile.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\IOS\IOSAudio\IOSAudio.Build.cs"><Link>Source\Runtime\IOS\IOSAudio\IOSAudio.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\IOS\IOSLocalNotification\IOSLocalNotification.Build.cs"><Link>Source\Runtime\IOS\IOSLocalNotification\IOSLocalNotification.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\IOS\IOSPlatformFeatures\IOSPlatformFeatures.Build.cs"><Link>Source\Runtime\IOS\IOSPlatformFeatures\IOSPlatformFeatures.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\IOS\IOSRuntimeSettings\IOSRuntimeSettings.Build.cs"><Link>Source\Runtime\IOS\IOSRuntimeSettings\IOSRuntimeSettings.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\IOS\LaunchDaemonMessages\LaunchDaemonMessages.Build.cs"><Link>Source\Runtime\IOS\LaunchDaemonMessages\LaunchDaemonMessages.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\IPC\IPC.Build.cs"><Link>Source\Runtime\IPC\IPC.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\ImageCore\ImageCore.Build.cs"><Link>Source\Runtime\ImageCore\ImageCore.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\ImageWrapper\ImageWrapper.Build.cs"><Link>Source\Runtime\ImageWrapper\ImageWrapper.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\ImageWriteQueue\ImageWriteQueue.Build.cs"><Link>Source\Runtime\ImageWriteQueue\ImageWriteQueue.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\InputCore\InputCore.Build.cs"><Link>Source\Runtime\InputCore\InputCore.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\InputDevice\InputDevice.Build.cs"><Link>Source\Runtime\InputDevice\InputDevice.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\InstallBundleManager\InstallBundleManager.Build.cs"><Link>Source\Runtime\InstallBundleManager\InstallBundleManager.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\InteractiveToolsFramework\InteractiveToolsFramework.Build.cs"><Link>Source\Runtime\InteractiveToolsFramework\InteractiveToolsFramework.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Interchange\Core\InterchangeCore.Build.cs"><Link>Source\Runtime\Interchange\Core\InterchangeCore.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Interchange\Engine\InterchangeEngine.Build.cs"><Link>Source\Runtime\Interchange\Engine\InterchangeEngine.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\JsonUtilities\JsonUtilities.Build.cs"><Link>Source\Runtime\JsonUtilities\JsonUtilities.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Json\Json.Build.cs"><Link>Source\Runtime\Json\Json.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Landscape\Landscape.Build.cs"><Link>Source\Runtime\Landscape\Landscape.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Launch\Launch.Build.cs"><Link>Source\Runtime\Launch\Launch.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\LevelSequence\LevelSequence.Build.cs"><Link>Source\Runtime\LevelSequence\LevelSequence.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Linux\AudioMixerSDL\AudioMixerSDL.Build.cs"><Link>Source\Runtime\Linux\AudioMixerSDL\AudioMixerSDL.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\LiveLinkAnimationCore\LiveLinkAnimationCore.Build.cs"><Link>Source\Runtime\LiveLinkAnimationCore\LiveLinkAnimationCore.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\LiveLinkInterface\LiveLinkInterface.Build.cs"><Link>Source\Runtime\LiveLinkInterface\LiveLinkInterface.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\LiveLinkMessageBusFramework\LiveLinkMessageBusFramework.Build.cs"><Link>Source\Runtime\LiveLinkMessageBusFramework\LiveLinkMessageBusFramework.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\MRMesh\MRMesh.build.cs"><Link>Source\Runtime\MRMesh\MRMesh.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\MaterialShaderQualitySettings\MaterialShaderQualitySettings.Build.cs"><Link>Source\Runtime\MaterialShaderQualitySettings\MaterialShaderQualitySettings.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\MediaAssets\MediaAssets.Build.cs"><Link>Source\Runtime\MediaAssets\MediaAssets.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\MediaUtils\MediaUtils.Build.cs"><Link>Source\Runtime\MediaUtils\MediaUtils.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Media\Media.Build.cs"><Link>Source\Runtime\Media\Media.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\MeshConversionEngineTypes\MeshConversionEngineTypes.Build.cs"><Link>Source\Runtime\MeshConversionEngineTypes\MeshConversionEngineTypes.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\MeshConversion\MeshConversion.Build.cs"><Link>Source\Runtime\MeshConversion\MeshConversion.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\MeshDescription\MeshDescription.Build.cs"><Link>Source\Runtime\MeshDescription\MeshDescription.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\MeshUtilitiesCommon\MeshUtilitiesCommon.Build.cs"><Link>Source\Runtime\MeshUtilitiesCommon\MeshUtilitiesCommon.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\MessagingCommon\MessagingCommon.Build.cs"><Link>Source\Runtime\MessagingCommon\MessagingCommon.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\MessagingRpc\MessagingRpc.Build.cs"><Link>Source\Runtime\MessagingRpc\MessagingRpc.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Messaging\Messaging.Build.cs"><Link>Source\Runtime\Messaging\Messaging.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\MoviePlayerProxy\MoviePlayerProxy.Build.cs"><Link>Source\Runtime\MoviePlayerProxy\MoviePlayerProxy.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\MoviePlayer\MoviePlayer.Build.cs"><Link>Source\Runtime\MoviePlayer\MoviePlayer.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\MovieSceneCapture\MovieSceneCapture.Build.cs"><Link>Source\Runtime\MovieSceneCapture\MovieSceneCapture.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\MovieSceneTracks\MovieSceneTracks.Build.cs"><Link>Source\Runtime\MovieSceneTracks\MovieSceneTracks.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\MovieScene\MovieScene.Build.cs"><Link>Source\Runtime\MovieScene\MovieScene.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\NNE\NNE.Build.cs"><Link>Source\Runtime\NNE\NNE.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\NavigationSystem\NavigationSystem.Build.cs"><Link>Source\Runtime\NavigationSystem\NavigationSystem.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Navmesh\Navmesh.Build.cs"><Link>Source\Runtime\Navmesh\Navmesh.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Net\Common\NetCommon.Build.cs"><Link>Source\Runtime\Net\Common\NetCommon.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Net\Core\NetCore.Build.cs"><Link>Source\Runtime\Net\Core\NetCore.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\NetworkFileSystem\NetworkFileSystem.Build.cs"><Link>Source\Runtime\NetworkFileSystem\NetworkFileSystem.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\NetworkFile\NetworkFile.Build.cs"><Link>Source\Runtime\NetworkFile\NetworkFile.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\NetworkReplayStreaming\HttpNetworkReplayStreaming\HttpNetworkReplayStreaming.Build.cs"><Link>Source\Runtime\NetworkReplayStreaming\HttpNetworkReplayStreaming\HttpNetworkReplayStreaming.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\NetworkReplayStreaming\InMemoryNetworkReplayStreaming\InMemoryNetworkReplayStreaming.build.cs"><Link>Source\Runtime\NetworkReplayStreaming\InMemoryNetworkReplayStreaming\InMemoryNetworkReplayStreaming.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\NetworkReplayStreaming\LocalFileNetworkReplayStreaming\LocalFileNetworkReplayStreaming.Build.cs"><Link>Source\Runtime\NetworkReplayStreaming\LocalFileNetworkReplayStreaming\LocalFileNetworkReplayStreaming.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\NetworkReplayStreaming\NetworkReplayStreaming\NetworkReplayStreaming.Build.cs"><Link>Source\Runtime\NetworkReplayStreaming\NetworkReplayStreaming\NetworkReplayStreaming.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\NetworkReplayStreaming\NullNetworkReplayStreaming\NullNetworkReplayStreaming.build.cs"><Link>Source\Runtime\NetworkReplayStreaming\NullNetworkReplayStreaming\NullNetworkReplayStreaming.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\NetworkReplayStreaming\SaveGameNetworkReplayStreaming\SaveGameNetworkReplayStreaming.Build.cs"><Link>Source\Runtime\NetworkReplayStreaming\SaveGameNetworkReplayStreaming\SaveGameNetworkReplayStreaming.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Networking\Networking.Build.cs"><Link>Source\Runtime\Networking\Networking.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\NonRealtimeAudioRenderer\NonRealtimeAudioRenderer.Build.cs"><Link>Source\Runtime\NonRealtimeAudioRenderer\NonRealtimeAudioRenderer.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\NullDrv\NullDrv.Build.cs"><Link>Source\Runtime\NullDrv\NullDrv.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\NullInstallBundleManager\NullInstallBundleManager.build.cs"><Link>Source\Runtime\NullInstallBundleManager\NullInstallBundleManager.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Online\BackgroundHTTPFileHash\BackgroundHTTPFileHash.Build.cs"><Link>Source\Runtime\Online\BackgroundHTTPFileHash\BackgroundHTTPFileHash.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Online\BackgroundHTTP\BackgroundHTTP.Build.cs"><Link>Source\Runtime\Online\BackgroundHTTP\BackgroundHTTP.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Online\BuildPatchServices\BuildPatchServices.Build.cs"><Link>Source\Runtime\Online\BuildPatchServices\BuildPatchServices.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Online\Experimental\EventLoopTests\EventLoopUnitTests.Build.cs"><Link>Source\Runtime\Online\Experimental\EventLoopTests\EventLoopUnitTests.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Online\Experimental\EventLoop\EventLoop.Build.cs"><Link>Source\Runtime\Online\Experimental\EventLoop\EventLoop.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Online\HTTPServer\HttpServer.Build.cs"><Link>Source\Runtime\Online\HTTPServer\HttpServer.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Online\HTTP\HTTP.Build.cs"><Link>Source\Runtime\Online\HTTP\HTTP.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Online\ICMP\Icmp.Build.cs"><Link>Source\Runtime\Online\ICMP\Icmp.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Online\ImageDownload\ImageDownload.Build.cs"><Link>Source\Runtime\Online\ImageDownload\ImageDownload.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Online\SSL\SSL.Build.cs"><Link>Source\Runtime\Online\SSL\SSL.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Online\Stomp\Stomp.Build.cs"><Link>Source\Runtime\Online\Stomp\Stomp.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Online\Voice\Voice.Build.cs"><Link>Source\Runtime\Online\Voice\Voice.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Online\WebSockets\WebSockets.Build.cs"><Link>Source\Runtime\Online\WebSockets\WebSockets.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Online\XMPP\XMPP.Build.cs"><Link>Source\Runtime\Online\XMPP\XMPP.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\OodleDataCompression\OodleDataCompression.Build.cs"><Link>Source\Runtime\OodleDataCompression\OodleDataCompression.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\OpenColorIOWrapper\OpenColorIOWrapper.Build.cs"><Link>Source\Runtime\OpenColorIOWrapper\OpenColorIOWrapper.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\OpenGLDrv\OpenGLDrv.Build.cs"><Link>Source\Runtime\OpenGLDrv\OpenGLDrv.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\OpusAudioDecoder\Module\OpusAudioDecoder.Build.cs"><Link>Source\Runtime\OpusAudioDecoder\Module\OpusAudioDecoder.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Overlay\Overlay.Build.cs"><Link>Source\Runtime\Overlay\Overlay.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\PacketHandlers\EncryptionComponents\AsymmetricEncryption\RSAEncryptionHandlerComponent\RSAEncryptionHandlerComponent.Build.cs"><Link>Source\Runtime\PacketHandlers\EncryptionComponents\AsymmetricEncryption\RSAEncryptionHandlerComponent\RSAEncryptionHandlerComponent.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\PacketHandlers\EncryptionComponents\EncryptionHandlerComponent\EncryptionHandlerComponent.Build.cs"><Link>Source\Runtime\PacketHandlers\EncryptionComponents\EncryptionHandlerComponent\EncryptionHandlerComponent.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\PacketHandlers\EncryptionComponents\RSAKeyAESEncryption\RSAKeyAESEncryption.Build.cs"><Link>Source\Runtime\PacketHandlers\EncryptionComponents\RSAKeyAESEncryption\RSAKeyAESEncryption.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\PacketHandlers\EncryptionComponents\SymmetricEncryption\BlockEncryption\AESBlockEncryptor\AESBlockEncryptor.Build.cs"><Link>Source\Runtime\PacketHandlers\EncryptionComponents\SymmetricEncryption\BlockEncryption\AESBlockEncryptor\AESBlockEncryptor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\PacketHandlers\EncryptionComponents\SymmetricEncryption\BlockEncryption\BlockEncryptionHandlerComponent\BlockEncryptionHandlerComponent.Build.cs"><Link>Source\Runtime\PacketHandlers\EncryptionComponents\SymmetricEncryption\BlockEncryption\BlockEncryptionHandlerComponent\BlockEncryptionHandlerComponent.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\PacketHandlers\EncryptionComponents\SymmetricEncryption\BlockEncryption\BlowFishBlockEncryptor\BlowFishBlockEncryptor.Build.cs"><Link>Source\Runtime\PacketHandlers\EncryptionComponents\SymmetricEncryption\BlockEncryption\BlowFishBlockEncryptor\BlowFishBlockEncryptor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\PacketHandlers\EncryptionComponents\SymmetricEncryption\BlockEncryption\TwoFishBlockEncryptor\TwoFishBlockEncryptor.Build.cs"><Link>Source\Runtime\PacketHandlers\EncryptionComponents\SymmetricEncryption\BlockEncryption\TwoFishBlockEncryptor\TwoFishBlockEncryptor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\PacketHandlers\EncryptionComponents\SymmetricEncryption\BlockEncryption\XORBlockEncryptor\XORBlockEncryptor.Build.cs"><Link>Source\Runtime\PacketHandlers\EncryptionComponents\SymmetricEncryption\BlockEncryption\XORBlockEncryptor\XORBlockEncryptor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\PacketHandlers\EncryptionComponents\SymmetricEncryption\StreamEncryption\StreamEncryptionHandlerComponent\StreamEncryptionHandlerComponent.Build.cs"><Link>Source\Runtime\PacketHandlers\EncryptionComponents\SymmetricEncryption\StreamEncryption\StreamEncryptionHandlerComponent\StreamEncryptionHandlerComponent.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\PacketHandlers\EncryptionComponents\SymmetricEncryption\StreamEncryption\XORStreamEncryptor\XORStreamEncryptor.Build.cs"><Link>Source\Runtime\PacketHandlers\EncryptionComponents\SymmetricEncryption\StreamEncryption\XORStreamEncryptor\XORStreamEncryptor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\PacketHandlers\PacketHandler\PacketHandler.Build.cs"><Link>Source\Runtime\PacketHandlers\PacketHandler\PacketHandler.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\PacketHandlers\ReliabilityHandlerComponent\ReliabilityHandlerComponent.Build.cs"><Link>Source\Runtime\PacketHandlers\ReliabilityHandlerComponent\ReliabilityHandlerComponent.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\PakFile\PakFile.Build.cs"><Link>Source\Runtime\PakFile\PakFile.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\PerfCounters\PerfCounters.Build.cs"><Link>Source\Runtime\PerfCounters\PerfCounters.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\PhysicsCore\PhysicsCore.Build.cs"><Link>Source\Runtime\PhysicsCore\PhysicsCore.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\PlatformThirdPartyHelpers\PosixShim\PosixShim.Build.cs"><Link>Source\Runtime\PlatformThirdPartyHelpers\PosixShim\PosixShim.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Portal\LauncherCheck\LauncherCheck.Build.cs"><Link>Source\Runtime\Portal\LauncherCheck\LauncherCheck.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Portal\LauncherPlatform\LauncherPlatform.Build.cs"><Link>Source\Runtime\Portal\LauncherPlatform\LauncherPlatform.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Portal\Messages\PortalMessages.Build.cs"><Link>Source\Runtime\Portal\Messages\PortalMessages.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Portal\Proxies\PortalProxies.Build.cs"><Link>Source\Runtime\Portal\Proxies\PortalProxies.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Portal\Rpc\PortalRpc.Build.cs"><Link>Source\Runtime\Portal\Rpc\PortalRpc.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Portal\Services\PortalServices.Build.cs"><Link>Source\Runtime\Portal\Services\PortalServices.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\PreLoadScreen\PreLoadScreen.Build.cs"><Link>Source\Runtime\PreLoadScreen\PreLoadScreen.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Projects\Projects.Build.cs"><Link>Source\Runtime\Projects\Projects.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\PropertyPath\PropertyPath.Build.cs"><Link>Source\Runtime\PropertyPath\PropertyPath.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\RHICore\RHICore.Build.cs"><Link>Source\Runtime\RHICore\RHICore.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\RHI\RHI.Build.cs"><Link>Source\Runtime\RHI\RHI.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\RSA\RSA.Build.cs"><Link>Source\Runtime\RSA\RSA.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\RadAudioCodec\Module\RadAudioDecoder.Build.cs"><Link>Source\Runtime\RadAudioCodec\Module\RadAudioDecoder.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\RawMesh\RawMesh.Build.cs"><Link>Source\Runtime\RawMesh\RawMesh.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\RenderCore\RenderCore.Build.cs"><Link>Source\Runtime\RenderCore\RenderCore.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Renderer\Renderer.Build.cs"><Link>Source\Runtime\Renderer\Renderer.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\RuntimeAssetCache\RuntimeAssetCache.Build.cs"><Link>Source\Runtime\RuntimeAssetCache\RuntimeAssetCache.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\SandboxFile\SandboxFile.Build.cs"><Link>Source\Runtime\SandboxFile\SandboxFile.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Serialization\Serialization.Build.cs"><Link>Source\Runtime\Serialization\Serialization.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\SessionMessages\SessionMessages.Build.cs"><Link>Source\Runtime\SessionMessages\SessionMessages.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\SessionServices\SessionServices.Build.cs"><Link>Source\Runtime\SessionServices\SessionServices.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\SignalProcessing\SignalProcessing.Build.cs"><Link>Source\Runtime\SignalProcessing\SignalProcessing.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\SkeletalMeshDescription\SkeletalMeshDescription.Build.cs"><Link>Source\Runtime\SkeletalMeshDescription\SkeletalMeshDescription.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\SlateCore\SlateCore.Build.cs"><Link>Source\Runtime\SlateCore\SlateCore.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\SlateNullRenderer\SlateNullRenderer.Build.cs"><Link>Source\Runtime\SlateNullRenderer\SlateNullRenderer.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\SlateRHIRenderer\SlateRHIRenderer.Build.cs"><Link>Source\Runtime\SlateRHIRenderer\SlateRHIRenderer.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Slate\Slate.Build.cs"><Link>Source\Runtime\Slate\Slate.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Sockets\Sockets.Build.cs"><Link>Source\Runtime\Sockets\Sockets.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\SoundFieldRendering\SoundFieldRendering.Build.cs"><Link>Source\Runtime\SoundFieldRendering\SoundFieldRendering.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\StaticMeshDescription\StaticMeshDescription.Build.cs"><Link>Source\Runtime\StaticMeshDescription\StaticMeshDescription.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\StorageServerClient\StorageServerClient.Build.cs"><Link>Source\Runtime\StorageServerClient\StorageServerClient.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\StreamingFile\StreamingFile.Build.cs"><Link>Source\Runtime\StreamingFile\StreamingFile.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\StreamingPauseRendering\StreamingPauseRendering.Build.cs"><Link>Source\Runtime\StreamingPauseRendering\StreamingPauseRendering.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\SymsLib\SymsLib.Build.cs"><Link>Source\Runtime\SymsLib\SymsLib.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\SynthBenchmark\SynthBenchmark.Build.cs"><Link>Source\Runtime\SynthBenchmark\SynthBenchmark.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\TextureUtilitiesCommon\TextureUtilitiesCommon.build.cs"><Link>Source\Runtime\TextureUtilitiesCommon\TextureUtilitiesCommon.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\TimeManagement\TimeManagement.Build.cs"><Link>Source\Runtime\TimeManagement\TimeManagement.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\TraceLog\TraceLog.Build.cs"><Link>Source\Runtime\TraceLog\TraceLog.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\TypedElementFramework\TypedElementFramework.Build.cs"><Link>Source\Runtime\TypedElementFramework\TypedElementFramework.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\TypedElementRuntime\TypedElementRuntime.Build.cs"><Link>Source\Runtime\TypedElementRuntime\TypedElementRuntime.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\UEJpegComp\UEJpegComp.Build.cs"><Link>Source\Runtime\UEJpegComp\UEJpegComp.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\UELibrary\UELibrary.Build.cs"><Link>Source\Runtime\UELibrary\UELibrary.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\UEWavComp\UEWavComp.Build.cs"><Link>Source\Runtime\UEWavComp\UEWavComp.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\UMG\UMG.Build.cs"><Link>Source\Runtime\UMG\UMG.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\UniversalObjectLocator\UniversalObjectLocator.Build.cs"><Link>Source\Runtime\UniversalObjectLocator\UniversalObjectLocator.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Unix\UnixCommonStartup\UnixCommonStartup.Build.cs"><Link>Source\Runtime\Unix\UnixCommonStartup\UnixCommonStartup.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\UnrealGame\UnrealGame.Build.cs"><Link>Source\Runtime\UnrealGame\UnrealGame.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\VectorVM\VectorVM.Build.cs"><Link>Source\Runtime\VectorVM\VectorVM.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\VirtualFileCache\VirtualFileCache.Build.cs"><Link>Source\Runtime\VirtualFileCache\VirtualFileCache.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\VirtualProduction\StageDataCore\StageDataCore.build.cs"><Link>Source\Runtime\VirtualProduction\StageDataCore\StageDataCore.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\VorbisAudioDecoder\Module\VorbisAudioDecoder.Build.cs"><Link>Source\Runtime\VorbisAudioDecoder\Module\VorbisAudioDecoder.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\VulkanRHI\VulkanRHI.Build.cs"><Link>Source\Runtime\VulkanRHI\VulkanRHI.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\WebBrowserTexture\WebBrowserTexture.Build.cs"><Link>Source\Runtime\WebBrowserTexture\WebBrowserTexture.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\WebBrowser\WebBrowser.Build.cs"><Link>Source\Runtime\WebBrowser\WebBrowser.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\WidgetCarousel\WidgetCarousel.Build.cs"><Link>Source\Runtime\WidgetCarousel\WidgetCarousel.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Windows\AudioMixerXAudio2\AudioMixerXAudio2.Build.cs"><Link>Source\Runtime\Windows\AudioMixerXAudio2\AudioMixerXAudio2.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Windows\D3D11RHI\D3D11RHI.Build.cs"><Link>Source\Runtime\Windows\D3D11RHI\D3D11RHI.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\Windows\WindowsPlatformFeatures\WindowsPlatformFeatures.Build.cs"><Link>Source\Runtime\Windows\WindowsPlatformFeatures\WindowsPlatformFeatures.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\Runtime\XmlParser\XmlParser.Build.cs"><Link>Source\Runtime\XmlParser\XmlParser.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\ADO\ADO.Build.cs"><Link>Source\ThirdParty\ADO\ADO.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\AMD\AMD_AGS\AMD_AGS.Build.cs"><Link>Source\ThirdParty\AMD\AMD_AGS\AMD_AGS.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\AMD\Amf\Amf.Build.cs"><Link>Source\ThirdParty\AMD\Amf\Amf.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\ARM\ArmlibGPUInfo\ArmlibGPUInfo.Build.cs"><Link>Source\ThirdParty\ARM\ArmlibGPUInfo\ArmlibGPUInfo.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\Alembic\AlembicLib.Build.cs"><Link>Source\ThirdParty\Alembic\AlembicLib.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\Android\cxa_demangle\cxademangle.Build.cs"><Link>Source\ThirdParty\Android\cxa_demangle\cxademangle.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\Android\detex\detex.Build.cs"><Link>Source\ThirdParty\Android\detex\detex.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\Android\libunwind\libunwind.Build.cs"><Link>Source\ThirdParty\Android\libunwind\libunwind.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\Apple\ProResLib\ProResLib.Build.cs"><Link>Source\ThirdParty\Apple\ProResLib\ProResLib.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\AtomicQueue\AtomicQueue.Build.cs"><Link>Source\ThirdParty\AtomicQueue\AtomicQueue.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\AvidDNxHD\DNxHR\DNxHR.Build.cs"><Link>Source\ThirdParty\AvidDNxHD\DNxHR\DNxHR.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\AvidDNxHD\DNxMXF\DNxMXF.Build.cs"><Link>Source\ThirdParty\AvidDNxHD\DNxMXF\DNxMXF.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\AvidDNxHD\DNxUncompressed\DNxUncompressed.Build.cs"><Link>Source\ThirdParty\AvidDNxHD\DNxUncompressed\DNxUncompressed.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\BLAKE3\BLAKE3.Build.cs"><Link>Source\ThirdParty\BLAKE3\BLAKE3.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\Blosc\Blosc.Build.cs"><Link>Source\ThirdParty\Blosc\Blosc.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\Boost\Boost.Build.cs"><Link>Source\ThirdParty\Boost\Boost.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\CEF3\CEF3.build.cs"><Link>Source\ThirdParty\CEF3\CEF3.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\Catch2\Catch2.build.cs"><Link>Source\ThirdParty\Catch2\Catch2.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\Catch2\Catch2Extras.Build.cs"><Link>Source\ThirdParty\Catch2\Catch2Extras.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\DirectML\DirectML.Build.cs"><Link>Source\ThirdParty\DirectML\DirectML.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\DirectShow\DirectShow.Build.cs"><Link>Source\ThirdParty\DirectShow\DirectShow.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\EOSSDK\EOSSDK.Build.cs"><Link>Source\ThirdParty\EOSSDK\EOSSDK.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\Eigen\Eigen.Build.cs"><Link>Source\ThirdParty\Eigen\Eigen.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\Expat\Expat.Build.cs"><Link>Source\ThirdParty\Expat\Expat.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\FBX\FBX.Build.cs"><Link>Source\ThirdParty\FBX\FBX.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\Facebook\Facebook.Build.cs"><Link>Source\ThirdParty\Facebook\Facebook.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\ForsythTriOO\ForsythTriOptimizer.Build.cs"><Link>Source\ThirdParty\ForsythTriOO\ForsythTriOptimizer.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\FreeImage\FreeImage.Build.cs"><Link>Source\ThirdParty\FreeImage\FreeImage.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\FreeType2\FreeType2.Build.cs"><Link>Source\ThirdParty\FreeType2\FreeType2.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\GoogleARCore\GoogleARCoreSDK.build.cs"><Link>Source\ThirdParty\GoogleARCore\GoogleARCoreSDK.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\GoogleGameSDK\GoogleGameSDK.Build.cs"><Link>Source\ThirdParty\GoogleGameSDK\GoogleGameSDK.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\GoogleOboe\GoogleOboe.Build.cs"><Link>Source\ThirdParty\GoogleOboe\GoogleOboe.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\GoogleTest\GoogleTest.Build.cs"><Link>Source\ThirdParty\GoogleTest\GoogleTest.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\HAPMedia\HAPLib\HAPLib.Build.cs"><Link>Source\ThirdParty\HAPMedia\HAPLib\HAPLib.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\HAPMedia\SnappyLib\SnappyLib.Build.cs"><Link>Source\ThirdParty\HAPMedia\SnappyLib\SnappyLib.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\HWCPipe\HWCPipe.Build.cs"><Link>Source\ThirdParty\HWCPipe\HWCPipe.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\HarfBuzz\HarfBuzz.Build.cs"><Link>Source\ThirdParty\HarfBuzz\HarfBuzz.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\ICU\ICU.Build.cs"><Link>Source\ThirdParty\ICU\ICU.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\Imath\Imath.Build.cs"><Link>Source\ThirdParty\Imath\Imath.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\Intel\Embree\Embree3.Build.cs"><Link>Source\ThirdParty\Intel\Embree\Embree3.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\Intel\ExtensionsFramework\IntelExtensionsFramework.Build.cs"><Link>Source\ThirdParty\Intel\ExtensionsFramework\IntelExtensionsFramework.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\Intel\ISPCTexComp\IntelISPCTexComp.Build.cs"><Link>Source\ThirdParty\Intel\ISPCTexComp\IntelISPCTexComp.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\Intel\ISPC\IntelISPC.Build.cs"><Link>Source\ThirdParty\Intel\ISPC\IntelISPC.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\Intel\OIDN\IntelOIDN.Build.cs"><Link>Source\ThirdParty\Intel\OIDN\IntelOIDN.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\Intel\TBB\IntelTBB.Build.cs"><Link>Source\ThirdParty\Intel\TBB\IntelTBB.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\Intel\VTune\IntelVTune.Build.cs"><Link>Source\ThirdParty\Intel\VTune\IntelVTune.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\Kiss_FFT\Kiss_FFT.Build.cs"><Link>Source\ThirdParty\Kiss_FFT\Kiss_FFT.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\LibTiff\LibTiff.Build.cs"><Link>Source\ThirdParty\LibTiff\LibTiff.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\MaterialX\MaterialX.Build.cs"><Link>Source\ThirdParty\MaterialX\MaterialX.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\MikkTSpace\MikkTSpace.build.cs"><Link>Source\ThirdParty\MikkTSpace\MikkTSpace.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\MsQuic\MsQuic.Build.cs"><Link>Source\ThirdParty\MsQuic\MsQuic.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\NVIDIA\CUDA\CUDAHeader.Build.cs"><Link>Source\ThirdParty\NVIDIA\CUDA\CUDAHeader.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\NVIDIA\GPUDirect\GPUDirect.Build.cs"><Link>Source\ThirdParty\NVIDIA\GPUDirect\GPUDirect.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\NVIDIA\GeForceNOW\GeForceNOW.Build.cs"><Link>Source\ThirdParty\NVIDIA\GeForceNOW\GeForceNOW.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\NVIDIA\NVML\NVML.build.cs"><Link>Source\ThirdParty\NVIDIA\NVML\NVML.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\NVIDIA\NVaftermath\NVaftermath.Build.cs"><Link>Source\ThirdParty\NVIDIA\NVaftermath\NVaftermath.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\NVIDIA\Rivermax\RivermaxLib.build.cs"><Link>Source\ThirdParty\NVIDIA\Rivermax\RivermaxLib.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\NVIDIA\nvDecode\nvDecode.Build.cs"><Link>Source\ThirdParty\NVIDIA\nvDecode\nvDecode.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\NVIDIA\nvEncode\nvEncode.Build.cs"><Link>Source\ThirdParty\NVIDIA\nvEncode\nvEncode.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\NVIDIA\nvapi\NVAPI.Build.cs"><Link>Source\ThirdParty\NVIDIA\nvapi\NVAPI.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\Oculus\LibOVRPlatform\LibOVRPlatform.build.cs"><Link>Source\ThirdParty\Oculus\LibOVRPlatform\LibOVRPlatform.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\Oculus\OculusOpenXRLoader\OculusOpenXRLoader.build.cs"><Link>Source\ThirdParty\Oculus\OculusOpenXRLoader\OculusOpenXRLoader.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\Ogg\UEOgg.Build.cs"><Link>Source\ThirdParty\Ogg\UEOgg.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\OpenColorIO\OpenColorIOLib.Build.cs"><Link>Source\ThirdParty\OpenColorIO\OpenColorIOLib.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\OpenGL\OpenGL.Build.cs"><Link>Source\ThirdParty\OpenGL\OpenGL.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\OpenSSL\OpenSSL.Build.cs"><Link>Source\ThirdParty\OpenSSL\OpenSSL.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\OpenSubdiv\OpenSubdiv.Build.cs"><Link>Source\ThirdParty\OpenSubdiv\OpenSubdiv.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\OpenVDB\OpenVDB.Build.cs"><Link>Source\ThirdParty\OpenVDB\OpenVDB.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\OpenXR\OpenXR.Build.cs"><Link>Source\ThirdParty\OpenXR\OpenXR.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\Perforce\Perforce.Build.cs"><Link>Source\ThirdParty\Perforce\Perforce.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\Python3\Python3.Build.cs"><Link>Source\ThirdParty\Python3\Python3.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\RenderDoc\RenderDoc.Build.cs"><Link>Source\ThirdParty\RenderDoc\RenderDoc.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\SPIRV-Reflect\SPIRVReflect.Build.cs"><Link>Source\ThirdParty\SPIRV-Reflect\SPIRVReflect.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\ShaderConductor\ShaderConductor.Build.cs"><Link>Source\ThirdParty\ShaderConductor\ShaderConductor.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\SoundTouchZ\SoundTouchZ.Build.cs"><Link>Source\ThirdParty\SoundTouchZ\SoundTouchZ.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\SpeedTree\SpeedTree.Build.cs"><Link>Source\ThirdParty\SpeedTree\SpeedTree.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\Steamworks\Steamworks.build.cs"><Link>Source\ThirdParty\Steamworks\Steamworks.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\VHACD\VHACD.Build.cs"><Link>Source\ThirdParty\VHACD\VHACD.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\Vorbis\Vorbis.Build.cs"><Link>Source\ThirdParty\Vorbis\Vorbis.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\Vorbis\VorbisFile.Build.cs"><Link>Source\ThirdParty\Vorbis\VorbisFile.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\Vulkan\Vulkan.Build.cs"><Link>Source\ThirdParty\Vulkan\Vulkan.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\WebRTC\WebRTC.Build.cs"><Link>Source\ThirdParty\WebRTC\WebRTC.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\WinHttp\WinHttp.Build.cs"><Link>Source\ThirdParty\WinHttp\WinHttp.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\Windows\DX11\DX11.Build.cs"><Link>Source\ThirdParty\Windows\DX11\DX11.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\Windows\DX11\DX11Audio.Build.cs"><Link>Source\ThirdParty\Windows\DX11\DX11Audio.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\Windows\DX12\DX12.Build.cs"><Link>Source\ThirdParty\Windows\DX12\DX12.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\Windows\DX9\DX9.Build.cs"><Link>Source\ThirdParty\Windows\DX9\DX9.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\Windows\DirectSound\DirectSound.Build.cs"><Link>Source\ThirdParty\Windows\DirectSound\DirectSound.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\Windows\DirectX\DirectX.Build.cs"><Link>Source\ThirdParty\Windows\DirectX\DirectX.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\Windows\PIX\WinPixEventRuntime.Build.cs"><Link>Source\ThirdParty\Windows\PIX\WinPixEventRuntime.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\Windows\VisualStudioDTE\VisualStudioDTE.Build.cs"><Link>Source\ThirdParty\Windows\VisualStudioDTE\VisualStudioDTE.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\Windows\XAudio2_9\XAudio2_9.Build.cs"><Link>Source\ThirdParty\Windows\XAudio2_9\XAudio2_9.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\Windows\XInput\XInput.Build.cs"><Link>Source\ThirdParty\Windows\XInput\XInput.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\asio\Asio.Build.cs"><Link>Source\ThirdParty\asio\Asio.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\astcenc\astcenc.Build.cs"><Link>Source\ThirdParty\astcenc\astcenc.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\coremod\coremod.Build.cs"><Link>Source\ThirdParty\coremod\coremod.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\etc2comp\etc2comp.Build.cs"><Link>Source\ThirdParty\etc2comp\etc2comp.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\heapprofd\heapprofd.Build.cs"><Link>Source\ThirdParty\heapprofd\heapprofd.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\hlslcc\HLSLCC.Build.cs"><Link>Source\ThirdParty\hlslcc\HLSLCC.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\libJPG\UElibJPG.Build.cs"><Link>Source\ThirdParty\libJPG\UElibJPG.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\libOpus\libOpus.build.cs"><Link>Source\ThirdParty\libOpus\libOpus.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\libPNG\UElibPNG.Build.cs"><Link>Source\ThirdParty\libPNG\UElibPNG.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\libPhonon\LibPhonon.Build.cs"><Link>Source\ThirdParty\libPhonon\LibPhonon.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\libSampleRate\UElibSampleRate.Build.cs"><Link>Source\ThirdParty\libSampleRate\UElibSampleRate.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\libWebSockets\libWebSockets.Build.cs"><Link>Source\ThirdParty\libWebSockets\libWebSockets.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\libcurl\libcurl.Build.cs"><Link>Source\ThirdParty\libcurl\libcurl.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\libjpeg-turbo\LibJpegTurbo.Build.cs"><Link>Source\ThirdParty\libjpeg-turbo\LibJpegTurbo.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\libpas\src\libpas\libpas.Build.cs"><Link>Source\ThirdParty\libpas\src\libpas\libpas.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\libstrophe\libstrophe.Build.cs"><Link>Source\ThirdParty\libstrophe\libstrophe.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\libvpx\libvpx.build.cs"><Link>Source\ThirdParty\libvpx\libvpx.build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\libzip\libzip.Build.cs"><Link>Source\ThirdParty\libzip\libzip.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\metis\metis.Build.cs"><Link>Source\ThirdParty\metis\metis.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\mimalloc\mimalloc.Build.cs"><Link>Source\ThirdParty\mimalloc\mimalloc.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\nanoflann\nanoflann.Build.cs"><Link>Source\ThirdParty\nanoflann\nanoflann.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\nanosvg\nanosvg.Build.cs"><Link>Source\ThirdParty\nanosvg\nanosvg.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\nghttp2\nghttp2.Build.cs"><Link>Source\ThirdParty\nghttp2\nghttp2.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\nvTextureTools\nvTextureTools.Build.cs"><Link>Source\ThirdParty\nvTextureTools\nvTextureTools.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\nvTriStrip\nvTriStrip.Build.cs"><Link>Source\ThirdParty\nvTriStrip\nvTriStrip.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\nvtesslib\nvTessLib.Build.cs"><Link>Source\ThirdParty\nvtesslib\nvTessLib.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\openexr\UEOpenExr.Build.cs"><Link>Source\ThirdParty\openexr\UEOpenExr.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\portmidi\portmidi.Build.cs"><Link>Source\ThirdParty\portmidi\portmidi.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\rpclib\rpclib.Build.cs"><Link>Source\ThirdParty\rpclib\rpclib.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\stb_image_resize\stb_image_resize2.Build.cs"><Link>Source\ThirdParty\stb_image_resize\stb_image_resize2.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\xxhash\xxhash.Build.cs"><Link>Source\ThirdParty\xxhash\xxhash.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\ThirdParty\zlib\zlib.Build.cs"><Link>Source\ThirdParty\zlib\zlib.Build.cs</Link></Compile>
  </ItemGroup>
</Project>
