
#pragma once

#include "CoreMinimal.h"
#include "ThumbnailHelpers.h"
#include "ThumbnailRendering/DefaultSizedThumbnailRenderer.h"
#include "AccessoryThumbnailRenderer.generated.h"

class FAccessoryThumbnailScene;
/**
 * 
 */
UCLASS()
class ACCESSORYEDITOR_API UAccessoryThumbnailRenderer : public UDefaultSizedThumbnailRenderer
{
	GENERATED_BODY()

public:
	UAccessoryThumbnailRenderer();

protected:
	virtual void Draw(UObject* Object, int32 X, int32 Y, uint32 Width, uint32 Height, FRenderTarget*, FCanvas* Canvas, bool bAdditionalViewFamily) override;
	
	virtual void BeginDestroy() override;

	virtual EThumbnailRenderFrequency GetThumbnailRenderFrequency(UObject* Object) const override;

private:
	TObjectInstanceThumbnailScene<FAccessoryThumbnailScene, 128> ThumbnailSceneCache;
};
