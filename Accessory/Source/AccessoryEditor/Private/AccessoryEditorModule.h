#pragma once

#include "CoreMinimal.h"
#include "IAccessoryEditorModule.h"
#include "Modules/ModuleInterface.h"
#include "Modules/ModuleManager.h"

DECLARE_LOG_CATEGORY_EXTERN(LogAccessoryEditor, Log, All);

class ACCESSORYEDITOR_API FAccessoryEditorModule : public IAccessoryEditorModule, public IHasToolBarExtensibility
{
public:

	virtual void StartupModule() override;
    virtual void ShutdownModule() override;
	virtual void LaunchEditor(TObjectPtr<UObject> InObject) const override;

	virtual TSharedPtr<FExtensibilityManager> GetToolBarExtensibilityManager() override {return ToolBarExtensibilityManager;}

private:
	TSharedPtr<FExtensibilityManager> ToolBarExtensibilityManager;
};
