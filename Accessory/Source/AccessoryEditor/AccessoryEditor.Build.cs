using UnrealBuildTool;

public class AccessoryEditor : ModuleRules
{
    public AccessoryEditor(ReadOnlyTargetRules Target) : base(Target)
    {
        PCHUsage = ModuleRules.PCHUsageMode.UseExplicitOrSharedPCHs;

        PublicDependencyModuleNames.AddRange(
            new string[]
            {
                "Core",
                "DeveloperSettings",
            }
        );

        PrivateDependencyModuleNames.AddRange(
            new string[]
            {
                "CoreUObject",
                "Engine",
                "UnrealEd",
                "Accessory",
                "AssetDefinition",
                "Slate",
                "SlateCore",
                "AdvancedPreviewScene",
                "AssetPipeline",
                "ContentBrowser",
                "InputCore", "Niagara","FbxUtilsModule", "FBX", "SkeletalMeshEditor","StaticMeshEditor", "RenderCore", "ToolMenus", 
                "EditorScriptingUtilities", "Projects", "EditorWidgets", "KismetWidgets", "SkeletalMeshUtilitiesCommon"
            }
        );
        
        // OptimizeCode = CodeOptimization.Never;
    }
}