// Fill out your copyright notice in the Description page of Project Settings.


#include "WardrobeComponent.h"

#include "Library/UGCBaseLibrary.h"
#include "WardrobeLibrary.h"
#include "WardrobeSettings.h"
#include "Asset/Garment.h"
#include "Engine/SkinnedAssetCommon.h"
#include "Engine/Texture2DDynamic.h"
#include "Engine/TextureRenderTarget2D.h"
#include "Kismet/KismetRenderingLibrary.h"
#include "Misc/UObjectToken.h"
#include "Rendering/SkeletalMeshRenderData.h"

#define LOCTEXT_NAMESPACE "WardrobeComponent"

static int CullingMaskSize = 256;

// Sets default values for this component's properties
UWardrobeComponent::UWardrobeComponent()
	: DefaultLodLevel(1)
	, bForceMipStreaming(false)
	, bUseInitialPresetInGame(true)
	, CastShadow(true)
	, bCastInsetShadow(false)
	, bCastCapsuleDirectShadow(false)
	, bRenderCustomDepth(false)
//, MergeMaskMaterial(nullptr)
{
	// Set this component to be initialized when the game starts, and to be ticked every frame.  You can turn these features
	// off to improve performance if you don't need them.
	PrimaryComponentTick.bCanEverTick = true;

	// BodyAnimClass = BodyABP.Class;
	// #if WITH_EDITOR
	// 	if(FModuleManager::Get().IsModuleLoaded("PropertyAccessEditor"))
	// #endif
	// 	{
	// 		ConstructorHelpers::FClassFinder<UAnimInstance> BodyPoseCopyABP(TEXT("/Garment/ABPT/Wardrobe_ABPT_PoseCopy.Wardrobe_ABPT_PoseCopy_C"));
	// 		BodyPoseCopyAnimClass = BodyPoseCopyABP.Class;
	// 	}
	// check(BodyPoseCopyAnimClass);

	BodyPoseCopyAnimClass = TSoftClassPtr<UAnimInstance>(FSoftObjectPath(TEXT("/Garment/ABPT/Wardrobe_ABPT_PoseCopy.Wardrobe_ABPT_PoseCopy_C")));

	bTickInEditor = true;
	bEnableDissolveEffect = false;
	FaceMeshComponentName = TEXT("Face");
	BodyMeshComponentName = TEXT("CharacterMesh0");
	CustomPrimitiveDataFloatSetting.Add(1, 1.0);
	ForcedLodLevel = DefaultLodLevel;
	LightingChannels.bChannel0 = 1;
	LightingChannels.bChannel1 = 1;
	LightingChannels.bChannel2 = 0;
	CustomDepthStencilValue = 0;
	bReceivesDecals = false;
	bEnableUpdateRateOptimizations = false;
	// bEnableLeaderPoseOptimizations = true;
	CullingVolumeThreshold = 5000.0f;
	bEnableLeaderPoseOptimization = false;
	bUseSkeletalMeshComponentsPool = true;
	VisibilityBasedAnimTickOption = EVisibilityBasedAnimTickOption::OnlyTickPoseWhenRendered;

	for (int i = 0; i < 6; ++i)
	{
		BodyFaceLodMap.Add(i, i);
	}

	AvatarProfile = CreateDefaultSubobject<UUGCAvatarProfile>(TEXT("AvatarProfile"));

	// auto Finder = ConstructorHelpers::FObjectFinder<UMaterialInterface>(TEXT("/Garment/GarmentProcessor/M_MergeMask.M_MergeMask"));
	// if (Finder.Succeeded())
	// {
	// 	MergeMaskMaterial = UMaterialInstanceDynamic::Create(Finder.Object, this);
	// }
}

#if WITH_EDITOR
void UWardrobeComponent::CheckForErrors()
{
	Super::CheckForErrors();

	if (BodyPoseCopyAnimClass == nullptr)
	{
		AActor* MyOwner = GetOwner();
		FFormatNamedArguments Arguments;
		Arguments.Add(TEXT("ComponentName"), FText::FromString(GetName()));
		Arguments.Add(TEXT("OwnerName"), FText::FromString("BodyPoseCopyAnimClass"));
		FMessageLog("LogGarment").Error()
			->AddToken(FUObjectToken::Create(MyOwner))
			->AddToken(FTextToken::Create(FText::Format(LOCTEXT("NoBodyPoseCopyAnimClass",
				"BodyPoseCopyAnimClass::{ComponentName}::{OwnerName} is none. Please use Wardrobe_ABPT_PoseCopy"), Arguments)));
	}
}
#endif


// Called when the game starts
void UWardrobeComponent::BeginPlay()
{
	Super::BeginPlay();

	// ...
}

void UWardrobeComponent::OnRegister()
{
	Super::OnRegister();

#if WITH_EDITOR
	TInlineComponentArray<UGarmentComponent*> AllGarmentComponents(GetOwner());
	for (UGarmentComponent* GarmentComponent : AllGarmentComponents)
	{
		GarmentComponent->DestroyComponent();
	}
#endif


	// Get BodyMeshComponent And FaceMeshComponent
	// We only support primitive components that implement the ILODSyncInterface.
	TInlineComponentArray<USkeletalMeshComponent*, 32> AllComponents(GetOwner());
	// const int32 NumPrimitiveComponents = AllComponents.Num();
	for (const auto& Component : AllComponents)
	{
		if (FaceMeshComponentName == Component->GetFName().ToString())
		{
			FaceMeshComponent = Component;
			FaceMeshComponent->SetUpdateAnimationInEditor(true);
			for (auto Index = 0; Index < FaceMeshComponent->GetNumMaterials(); ++Index)
			{
				auto MID = FaceMeshComponent->CreateAndSetMaterialInstanceDynamic(Index);
				auto CullingMask = GetCullingMask(0);
				FUGCBaseLibrary::SetMaterialTextureParameterValue(MID, TEXT("Culling Mask_S"), CullingMask);
			}
		}
		else if (BodyMeshComponentName == Component->GetFName().ToString())
		{
			BodyMeshComponent = Component;
			BodyMeshComponent->SetUpdateAnimationInEditor(true);
			for (auto Index = 0; Index < BodyMeshComponent->GetNumMaterials(); ++Index)
			{
				auto MID = BodyMeshComponent->CreateAndSetMaterialInstanceDynamic(Index);
				auto CullingMask = GetCullingMask(0);
				FUGCBaseLibrary::SetMaterialTextureParameterValue(MID, TEXT("Culling Mask_S"), CullingMask);
			}
		}
	}

	ForcedLodLevel = DefaultLodLevel;
	SetForcedLodLevel(ForcedLodLevel);
	SetForceMipStreaming(bForceMipStreaming);
	SetLightingChannels(LightingChannels);
	SetCastShadow(CastShadow);
	SetCastInsetShadow(bCastInsetShadow);
	SetCastCapsuleDirectShadow(bCastCapsuleDirectShadow);
	SetRenderCustomDepth(bRenderCustomDepth);
	SetCustomDepthStencilValue(CustomDepthStencilValue);
	SetReceivesDecals(bReceivesDecals);
	SetVisibilityBasedAnimTickOption(VisibilityBasedAnimTickOption);

	// Create Mask Merge Material
	// const FString MaterialPath = TEXT("/Garment/GarmentProcessor/M_MergeMask.M_MergeMask");
	// if (auto Material = Cast<UMaterialInterface>(StaticLoadObject(UMaterialInterface::StaticClass(), this, *MaterialPath)))
	// {
	// 	MergeMaskMaterial = UMaterialInstanceDynamic::Create(Material, this);
	// }

	if (!EcoJointDynamicsComponent)
	{
		EcoJointDynamicsComponent = NewObject<UEcoJointDynamicsComponent>(GetOwner(), "EcoJointDynamicsComponent");
		// GetOwner()->AddInstanceComponent(EcoJointDynamicsComponent);
		EcoJointDynamicsComponent->RegisterComponent();

		for (auto GarmentComponent : CharacterGarmentComponents)
		{
			EcoJointDynamicsComponent->OnAvatarPartLoaded(GarmentComponent->GetGarmentAsset()->GetBodyPartTypeName());
		}
	}

	if (!DissolveComponent)
	{
		DissolveComponent = NewObject<UDissolveVFXComponent>(GetOwner(), "DissolveVFXComponent");
		// GetOwner()->AddInstanceComponent(DissolveComponent);
		DissolveComponent->RegisterComponent();
		DissolveComponent->SetPlayFinishDelegate(FSimpleDelegate::CreateUObject(this, &UWardrobeComponent::EndOfPutOnCharacterGarment));
	}

	if (InitialPreset)
	{
		auto OriginEnableDissolveEffect = bEnableDissolveEffect;
		bEnableDissolveEffect = false;

		if (bUseInitialPresetInGame)
		{
			PutOnGarmentAssetsFormPreset(InitialPreset);
		}
		else
		{
#if WITH_EDITOR
			if (GIsEditor && (!GetWorld() || !GetWorld()->IsPlayInEditor()))
			{
				PutOnGarmentAssetsFormPreset(InitialPreset);
			}
#endif
		}

		bEnableDissolveEffect = OriginEnableDissolveEffect;
	}
}

void UWardrobeComponent::OnUnregister()
{
	Super::OnUnregister();

	for (auto GarmentComponent : CharacterGarmentComponents)
	{
		GarmentComponent->DestroyComponent();
	}
	CharacterGarmentComponents.Empty();
	CullingMasks.Empty();
	//MergeMaskMaterial = nullptr;

	if (EcoJointDynamicsComponent)
	{
		EcoJointDynamicsComponent->DestroyComponent();
		EcoJointDynamicsComponent = nullptr;
	}

	if (DissolveComponent)
	{
		DissolveComponent->DestroyComponent();
		DissolveComponent = nullptr;
	}
}

void UWardrobeComponent::OnComponentDestroyed(bool bDestroyingHierarchy)
{
	Super::OnComponentDestroyed(bDestroyingHierarchy);
}


// Called every frame
void UWardrobeComponent::TickComponent(float DeltaTime, ELevelTick TickType,
	FActorComponentTickFunction* ThisTickFunction)
{
	// Super::TickComponent(DeltaTime, TickType, ThisTickFunction);
#if WITH_EDITOR
	CacheHeelsData();
#endif

	DispatchHeelsData();
	DoGarmentCulling(true);
}

void UWardrobeComponent::PutOnGarmentAsset(UGarment* GarmentToBePutOn)
{
	if (!GarmentToBePutOn)
	{
		UE_LOG(LogGarment, Error, TEXT("[UWardrobeComponent::PutOnGarment]GarmentToBePutOn == nullptr"))
		return;
	}
	if (!UWardrobeLibrary::IsGarmentValid(GarmentToBePutOn))
	{
		UE_LOG(LogGarment, Error, TEXT("[UWardrobeComponent::PutOnGarment]Garment is Invalid!"))
		return;
	}

	PrePutOnGarment();

	PutOnGarmentAssetInternal(GarmentToBePutOn);

	PostPutOnGarment();
}

void UWardrobeComponent::PutOnGarmentAssets(TArray<UGarment*> GarmentsToBePutOn)
{
	PrePutOnGarment();

	TArray<UGarment*> PutOnGarments;
	for (auto GarmentToBePutOn : GarmentsToBePutOn)
	{
		PutOnGarmentAssetInternal(GarmentToBePutOn);
	}

	PostPutOnGarment();
}

void UWardrobeComponent::PutOnGarmentAssetsFormPreset(UWardrobePreset* Preset, bool bRemoveAllBeforePutOn)
{
	TRACE_CPUPROFILER_EVENT_SCOPE(PutOnGarmentAssetsFormPreset)
	if (!Preset)
	{
		UE_LOG(LogGarment, Error, TEXT("[UWardrobeComponent::PutOnGarmentAssetsFormPreset]Preset == nullptr"))
		return;
	}

	PrePutOnGarment();

	if (bRemoveAllBeforePutOn)
	{
		for (UGarmentComponent* GarmentComponent : CharacterGarmentComponents)
		{
			GarmentComponent->bIsBanned = true;
			BannedCharacterGarmentComponents.AddUnique(GarmentComponent);
		}
		CharacterGarmentComponents.Empty();
	}

	for (FPresetGarment& PresetGarment : Preset->PresetGarments)
	{
		if (PresetGarment.Garment)
		{
			PutOnGarmentAssetInternal(PresetGarment.Garment, PresetGarment.UGCData);
		}
	}

	PostPutOnGarment();
}

void UWardrobeComponent::RemoveGarmentsByGarmentId(const TArray<FString>& GarmentIdToBeRemovedArray)
{
	if (GarmentIdToBeRemovedArray.IsEmpty())
	{
		UE_LOG(LogGarment, Warning, TEXT("[UWardrobeComponent::RemoveGarmentsByGarmentId]GarmentToBeRemovedIdArray is Empty!!!"))
		return;
	}

	TArray<UGarmentComponent*> CharacterGarmentsToBeRemoved = CharacterGarmentComponents.FilterByPredicate(
		[&GarmentIdToBeRemovedArray](UGarmentComponent* GarmentComponent)
		{
			for (const auto& BodyPartIdToBeRemoved : GarmentIdToBeRemovedArray)
			{
				if (GarmentComponent->GetGarmentAsset()->GarmentId == BodyPartIdToBeRemoved)
				{
					GarmentComponent->bIsBanned = true;
					return true;
				}
			}
			return false;
		});

	RemoveGarmentAssetInternal(CharacterGarmentsToBeRemoved);
}

void UWardrobeComponent::RemoveGarmentsByBodyPartType(const TArray<EGarmentBodyPartType>& BodyPartTypeToBeRemovedArray)
{
	if (BodyPartTypeToBeRemovedArray.IsEmpty())
	{
		UE_LOG(LogGarment, Warning, TEXT("[UWardrobeComponent::RemoveGarmentsByBodyPartType]BodyPartTypeToBeRemovedArray is Empty!!!"))
		return;
	}

	TArray<UGarmentComponent*> CharacterGarmentsToBeRemoved = CharacterGarmentComponents.FilterByPredicate
	([&BodyPartTypeToBeRemovedArray](UGarmentComponent* GarmentComponent)
	{
		for (const auto& BodyPartTypeToBeRemoved : BodyPartTypeToBeRemovedArray)
		{
			if (GarmentComponent->GetGarmentAsset()->GarmentBodyPartType == BodyPartTypeToBeRemoved)
			{
				GarmentComponent->bIsBanned = true;
				return true;
			}
		}
		return false;
	});

	RemoveGarmentAssetInternal(CharacterGarmentsToBeRemoved);
}

void UWardrobeComponent::RemoveAllGarments()
{
	if (bEnableDissolveEffect && DissolveComponent)
	{
		DissolveComponent->FinishAtOnce();
	}

	for (auto GarmentComponent : CharacterGarmentComponents)
	{
		if (EcoJointDynamicsComponent)
		{
			EcoJointDynamicsComponent->OnAvatarPartRemoved(GarmentComponent->GetGarmentAsset()->GetBodyPartTypeName());
		}
		GarmentComponent->DestroyComponent();
	}
	CharacterGarmentComponents.Empty();

	RefreshCullingMask(CullingMasks.Num() - 1);

	UWardrobeLibrary::DoTighten(CharacterGarmentComponents);

	// Broadcast Delegate of Put On
	if (OnPostGarmentRemoveDelegate.IsBound())
	{
		OnPostGarmentRemoveDelegate.Broadcast();
	}

	if (OnUpdateXieZiIkPointsDelegate.IsBound())
	{
		OnUpdateXieZiIkPointsDelegate.Broadcast(true, nullptr);
	}

	if (OnUpdateHeelIKPointsDelegate.IsBound())
	{
		OnUpdateHeelIKPointsDelegate.Broadcast(FWardrobeHeelsData(true, nullptr));
	}
}

FWardrobeSharedData UWardrobeComponent::GetWardrobeSharedData()
{
	FWardrobeSharedData WardrobeSharedData;
	const auto& XieZiGarment = GetWornXieZiGarment();
	if (XieZiGarment && XieZiGarment->PatchXieZi)
	{
		WardrobeSharedData.HeelsHeight = XieZiGarment->PatchXieZi->PelvisHeight;
		WardrobeSharedData.FootBoneAngle = XieZiGarment->PatchXieZi->FootBoneAngle;
		WardrobeSharedData.BallBoneAngle = XieZiGarment->PatchXieZi->BallBoneAngle;
	}
	else
	{
		WardrobeSharedData.HeelsHeight = 0.0f;
		WardrobeSharedData.FootBoneAngle = 0.0f;
		WardrobeSharedData.BallBoneAngle = 0.0f;
	}

	return WardrobeSharedData;
}

bool UWardrobeComponent::SetGarmentUGCData(UUGCUserData* InUGCData, UGarment* InGarment)
{
	if (!InUGCData && !InGarment)
	{
		return false;
	}

	if (InUGCData && InGarment)
	{
		if (InUGCData->Profile != InGarment->UGCProfile)
		{
			UE_LOG(LogGarment, Error, TEXT("UGCData is not matched with InGarment's UGC Profile"));
			return false;
		}
	}

	for (UGarmentComponent* GarmentComponent : CharacterGarmentComponents)
	{
		auto Garment = GarmentComponent->GetGarmentAsset();
		if ((InUGCData && InUGCData->Profile->GetUGCObject() == Garment
			|| (InGarment && InGarment == Garment)))
		{
			GarmentComponent->SetUGCUserData(InUGCData);
			return true;
		}
	}

	UE_LOG(LogGarment, Error, TEXT("UGCData is not matched with any Garment's UGC Profile"));
	return false;
}

void UWardrobeComponent::SetForcedLodLevel(const int32 LodLevel)
{
	ForcedLodLevel = LodLevel;

	for (auto GarmentComponent : CharacterGarmentComponents)
	{
		GarmentComponent->SetForcedLodLevel(LodLevel);
	}

	if (BodyMeshComponent)
	{
		BodyMeshComponent->OverrideMinLOD(LodLevel);
		BodyMeshComponent->SetForcedLOD(LodLevel + 1);
	}

	if (FaceMeshComponent)
	{
		int32 FaceLod = BodyFaceLodMap.Find(LodLevel) ? BodyFaceLodMap[LodLevel] : LodLevel;
		FaceLod = FMath::Max(0, FaceLod);
		FaceMeshComponent->OverrideMinLOD(FaceLod);
		FaceMeshComponent->SetForcedLOD(FaceLod + 1);
	}
}

int32 UWardrobeComponent::GetForcedLodLevel()
{
	return ForcedLodLevel;
}

void UWardrobeComponent::SetForceMipStreaming(const bool InForceMipStreaming)
{
	bForceMipStreaming = InForceMipStreaming;
	for (auto GarmentComponent : CharacterGarmentComponents)
	{
		GarmentComponent->SetForceMipStreaming(InForceMipStreaming);
	}
}

void UWardrobeComponent::SetCustomPrimitiveDataFloatSetting(const TMap<int32, float> InCustomPrimitiveDataFloatSetting)
{
	CustomPrimitiveDataFloatSetting = InCustomPrimitiveDataFloatSetting;
	for (auto GarmentComponent : CharacterGarmentComponents)
	{
		GarmentComponent->SetPrimitiveDataFloatSetting(CustomPrimitiveDataFloatSetting);
	}
}

void UWardrobeComponent::SetLightingChannels(const FLightingChannels InLightingChannels)
{
	LightingChannels = InLightingChannels;
	for (auto GarmentComponent : CharacterGarmentComponents)
	{
		GarmentComponent->SetLightingChannels(LightingChannels);
	}
}

void UWardrobeComponent::SetCastShadow(bool NewCastShadow)
{
	CastShadow = NewCastShadow;
	for (auto GarmentComponent : CharacterGarmentComponents)
	{
		GarmentComponent->SetCastShadow(CastShadow);
	}
}

void UWardrobeComponent::SetCastInsetShadow(bool bInCastInsetShadow)
{
	bCastInsetShadow = bInCastInsetShadow;
	for (auto GarmentComponent : CharacterGarmentComponents)
	{
		GarmentComponent->SetCastInsetShadow(bCastInsetShadow);
	}
}

void UWardrobeComponent::SetMobileInsetShadowGroup(int32 InInsetShadowGroup)
{
	if (InInsetShadowGroup != InsetShadowGroup)
	{
		InsetShadowGroup = InInsetShadowGroup;

		for (auto GarmentComponent : CharacterGarmentComponents)
		{
			GarmentComponent->SetMobileInsetShadowGroup(InsetShadowGroup);
		}
	}
}

void UWardrobeComponent::SetCastCapsuleDirectShadow(bool bNewValue)
{
	bCastCapsuleDirectShadow = bNewValue;
	for (auto GarmentComponent : CharacterGarmentComponents)
	{
		GarmentComponent->SetCastCapsuleDirectShadow(bCastCapsuleDirectShadow);
	}
}

void UWardrobeComponent::SetRenderCustomDepth(bool bValue)
{
	bRenderCustomDepth = bValue;
	for (auto GarmentComponent : CharacterGarmentComponents)
	{
		GarmentComponent->SetRenderCustomDepth(bRenderCustomDepth);
	}
}

void UWardrobeComponent::SetCustomDepthStencilValue(int32 Value)
{
	// Clamping to currently usable stencil range (as specified in property UI and tooltips)
	int32 ClampedValue = FMath::Clamp(Value, 0, 255);
	CustomDepthStencilValue = ClampedValue;
	for (auto GarmentComponent : CharacterGarmentComponents)
	{
		GarmentComponent->SetCustomDepthStencilValue(CustomDepthStencilValue);
	}
}

void UWardrobeComponent::SetReceivesDecals(bool bNewReceivesDecals)
{
	if (bNewReceivesDecals != bReceivesDecals)
	{
		bReceivesDecals = bNewReceivesDecals;
		for (auto GarmentComponent : CharacterGarmentComponents)
		{
			GarmentComponent->SetReceivesDecals(bReceivesDecals);
		}
	}
}

void UWardrobeComponent::SetDisablePostProcessBlueprint(bool bInDisablePostProcess)
{
	for (const auto& GarmentComponent : GetAllGarmentComponents())
	{
		for (auto& MeshComponent : GarmentComponent->GetMeshComponents())
		{
			if (auto SkeletalMeshComponent = Cast<USkeletalMeshComponent>(MeshComponent))
			{
				SkeletalMeshComponent->SetDisablePostProcessBlueprint(bInDisablePostProcess);
			}
		}
	}
}

void UWardrobeComponent::SetVisibilityBasedAnimTickOption(EVisibilityBasedAnimTickOption NewOption)
{
	for (const auto& GarmentComponent : GetAllGarmentComponents())
	{
		for (auto& MeshComponent : GarmentComponent->GetMeshComponents())
		{
			if (auto SkeletalMeshComponent = Cast<USkeletalMeshComponent>(MeshComponent))
			{
				SkeletalMeshComponent->VisibilityBasedAnimTickOption = NewOption;
			}
		}
	}
}

void UWardrobeComponent::GetBannedGarmentsId(TArray<FString>& OutGarmentsId)
{
	OutGarmentsId = BannedGarmentsId;
}

TArray<EGarmentBodyPartType> UWardrobeComponent::GetBannedBodyPartType()
{
	return BannedBodyPartType;
}

TArray<UGarment*> UWardrobeComponent::GetAllGarments()
{
	TArray<UGarment*> Garments;
	for (UGarmentComponent* Component : CharacterGarmentComponents)
	{
		Garments.Add(Component->GetGarmentAsset());
	}
	return Garments;
}

USkeletalMeshComponent* UWardrobeComponent::GetBodyMeshComponent()
{
	return BodyMeshComponent;
}

USkeletalMeshComponent* UWardrobeComponent::GetFaceMeshComponent()
{
	return FaceMeshComponent;
}

UUGCComponent* UWardrobeComponent::GetUGCComponent(UGarment* UGCGarment)
{
	for (UGarmentComponent* GarmentComponent : CharacterGarmentComponents)
	{
		if (GarmentComponent->GetGarmentAsset() == UGCGarment)
		{
			return GarmentComponent->GetUGCComponent();
		}
	}
	return nullptr;
}

UGarmentComponent* UWardrobeComponent::ShowSingleGarment(UGarment* GarmentToBeShown)
{
	bool bRestore = true;
	UGarmentComponent* ShowGarmentComponent = nullptr;
	if (GarmentToBeShown)
	{
		for (UGarmentComponent* GarmentComponent : CharacterGarmentComponents)
		{
			if (GarmentComponent->GetGarmentAsset() == GarmentToBeShown)
			{
				bRestore = false;
				ShowGarmentComponent = GarmentComponent;
				break;
			}
		}
	}

	if (BodyMeshComponent)
	{
		BodyMeshComponent->SetVisibility(bRestore, true);
	}

	if (!bRestore)
	{
		ShowGarmentComponent->SetGarmentVisibility(true);
		ShowGarmentComponent->SetCullingMask(CullingMasks.Last());
	}
	else
	{
		for (UGarmentComponent* GarmentComponent : CharacterGarmentComponents)
		{
			if (GarmentComponent->GetGarmentAsset() == GarmentToBeShown)
			{
				GarmentComponent->SetCullingMask(GetCullingMask(GarmentComponent->GetCullingLayerIndex() + 1));
			}
		}
	}

	return ShowGarmentComponent;
}

TArray<USkeletalMeshComponent*> UWardrobeComponent::GetAllGarmentsSkeletalMeshComponents()
{
	TArray<USkeletalMeshComponent*> AllSkeletalMeshComponents;
	for (const auto& GarmentComponent : GetAllGarmentComponents())
	{
		for (auto& MeshComponent : GarmentComponent->GetMeshComponents())
		{
			if (auto SkeletalMeshComponent = Cast<USkeletalMeshComponent>(MeshComponent))
			{
				AllSkeletalMeshComponents.AddUnique(SkeletalMeshComponent);
			}
		}
	}
	return AllSkeletalMeshComponents;
}

void UWardrobeComponent::ResetDynamicsForAllGarments()
{
	for (const auto& GarmentComponent : GetAllGarmentComponents())
	{
		for (auto& MeshComponent : GarmentComponent->GetMeshComponents())
		{
			if (auto SkeletalMeshComponent = Cast<USkeletalMeshComponent>(MeshComponent))
			{
				if (SkeletalMeshComponent->PostProcessAnimInstance)
				{
					SkeletalMeshComponent->PostProcessAnimInstance->ResetDynamics(ETeleportType::ResetPhysics);
				}
			}
		}
	}
}

TArray<UGarmentComponent*> UWardrobeComponent::GetAllGarmentComponents()
{
	return CharacterGarmentComponents;
}

void UWardrobeComponent::UpdateBodyPoseCopyAnimClass(UClass* PoseCopyAnimClass)
{
	BodyPoseCopyAnimClass = PoseCopyAnimClass;
	for (UGarmentComponent* Component : CharacterGarmentComponents)
	{
		if (!Component->GetGarmentAsset())
			continue;
		if (Component->GetGarmentAsset()->GarmentBodyPartType == EGarmentBodyPartType::GBPT_TouFa)
		{
			Component->SetPose(BodyPoseCopyAnimClass.LoadSynchronous(), bEnableLeaderPoseOptimization, FaceMeshComponent);
		}
		else
		{
			Component->SetPose(BodyPoseCopyAnimClass.LoadSynchronous(), bEnableLeaderPoseOptimization, BodyMeshComponent);
		}
	}
}

void UWardrobeComponent::SetSectionVisibility(bool bShow)
{
	TArray<int32> CullingSectionIndices;

	for (const auto& GarmentComponent : CharacterGarmentComponents)
	{
		for (const auto& MeshComponent : GarmentComponent->GetMeshComponents())
		{
			if (auto SkeletalMeshComponent = Cast<USkinnedMeshComponent>(MeshComponent))
			{
				if (auto Mesh = SkeletalMeshComponent->GetSkinnedAsset())
				{
					const auto& LODRenderData = Mesh->GetResourceForRendering()->LODRenderData;
					CullingSectionIndices.Empty();

					int32 LODIndex = FMath::Min(ForcedLodLevel, Mesh->GetLODNum() - 1);
					// for (int LODIndex = 0; LODIndex < SkeletalMeshComponent->GetNumLODs(); LODIndex++)
					{
						const auto& PositionVertexBuffer = LODRenderData[LODIndex].StaticVertexBuffers.PositionVertexBuffer;
						for (int32 SectionIndex = 0; SectionIndex < LODRenderData[LODIndex].RenderSections.Num(); SectionIndex++)
						{
							const auto& Section = LODRenderData[LODIndex].RenderSections[SectionIndex];

							TArray<FVector> Points;
							Points.Reserve(Section.NumVertices);
							for (uint32 i = 0; i < Section.NumVertices; i++)
							{
								Points.Add(
									TArray<UE::Math::TVector<double>>::ElementType(PositionVertexBuffer.VertexPosition(Section.BaseVertexIndex + i))
								);
							}

							FBox SectionBox(Points.GetData(), Points.Num());
							if (SectionBox.GetVolume() < CullingVolumeThreshold)
							{
								CullingSectionIndices.Add(SectionIndex);
							}
						}
					}


					for (const auto& SectionIndex : CullingSectionIndices)
					{
						SkeletalMeshComponent->ShowMaterialSection(
							SectionIndex,
							SectionIndex,
							bShow,
							LODIndex);
					}
				}
			}
		}
	}
}

void UWardrobeComponent::SetSectionHiddenLevel(int32 SectionLevel)
{
	if (SectionLevel == 0)
	{
		ShowAllSections();
		return;
	}

	static TArray<FString> SectionLevelSuffix = { TEXT("_C0"), TEXT("_C1"), TEXT("_C2") };
	SectionLevel = FMath::Min(SectionLevel, SectionLevelSuffix.Num() - 1);

	for (const auto& GarmentComponent : CharacterGarmentComponents)
	{
		for (const auto& SkeletalMeshComponent : GarmentComponent->FilterMeshComponents<USkinnedMeshComponent>())
		{
			TArray<int32> CullingMaterialIndices;

			auto SlotNames = SkeletalMeshComponent->GetMaterialSlotNames();
			for (int Index = 0; Index < SlotNames.Num(); ++Index)
			{
				for (int i = SectionLevel; i >= 1; --i)
				{
					if (SlotNames[Index].ToString().Contains(SectionLevelSuffix[i]))
					{
						CullingMaterialIndices.Add(Index);
						break;
					}
				}
			}

			for (int32 LodIndex = 0; LodIndex < SkeletalMeshComponent->GetSkinnedAsset()->GetLODNum(); LodIndex++)
			{
				for (const auto& MaterialIndex : CullingMaterialIndices)
				{
					SkeletalMeshComponent->ShowMaterialSection(
						MaterialIndex,
						INDEX_NONE,
						false,
						LodIndex);
				}
			}
		}
	}
}

void UWardrobeComponent::ShowAllSections()
{
	for (const auto& GarmentComponent : CharacterGarmentComponents)
	{
		for (const auto& MeshComponent : GarmentComponent->GetMeshComponents())
		{
			if (auto SkeletalMeshComponent = Cast<USkinnedMeshComponent>(MeshComponent))
			{
				if (auto Mesh = SkeletalMeshComponent->GetSkinnedAsset())
				{
					for (int32 LodIndex = 0; LodIndex < Mesh->GetLODNum(); LodIndex++)
					{
						SkeletalMeshComponent->ShowAllMaterialSections(LodIndex);
					}
				}
			}
		}
	}
}

UDissolveVFXComponent* UWardrobeComponent::GetDissolveComponent()
{
	return DissolveComponent;
}

UUGCAvatarProfile* UWardrobeComponent::GetAvatarProfile()
{
	return AvatarProfile;
}

void UWardrobeComponent::UpdateBodyCullingMask()
{
	if (BodyMeshComponent)
	{
		for (auto Index = 0; Index < BodyMeshComponent->GetNumMaterials(); ++Index)
		{
			auto MID = BodyMeshComponent->CreateAndSetMaterialInstanceDynamic(Index);
			auto CullingMask = GetCullingMask(0);
			FUGCBaseLibrary::SetMaterialTextureParameterValue(MID, TEXT("Culling Mask_S"), CullingMask);
		}
	}
}

#if WITH_EDITOR
void UWardrobeComponent::SetGarmentVisibility(bool bVisible)
{
	bGarmentVisible = bVisible;

	TArray<USceneComponent*> ChildrenComponents;
	if (BodyMeshComponent)
	{
		BodyMeshComponent->GetChildrenComponents(true, ChildrenComponents);
	}

	for (auto& Component : ChildrenComponents)
	{
		if (Component != FaceMeshComponent)
		{
			Component->SetVisibility(bGarmentVisible);
		}
	}
}

void UWardrobeComponent::ToggleGarmentVisibility()
{
	SetGarmentVisibility(!bGarmentVisible);
}

bool UWardrobeComponent::IsGarmentVisible() const
{
	return bGarmentVisible;
}
#endif

UGarment* UWardrobeComponent::GetWornXieZiGarment()
{
	for (const auto& Garment : GetAllGarments())
	{
		if (EnumHasAnyFlags((EGarmentPatchType)Garment->GarmentPatchFlags, EGarmentPatchType::GPT_XieZi))
		{
			return Garment;
		}
	}
	return nullptr;
}

void UWardrobeComponent::CacheHeelsData()
{
	TRACE_CPUPROFILER_EVENT_SCOPE(CacheHeelsData)
	const auto& XieZiGarment = GetWornXieZiGarment();
	UGarmentPatch_XieZi* Patch_XieZi = nullptr;
	bool bShouldResetHeels = true;
	if (XieZiGarment)
	{
		Patch_XieZi = XieZiGarment->PatchXieZi;
		bShouldResetHeels = false;
	}

	HeelsHeightQueue.Add(GFrameCounter, FWardrobeHeelsData(bShouldResetHeels, Patch_XieZi));
}

void UWardrobeComponent::DispatchHeelsData()
{
	TRACE_CPUPROFILER_EVENT_SCOPE(DispatchHeelsData)
	bool bHasDispatched = false;
	for (const auto& [FrameCounter, HeelsData] : HeelsHeightQueue)
	{
		TRACE_CPUPROFILER_EVENT_SCOPE(DispatchHeelsData_ForLoop)
		if (FrameCounter < GFrameCounter)
		{
			TRACE_CPUPROFILER_EVENT_SCOPE(DispatchHeelsData_FrameCounter<GFrameCounter)
			if (OnUpdateXieZiIkPointsDelegate.IsBound())
			{
				TRACE_CPUPROFILER_EVENT_SCOPE(DispatchHeelsData_OnUpdateXieZiIkPointsDelegate)
				OnUpdateXieZiIkPointsDelegate.Broadcast(HeelsData.bShouldReset, HeelsData.PatchXieZi);
				bHasDispatched = true;
			}

			if (OnUpdateHeelIKPointsDelegate.IsBound())
			{
				TRACE_CPUPROFILER_EVENT_SCOPE(DispatchHeelsData_OnUpdateHeelIKPointsDelegate)
				OnUpdateHeelIKPointsDelegate.Broadcast(HeelsData);
				bHasDispatched = true;
			}
		}
	}
	if (bHasDispatched)
	{
		TRACE_CPUPROFILER_EVENT_SCOPE(DispatchHeelsData_HeelsHeightQueue_Empty())
		HeelsHeightQueue.Empty();
	}
}

UTexture* UWardrobeComponent::GetCullingMask(int CullingLayerIndex)
{
	if (CullingLayerIndex < 0)
	{
		return nullptr;
	}

	if (CullingMasks.IsValidIndex(CullingLayerIndex) && CullingMasks[CullingLayerIndex])
	{
		return CullingMasks[CullingLayerIndex];
	}
	else
	{
		CreateCullingMasks(CullingLayerIndex + 1);
		return CullingMasks[CullingLayerIndex];
	}
}

void UWardrobeComponent::CreateCullingMasks(int CullingLayerNumber)
{
	for (auto i = 0; i < CullingMasks.Num(); ++i)
	{
		if (!CullingMasks[i])
		{
			UTextureRenderTarget2D* CullingMask = NewObject<UTextureRenderTarget2D>(GetOwner());
			CullingMask->RenderTargetFormat = ETextureRenderTargetFormat::RTF_R8;
			CullingMask->ClearColor = FLinearColor::White;
			//CullingMask->bAutoGenerateMips = true;
			//CullingMask->bCanCreateUAV = false;
			CullingMask->InitAutoFormat(CullingMaskSize, CullingMaskSize);
			CullingMask->UpdateResourceImmediate(true);

			// FTexture2DDynamicCreateInfo CreateInfo;
			// CreateInfo.Filter = TF_Nearest;
			// CreateInfo.SamplerAddressMode = AM_Clamp;
			// CreateInfo.Format = PF_R8;
			// CreateInfo.bSRGB = false;
			// UTexture2DDynamic* CullingMask = UTexture2DDynamic::Create(CullingMaskSize, CullingMaskSize, CreateInfo);
			// UWardrobeLibrary::MergeMaskTexture({}, CullingMask);
			CullingMasks[i] = CullingMask;
		}
	}

	for (auto i = CullingMasks.Num(); i < CullingLayerNumber; ++i)
	{
		UTextureRenderTarget2D* CullingMask = NewObject<UTextureRenderTarget2D>(GetOwner());
		CullingMask->RenderTargetFormat = ETextureRenderTargetFormat::RTF_R8;
		CullingMask->ClearColor = FLinearColor::White;
		//CullingMask->bAutoGenerateMips = true;
		//CullingMask->bCanCreateUAV = false;
		CullingMask->InitAutoFormat(CullingMaskSize, CullingMaskSize);
		CullingMask->UpdateResourceImmediate(true);

		// FTexture2DDynamicCreateInfo CreateInfo;
		// CreateInfo.Filter = TF_Nearest;
		// CreateInfo.SamplerAddressMode = AM_Clamp;
		// CreateInfo.Format = PF_R8;
		// CreateInfo.bSRGB = false;
		// UTexture2DDynamic* CullingMask = UTexture2DDynamic::Create(CullingMaskSize, CullingMaskSize, CreateInfo);
		// UWardrobeLibrary::MergeMaskTexture({}, CullingMask);
		CullingMasks.Add(CullingMask);
	}
}

void UWardrobeComponent::RefreshCullingMask(int CullingLayerIndex, bool bRecursive)
{
	if (CullingLayerIndex < 0)
	{
		return;
	}

	if (CullingMasks.IsValidIndex(CullingLayerIndex) && CullingMasks[CullingLayerIndex])
	{
		// CullingMasks[CullingLayerIndex]->UpdateResource();
		// UWardrobeLibrary::MergeMaskTexture({}, CullingMasks[CullingLayerIndex]);
		CullingMasks[CullingLayerIndex]->UpdateResourceImmediate(true);
	}
	else
	{
		CreateCullingMasks(CullingLayerIndex + 1);
		// CullingMasks[CullingLayerIndex]->UpdateResourceImmediate(true);
	}

	if (bRecursive)
	{
		for (int i = CullingLayerIndex - 1; i >= 0; --i)
		{
			// CullingMasks[i]->UpdateResourceImmediate(true);
			// UWardrobeLibrary::MergeMaskTexture({}, CullingMasks[i]);
			CullingMasks[i]->UpdateResourceImmediate(true);
		}
	}
}

void UWardrobeComponent::PrePutOnGarment()
{
	TRACE_CPUPROFILER_EVENT_SCOPE(PrePutOnGarment)
	if (bEnableDissolveEffect && DissolveComponent)
	{
		DissolveComponent->PrepareForNextPlay(AvatarProfile->DissolveVfx);
	}

	PutOnGarmentComponents.Empty();
	BannedCharacterGarmentComponents.Empty();
}

bool UWardrobeComponent::PutOnGarmentAssetInternal(UGarment* GarmentToBePutOn, UUGCUserData* InUGCData)
{
	TRACE_CPUPROFILER_EVENT_SCOPE(PutOnGarmentAssetInternal)
	if (!UWardrobeLibrary::IsGarmentValid(GarmentToBePutOn))
	{
		UE_LOG(LogGarment, Error, TEXT("[UWardrobeComponent::PutOnGarmentAssetInternal]GarmentToBePutOn Is InValid!!"))
		return false;
	}

	for (const auto& CharacterGarment : CharacterGarmentComponents)
	{
		if (CharacterGarment->GetGarmentAsset() == GarmentToBePutOn)
		{
			UE_LOG(LogGarment, Log, TEXT("[UWardrobeComponent::PutOnGarmentAssetInternal]Garment %s Already Has Been Worn"),
				*GarmentToBePutOn->GarmentId);
			return false;
		}
	}

	// 0.1 Try to Put On New Garment 
	auto GarmentComponent = BeginPutOnCharacterGarment(GarmentToBePutOn);
	if (!GarmentComponent)
	{
		return false;
	}
	PutOnGarmentComponents.AddUnique(GarmentComponent);

	// 0.2 BanChecking
	UWardrobeLibrary::BanChecking(
		BannedCharacterGarmentComponents,
		CharacterGarmentComponents,
		GarmentToBePutOn,
		false);

	// 1.1 New Garment Settings 
	CharacterGarmentComponents.AddUnique(GarmentComponent);
	if (GarmentToBePutOn->GarmentBodyPartType == EGarmentBodyPartType::GBPT_TouFa)
	{
		GarmentComponent->SetTranslucencySortDistanceOffset(-30);
		GarmentComponent->SetPose(BodyPoseCopyAnimClass.LoadSynchronous(), bEnableLeaderPoseOptimization, FaceMeshComponent);
	}
	else
	{
		GarmentComponent->SetPose(BodyPoseCopyAnimClass.LoadSynchronous(), bEnableLeaderPoseOptimization, BodyMeshComponent);
		// GarmentComponent->SetTranslucencySortDistanceOffset(10);
	}
	GarmentComponent->SetPrimitiveDataFloatSetting(CustomPrimitiveDataFloatSetting);
	GarmentComponent->SetForcedLodLevel(GetForcedLodLevel());
	GarmentComponent->SetForceMipStreaming(bForceMipStreaming);
	GarmentComponent->SetLightingChannels(LightingChannels);
	GarmentComponent->SetCastShadow(CastShadow);
	GarmentComponent->SetCastInsetShadow(bCastInsetShadow);
	GarmentComponent->SetCastCapsuleDirectShadow(bCastCapsuleDirectShadow);
	GarmentComponent->SetRenderCustomDepth(bRenderCustomDepth);
	GarmentComponent->SetCustomDepthStencilValue(CustomDepthStencilValue);
	GarmentComponent->SetReceivesDecals(bReceivesDecals);
	GarmentComponent->SetEnableUpdateRateOptimizations(bEnableUpdateRateOptimizations);
	GarmentComponent->SetVisibilityBasedAnimTickOption(VisibilityBasedAnimTickOption);

#if WITH_EDITOR
	GarmentComponent->SetGarmentVisibility(bGarmentVisible);
#endif

	// 1.2 Make MID for CuffTightening, DissolveMaterials
	GarmentComponent->CreateMeshComponentMaterialsInstanceDynamic();
	// Todo: Remove if after updating all assets with using culling mask.
	if (GarmentToBePutOn->CullingMask && GarmentToBePutOn->GarmentBodyPartType != EGarmentBodyPartType::GBPT_TouFa)
	{
		// GarmentToBePutOn->CullingMask->NeverStream = true;
		// GarmentToBePutOn->CullingMask->UpdateResource();
		GarmentComponent->SetCullingMask(GetCullingMask(GarmentComponent->GetCullingLayerIndex() + 1));
	}

	// 1.3 Set UGC
	if (GarmentComponent->PrepareUGC())
	{
		if (LoadUGCUserData.IsBound())
		{
			GarmentComponent->SetLoadUGCUserData(LoadUGCUserData);
		}
		if (SaveUGCUserData.IsBound())
		{
			GarmentComponent->SetSaveUGCUserData(SaveUGCUserData);
		}
		GarmentComponent->ApplyUGC();

		if (InUGCData)
		{
			GarmentComponent->SetUGCUserData(InUGCData);
		}
	}

	if (bEnableDissolveEffect && DissolveComponent && DissolveComponent->CanDissolve())
	{
		DissolveComponent->AddAppearMeshComponents(GarmentComponent->GetMeshComponents());
	}

	return true;
}

void UWardrobeComponent::PostPutOnGarment()
{
	TRACE_CPUPROFILER_EVENT_SCOPE(PostPutOnGarment)
	if (PutOnGarmentComponents.IsEmpty())
	{
		return;
	}

	for (auto Iter = PutOnGarmentComponents.CreateIterator(); Iter; ++Iter)
	{
		auto GarmentComponent = *Iter;
		if (BannedCharacterGarmentComponents.Contains(GarmentComponent))
		{
			CharacterGarmentComponents.Remove(GarmentComponent);
			Iter.RemoveCurrent();
		}
	}

	if (EcoJointDynamicsComponent)
	{
		for (auto GarmentComponentToBePutOn : PutOnGarmentComponents)
		{
			const auto PartName = UEnum::GetValueAsName(GarmentComponentToBePutOn->GetGarmentAsset()->GarmentBodyPartType);
			EcoJointDynamicsComponent->OnAvatarPartLoaded(PartName);
			UE_LOG(LogGarment, Log, TEXT("[UWardrobeComponent::PutOnGarmentAsset] EcoJointDynamicsComponent->OnAvatarPartLoaded()"))
		}
	}

	if (bEnableDissolveEffect && DissolveComponent && DissolveComponent->CanDissolve())
	{
		if (!BannedCharacterGarmentComponents.IsEmpty())
		{
			BannedGarmentsId.Empty();
			BannedBodyPartType.Empty();
			for (const auto& BannedCharacterGarment : BannedCharacterGarmentComponents)
			{
				DissolveComponent->AddDisappearMeshComponents(BannedCharacterGarment->GetMeshComponents());
				auto Garment = BannedCharacterGarment->GetGarmentAsset();
				BannedGarmentsId.AddUnique(Garment->GarmentId);
				BannedBodyPartType.AddUnique(Garment->GarmentBodyPartType);
			}
		}

		DissolveComponent->PlayDissolve();
		// // 2.1 PutOnDissolveNiagara
		// UWardrobeLibrary::PutOnDissolveNiagara(CharacterGarmentToBePutOn, DissolveNiagara);
		// if(!BannedCharacterGarments.IsEmpty())
		// {
		// 	UWardrobeLibrary::PutOnDissolveNiagara(BannedCharacterGarments, DissolveNiagara);
		// }
		// // 2.2 Enable Material Clip of New Garment And Banned Garment
		// // 
		// // Cause The Initial DissolveValue == 0.0,
		// // And GarmentToBePutOn Should Not Visible,
		// // So GarmentToBePutOn Should EnableRevertDissolve.
		// // DissolveValue: 0.0--->1.0
		// // Not Revert Garment Visible: 1.0--->0.0
		// // Revert Garment Visible: 0.0--->1.0
		// //
		// // eg.
		// // XieZiToBePutOn Cuff SdfA: 0--->1
		// // XieZiBanned Cuff SdfB: 1--->0
		// // KuZi Sdf: Max(sdfA, sdfB)
		//
		// if(!BannedCharacterGarments.IsEmpty())
		// {
		// 	for(const auto& BannedCharacterGarment: BannedCharacterGarments)
		// 	{
		// 		UWardrobeLibrary::ToggleMaterialsDissolve(BannedCharacterGarment, true, false);
		// 	}
		// }
		// UWardrobeLibrary::ToggleMaterialsDissolve(CharacterGarmentToBePutOn, true,true);
		//
		// // 2.3 Do Dissolve ZonesCulling
		// UWardrobeLibrary::DoDissolveZonesCulling(
		// 	CharacterGarments,
		// 	BannedCharacterGarments,
		// 	CharacterGarmentToBePutOn,
		// 	BodyMeshComponent);
		//
		// // 2.4 Do Dissolve CuffingTightening
		// UWardrobeLibrary::DoTighten(CharacterGarments);
		//
		// // 2.5 PlayDissolve By Timeline
		// PlayGarmentDissolveVfx();
	}
	else
	{
		EndOfPutOnCharacterGarment();

		// int Size = CullingMaskSize * CullingMaskSize;
		// TArray<uint8> Pixels;
		// Pixels.Init(255, Size);
		//
		// for (int i = CullingMasks.Num() - 1; i >= 0; i--)
		// {
		// 	TArray<UTexture2D*> Masks;
		// 	for (UGarmentComponent* GarmentComponent : CharacterGarmentComponents)
		// 	{
		// 		auto Garment = GarmentComponent->GetGarmentAsset();
		// 		if (Garment->CullingMask && GarmentComponent->GetCullingLayerIndex() == i)
		// 		{
		// 			Masks.Add(Garment->CullingMask);
		// 		}
		// 	}
		// 	UWardrobeLibrary::MergeMaskTexture(Pixels, CullingMaskSize, CullingMaskSize, Masks, GetCullingMask(i));
		// }
		DoGarmentCulling(false);
		CacheGarmentCullingWork();

		// if (MergeMaskMaterial)
		// {
		// 	if (BannedCharacterGarmentComponents.IsEmpty())
		// 	{
		// 		int UpdateMaxIndex = -1;
		// 		for (UGarmentComponent* GarmentComponent : PutOnGarmentComponents)
		// 		{
		// 			auto Garment = GarmentComponent->GetGarmentAsset();
		// 			if (auto CullingMask = Garment->CullingMask)
		// 			{
		// 				CullingMask->UpdateResource();
		// 				int CullingLayerIndex = GarmentComponent->GetCullingLayerIndex();
		// 				UpdateMaxIndex = FMath::Max(UpdateMaxIndex, CullingLayerIndex);
		// 				auto LayerMask = GetCullingMask(CullingLayerIndex);
		// 				// FUGCBaseLibrary::SetMaterialTextureParameterValue(MergeMaskMaterial, TEXT("Mask0"), LayerMask);
		// 				// FUGCBaseLibrary::SetMaterialTextureParameterValue(MergeMaskMaterial, TEXT("Mask1"), CullingMask);
		// 				// UKismetRenderingLibrary::DrawMaterialToRenderTarget(this, LayerMask, MergeMaskMaterial);
		// 			}
		// 		}
		// 		for (int i = UpdateMaxIndex; i > 0; --i)
		// 		{
		// 			auto OuterLayerMask = GetCullingMask(i);
		// 			auto InnerLayerMask = GetCullingMask(i - 1);
		// 			FUGCBaseLibrary::SetMaterialTextureParameterValue(MergeMaskMaterial, TEXT("Mask0"), InnerLayerMask);
		// 			FUGCBaseLibrary::SetMaterialTextureParameterValue(MergeMaskMaterial, TEXT("Mask1"), OuterLayerMask);
		// 			UKismetRenderingLibrary::DrawMaterialToRenderTarget(this, InnerLayerMask, MergeMaskMaterial);
		// 		}
		// 		EndOfPutOnCharacterGarment();
		// 	}
		// 	else
		// 	{
		// 		int RefreshMaxIndex = -1;
		// 		for (UGarmentComponent* GarmentComponent : BannedCharacterGarmentComponents)
		// 		{
		// 			RefreshMaxIndex = FMath::Max(RefreshMaxIndex, GarmentComponent->GetCullingLayerIndex());
		// 		}
		// 		RefreshCullingMask(RefreshMaxIndex);
		// 		EndOfPutOnCharacterGarment();
		// 		for (UGarmentComponent* GarmentComponent : CharacterGarmentComponents)
		// 		{
		// 			if (PutOnGarmentComponents.Contains(GarmentComponent))
		// 				continue;
		//
		// 			auto Garment = GarmentComponent->GetGarmentAsset();
		// 			if (auto CullingMask = Garment->CullingMask)
		// 			{
		// 				CullingMask->UpdateResource();
		// 				int CullingLayerIndex = GarmentComponent->GetCullingLayerIndex();
		// 				if (CullingLayerIndex <= RefreshMaxIndex)
		// 				{
		// 					auto LayerMask = GetCullingMask(CullingLayerIndex);
		// 					FUGCBaseLibrary::SetMaterialTextureParameterValue(MergeMaskMaterial, TEXT("Mask0"), LayerMask);
		// 					FUGCBaseLibrary::SetMaterialTextureParameterValue(MergeMaskMaterial, TEXT("Mask1"), CullingMask);
		// 					UKismetRenderingLibrary::DrawMaterialToRenderTarget(this, LayerMask, MergeMaskMaterial);
		// 				}
		// 			}
		// 		}
		// 		int UpdateMaxIndex = -1;
		// 		for (UGarmentComponent* GarmentComponent : PutOnGarmentComponents)
		// 		{
		// 			auto Garment = GarmentComponent->GetGarmentAsset();
		// 			if (auto CullingMask = Garment->CullingMask)
		// 			{
		// 				CullingMask->UpdateResource();
		// 				int CullingLayerIndex = GarmentComponent->GetCullingLayerIndex();
		// 				UpdateMaxIndex = FMath::Max(UpdateMaxIndex, CullingLayerIndex);
		// 				auto LayerMask = GetCullingMask(CullingLayerIndex);
		// 				FUGCBaseLibrary::SetMaterialTextureParameterValue(MergeMaskMaterial, TEXT("Mask0"), LayerMask);
		// 				FUGCBaseLibrary::SetMaterialTextureParameterValue(MergeMaskMaterial, TEXT("Mask1"), CullingMask);
		// 				UKismetRenderingLibrary::DrawMaterialToRenderTarget(this, LayerMask, MergeMaskMaterial);
		// 			}
		// 		}
		// 		UpdateMaxIndex = FMath::Max(UpdateMaxIndex, RefreshMaxIndex + 1);
		// 		for (int i = UpdateMaxIndex; i > 0; --i)
		// 		{
		// 			auto OuterLayerMask = GetCullingMask(i);
		// 			auto InnerLayerMask = GetCullingMask(i - 1);
		// 			FUGCBaseLibrary::SetMaterialTextureParameterValue(MergeMaskMaterial, TEXT("Mask0"), InnerLayerMask);
		// 			FUGCBaseLibrary::SetMaterialTextureParameterValue(MergeMaskMaterial, TEXT("Mask1"), OuterLayerMask);
		// 			UKismetRenderingLibrary::DrawMaterialToRenderTarget(this, InnerLayerMask, MergeMaskMaterial);
		// 		}
		// 	}
		// }
	}

	// Broadcast Delegate of Put On
	if (OnPostGarmentPutOnDelegate.IsBound())
	{
		OnPostGarmentPutOnDelegate.Broadcast();
	}
}

void UWardrobeComponent::RemoveGarmentAssetInternal(TArray<UGarmentComponent*> GarmentsToBeRemoved)
{
	if (GarmentsToBeRemoved.IsEmpty())
	{
		return;
	}

	for (const auto& CharacterGarmentToBeRemoved : GarmentsToBeRemoved)
	{
		if (EnumHasAnyFlags((EGarmentPatchType)CharacterGarmentToBeRemoved->GetGarmentAsset()->GarmentPatchFlags, EGarmentPatchType::GPT_XieZi))
		{
			if (OnUpdateXieZiIkPointsDelegate.IsBound())
			{
				OnUpdateXieZiIkPointsDelegate.Broadcast(true, nullptr);
			}

			if (OnUpdateHeelIKPointsDelegate.IsBound())
			{
				OnUpdateHeelIKPointsDelegate.Broadcast(FWardrobeHeelsData(true, nullptr));
			}
		}
	}

	// int RefreshMaxIndex = -1;
	// for (UGarmentComponent* GarmentComponent : GarmentsToBeRemoved)
	// {
	// 	RefreshMaxIndex = FMath::Max(RefreshMaxIndex, GarmentComponent->GetCullingLayerIndex());
	// }
	// RefreshCullingMask(RefreshMaxIndex);

	UWardrobeLibrary::RemoveCharacterGarments(GarmentsToBeRemoved, CharacterGarmentComponents, EcoJointDynamicsComponent);

	// TArray<UTexture2D*> Masks;
	// for (int i = CullingMasks.Num() - 1; i >= 0; i--)
	// {
	// 	for (UGarmentComponent*	GarmentComponent : CharacterGarmentComponents)
	// 	{
	// 		auto Garment = GarmentComponent->GetGarmentAsset();
	// 		if (Garment->CullingMask && GarmentComponent->GetCullingLayerIndex() == i)
	// 		{
	// 			Masks.Add(Garment->CullingMask);
	// 		}
	// 	}
	// 	UWardrobeLibrary::MergeMaskTexture(Masks, GetCullingMask(i));
	// }

	// int Size = CullingMaskSize * CullingMaskSize;
	// TArray<uint8> Pixels;
	// Pixels.Init(255, Size);
	//
	// for (int i = CullingMasks.Num() - 1; i >= 0; i--)
	// {
	// 	TArray<UTexture2D*> Masks;
	// 	for (UGarmentComponent* GarmentComponent : CharacterGarmentComponents)
	// 	{
	// 		auto Garment = GarmentComponent->GetGarmentAsset();
	// 		if (Garment->CullingMask && GarmentComponent->GetCullingLayerIndex() == i)
	// 		{
	// 			Masks.Add(Garment->CullingMask);
	// 		}
	// 	}
	// 	UWardrobeLibrary::MergeMaskTexture(Pixels, CullingMaskSize, CullingMaskSize, Masks, GetCullingMask(i));
	// }
	DoGarmentCulling(false);
	CacheGarmentCullingWork();
	// if (MergeMaskMaterial)
	// {
	// 	for (UGarmentComponent* GarmentComponent : CharacterGarmentComponents)
	// 	{
	// 		auto Garment = GarmentComponent->GetGarmentAsset();
	// 		if (auto CullingMask = Garment->CullingMask)
	// 		{
	// 			CullingMask->UpdateResource();
	// 			int CullingLayerIndex = GarmentComponent->GetCullingLayerIndex();
	// 			if (CullingLayerIndex <= RefreshMaxIndex)
	// 			{
	// 				auto LayerMask = GetCullingMask(CullingLayerIndex);
	// 				FUGCBaseLibrary::SetMaterialTextureParameterValue(MergeMaskMaterial, TEXT("Mask0"), LayerMask);
	// 				FUGCBaseLibrary::SetMaterialTextureParameterValue(MergeMaskMaterial, TEXT("Mask1"), CullingMask);
	// 				UKismetRenderingLibrary::DrawMaterialToRenderTarget(this, LayerMask, MergeMaskMaterial);
	// 			}
	// 		}
	// 	}
	//
	// 	int UpdateMaxIndex = RefreshMaxIndex + 1;
	// 	for (int i = UpdateMaxIndex; i > 0; --i)
	// 	{
	// 		auto OuterLayerMask = GetCullingMask(i);
	// 		auto InnerLayerMask = GetCullingMask(i - 1);
	// 		FUGCBaseLibrary::SetMaterialTextureParameterValue(MergeMaskMaterial, TEXT("Mask0"), InnerLayerMask);
	// 		FUGCBaseLibrary::SetMaterialTextureParameterValue(MergeMaskMaterial, TEXT("Mask1"), OuterLayerMask);
	// 		UKismetRenderingLibrary::DrawMaterialToRenderTarget(this, InnerLayerMask, MergeMaskMaterial);
	// 	}
	// }

	UWardrobeLibrary::DoTighten(CharacterGarmentComponents);

	// Broadcast Delegate of Remove
	if (OnPostGarmentRemoveDelegate.IsBound())
	{
		OnPostGarmentRemoveDelegate.Broadcast();
	}
}

UGarmentComponent* UWardrobeComponent::BeginPutOnCharacterGarment(UGarment* GarmentToBePutOn)
{
	UGarmentComponent* GarmentComponent = nullptr;

	if (GarmentToBePutOn->GarmentBodyPartType == EGarmentBodyPartType::GBPT_TouFa)
	{
		if (!FaceMeshComponent)
		{
			UE_LOG(LogGarment, Warning, TEXT("[UWardrobeComponent::PutOnGarment]Can't find FaceMeshComponent from FaceMeshName!"))
			return nullptr;
		}
		GarmentComponent = NewObject<UGarmentComponent>(GetOwner());
		GarmentComponent->AttachToComponent(FaceMeshComponent, FAttachmentTransformRules::KeepRelativeTransform);
		GarmentComponent->SetGarmentAsset(GarmentToBePutOn, FaceMeshComponent);
	}
	else
	{
		if (!BodyMeshComponent)
		{
			UE_LOG(LogGarment, Warning, TEXT("[UWardrobeComponent::PutOnGarment]Can't find BodyMeshComponent from BodyMeshName!"))
			return nullptr;
		}
		GarmentComponent = NewObject<UGarmentComponent>(GetOwner());
		GarmentComponent->AttachToComponent(BodyMeshComponent, FAttachmentTransformRules::KeepRelativeTransform);
		GarmentComponent->SetGarmentAsset(GarmentToBePutOn, BodyMeshComponent);
	}

	GarmentComponent->RegisterComponent();

	return GarmentComponent;
}

void UWardrobeComponent::EndOfPutOnCharacterGarment()
{
	TRACE_CPUPROFILER_EVENT_SCOPE(EndOfPutOnCharacterGarment)
	if (!BannedCharacterGarmentComponents.IsEmpty())
	{
		BannedGarmentsId.Empty();
		BannedBodyPartType.Empty();
		// 4.1 RemoveBannedCharacterGarment
		for (const auto& BannedCharacterGarment : BannedCharacterGarmentComponents)
		{
			auto Garment = BannedCharacterGarment->GetGarmentAsset();
			BannedGarmentsId.AddUnique(Garment->GarmentId);
			BannedBodyPartType.AddUnique(Garment->GarmentBodyPartType);
		}
		UWardrobeLibrary::RemoveCharacterGarments(
			BannedCharacterGarmentComponents,
			CharacterGarmentComponents,
			EcoJointDynamicsComponent);
	}

	// 4.2 Do Cuff Tightening
	UWardrobeLibrary::DoTighten(CharacterGarmentComponents);

	// 4.3 Cache HeelsData in Queue, Dispatch At Next Frame in Tick()
	CacheHeelsData();
	// DispatchHeelsData();

	BannedCharacterGarmentComponents.Empty();
	//UWardrobeLibrary::ResetCharacterGarmentsMidParameters(CharacterGarmentComponents);
}

void UWardrobeComponent::CacheGarmentCullingWork()
{
	if (CacheGarmentCullingFrameCounter.IsEmpty())
	{
		CacheGarmentCullingFrameCounter.Add(GFrameCounter);
	}
	else if (CacheGarmentCullingFrameCounter[0] < GFrameCounter)
	{
		CacheGarmentCullingFrameCounter[0] = GFrameCounter;
	}
}

void UWardrobeComponent::DoGarmentCulling(bool bCheckFrame)
{
	if (!bCheckFrame
		|| (!CacheGarmentCullingFrameCounter.IsEmpty() && (CacheGarmentCullingFrameCounter[0] + UWardrobeSettings::Get()->MergeCullingMaskDelayFrameCounter) < GFrameCounter))
	{
		TRACE_CPUPROFILER_EVENT_SCOPE(DoGarmentCulling)
		int Size = CullingMaskSize * CullingMaskSize;
		TArray<uint8> Pixels;
		Pixels.Init(255, Size);

		for (int i = CullingMasks.Num() - 1; i >= 0; i--)
		{
			TArray<UTexture2D*> Masks;
			for (UGarmentComponent* GarmentComponent : CharacterGarmentComponents)
			{
				auto Garment = GarmentComponent->GetGarmentAsset();
				if (Garment->CullingMask && GarmentComponent->GetCullingLayerIndex() == i)
				{
					Masks.Add(Garment->CullingMask);
				}
			}
			UWardrobeLibrary::MergeMaskTexture(Pixels, CullingMaskSize, CullingMaskSize, Masks, GetCullingMask(i));
		}
		CacheGarmentCullingFrameCounter.Empty();
	}
}

#undef LOCTEXT_NAMESPACE
