#pragma once
#include "CoreMinimal.h"
#include "SAssetEditorViewport.h"
#include "SCommonEditorViewportToolbarBase.h"
#include "Asset/Garment.h"
#include "Model/GarmentEditorCommunicator.h"

class GARMENTEDITOR_API SGarmentEditorViewport : public SEditorViewport, public ICommonEditorViewportToolbarInfoProvider
{
public:
	SLATE_BEGIN_ARGS(SGarmentEditorViewport)
		{
		}

		// SLATE_ARGUMENT(UGarment*, PreviewGarment)
		SLATE_ARGUMENT(TSharedPtr<FGarmentEditorCommunicator>, Communicator)

	SLATE_END_ARGS()

	void Construct(const FArguments& InArgs);
	~SGarmentEditorViewport() override;

private:
	void OnPreviewCharacterPutOnGarmentEvent(UGarment* GarmentToBePutOn);

public:
	// ICommonEditorViewportToolbarInfoProvider Interface
	// Get the viewport widget
	virtual TSharedRef<class SEditorViewport> GetViewportWidget() override;
	virtual TSharedPtr<FExtender> GetExtenders() const override;
	// Called to inform the host that a button was clicked (typically used to focus on a particular viewport in a multi-viewport setup)
	virtual void OnFloatingButtonClicked() override;
	// ICommonEditorViewportToolbarInfoProvider Interface End;

	FReply OnToggleGarmentVisibilityClicked() const;
	bool GarmentVisibility() const;

	FReply OnToggleSkinMaskVisibilityClicked();
	bool SkinMaskVisibility() const;

	int32 GetLODModelCount() const;
	
	int32 GetLodSelection() const;
	void OnSetLodModel(int32 LODSelectionType);
	bool IsLodModelSelected(int32 LODSelectionType) const;
	int GetSectionHiddenLevel() const;
	void OnSetSectionHiddenLevel(int InSectionHiddenLevel);
	bool IsSectionHiddenLevelSelected(int InSectionHiddenLevel) const;

	void SpawnPreviewCharacterActor();

	void RefreshBanMaskInfo(const FBanMask& BanMask);

protected:
	/** SEditorViewport interface */
	// Implement this to add a viewport toolbar to the inside top of the viewport
	virtual TSharedPtr<SWidget> MakeViewportToolbar() override;
	virtual void OnFocusViewportToSelection() override;
	virtual TSharedRef<class FEditorViewportClient> MakeEditorViewportClient() override;
	virtual void PopulateViewportOverlays(TSharedRef<SOverlay> Overlay) override;

	FText GetDisplayString() const;

	virtual void BindCommands() override;

private:
	TSharedPtr<class FGarmentEditorPreviewScene> PreviewScene;
	TSharedPtr<class FGarmentEditorViewportClient> EditorViewportClient;
	TSharedPtr<FExtender> Extender;

	TSharedPtr<FGarmentEditorCommunicator> Communicator;

	TObjectPtr<AActor> PreviewCharacter;
	int SectionHiddenLevel = 0;
	bool bSkinMaskVisible = false;

	FDelegateHandle PreviewFeatureLevelChangedHandle;
	
	TSharedPtr<SVerticalBox> BanMaskInfoWidget;

	FBox FocusedBox;
};
