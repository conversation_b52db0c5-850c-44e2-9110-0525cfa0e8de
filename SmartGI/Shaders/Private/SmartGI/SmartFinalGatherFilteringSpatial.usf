#include "/Engine/Private/Common.ush"
#include "DeferredShadingCommonMobile.ush"
#include "/Engine/Private/BRDF.ush"
#include "/Engine/Private/SHCommon.ush"

#define OCTAHEDRAL_COMMON 1
#include "SmartCommon.ush"

#include "SmartFinalGatherCommon.ush"

#ifndef THREADGROUP_SIZE
#define THREADGROUP_SIZE 1
#endif

RWTexture2D<LIGHTING_FORMAT> RWScreenProbeRadiance;

Texture2D<LIGHTING_FORMAT> ScreenProbeRadiance;

Texture2D<float> ScreenProbeHitDistance;

float SpatialFilterMaxRadianceHitAngle;
float SpatialFilterPositionWeightScale;
int SpatialFilterHalfKernelSize;

float GetFilterPositionWeight(float ProbeDepth, float SceneDepth)
{
	float DepthDifference = abs(ProbeDepth - SceneDepth);
	float RelativeDepthDifference = DepthDifference / SceneDepth;
	return ProbeDepth >= 0 ? exp2(-SpatialFilterPositionWeightScale * (RelativeDepthDifference * RelativeDepthDifference)) : 0;
}

void GatherNeighborRadiance(
	int2 NeighborScreenTileCoord,
	uint2 ProbeTexelCoord,
	float3 WorldPosition,
	float3 WorldConeDirection,
	float SceneDepth, 
	float HitDistance, 
	float ScreenProbeTracesMoving,
	inout float3 TotalRadiance, 
	inout float TotalWeight)
{
	if (all(and(NeighborScreenTileCoord >= 0, NeighborScreenTileCoord < (int2)ScreenProbeViewSize)))
	{
		uint2 NeighborScreenProbeAtlasCoord = NeighborScreenTileCoord;
		uint2 NeighborScreenProbeScreenPosition = GetUniformScreenProbeScreenPosition(NeighborScreenProbeAtlasCoord);
		float NeighborSceneDepth = GetScreenProbeDepth(NeighborScreenProbeAtlasCoord);
		float PositionWeight = GetFilterPositionWeight(NeighborSceneDepth, SceneDepth);

#define FILTER_SEARCH_ADAPTIVE_PROBES 0
#if FILTER_SEARCH_ADAPTIVE_PROBES
		if (PositionWeight <= 0.0f)
		{
			uint NumAdaptiveProbes = ScreenTileAdaptiveProbeHeader[NeighborScreenTileCoord];

			for (uint AdaptiveProbeListIndex = 0; AdaptiveProbeListIndex < NumAdaptiveProbes; AdaptiveProbeListIndex++)
			{
				uint2 AdaptiveProbeCoord = GetAdaptiveProbeCoord(NeighborScreenTileCoord, AdaptiveProbeListIndex);
				uint AdaptiveProbeIndex = ScreenTileAdaptiveProbeIndices[AdaptiveProbeCoord];
				uint ScreenProbeIndex = AdaptiveProbeIndex + NumUniformScreenProbes;

				uint2 NewNeighborScreenProbeScreenPosition = GetScreenProbeScreenPosition(ScreenProbeIndex);
				uint2 NewNeighborScreenProbeAtlasCoord = uint2(ScreenProbeIndex % ScreenProbeAtlasViewSize.x, ScreenProbeIndex / ScreenProbeAtlasViewSize.x);
				float NewNeighborSceneDepth = GetScreenProbeDepth(NewNeighborScreenProbeAtlasCoord);
				float NewPositionWeight = GetFilterPositionWeight(NewNeighborSceneDepth, SceneDepth);

				if (NewPositionWeight > PositionWeight)
				{
					PositionWeight = NewPositionWeight;
					NeighborScreenProbeAtlasCoord = NewNeighborScreenProbeAtlasCoord;
					NeighborScreenProbeScreenPosition = NewNeighborScreenProbeScreenPosition;
					NeighborSceneDepth = NewNeighborSceneDepth;
				}
			}
		}
#endif
		
		if (PositionWeight > 0.0f)
		{
			uint2 NeighborTraceCoord = NeighborScreenProbeAtlasCoord * ScreenProbeGatherOctahedronResolution + ProbeTexelCoord;
			float NeighborRadianceDepth = DecodeProbeHitDistanceForFiltering(ScreenProbeHitDistance.Load(int3(NeighborTraceCoord, 0)).x);

			if (NeighborRadianceDepth >= 0)
			{
				float AngleWeight = 1.0f;

				// Increase spatial filtering when temporal filter will be reduced
				if (ScreenProbeTracesMoving <= .01f)
				{
					// Clamp neighbor's hit distance to our own.  This helps preserve contact shadows, as a long neighbor hit distance will cause a small NeighborAngle and bias toward distant lighting.
					if (HitDistance >= 0)
					{
						NeighborRadianceDepth = min(NeighborRadianceDepth, HitDistance);
					}
					float2 NeighborScreenUV = (NeighborScreenProbeScreenPosition + .5f) * View.BufferSizeAndInvSize.zw;
					float3 NeighborWorldPosition = GetWorldPositionFromScreenUV(NeighborScreenUV, NeighborSceneDepth);
					float3 NeighborHitPosition = NeighborWorldPosition + WorldConeDirection * NeighborRadianceDepth;
					float3 ToNeighborHit = NeighborHitPosition - WorldPosition;
					float NeighborAngle = acosFast(dot(ToNeighborHit, WorldConeDirection) / length(ToNeighborHit));
					AngleWeight = 1.0f - saturate(NeighborAngle / SpatialFilterMaxRadianceHitAngle);
				}

				float Weight = PositionWeight * AngleWeight;
				TotalRadiance += ScreenProbeRadiance.Load(int3(NeighborTraceCoord, 0)).xyz * Weight;
				TotalWeight += Weight;
			}
		}
	}
}

[numthreads(PROBE_THREADGROUP_SIZE_2D, PROBE_THREADGROUP_SIZE_2D, 1)]
void SpatialFilteringCS(
	uint3 GroupId : SV_GroupID,
	uint3 DispatchThreadId : SV_DispatchThreadID,
	uint3 GroupThreadId : SV_GroupThreadID)
{
	uint2 ScreenProbeAtlasCoord = DispatchThreadId.xy / ScreenProbeGatherOctahedronResolution;
	uint2 ProbeTexelCoord = DispatchThreadId.xy - ScreenProbeAtlasCoord * ScreenProbeGatherOctahedronResolution;
	uint ScreenProbeIndex = ScreenProbeAtlasCoord.y * ScreenProbeAtlasViewSize.x + ScreenProbeAtlasCoord.x;

	uint2 ScreenProbeScreenPosition = GetScreenProbeScreenPosition(ScreenProbeIndex);
	uint2 ScreenTileCoord = GetScreenTileCoord(ScreenProbeScreenPosition);

	if (ScreenProbeIndex < GetNumScreenProbes() && ScreenProbeAtlasCoord.x < ScreenProbeAtlasViewSize.x)
	{
		float SceneDepth = GetScreenProbeDepth(ScreenProbeAtlasCoord);

		if (SceneDepth > 0.0f)
		{
			float ScreenProbeTracesMoving = GetScreenProbeMoving(ScreenProbeAtlasCoord);

			float2 ScreenUV = GetScreenUVFromScreenProbePosition(ScreenProbeScreenPosition);
			float3 WorldPosition = GetWorldPositionFromScreenUV(ScreenUV, SceneDepth);

			float2 ProbeTexelCenter = GetProbeTexelCenter(ScreenTileCoord);
			float2 ProbeUV = (ProbeTexelCoord + ProbeTexelCenter) / (float)ScreenProbeGatherOctahedronResolution;
			float3 WorldConeDirection = EquiAreaSphericalMapping(ProbeUV);
			float HitDistance = DecodeProbeHitDistanceForFiltering(ScreenProbeHitDistance.Load(int3(DispatchThreadId.xy, 0)).x);
			float3 TotalRadiance = 0;
			float TotalWeight = 0;

			{
				TotalRadiance = ScreenProbeRadiance.Load(int3(DispatchThreadId.xy, 0)).xyz;
				TotalWeight = 1.0f;
			}

			int2 Offsets[4];
			Offsets[0] = int2(-1, 0);
			Offsets[1] = int2(1, 0);
			Offsets[2] = int2(0, -1);
			Offsets[3] = int2(0, 1);

			LOOP
			for (uint OffsetIndex = 0; OffsetIndex < 4; OffsetIndex++)
			{
				GatherNeighborRadiance(ScreenTileCoord + Offsets[OffsetIndex], ProbeTexelCoord, WorldPosition, WorldConeDirection, SceneDepth, HitDistance, ScreenProbeTracesMoving, TotalRadiance, TotalWeight);
			}

			// Increase spatial filtering when temporal filter will be reduced
			if (ScreenProbeTracesMoving > .01f)
			{
				int2 Offsets[8];
				Offsets[0] = int2(-2, 0);
				Offsets[1] = int2(2, 0);
				Offsets[2] = int2(0, -2);
				Offsets[3] = int2(0, 2);
				Offsets[4] = int2(-1, 1);
				Offsets[5] = int2(1, 1);
				Offsets[6] = int2(-1, -1);
				Offsets[7] = int2(1, -1);

				LOOP
				for (uint OffsetIndex = 0; OffsetIndex < 8; OffsetIndex++)
				{
					GatherNeighborRadiance(ScreenTileCoord + Offsets[OffsetIndex], ProbeTexelCoord, WorldPosition, WorldConeDirection, SceneDepth, HitDistance, ScreenProbeTracesMoving, TotalRadiance, TotalWeight);
				}
			}

			if (TotalWeight > 0)
			{
				TotalRadiance /= TotalWeight;
			}

			RWScreenProbeRadiance[DispatchThreadId.xy] = ToLightingFormat(TotalRadiance);
		}
		else
		{
			RWScreenProbeRadiance[DispatchThreadId.xy] = ToLightingFormat(float3(0.0f, 0.0f, 0.0f));
		}
	}
}
