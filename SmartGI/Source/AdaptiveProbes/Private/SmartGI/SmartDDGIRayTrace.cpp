#include "SmartDDGILighting.h"

#include "RendererPrivate.h"
#include "ScenePrivate.h"
#include "SceneUtils.h"
#include "PipelineStateCache.h"
#include "ShaderParameterStruct.h"

#include "SmartGI/SmartCommon.h"
#include "SmartGI/SmartDDGICommon.h"
#include "SmartGI/SmartTracingInput.h"
#include "SmartGI/SmartLightCommon.h"
#include "SmartGI/SmartSceneContext.h"
#include "SmartGI/SmartViewState.h"

extern int32 GSmartRadiosityEnable;

extern int32 GSmartDDGIRayCount;
extern float GSmartDDGIBlendSpeed;
extern float GSmartDDGIEmissiveMultiplier;
extern int32 GSmartDDGIProbeRelocation;
extern int32 GSmartDDGIProbeStateClassifier;
extern int32 GSmartDDGIDynamicVolume;

extern int32 GSmartGIUseShadowMap;
extern int32 GSmartDDGISimplexPlacement;

#if RHI_RAYTRACING

class FDDGIRayTraceRGS : public FGlobalShader
{
	DECLARE_GLOBAL_SHADER(FDDGIRayTraceRGS)
	SHADER_USE_ROOT_PARAMETER_STRUCT(FDDGIRayTraceRGS, FGlobalShader)

	BEGIN_SHADER_PARAMETER_STRUCT(FParameters, )
		// View
		SHADER_PARAMETER_STRUCT_REF(FViewUniformShaderParameters, ViewUniformBuffer)

		// Hardware Ray Tracing
		SHADER_PARAMETER_RDG_BUFFER_SRV(RaytracingAccelerationStructure, TLAS)

		// DDGI
		SHADER_PARAMETER_STRUCT_ARRAY(FDDGIVolumeDesc, DDGIVolumeDesc, [2])
		SHADER_PARAMETER_STRUCT_ARRAY(FDDGIVolumeSRVResources, DDGIVolumeSRVResources, [2])
		SHADER_PARAMETER_RDG_BUFFER_UAV(RWStructuredBuffer<FDDGIRayDataPacked>, RayDataBuffer)
		SHADER_PARAMETER_RDG_BUFFER_UAV(RWStructuredBuffer<FDDGIRayPayloadPacked>, RayPayloadBuffer)
		SHADER_PARAMETER(uint32, RayCount)
		SHADER_PARAMETER(FMatrix44f, DDGIRotationTransform)

	END_SHADER_PARAMETER_STRUCT()
	class FDDGIProbeRelocation : SHADER_PERMUTATION_BOOL("DDGI_PROBE_RELOCATION");
	class FDDGIProbeStateClassifier : SHADER_PERMUTATION_BOOL("DDGI_PROBE_CLASSIFICATION");
	class FDDGIDynamicVolume : SHADER_PERMUTATION_BOOL("ENABLE_DYNAMIC_VOLUME");
	class FDDGIVolumeIndex : SHADER_PERMUTATION_INT("DDGI_VOLUME_INDEX", 2);
	class FDDGISimplexPlacement : SHADER_PERMUTATION_BOOL("DDGI_SIMPLEX_PLACEMENT");

	using FPermutationDomain = TShaderPermutationDomain<
		FDDGIProbeRelocation, FDDGIProbeStateClassifier,
		FDDGIDynamicVolume, FDDGIVolumeIndex, FDDGISimplexPlacement
	>;

	static bool ShouldCompilePermutation(const FGlobalShaderPermutationParameters& Parameters)
	{
		const FPermutationDomain PermutationVector(Parameters.PermutationId);
		return DoesPlatformSupportSmartGIWithDDGI(Parameters.Platform);
	}

	static void ModifyCompilationEnvironment(const FGlobalShaderPermutationParameters& Parameters, FShaderCompilerEnvironment& OutEnvironment)
	{
		FGlobalShader::ModifyCompilationEnvironment(Parameters, OutEnvironment);
		OutEnvironment.SetDefine(TEXT("ENABLE_TWO_SIDED_GEOMETRY"), 1);	// If false, it will cull back face triangles. We want this on for probe relocation and to stop light leak.
		OutEnvironment.SetDefine(TEXT("ENABLE_MATERIALS"), 0);			// If false, forces the geo to opaque (no alpha test). We want this off for speed.
	}

	static ERayTracingPayloadType GetRayTracingPayloadType(const int32 PermutationId)
	{
		return ERayTracingPayloadType::RayTracingMaterial;
	}
};

IMPLEMENT_GLOBAL_SHADER(FDDGIRayTraceRGS, "/Plugin/SmartGI/Private/SmartGI/SmartDDGIRayTraceRGS.usf", "DDGIRayTraceRGS", SF_RayGen);


class FDDGIRayTraceLightingCS : public FGlobalShader
{
	DECLARE_GLOBAL_SHADER(FDDGIRayTraceLightingCS)
	SHADER_USE_PARAMETER_STRUCT(FDDGIRayTraceLightingCS, FGlobalShader)

	BEGIN_SHADER_PARAMETER_STRUCT(FParameters, )
		// View
		SHADER_PARAMETER_STRUCT_REF(FViewUniformShaderParameters, ViewUniformBuffer)
		SHADER_PARAMETER_STRUCT_INCLUDE(FSmartDiffuseTracingParameters, DiffuseTracingParameters)

		// DDGI
		SHADER_PARAMETER_STRUCT_ARRAY(FDDGIVolumeDesc, DDGIVolumeDesc, [2])
		SHADER_PARAMETER_STRUCT_ARRAY(FDDGIVolumeSRVResources, DDGIVolumeSRVResources, [2])

		SHADER_PARAMETER_RDG_BUFFER_SRV(StructuredBuffer<FDDGIRayDataPacked>, RayDataBuffer)
		SHADER_PARAMETER_RDG_BUFFER_SRV(StructuredBuffer<FDDGIRayPayloadPacked>, RayPayloadBuffer)
		SHADER_PARAMETER_RDG_BUFFER_UAV(RWStructuredBuffer<FDDGIRayRadiancePacked>, RayRadianceBuffer)
		SHADER_PARAMETER(float, EmissiveMultiplier)
		SHADER_PARAMETER(uint32, RayCount)
		SHADER_PARAMETER(FMatrix44f, DDGIRotationTransform)

		// Direct Lighting
		SHADER_PARAMETER_STRUCT_INCLUDE(FSmartDirectLightingParameters, DirectLightingParameters)
		SHADER_PARAMETER(uint32, SmartNumConvexHullPlanes)
		SHADER_PARAMETER_ARRAY(FVector4f, SmartViewFrustumConvexHull, [6])

		// Shadow Ray
		SHADER_PARAMETER_STRUCT_INCLUDE(FSmartShadowRayParameters, ShadowRayParameters)

		// Sky Light
		SHADER_PARAMETER_RDG_UNIFORM_BUFFER(FReflectionUniformParameters, ReflectionStruct)
	END_SHADER_PARAMETER_STRUCT()

	//class FDDGIProbeRelocation : SHADER_PERMUTATION_BOOL("DDGI_PROBE_RELOCATION");
	//class FDDGIProbeStateClassifier : SHADER_PERMUTATION_BOOL("DDGI_PROBE_CLASSIFICATION");
	//class FDDGIRadiosityEnable : SHADER_PERMUTATION_BOOL("DDGI_MULTIBOUNCE_ENABLE");
	//class FDDGIDynamicVolume : SHADER_PERMUTATION_BOOL("ENABLE_DYNAMIC_VOLUME");
	class FDDGISimplexPlacement : SHADER_PERMUTATION_BOOL("DDGI_SIMPLEX_PLACEMENT");
	class FDDGIVolumeIndex : SHADER_PERMUTATION_INT("DDGI_VOLUME_INDEX", 2);
	class FConeTraceMethod : SHADER_PERMUTATION_INT("CONE_TRACE_METHOD", 3);
	class FHasLightTypeDirectional : SHADER_PERMUTATION_BOOL("HAS_LIGHT_TYPE_DIRECTIONAL");
	class FDenseShadowMap : SHADER_PERMUTATION_BOOL("DENSE_SHADOW_MAP");
	class FManyLightsCulling : SHADER_PERMUTATION_BOOL("SMART_MANY_LIGHTS_CULLING");
	//class FDynamicSkyLight : SHADER_PERMUTATION_BOOL("ENABLE_DYNAMIC_SKY_LIGHT");

#if H3D_SMARTGI_USEGI
	class FVirtualShadowMap : SHADER_PERMUTATION_BOOL("VIRTUAL_SHADOW_MAP");
	using FPermutationDomain = TShaderPermutationDomain<
		/*FDDGIProbeRelocation, FDDGIProbeStateClassifier, FDDGIRadiosityEnable, FDDGIDynamicVolume,*/
		FDDGIVolumeIndex, FDDGISimplexPlacement, 
		FConeTraceMethod, FHasLightTypeDirectional, FDenseShadowMap, FVirtualShadowMap, FManyLightsCulling
		/*FDynamicSkyLight*/ 
	>;
#else
	using FPermutationDomain = TShaderPermutationDomain<
		/*FDDGIProbeRelocation, FDDGIProbeStateClassifier, FDDGIRadiosityEnable, FDDGIDynamicVolume,*/
		FDDGIVolumeIndex, FDDGISimplexPlacement, 
		FConeTraceMethod, FHasLightTypeDirectional, FDenseShadowMap, FManyLightsCulling
		/*FDynamicSkyLight*/ 
	>;	
#endif

	static bool ShouldCompilePermutation(const FGlobalShaderPermutationParameters& Parameters)
	{
		const FPermutationDomain PermutationVector(Parameters.PermutationId);
		const int32 ConeTraceMethod = PermutationVector.Get<FConeTraceMethod>();
		return ShouldCompileWithRayTracingInline(ConeTraceMethod, Parameters) && DoesPlatformSupportSmartGIWithDDGI(Parameters.Platform);
	}

	static void ModifyCompilationEnvironment(const FGlobalShaderPermutationParameters& Parameters, FShaderCompilerEnvironment& OutEnvironment)
	{
		FGlobalShader::ModifyCompilationEnvironment(Parameters, OutEnvironment);
		OutEnvironment.SetDefine(TEXT("MAX_MOBILE_SHADOWCASCADES"), MAX_MOBILE_SHADOWCASCADES);
#if H3D_SMARTGI_USEGI
		FVirtualShadowMapArray::SetShaderDefines(OutEnvironment);
#endif
		const FPermutationDomain PermutationVector(Parameters.PermutationId);
		const int32 ConeTraceMethod = PermutationVector.Get<FConeTraceMethod>();
		ProcessRayTracingInlinePermutation(ConeTraceMethod, OutEnvironment);

		OutEnvironment.SetDefine(TEXT("DDGI_MULTIBOUNCE_ENABLE"), 1);
		OutEnvironment.SetDefine(TEXT("DDGI_PROBE_RELOCATION"), 1);
		OutEnvironment.SetDefine(TEXT("DDGI_PROBE_CLASSIFICATION"), 1);
		OutEnvironment.SetDefine(TEXT("ENABLE_DYNAMIC_VOLUME"), 1);

		//OutEnvironment.SetDefine(TEXT("HAS_LIGHT_TYPE_DIRECTIONAL"), 1);
		//OutEnvironment.SetDefine(TEXT("ENABLE_DYNAMIC_SKY_LIGHT"), 1);
		//OutEnvironment.SetDefine(TEXT("SMART_MANY_LIGHTS_CULLING"), 0);
	}
};

IMPLEMENT_GLOBAL_SHADER(FDDGIRayTraceLightingCS, "/Plugin/SmartGI/Private/SmartGI/SmartDDGIRayTraceLighting.usf", "DDGIRayTraceLightingCS", SF_Compute);

#endif //RHI_RAYTRACING

static FMatrix44f ComputeRandomRotation()
{
	// This approach is based on James Arvo's implementation from Graphics Gems 3 (pg 117-120).
	// Also available at: http://citeseerx.ist.psu.edu/viewdoc/download?doi=10.1.1.53.1357&rep=rep1&type=pdf

	// Setup a random rotation matrix using 3 uniform RVs
	float u1 = 2.f * 3.14159265359 * FMath::FRand();
	float cos1 = FGenericPlatformMath::Cos(u1);
	float sin1 = FGenericPlatformMath::Sin(u1);

	float u2 = 2.f * 3.14159265359 * FMath::FRand();
	float cos2 = FGenericPlatformMath::Cos(u2);
	float sin2 = FGenericPlatformMath::Sin(u2);

	float u3 = FMath::FRand();
	float sq3 = 2.f * FGenericPlatformMath::Sqrt(u3 * (1.f - u3));

	float s2 = 2.f * u3 * sin2 * sin2 - 1.f;
	float c2 = 2.f * u3 * cos2 * cos2 - 1.f;
	float sc = 2.f * u3 * sin2 * cos2;

	// Create the random rotation matrix
	float _11 = cos1 * c2 - sin1 * sc;
	float _12 = sin1 * c2 + cos1 * sc;
	float _13 = sq3 * cos2;

	float _21 = cos1 * sc - sin1 * s2;
	float _22 = sin1 * sc + cos1 * s2;
	float _23 = sq3 * sin2;

	float _31 = cos1 * (sq3 * cos2) - sin1 * (sq3 * sin2);
	float _32 = sin1 * (sq3 * cos2) + cos1 * (sq3 * sin2);
	float _33 = 1.f - 2.f * u3;

	return FMatrix44f(
		FPlane4f(_11, _12, _13, 0.f),
		FPlane4f(_21, _22, _23, 0.f),
		FPlane4f(_31, _32, _33, 0.f),
		FPlane4f(0.f, 0.f, 0.f, 1.f)
	);
}

void FSmartDDGIRenderer::DDGIRayTrace(
	FRDGBuilder& GraphBuilder,
	FSmartSceneContext& SceneContext,
	FViewInfo& View,
	uint32 VolumeIndex
)
{
#if H3D_SMARTGI_USEGI && RHI_RAYTRACING
	SCOPE_CYCLE_COUNTER(STAT_SmartGI_DDGIRayTrace);

	FDDGIRayTraceRGS::FParameters* PassParameters = GraphBuilder.AllocParameters<FDDGIRayTraceRGS::FParameters>();
	
	// View
	PassParameters->ViewUniformBuffer = View.ViewUniformBuffer;

	// Hardware Ray Tracing
	PassParameters->TLAS = View.GetRayTracingSceneLayerViewChecked(ERayTracingSceneLayer::Base);

	// DDGI
	const FDDGILightingState& DDGIState = Scene->DDGIState;
	const auto& DDGIVolumeDesc = DDGIState.DDGIVolumeDesc[VolumeIndex];
	PassParameters->DDGIVolumeDesc[0] = DDGIState.DDGIVolumeDesc[0];
	PassParameters->DDGIVolumeDesc[1] = DDGIState.DDGIVolumeDesc[1];

	FDDGIVolumeSRVResources DDGIVolumeSRVResources0;
	DDGISetupDDGIVolumeSRVResources(GraphBuilder, DDGIVolumeSRVResources0, 0);
	FDDGIVolumeSRVResources DDGIVolumeSRVResources1;
	DDGISetupDDGIVolumeSRVResources(GraphBuilder, DDGIVolumeSRVResources1, 1);
	PassParameters->DDGIVolumeSRVResources[0] = DDGIVolumeSRVResources0;
	PassParameters->DDGIVolumeSRVResources[1] = DDGIVolumeSRVResources1;

	PassParameters->RayDataBuffer = GraphBuilder.CreateUAV(DDGIContext.DDGIRayDataBuffer[VolumeIndex]);
	PassParameters->RayPayloadBuffer = GraphBuilder.CreateUAV(DDGIContext.DDGIRayPayloadBuffer[VolumeIndex]);

	PassParameters->RayCount = fmin((uint32)GSmartDDGIRayCount, DDGI_MAX_RAYCOUNT);
	PassParameters->DDGIRotationTransform = ComputeRandomRotation();

	FDDGIRayTraceRGS::FPermutationDomain PermutationVector;
	PermutationVector.Set< FDDGIRayTraceRGS::FDDGIProbeRelocation >(GSmartDDGIProbeRelocation > 0);
	PermutationVector.Set< FDDGIRayTraceRGS::FDDGIProbeStateClassifier >(GSmartDDGIProbeStateClassifier > 0);
	PermutationVector.Set< FDDGIRayTraceRGS::FDDGIDynamicVolume >(GSmartDDGIDynamicVolume > 0);
    PermutationVector.Set< FDDGIRayTraceRGS::FDDGIVolumeIndex >(VolumeIndex);
    PermutationVector.Set< FDDGIRayTraceRGS::FDDGISimplexPlacement>(GSmartDDGISimplexPlacement > 0);

	TShaderRef<FDDGIRayTraceRGS> RayGenerationShader = View.ShaderMap->GetShader<FDDGIRayTraceRGS>(PermutationVector);
	ClearUnusedGraphResources(RayGenerationShader, PassParameters);

	const int32 RayCount = fmin(GSmartDDGIRayCount, (int32)DDGI_MAX_RAYCOUNT);
	const int32 ProbeCount = DDGIVolumeDesc.ProbeCount;
	FIntPoint DispatchSize = FIntPoint{ RayCount, ProbeCount };

	GraphBuilder.AddPass(
		RDG_EVENT_NAME("DDGIRayTraceRGS"),
		PassParameters,
		ERDGPassFlags::Compute,
		[PassParameters, RayGenerationShader, DispatchSize, &View](FRHIRayTracingCommandList& RHICmdList)
		{
			FRHIRayTracingScene* RayTracingSceneRHI = View.GetRayTracingSceneChecked();

			FRayTracingShaderBindingsWriter GlobalResources;
			SetShaderParameters(GlobalResources, RayGenerationShader, *PassParameters);

			RHICmdList.RayTraceDispatch(
				View.RayTracingMaterialPipeline,
				RayGenerationShader.GetRayTracingShader(),
				RayTracingSceneRHI, GlobalResources,
				DispatchSize.X, DispatchSize.Y);
		}
	);
#endif //RHI_RAYTRACING
}

void FSmartDDGIRenderer::DDGIRayTraceLighting(FRDGBuilder& GraphBuilder, FSmartSceneContext& SceneContext, FViewInfo& View, uint32 VolumeIndex)
{
#if RHI_RAYTRACING
	//SCOPE_CYCLE_COUNTER(STAT_SmartGI_DDGIRayTrace);

	FDDGIRayTraceLightingCS::FParameters* PassParameters = GraphBuilder.AllocParameters<FDDGIRayTraceLightingCS::FParameters>();

	// View
	PassParameters->ViewUniformBuffer = View.ViewUniformBuffer;
	FSmartDiffuseTracingParameters DiffuseTracingParameters;
	SetupSmartDiffuseTracingParameters(View, DiffuseTracingParameters);
	PassParameters->DiffuseTracingParameters = DiffuseTracingParameters;

	// DDGI
	const FDDGILightingState& DDGIState = Scene->DDGIState;
	const auto& DDGIVolumeDesc = DDGIState.DDGIVolumeDesc[VolumeIndex];
	PassParameters->DDGIVolumeDesc[0] = DDGIState.DDGIVolumeDesc[0];
	PassParameters->DDGIVolumeDesc[1] = DDGIState.DDGIVolumeDesc[1];

	FDDGIVolumeSRVResources DDGIVolumeSRVResources0;
	DDGISetupDDGIVolumeSRVResources(GraphBuilder, DDGIVolumeSRVResources0, 0);
	FDDGIVolumeSRVResources DDGIVolumeSRVResources1;
	DDGISetupDDGIVolumeSRVResources(GraphBuilder, DDGIVolumeSRVResources1, 1);
	PassParameters->DDGIVolumeSRVResources[0] = DDGIVolumeSRVResources0;
	PassParameters->DDGIVolumeSRVResources[1] = DDGIVolumeSRVResources1;

	PassParameters->RayDataBuffer = GraphBuilder.CreateSRV(DDGIContext.DDGIRayDataBuffer[VolumeIndex]);
	PassParameters->RayPayloadBuffer = GraphBuilder.CreateSRV(DDGIContext.DDGIRayPayloadBuffer[VolumeIndex]);
	PassParameters->RayRadianceBuffer = GraphBuilder.CreateUAV(DDGIContext.DDGIRayRadianceBuffer[VolumeIndex]);

	PassParameters->EmissiveMultiplier = GSmartDDGIEmissiveMultiplier;
	PassParameters->RayCount = fmin((uint32)GSmartDDGIRayCount, DDGI_MAX_RAYCOUNT);
	PassParameters->DDGIRotationTransform = ComputeRandomRotation();

	// ManyLights
	FSmartDirectLightingParameters DirectLightingParameters;
	bool bHasLightTypeDirectional = false;
	bool bUseDenseShadowMap = false;
	bool bUseVirtualShadowMap = false;
	SetupSmartDirectLightingParameters(GraphBuilder, Scene, View, SceneRenderer, DirectLightingParameters, bHasLightTypeDirectional, bUseDenseShadowMap, bUseVirtualShadowMap);
	PassParameters->DirectLightingParameters = DirectLightingParameters;

	PassParameters->SmartNumConvexHullPlanes = View.ViewFrustum.Planes.Num();
	for (int32 i = 0; i < View.ViewFrustum.Planes.Num(); i++)
	{
		PassParameters->SmartViewFrustumConvexHull[i] = FVector4f((FVector3f)View.ViewFrustum.Planes[i], View.ViewFrustum.Planes[i].W);
	}

	// Shadow Ray
	FSmartShadowRayParameters ShadowRayParameters;
	SetupSmartShadowRayParameters(GraphBuilder, Scene, View, ShadowRayParameters);
	PassParameters->ShadowRayParameters = ShadowRayParameters;

	// Sky Light
	PassParameters->ReflectionStruct = CreateReflectionUniformBuffer(GraphBuilder, View);

	FDDGIRayTraceLightingCS::FPermutationDomain PermutationVector;
	//PermutationVector.Set< FDDGIRayTraceLightingCS::FDDGIProbeRelocation >(GSmartDDGIProbeRelocation > 0);
	//PermutationVector.Set< FDDGIRayTraceLightingCS::FDDGIProbeStateClassifier >(GSmartDDGIProbeStateClassifier > 0);
	//PermutationVector.Set< FDDGIRayTraceLightingCS::FDDGIRadiosityEnable >(GSmartRadiosityEnable > 0);
	//PermutationVector.Set< FDDGIRayTraceLightingCS::FDDGIDynamicVolume >(GSmartDDGIDynamicVolume > 0);
	PermutationVector.Set< FDDGIRayTraceLightingCS::FDDGIVolumeIndex >(VolumeIndex);
	PermutationVector.Set< FDDGIRayTraceLightingCS::FDDGISimplexPlacement>(GSmartDDGISimplexPlacement > 0);
	PermutationVector.Set< FDDGIRayTraceLightingCS::FConeTraceMethod>(SmartGI::GetConeTraceMethod(View.GetShaderPlatform()));
	PermutationVector.Set< FDDGIRayTraceLightingCS::FHasLightTypeDirectional >(bHasLightTypeDirectional);
#if H3D_SMARTGI_USEGI
	PermutationVector.Set< FDDGIRayTraceLightingCS::FVirtualShadowMap >(bUseVirtualShadowMap);
#endif
	PermutationVector.Set< FDDGIRayTraceLightingCS::FDenseShadowMap >(bUseDenseShadowMap);
	PermutationVector.Set< FDDGIRayTraceLightingCS::FManyLightsCulling >(SmartGI::IsManylightsCullingEnabled() && SmartGI::IsLightCullingEnableEnabled());
	//PermutationVector.Set< FDDGIRayTraceLightingCS::FDynamicSkyLight >(SmartGI::ShouldHandleSkyLight(Scene, *View.Family));

	TShaderRef<FDDGIRayTraceLightingCS> ComputeShader = View.ShaderMap->GetShader<FDDGIRayTraceLightingCS>(PermutationVector);

	const int32 RayCount = fmin(GSmartDDGIRayCount, (int32)DDGI_MAX_RAYCOUNT);
	const int32 ProbeCount = DDGIVolumeDesc.ProbeCount;
	FIntPoint DispatchSize = FIntPoint{ RayCount, ProbeCount };

	FComputeShaderUtils::AddPass(
		GraphBuilder,
		RDG_EVENT_NAME("DDGIRayTraceLightingCS"),
		ComputeShader,
		PassParameters,
		FComputeShaderUtils::GetGroupCount(DispatchSize, 8)
	);
#endif //RHI_RAYTRACING
}

void FSmartDDGIRenderer::PrepareHardwareRayTracingDDGIRayTrace(
	const FViewInfo& View, 
	const FScene* Scene, 
	TArray<FRHIRayTracingShader*>& OutRayGenShaders
)
{
#if RHI_RAYTRACING
	for (int32 VolumeIndex = 0; VolumeIndex <= 1; ++VolumeIndex)
	{
		FDDGIRayTraceRGS::FPermutationDomain PermutationVector;
		PermutationVector.Set< FDDGIRayTraceRGS::FDDGIProbeRelocation >(GSmartDDGIProbeRelocation > 0);
		PermutationVector.Set< FDDGIRayTraceRGS::FDDGIProbeStateClassifier >(GSmartDDGIProbeStateClassifier > 0);
		PermutationVector.Set< FDDGIRayTraceRGS::FDDGIDynamicVolume >(GSmartDDGIDynamicVolume > 0);
		PermutationVector.Set< FDDGIRayTraceRGS::FDDGIVolumeIndex >(VolumeIndex);
        PermutationVector.Set< FDDGIRayTraceRGS::FDDGISimplexPlacement>(GSmartDDGISimplexPlacement > 0);
		TShaderRef<FDDGIRayTraceRGS> RayGenerationShader = View.ShaderMap->GetShader<FDDGIRayTraceRGS>(PermutationVector);
		OutRayGenShaders.Add(RayGenerationShader.GetRayTracingShader());
	}
#endif //RHI_RAYTRACING
}