#include "SmartGI/SmartFinalGather.h"
#include "RendererPrivate.h"
#include "ScenePrivate.h"
#include "SceneUtils.h"
#include "PipelineStateCache.h"
#include "ShaderParameterStruct.h"
#include "PixelShaderUtils.h"
#include "SmartGI/SmartSceneContext.h"

int32 GSmartBentNormalNumPixelRays = 4;
FAutoConsoleVariableRef GVarSmartBentNormalNumPixelRays(
	TEXT("r.SmartGI.ScreenSpaceBentNormal.NumRays"),
	GSmartBentNormalNumPixelRays,
	TEXT("Whether to compute screen space directional occlusion to add high frequency occlusion (contact shadows) which Screen Probes lack due to downsampling."),
	ECVF_Scalability | ECVF_RenderThreadSafe
);

float GSmartBentNormalSlopeCompareToleranceScale = 2.0f;
FAutoConsoleVariableRef CVarSmartBentNormalSlopeCompareToleranceScale(
	TEXT("r.SmartGI.ScreenSpaceBentNormal.SlopeCompareToleranceScale"),
	GSmartBentNormalSlopeCompareToleranceScale,
	TEXT("Scales the slope threshold that screen space traces use to determine whether there was a hit."),
	ECVF_Scalability | ECVF_RenderThreadSafe
);

class FSmartSSBentNormalCS : public FGlobalShader
{
	DECLARE_GLOBAL_SHADER(FSmartSSBentNormalCS)
	SHADER_USE_PARAMETER_STRUCT(FSmartSSBentNormalCS, FGlobalShader)

	BEGIN_SHADER_PARAMETER_STRUCT(FParameters, )
		SHADER_PARAMETER_RDG_TEXTURE_UAV(RWTexture2D<float4>, RWScreenBentNormal)
		SHADER_PARAMETER_RDG_UNIFORM_BUFFER(FSceneTextureUniformParameters, SceneTexturesStruct)
		SHADER_PARAMETER_STRUCT_INCLUDE(FSceneTextureParameters, SceneTextures)
		SHADER_PARAMETER_RDG_UNIFORM_BUFFER(FMobileSceneTextureUniformParameters, MobileSceneTextures)
		SHADER_PARAMETER_STRUCT_INCLUDE(FSmartScreenProbeParameters, ScreenProbeParameters)
		SHADER_PARAMETER_STRUCT_REF(FViewUniformShaderParameters, ViewUniformBuffer)
		SHADER_PARAMETER_RDG_TEXTURE(Texture2D<uint>, LightingChannelsTexture)
		SHADER_PARAMETER(FVector4f, HZBUvFactorAndInvFactor)
		SHADER_PARAMETER(float, SlopeCompareToleranceScale)
		SHADER_PARAMETER_RDG_TEXTURE(Texture2D, FurthestHZBTexture)
		SHADER_PARAMETER_SAMPLER(SamplerState, FurthestHZBTextureSampler)
	END_SHADER_PARAMETER_STRUCT()

	class FNumPixelRays : SHADER_PERMUTATION_SPARSE_INT("NUM_PIXEL_RAYS", 4, 8, 16);
	using FPermutationDomain = TShaderPermutationDomain<FNumPixelRays>;

	static bool ShouldCompilePermutation(const FGlobalShaderPermutationParameters& Parameters)
	{
		return DoesPlatformSupportSmartGI(Parameters.Platform);
	}

	static int32 GetGroupSize() 
	{
		return 8;
	}

	static void ModifyCompilationEnvironment(const FGlobalShaderPermutationParameters& Parameters, FShaderCompilerEnvironment& OutEnvironment)
	{
		FGlobalShader::ModifyCompilationEnvironment(Parameters, OutEnvironment);
		OutEnvironment.SetDefine(TEXT("THREADGROUP_SIZE"), GetGroupSize());
		OutEnvironment.CompilerFlags.Add(CFLAG_Wave32);
	}
};

IMPLEMENT_GLOBAL_SHADER(FSmartSSBentNormalCS, "/Plugin/SmartGI/Private/SmartGI/SmartSSBentNormal.usf", "ScreenSpaceBentNormalCS", SF_Compute);

FRDGTextureRef ComputeSmartSSBentNormal(
	FRDGBuilder& GraphBuilder, 
	const FScene* Scene,
	const FViewInfo& View, 
	FSmartSceneContext& SceneContext,
	FRDGTextureRef LightingChannelsTexture,
	const FSmartScreenProbeParameters& ScreenProbeParameters)
{
	FRDGTextureDesc ScreenBentNormalDesc(FRDGTextureDesc::Create2D(View.GetSceneTexturesConfig().Extent, PF_R8G8B8A8, FClearValueBinding::Black, TexCreate_ShaderResource | TexCreate_UAV));
	FRDGTextureRef ScreenBentNormal = GraphBuilder.CreateTexture(ScreenBentNormalDesc, TEXT("Smart.ScreenBentNormal"));

	int32 NumPixelRays = FMath::Clamp(GSmartBentNormalNumPixelRays,4,32);

	if (View.ViewState->SmartGI.SmartFinalGatherQuality >= 6.0f)
	{
		NumPixelRays = 16;
	}
	else if (View.ViewState->SmartGI.SmartFinalGatherQuality >= 2.0f)
	{
		NumPixelRays = 8;
	}

	{
		FSmartSSBentNormalCS::FParameters* PassParameters = GraphBuilder.AllocParameters<FSmartSSBentNormalCS::FParameters>();
		PassParameters->RWScreenBentNormal = GraphBuilder.CreateUAV(FRDGTextureUAVDesc(ScreenBentNormal));
		PassParameters->SceneTexturesStruct = SceneContext.GetUniformBuffer();
		PassParameters->SceneTextures = SceneContext.GetSceneTextures();
		PassParameters->MobileSceneTextures = SceneContext.GetMobileUniformBuffer();

		if (!PassParameters->SceneTextures.GBufferVelocityTexture)
		{
			PassParameters->SceneTextures.GBufferVelocityTexture = GSystemTextures.GetBlackDummy(GraphBuilder);
		}

		PassParameters->ScreenProbeParameters = ScreenProbeParameters;
		PassParameters->ViewUniformBuffer = View.ViewUniformBuffer;
		PassParameters->LightingChannelsTexture = LightingChannelsTexture;

		const FVector2D ViewportUVToHZBBufferUV(
			float(View.ViewRect.Width()) / float(2 * View.HZBMipmap0Size.X),
			float(View.ViewRect.Height()) / float(2 * View.HZBMipmap0Size.Y)
		);

		PassParameters->HZBUvFactorAndInvFactor = FVector4f(
			ViewportUVToHZBBufferUV.X,
			ViewportUVToHZBBufferUV.Y,
			1.0f / ViewportUVToHZBBufferUV.X,
			1.0f / ViewportUVToHZBBufferUV.Y);

		PassParameters->FurthestHZBTexture = View.HZB;
		PassParameters->FurthestHZBTextureSampler = TStaticSamplerState<SF_Point>::GetRHI();
		PassParameters->SlopeCompareToleranceScale = GSmartBentNormalSlopeCompareToleranceScale;

		FSmartSSBentNormalCS::FPermutationDomain PermutationVector;
		PermutationVector.Set< FSmartSSBentNormalCS::FNumPixelRays >(NumPixelRays);
		auto ComputeShader = View.ShaderMap->GetShader<FSmartSSBentNormalCS>(PermutationVector);

		FComputeShaderUtils::AddPass(
			GraphBuilder,
			RDG_EVENT_NAME("ScreenSpaceBentNormal Rays=%u", NumPixelRays),
			ComputeShader,
			PassParameters,
			FComputeShaderUtils::GetGroupCount(View.ViewRect.Size(), FSmartSSBentNormalCS::GetGroupSize()));

		FSmartFinalGatherTemporalState& FinalGatherState = View.ViewState->SmartGI.FinalGatherState;
		GraphBuilder.QueueTextureExtraction(ScreenBentNormal, &FinalGatherState.BentNormalHistoryRT);
	}

	return ScreenBentNormal;
}