#include "H3D/H3DWeiJianWeiDefine.h"

#if H3D_SMARTGI_USEGI
#include "SmartGI/SmartFinalGather.h"
#include "SmartGI/SmartSceneContext.h"
#include "RendererPrivate.h"
#include "ScenePrivate.h"
#include "SceneUtils.h"
#include "PipelineStateCache.h"
#include "ShaderParameterStruct.h"
#include "PixelShaderUtils.h"
#include "ReflectionEnvironment.h"
#include "ScreenSpaceDenoise.h"
#include "SceneTextureParameters.h"
#include "ShaderCompilerCore.h"
extern int32 GSmartFilterCalculateMoving;
extern int32 GSmartVisualizeScreenProbePlacement;
extern int32 GSmartGIFinalGatherUseCS;
extern float GFinalGatherResolutionScale;
extern int32 GSmartScreenProbeReuseAdaptivePlacement;

SMARTREFLECTIONS_API extern float GSmartReflectionMaxRoughnessToTrace;
SMARTREFLECTIONS_API extern float GSmartReflectionRoughnessFadeLength;

int32 GSmartFinalGatherIntegrate = 1;
FAutoConsoleVariableRef GVarSmartFinalGatherIntegrate(
	TEXT("r.SmartGI.FinalGather.Integrate"),
	GSmartFinalGatherIntegrate,
	TEXT("Enable Integrate."),
	ECVF_Scalability | ECVF_RenderThreadSafe
);

#if PLATFORM_ANDROID || PLATFORM_IOS
int32 GSmartStochasticProbeInterpolation = 1;
#else
int32 GSmartStochasticProbeInterpolation = 0;
#endif
FAutoConsoleVariableRef GVarSmartStochasticProbeInterpolation(
	TEXT("r.SmartGI.FinalGather.Integrate.Stochastic"),
	GSmartStochasticProbeInterpolation,
	TEXT("Enable Stochastic Integrate."),
	ECVF_Scalability | ECVF_RenderThreadSafe
);

#if PLATFORM_ANDROID || PLATFORM_IOS
int32 GSmartReflectionRoughness = 0;
#else
int32 GSmartReflectionRoughness = 1;
#endif
FAutoConsoleVariableRef GVarSmartReflectionRoughness(
	TEXT("r.SmartGI.FinalGather.Integrate.ReflectionRoughness"),
	GSmartReflectionRoughness,
	TEXT("Enable ReflectionRoughness Integrate."),
	ECVF_Scalability | ECVF_RenderThreadSafe
);

int32 GSmartNumSpecularSamples = 4;
FAutoConsoleVariableRef GVarSmartNumSpecularSamples(
	TEXT("r.SmartGI.Reflections.NumSpecularSamples"),
	GSmartNumSpecularSamples,
	TEXT(""),
	ECVF_Scalability | ECVF_RenderThreadSafe
);

float GSmartScreenProbeMaxRoughnessToEvaluateRoughSpecular = .8f;
FAutoConsoleVariableRef GVarSmartRoughnessToEvaluateRoughSpecular(
	TEXT("r.SmartGI.Reflections.MaxRoughnessToEvaluateRoughSpecular"),
	GSmartScreenProbeMaxRoughnessToEvaluateRoughSpecular,
	TEXT("Maximum roughness value to evaluate rough specular in Screen Probe Gather"),
	ECVF_Scalability | ECVF_RenderThreadSafe
);

extern int32 GSmartInterpolationMethod;

class FSmartFinalGatherIntegrateCS : public FGlobalShader
{
	DECLARE_GLOBAL_SHADER(FSmartFinalGatherIntegrateCS)
		SHADER_USE_PARAMETER_STRUCT(FSmartFinalGatherIntegrateCS, FGlobalShader)

		BEGIN_SHADER_PARAMETER_STRUCT(FParameters, )
		SHADER_PARAMETER_RDG_TEXTURE_UAV(RWTexture2D<float4>, RWDiffuseIndirect)
		SHADER_PARAMETER_RDG_TEXTURE_UAV(RWTexture2D<float3>, RWBackfaceDiffuseIndirect)
		SHADER_PARAMETER_RDG_TEXTURE_UAV(RWTexture2D<float3>, RWRoughSpecularIndirect)
		SHADER_PARAMETER(FVector4f, StateReuseFrameIndexMod8)
		SHADER_PARAMETER(FVector4f, NeedUpdateProbeOffset)
		SHADER_PARAMETER_RDG_TEXTURE(Texture2D<uint>, RadianceMask)
		SHADER_PARAMETER_STRUCT_INCLUDE(FSmartScreenProbeParameters, ScreenProbeParameters)
		SHADER_PARAMETER_STRUCT_INCLUDE(FSmartFinalGatherParameters, FinalGatherParameters)
		SHADER_PARAMETER_STRUCT_REF(FViewUniformShaderParameters, View)
		SHADER_PARAMETER_RDG_UNIFORM_BUFFER(FSceneTextureUniformParameters, SceneTexturesStruct)
		SHADER_PARAMETER_RDG_UNIFORM_BUFFER(FMobileSceneTextureUniformParameters, MobileSceneTextures)
		SHADER_PARAMETER(float, FullResolutionJitterWidth)
		SHADER_PARAMETER(float, ResolutionScale)
		SHADER_PARAMETER(float, MaxRoughnessToTrace)
		SHADER_PARAMETER(float, RoughnessFadeLength)
		SHADER_PARAMETER(float, MaxRoughnessToEvaluateRoughSpecular)
		SHADER_PARAMETER(float, SmartGIIntensityScale)
		RDG_BUFFER_ACCESS(IndirectArgs, ERHIAccess::IndirectArgs)
	END_SHADER_PARAMETER_STRUCT()

	static bool ShouldCompilePermutation(const FGlobalShaderPermutationParameters& Parameters)
	{
		return DoesPlatformSupportSmartGI(Parameters.Platform);
	}

	class FComputeRoughSpecular : SHADER_PERMUTATION_BOOL("COMPUTE_ROUGH_SPECULAR");
	class FStochasticProbeInterpolation : SHADER_PERMUTATION_BOOL("STOCHASTIC_PROBE_INTERPOLATION");
	class FDebugScreenProbePlacement : SHADER_PERMUTATION_BOOL("DEBUG_VISUALIZE_SCREEN_PROBE_PLACEMENT");
	class FNumSpecularSamples : SHADER_PERMUTATION_SPARSE_INT("NUM_SPECULAR_SAMPLES", 1, 2, 4, 8, 16, 32);
	class FScreenProbeMoving : SHADER_PERMUTATION_BOOL("ENABLE_PROBE_MOVING");
	class FSupportBackfaceDiffuse : SHADER_PERMUTATION_BOOL("SUPPORT_BACKFACE_DIFFUSE");
	class FScreenProbeReusse : SHADER_PERMUTATION_BOOL("SCREEN_PROBE_REUSE");
	//class FSmartInterpolationMethod : SHADER_PERMUTATION_BOOL("SMARTER_INTERPOLATION_METHOD");
	class FProbeIrradianceFormat : SHADER_PERMUTATION_ENUM_CLASS("PROBE_IRRADIANCE_FORMAT", ESmartIrradianceFormat);

	using FPermutationDomain = TShaderPermutationDomain<FComputeRoughSpecular, FStochasticProbeInterpolation, FDebugScreenProbePlacement, FNumSpecularSamples, FScreenProbeMoving, FSupportBackfaceDiffuse/*, FSmartInterpolationMethod*/, FProbeIrradianceFormat,FScreenProbeReusse>;
	
	static void ModifyCompilationEnvironment(const FGlobalShaderPermutationParameters& Parameters, FShaderCompilerEnvironment& OutEnvironment)
	{
		FGlobalShader::ModifyCompilationEnvironment(Parameters, OutEnvironment);
		OutEnvironment.SetDefine(TEXT("THREADGROUP_SIZE"), GetGroupSize());
	}

	static int32 GetGroupSize()
	{
		return 8;
	}
};

IMPLEMENT_GLOBAL_SHADER(FSmartFinalGatherIntegrateCS, "/Plugin/SmartGI/Private/SmartGI/SmartFinalGatherIntegrate.usf", "LightingIntegrateCS", SF_Compute);

class FSmartFinalGatherIntegratePS : public FGlobalShader
{
public:
	DECLARE_GLOBAL_SHADER(FSmartFinalGatherIntegratePS);
	SHADER_USE_PARAMETER_STRUCT(FSmartFinalGatherIntegratePS, FGlobalShader)

	class FComputeRoughSpecular : SHADER_PERMUTATION_BOOL("COMPUTE_ROUGH_SPECULAR");
	class FStochasticProbeInterpolation : SHADER_PERMUTATION_BOOL("STOCHASTIC_PROBE_INTERPOLATION");
	class FDebugScreenProbePlacement : SHADER_PERMUTATION_BOOL("DEBUG_VISUALIZE_SCREEN_PROBE_PLACEMENT");
	class FNumSpecularSamples : SHADER_PERMUTATION_SPARSE_INT("NUM_SPECULAR_SAMPLES", 1, 2, 4, 8, 16, 32);
	class FScreenProbeMoving : SHADER_PERMUTATION_BOOL("ENABLE_PROBE_MOVING");
	class FSupportBackfaceDiffuse : SHADER_PERMUTATION_BOOL("SUPPORT_BACKFACE_DIFFUSE");
	class FScreenProbeReusse : SHADER_PERMUTATION_BOOL("SCREEN_PROBE_REUSE");
	//class FSmartInterpolationMethod : SHADER_PERMUTATION_BOOL("SMARTER_INTERPOLATION_METHOD");
	class FProbeIrradianceFormat : SHADER_PERMUTATION_ENUM_CLASS("PROBE_IRRADIANCE_FORMAT", ESmartIrradianceFormat);

	using FPermutationDomain = TShaderPermutationDomain<FComputeRoughSpecular, FStochasticProbeInterpolation, FDebugScreenProbePlacement, FNumSpecularSamples, FScreenProbeMoving, FSupportBackfaceDiffuse/*, FSmartInterpolationMethod*/, FProbeIrradianceFormat,FScreenProbeReusse>;

	BEGIN_SHADER_PARAMETER_STRUCT(FParameters, )
		SHADER_PARAMETER_STRUCT_INCLUDE(FSmartScreenProbeParameters, ScreenProbeParameters)
		SHADER_PARAMETER_STRUCT_INCLUDE(FSmartFinalGatherParameters, FinalGatherParameters)
		SHADER_PARAMETER(FVector4f, StateReuseFrameIndexMod8)
		SHADER_PARAMETER(FVector4f, NeedUpdateProbeOffset)
		SHADER_PARAMETER_RDG_TEXTURE(Texture2D<uint>, RadianceMask)
		SHADER_PARAMETER_STRUCT_REF(FViewUniformShaderParameters, View)
		SHADER_PARAMETER_RDG_UNIFORM_BUFFER(FSceneTextureUniformParameters, SceneTexturesStruct)
		SHADER_PARAMETER_RDG_UNIFORM_BUFFER(FMobileSceneTextureUniformParameters, MobileSceneTextures)
		SHADER_PARAMETER(float, FullResolutionJitterWidth)
		SHADER_PARAMETER(float, ResolutionScale)
		SHADER_PARAMETER(float, MaxRoughnessToTrace)
		SHADER_PARAMETER(float, RoughnessFadeLength)
		SHADER_PARAMETER(float, MaxRoughnessToEvaluateRoughSpecular)
		SHADER_PARAMETER(float, SmartGIIntensityScale)
		RENDER_TARGET_BINDING_SLOTS()
	END_SHADER_PARAMETER_STRUCT()

	static bool ShouldCompilePermutation(const FGlobalShaderPermutationParameters& Parameters)
	{
		return DoesPlatformSupportSmartGI(Parameters.Platform);
	}

};

IMPLEMENT_GLOBAL_SHADER(FSmartFinalGatherIntegratePS, "/Plugin/SmartGI/Private/SmartGI/SmartFinalGatherIntegrate.usf", "LightingIntegratePS", SF_Pixel);

void FSmartFinalGather::InterpolateAndIntegrate(
	FRDGBuilder& GraphBuilder,
	FSmartSceneContext& SceneContext,
	FViewInfo& View,
	FSmartScreenProbeParameters ScreenProbeParameters,
	FSmartFinalGatherParameters FinalGatherParameters,
	FRDGTextureRef DiffuseIndirect,
	FRDGTextureRef BackfaceDiffuseIndirect,
	FRDGTextureRef RoughSpecularIndirect,
	FRDGTextureRef RadianceMask,
	bool bScreenProbeReuse
	)
{
	const bool bIntegrate = GSmartFinalGatherIntegrate != 0;
	const bool bSupportBackfaceDiffuse = BackfaceDiffuseIndirect != nullptr;

	if (bIntegrate)
	{
		SCOPE_CYCLE_COUNTER(STAT_SmartGI_Integrate);

		if (GSmartGIFinalGatherUseCS)
		{
			FSmartFinalGatherTemporalState& FinalGatherState = View.ViewState->SmartGI.FinalGatherState;
			FSmartFinalGatherIntegrateCS::FParameters* CSPassParameters = GraphBuilder.AllocParameters<FSmartFinalGatherIntegrateCS::FParameters>();
			CSPassParameters->RWDiffuseIndirect = GraphBuilder.CreateUAV(FRDGTextureUAVDesc(DiffuseIndirect));
			CSPassParameters->RWBackfaceDiffuseIndirect = bSupportBackfaceDiffuse ? GraphBuilder.CreateUAV(FRDGTextureUAVDesc(BackfaceDiffuseIndirect), ERDGUnorderedAccessViewFlags::SkipBarrier) : nullptr;
			CSPassParameters->RWRoughSpecularIndirect = GraphBuilder.CreateUAV(FRDGTextureUAVDesc(RoughSpecularIndirect));
			CSPassParameters->FinalGatherParameters = FinalGatherParameters;

			CSPassParameters->ScreenProbeParameters = ScreenProbeParameters;
			CSPassParameters->View = View.ViewUniformBuffer;
			CSPassParameters->SceneTexturesStruct = SceneContext.GetUniformBuffer();
			CSPassParameters->MobileSceneTextures = SceneContext.GetMobileUniformBuffer();
			CSPassParameters->FullResolutionJitterWidth = SmartFinalGather::GetScreenProbeFullResolutionJitterWidth(View);
			CSPassParameters->ResolutionScale = GFinalGatherResolutionScale;
			CSPassParameters->RadianceMask=RadianceMask;
			CSPassParameters->NeedUpdateProbeOffset= FinalGatherState.NeedUpdateProbeOffset;
			CSPassParameters->StateReuseFrameIndexMod8=FinalGatherState.StateReuseFrameIndexMod8;

			CSPassParameters->MaxRoughnessToTrace = GSmartReflectionMaxRoughnessToTrace;
			CSPassParameters->RoughnessFadeLength = GSmartReflectionRoughnessFadeLength;
			CSPassParameters->MaxRoughnessToEvaluateRoughSpecular = GSmartScreenProbeMaxRoughnessToEvaluateRoughSpecular;
			CSPassParameters->SmartGIIntensityScale = View.FinalPostProcessSettings.SmartIntensityScale;//SmartGI::GetSmartGIIntensityScale();

			FSmartFinalGatherIntegrateCS::FPermutationDomain CSPermutationVector;
			CSPermutationVector.Set< FSmartFinalGatherIntegrateCS::FComputeRoughSpecular >(GSmartReflectionRoughness > 0);
			CSPermutationVector.Set< FSmartFinalGatherIntegrateCS::FDebugScreenProbePlacement >(GSmartVisualizeScreenProbePlacement > 0);
			CSPermutationVector.Set< FSmartFinalGatherIntegrateCS::FStochasticProbeInterpolation >(GSmartStochasticProbeInterpolation > 0);
			CSPermutationVector.Set< FSmartFinalGatherIntegrateCS::FNumSpecularSamples >(GSmartNumSpecularSamples);
			CSPermutationVector.Set< FSmartFinalGatherIntegrateCS::FScreenProbeMoving>(GSmartFilterCalculateMoving != 0);
			CSPermutationVector.Set< FSmartFinalGatherIntegrateCS::FSupportBackfaceDiffuse>(bSupportBackfaceDiffuse);
			CSPermutationVector.Set< FSmartFinalGatherIntegrateCS::FScreenProbeReusse>(bScreenProbeReuse);
			//CSPermutationVector.Set< FSmartFinalGatherIntegrateCS::FSmartInterpolationMethod>(GSmartInterpolationMethod != 0);
			CSPermutationVector.Set< FSmartFinalGatherIntegrateCS::FProbeIrradianceFormat>(SmartFinalGather::GetIrradianceFormat(View.Family->EngineShowFlags));
			auto ComputeShader = View.ShaderMap->GetShader<FSmartFinalGatherIntegrateCS>(CSPermutationVector);

			FComputeShaderUtils::AddPass(
				GraphBuilder,
				RDG_EVENT_NAME("Integrate CS"),
				ComputeShader,
				CSPassParameters,
				FComputeShaderUtils::GetGroupCount(View.ViewRect.Scale(GFinalGatherResolutionScale).Size(), FSmartFinalGatherIntegrateCS::GetGroupSize()));
			if(bScreenProbeReuse)
			{
				if(FinalGatherState.NeedUpdateProbeOffset.X==0&&FinalGatherState.NeedUpdateProbeOffset.Y==0)
				{
					FinalGatherState.PreNeedUpdateProbeOffset=FVector4f(0, 0, 0, 0);
					FinalGatherState.NeedUpdateProbeOffset=FVector4f(1, 0, 0, 0);
					uint32 FrameIndex = View.ViewState->GetFrameIndex(); 
					FinalGatherState.StateReuseFrameIndexMod8=FVector4f(FrameIndex%8,0,0,0);
				}
				else if(FinalGatherState.NeedUpdateProbeOffset.X==1&&FinalGatherState.NeedUpdateProbeOffset.Y==0)
				{
					FinalGatherState.PreNeedUpdateProbeOffset=FVector4f(1, 0, 0, 0);
					FinalGatherState.NeedUpdateProbeOffset=FVector4f(0, 1, 0, 0);
				}
				else if(FinalGatherState.NeedUpdateProbeOffset.X==0&&FinalGatherState.NeedUpdateProbeOffset.Y==1)
				{
					FinalGatherState.PreNeedUpdateProbeOffset=FVector4f(0, 1, 0, 0);
					FinalGatherState.NeedUpdateProbeOffset=FVector4f(1, 1, 0, 0);
				}
				else
				{
					FinalGatherState.PreNeedUpdateProbeOffset=FVector4f(1, 1, 0, 0);
					FinalGatherState.NeedUpdateProbeOffset=FVector4f(0, 0, 0, 0);
				}
			}
		}
		else
		{
			FSmartFinalGatherTemporalState& FinalGatherState = View.ViewState->SmartGI.FinalGatherState;
			FSmartFinalGatherIntegratePS::FParameters* PSPassParameters = GraphBuilder.AllocParameters<FSmartFinalGatherIntegratePS::FParameters>();
			PSPassParameters->RenderTargets[0] = FRenderTargetBinding(DiffuseIndirect, ERenderTargetLoadAction::ELoad);
			PSPassParameters->RenderTargets[1] = FRenderTargetBinding(RoughSpecularIndirect, ERenderTargetLoadAction::ELoad);
			if (bSupportBackfaceDiffuse)
			{
				PSPassParameters->RenderTargets[2] = FRenderTargetBinding(BackfaceDiffuseIndirect, ERenderTargetLoadAction::ELoad);
			}

			PSPassParameters->FinalGatherParameters = FinalGatherParameters;
			PSPassParameters->ScreenProbeParameters = ScreenProbeParameters;
			PSPassParameters->View = View.ViewUniformBuffer;
			PSPassParameters->SceneTexturesStruct = SceneContext.GetUniformBuffer();
			PSPassParameters->MobileSceneTextures = SceneContext.GetMobileUniformBuffer();
			PSPassParameters->FullResolutionJitterWidth = SmartFinalGather::GetScreenProbeFullResolutionJitterWidth(View);
			PSPassParameters->ResolutionScale = GFinalGatherResolutionScale;
			PSPassParameters->RadianceMask=RadianceMask;
			PSPassParameters->NeedUpdateProbeOffset= FinalGatherState.NeedUpdateProbeOffset;
			PSPassParameters->StateReuseFrameIndexMod8=FinalGatherState.StateReuseFrameIndexMod8;

			PSPassParameters->MaxRoughnessToTrace = GSmartReflectionMaxRoughnessToTrace;
			PSPassParameters->RoughnessFadeLength = GSmartReflectionRoughnessFadeLength;
			PSPassParameters->MaxRoughnessToEvaluateRoughSpecular = GSmartScreenProbeMaxRoughnessToEvaluateRoughSpecular;
			PSPassParameters->SmartGIIntensityScale = View.FinalPostProcessSettings.SmartIntensityScale;//SmartGI::GetSmartGIIntensityScale();

			FSmartFinalGatherIntegratePS::FPermutationDomain PSPermutationVector;
			PSPermutationVector.Set< FSmartFinalGatherIntegratePS::FComputeRoughSpecular >(GSmartReflectionRoughness > 0);
			PSPermutationVector.Set< FSmartFinalGatherIntegratePS::FDebugScreenProbePlacement >(GSmartVisualizeScreenProbePlacement > 0);
			PSPermutationVector.Set< FSmartFinalGatherIntegratePS::FStochasticProbeInterpolation >(GSmartStochasticProbeInterpolation > 0);
			PSPermutationVector.Set< FSmartFinalGatherIntegratePS::FNumSpecularSamples >(GSmartNumSpecularSamples);
			PSPermutationVector.Set< FSmartFinalGatherIntegratePS::FScreenProbeMoving>(GSmartFilterCalculateMoving != 0);
			PSPermutationVector.Set< FSmartFinalGatherIntegratePS::FSupportBackfaceDiffuse>(bSupportBackfaceDiffuse);
			PSPermutationVector.Set< FSmartFinalGatherIntegratePS::FScreenProbeReusse>(bScreenProbeReuse);
			//PSPermutationVector.Set< FSmartFinalGatherIntegratePS::FSmartInterpolationMethod>(GSmartInterpolationMethod != 0);
			PSPermutationVector.Set< FSmartFinalGatherIntegratePS::FProbeIrradianceFormat>(SmartFinalGather::GetIrradianceFormat(View.Family->EngineShowFlags));
			
			auto PSShader = View.ShaderMap->GetShader<FSmartFinalGatherIntegratePS>(PSPermutationVector);

			auto FinalGatherViewRect = View.ViewRect.Scale(GFinalGatherResolutionScale);

			FPixelShaderUtils::AddFullscreenPass(
				GraphBuilder,
				View.ShaderMap,
				RDG_EVENT_NAME("Integrate PS"),
				PSShader,
				PSPassParameters,
				FIntRect(0,0,FinalGatherViewRect.Width(),FinalGatherViewRect.Height()));
			if(bScreenProbeReuse)
			{
				if(FinalGatherState.NeedUpdateProbeOffset.X==0&&FinalGatherState.NeedUpdateProbeOffset.Y==0)
				{
					FinalGatherState.PreNeedUpdateProbeOffset=FVector4f(0, 0, 0, 0);
					FinalGatherState.NeedUpdateProbeOffset=FVector4f(1, 0, 0, 0);
					uint32 FrameIndex = View.ViewState->GetFrameIndex(); 
					FinalGatherState.StateReuseFrameIndexMod8=FVector4f(FrameIndex%8,0,0,0);
				}
				else if(FinalGatherState.NeedUpdateProbeOffset.X==1&&FinalGatherState.NeedUpdateProbeOffset.Y==0)
				{
					FinalGatherState.PreNeedUpdateProbeOffset=FVector4f(1, 0, 0, 0);
					FinalGatherState.NeedUpdateProbeOffset=FVector4f(0, 1, 0, 0);
				}
				else if(FinalGatherState.NeedUpdateProbeOffset.X==0&&FinalGatherState.NeedUpdateProbeOffset.Y==1)
				{
					FinalGatherState.PreNeedUpdateProbeOffset=FVector4f(0, 1, 0, 0);
					FinalGatherState.NeedUpdateProbeOffset=FVector4f(1, 1, 0, 0);
				}
				else
				{
					FinalGatherState.PreNeedUpdateProbeOffset=FVector4f(1, 1, 0, 0);
					FinalGatherState.NeedUpdateProbeOffset=FVector4f(0, 0, 0, 0);
				}
			}
		}
	}
}

#endif