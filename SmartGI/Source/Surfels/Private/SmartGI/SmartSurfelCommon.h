
#pragma once

#include "SurfelDefinitionShared.ush"
#include "CoreMinimal.h"

#ifndef SHADING_PATH_DEFERRED
#define SHADING_PATH_DEFERREDT 1
#endif

// This per-surfel surfel structure will be accessed rapidly on GI lookup, so keep it as small as possible
//	But also ensure that it is 16-byte aligned for structured buffer access performance
BEGIN_SHADER_PARAMETER_STRUCT(FSurfel, )
	SHADER_PARAMETER(FVector3f, position)
	SHADER_PARAMETER(uint32, normal)

	SHADER_PARAMETER(FVector2f, finalLightingAndPrimID)
	SHADER_PARAMETER(uint32, data) // 20bit rayOffset, 8bit rayCount, 4bit radiusLevel
	SHADER_PARAMETER(uint32, LifeRecycle)
END_SHADER_PARAMETER_STRUCT()

BEGIN_SHADER_PARAMETER_STRUCT(FSurfelData, )
	SHADER_PARAMETER(FVector4f, DirectLightingAndEmissive)
	SHADER_PARAMETER(uint32, albedo) // 16bit life frames, 16bit recycle frames

	SHADER_PARAMETER(FVector3f, LocalPosition)
	SHADER_PARAMETER(uint32, LocalNormal)
END_SHADER_PARAMETER_STRUCT()

BEGIN_SHADER_PARAMETER_STRUCT(FSurfelEstimatorData, )
	SHADER_PARAMETER(FVector3f, mean)
	SHADER_PARAMETER(float, inconsistency)

	SHADER_PARAMETER(FVector3f, ShortMeanAndVariance)
	SHADER_PARAMETER(float, vbbr)
END_SHADER_PARAMETER_STRUCT()

BEGIN_SHADER_PARAMETER_STRUCT(FSurfelRayData, )
	SHADER_PARAMETER(FVector3f, direction)
	SHADER_PARAMETER(float, depth)

	SHADER_PARAMETER(FVector3f, radiance)
	SHADER_PARAMETER(uint32, surfelIndex)
END_SHADER_PARAMETER_STRUCT()

BEGIN_SHADER_PARAMETER_STRUCT(FSurfelRayDataPacked, )
	SHADER_PARAMETER(FVector4f, data)
END_SHADER_PARAMETER_STRUCT()

BEGIN_SHADER_PARAMETER_STRUCT(FSurfelGridCell, )
	SHADER_PARAMETER(uint32, count)
	SHADER_PARAMETER(uint32, offset)
	SHADER_PARAMETER(bool, bHasEmissive)
END_SHADER_PARAMETER_STRUCT()
