#include "SmartGI/SmartVoxelLighting.h"
#include "RendererPrivate.h"
#include "ScenePrivate.h"
#include "SceneUtils.h"
#include "PipelineStateCache.h"
#include "ShaderParameterStruct.h"
#include "MeshPassProcessor.h"
#include "MeshPassProcessor.inl"
#include "ShaderParameterStruct.h"
#include "PixelShaderUtils.h"
#include "VolumeLighting.h"
#include "SmartGI/SmartCommon.h"
#include "SmartGI/SmartTracingInput.h"
#include "SmartGI/SmartViewState.h"
#include "SmartGI/SmartSceneContext.h"

extern int32 GSmartDebugIncrementUpdate;
extern int32 GSmartSceneVoxelLightingReset;
extern int32 GSmartVoxelizeGBufferMinClipmap;

int32 GSmartVoxelizeGBufferTileSize = 8;
FAutoConsoleVariableRef CVarSmartVoxelizeGBufferTileSize(
	TEXT("r.SmartGI.VoxelLighting.VoxelizeGBufferTileSize"),
	GSmartVoxelizeGBufferTileSize,
	TEXT(""),
	ECVF_Scalability | ECVF_RenderThreadSafe
);

float GSmartVoxelizeGBufferLightingScale = 1.0f;
FAutoConsoleVariableRef CVarSmartVoxelizeGBufferLightingScale(
	TEXT("r.SmartGI.DirectLighting.VoxelizeGBufferLightingScale"),
	GSmartVoxelizeGBufferLightingScale,
	TEXT(""),
	ECVF_Scalability | ECVF_RenderThreadSafe
);

float GSmartVoxelizeGBufferBlendFactor = 0.1f;
FAutoConsoleVariableRef CVarSmartVoxelizeGBufferBlendFactor(
	TEXT("r.SmartGI.VoxelLighting.VoxelizeGBufferBlendFactor"),
	GSmartVoxelizeGBufferBlendFactor,
	TEXT(""),
	ECVF_Scalability | ECVF_RenderThreadSafe
);

int32 GSmartVoxelizeGBufferWriteColor = 1;
FAutoConsoleVariableRef CVarSmartVoxelizeGBufferWriteColor(
	TEXT("r.SmartGI.VoxelLighting.VoxelizeGBufferWriteColor"),
	GSmartVoxelizeGBufferWriteColor,
	TEXT(""),
	ECVF_Scalability | ECVF_RenderThreadSafe
);

int32 GSmartVisualizeIncrementUpdate = 0;
FAutoConsoleVariableRef CVarSmartVisualizeIncrementUpdate(
	TEXT("r.SmartGI.VisualizeIncrementUpdate"),
	GSmartVisualizeIncrementUpdate,
	TEXT(""),
	ECVF_Scalability | ECVF_RenderThreadSafe
);

class FSmartVoxelizeVS : public FMeshMaterialShader
{
	DECLARE_SHADER_TYPE(FSmartVoxelizeVS, MeshMaterial);

public:

	static bool ShouldCompilePermutation(const FMeshMaterialShaderPermutationParameters& Parameters)
	{
		//@todo DynamicGI - filter
		return DoesPlatformSupportSmartGI(Parameters.Platform);
	}

	FSmartVoxelizeVS(const ShaderMetaType::CompiledShaderInitializerType& Initializer)
		: FMeshMaterialShader(Initializer)
	{
		PassUniformBuffer.Bind(Initializer.ParameterMap, FSmartClipmapParameters::FTypeInfo::GetStructMetadata()->GetShaderVariableName());
	}

	FSmartVoxelizeVS() = default;

	void GetShaderBindings(
		const FScene* Scene,
		ERHIFeatureLevel::Type FeatureLevel,
		const FPrimitiveSceneProxy* PrimitiveSceneProxy,
		const FMaterialRenderProxy& MaterialRenderProxy,
		const FMaterial& Material,
		const FMeshMaterialShaderElementData& ShaderElementData,
		FMeshDrawSingleShaderBindings& ShaderBindings) const
	{
		FMeshMaterialShader::GetShaderBindings(Scene, FeatureLevel, PrimitiveSceneProxy, MaterialRenderProxy, Material, ShaderElementData, ShaderBindings);
		ShaderBindings.Add(GetUniformBufferParameter<FSmartClipmapParameters>(), Scene->UniformBuffers.SmartClipmapUniformBuffer);
	}
};

IMPLEMENT_MATERIAL_SHADER_TYPE(, FSmartVoxelizeVS, TEXT("/Plugin/SmartGI/Private/SmartGI/SmartVoxelizerVS.usf"), TEXT("Main"), SF_Vertex);

class FSmartVoxelizePS : public FMeshMaterialShader
{
	DECLARE_SHADER_TYPE(FSmartVoxelizePS, MeshMaterial);

	BEGIN_SHADER_PARAMETER_STRUCT(FParameters, )		
		SHADER_PARAMETER_RDG_TEXTURE_UAV(RWTexture3D, RWAlbedoTexture)
		SHADER_PARAMETER_RDG_TEXTURE_UAV(RWTexture3D, RWNormalTexture)
		SHADER_PARAMETER_RDG_TEXTURE_UAV(RWTexture3D, RWEmissiveTexture)
		SHADER_PARAMETER_RDG_TEXTURE_UAV(RWTexture3D, RWOpacityTexture)
		RENDER_TARGET_BINDING_SLOTS()
	END_SHADER_PARAMETER_STRUCT()

public:

	static bool ShouldCompilePermutation(const FMeshMaterialShaderPermutationParameters& Parameters)
	{
		//@todo DynamicGI - filter
		return DoesPlatformSupportSmartGI(Parameters.Platform);
	}

	FSmartVoxelizePS(const ShaderMetaType::CompiledShaderInitializerType& Initializer)
		: FMeshMaterialShader(Initializer)
	{
		PassUniformBuffer.Bind(Initializer.ParameterMap, FSmartClipmapParameters::FTypeInfo::GetStructMetadata()->GetShaderVariableName());
		RWVoxelAlbedo.Bind(Initializer.ParameterMap, TEXT("RWAlbedoTexture"));
		RWVoxelNormal.Bind(Initializer.ParameterMap, TEXT("RWNormalTexture"));
		RWVoxelEmissive.Bind(Initializer.ParameterMap, TEXT("RWEmissiveTexture"));
		RWVoxelOpacity.Bind(Initializer.ParameterMap, TEXT("RWOpacityTexture"));
	}

	FSmartVoxelizePS() = default;

	static void ModifyCompilationEnvironment(const FMaterialShaderPermutationParameters& Parameters, FShaderCompilerEnvironment& OutEnvironment)
	{
		FMeshMaterialShader::ModifyCompilationEnvironment(Parameters, OutEnvironment);
		OutEnvironment.SetDefine(TEXT("VF_SUPPORTS_PRIMITIVE_SCENE_DATA"), 1); // for fetching primitive's LightingChannel
	}

	void GetShaderBindings(
		const FScene* Scene,
		ERHIFeatureLevel::Type FeatureLevel,
		const FPrimitiveSceneProxy* PrimitiveSceneProxy,
		const FMaterialRenderProxy& MaterialRenderProxy,
		const FMaterial& Material,
		const FMeshMaterialShaderElementData& ShaderElementData,
		FMeshDrawSingleShaderBindings& ShaderBindings) const
	{
		FMeshMaterialShader::GetShaderBindings(Scene, FeatureLevel, PrimitiveSceneProxy, MaterialRenderProxy, Material, ShaderElementData, ShaderBindings);
		ShaderBindings.Add(GetUniformBufferParameter<FSmartClipmapParameters>(), Scene->UniformBuffers.SmartClipmapUniformBuffer);
	}
protected:
	LAYOUT_FIELD(FShaderResourceParameter, RWVoxelAlbedo);
	LAYOUT_FIELD(FShaderResourceParameter, RWVoxelNormal);
	LAYOUT_FIELD(FShaderResourceParameter, RWVoxelEmissive);
	LAYOUT_FIELD(FShaderResourceParameter, RWVoxelOpacity);
};

IMPLEMENT_MATERIAL_SHADER_TYPE(, FSmartVoxelizePS, TEXT("/Plugin/SmartGI/Private/SmartGI/SmartVoxelizerPS.usf"), TEXT("Main"), SF_Pixel);


BEGIN_SHADER_PARAMETER_STRUCT(FSmartVoxelizePassParameters, )
	SHADER_PARAMETER_STRUCT_REF(FViewUniformShaderParameters, View)
	SHADER_PARAMETER_STRUCT_INCLUDE(FSmartVoxelizePS::FParameters, PS)
	SHADER_PARAMETER_RDG_UNIFORM_BUFFER(FSceneUniformParameters, Scene)
	SHADER_PARAMETER_STRUCT_INCLUDE(FInstanceCullingDrawParams, InstanceCullingDrawParams)
END_SHADER_PARAMETER_STRUCT()

class FSmartVoxelizeGBufferCS : public FGlobalShader
{
	DECLARE_GLOBAL_SHADER(FSmartVoxelizeGBufferCS)
	SHADER_USE_PARAMETER_STRUCT(FSmartVoxelizeGBufferCS, FGlobalShader)

	BEGIN_SHADER_PARAMETER_STRUCT(FParameters, )
		SHADER_PARAMETER_RDG_TEXTURE(Texture2D, SceneColorTexture)
		SHADER_PARAMETER_SAMPLER(SamplerState, SceneColorSampler)
		SHADER_PARAMETER_RDG_TEXTURE_UAV(RWTexture3D, RWAlbedoTexture)
		SHADER_PARAMETER_RDG_TEXTURE_UAV(RWTexture3D, RWNormalTexture)
		SHADER_PARAMETER_RDG_TEXTURE_UAV(RWTexture3D, RWEmissiveTexture)
		SHADER_PARAMETER_RDG_TEXTURE_UAV(RWTexture3D, RWOpacityTexture)
		SHADER_PARAMETER_STRUCT_REF(FViewUniformShaderParameters, View)
		SHADER_PARAMETER_RDG_UNIFORM_BUFFER(FMobileSceneTextureUniformParameters, MobileSceneTextures)
		SHADER_PARAMETER_STRUCT_REF(FSmartVoxelTracingParameters, VoxelTracingParameters)
		SHADER_PARAMETER(uint32, SmartVoxelizeGBufferTileSize)
		SHADER_PARAMETER(int32, SmartVoxelizeGBufferMinClipmap)
		SHADER_PARAMETER(float, BlendFactor)
		SHADER_PARAMETER(float, SmartVoxelizeGBufferLightingScale)
	END_SHADER_PARAMETER_STRUCT()

	class FVoxelizeGBufferWriteColor : SHADER_PERMUTATION_BOOL("VOXELIZE_GBUFFER_WRITE_COLOR");
	using FPermutationDomain = TShaderPermutationDomain<FVoxelizeGBufferWriteColor>;
	
	static bool ShouldCompilePermutation(const FGlobalShaderPermutationParameters& Parameters)
	{
		return DoesPlatformSupportSmartGI(Parameters.Platform);
	}

	static void ModifyCompilationEnvironment(const FGlobalShaderPermutationParameters& Parameters, FShaderCompilerEnvironment& OutEnvironment)
	{
		FGlobalShader::ModifyCompilationEnvironment(Parameters, OutEnvironment);
	}
};

IMPLEMENT_GLOBAL_SHADER(FSmartVoxelizeGBufferCS, "/Plugin/SmartGI/Private/SmartGI/SmartVoxelizeGBuffer.usf", "VoxelizeGBufferCS", SF_Compute);

void FSmartVoxelRenderer::VoxelizeGBuffer(
	FRDGBuilder& GraphBuilder,
	FSmartSceneContext& SceneContext,
	FViewInfo& View,
	FSmartTracingInputs& TracingInputs
)
{
	FSmartVoxelizeGBufferCS::FParameters* PassParameters = GraphBuilder.AllocParameters<FSmartVoxelizeGBufferCS::FParameters>();

	FVoxelLightingState& VoxelState = const_cast<FVoxelLightingState&>(Scene->VoxelState);
	PassParameters->RWAlbedoTexture = VoxelState.AlbedoTexture.GetUAV(GraphBuilder);
	PassParameters->RWNormalTexture = VoxelState.NormalTexture.GetUAV(GraphBuilder);
	PassParameters->RWOpacityTexture = VoxelState.OpacityTexture.GetUAV(GraphBuilder);
	PassParameters->RWEmissiveTexture = VoxelState.EmissiveTexture.GetUAV(GraphBuilder);

	PassParameters->View = View.ViewUniformBuffer;
	PassParameters->MobileSceneTextures = SceneContext.GetMobileUniformBuffer();
	FSmartTracingParameters TracingParameters;
	SetupSmartVoxelTracingParameters(TracingInputs, TracingParameters);
	PassParameters->VoxelTracingParameters = TracingParameters.VoxelTracingParameters;
	PassParameters->SmartVoxelizeGBufferTileSize = GSmartVoxelizeGBufferTileSize;
	PassParameters->SmartVoxelizeGBufferMinClipmap = GSmartVoxelizeGBufferMinClipmap;
	PassParameters->BlendFactor = GSmartVoxelizeGBufferBlendFactor;
	
	//@Note: SceneColor = DI, PrevSceneColor = DI + GI + Skylight
	// if we have PrevSceneColor, then reuse it for better GBuffer-based VoxelizeLighting.
	FRDGTextureRef InputColor = SceneContext.GetSceneColor(GraphBuilder);
	FIntPoint ViewportOffset = View.ViewRect.Min;
	FIntPoint ViewportExtent = View.ViewRect.Size();
	FIntPoint PrevColorBufferSize = SceneContext.GetBufferSizeXY();
	/*if (View.PrevViewInfo.CustomSSRInput.IsValid())
	{
		InputColor = GraphBuilder.RegisterExternalTexture(View.PrevViewInfo.CustomSSRInput.RT[0]);
		ViewportOffset = View.PrevViewInfo.CustomSSRInput.ViewportRect.Min;
		ViewportExtent = View.PrevViewInfo.CustomSSRInput.ViewportRect.Size();
		PrevColorBufferSize = InputColor->Desc.Extent;
	}
	else if (View.PrevViewInfo.TSRHistory.IsValid())
	{
		InputColor = GraphBuilder.RegisterExternalTexture(View.PrevViewInfo.TSRHistory.ColorArray);
		ViewportOffset = View.PrevViewInfo.TSRHistory.OutputViewportRect.Min;
		ViewportExtent = View.PrevViewInfo.TSRHistory.OutputViewportRect.Size();
		PrevColorBufferSize = InputColor->Desc.Extent;
	}
	else if (View.PrevViewInfo.TemporalAAHistory.IsValid())
	{
		InputColor = GraphBuilder.RegisterExternalTexture(View.PrevViewInfo.TemporalAAHistory.RT[0]);
		ViewportOffset = View.PrevViewInfo.TemporalAAHistory.ViewportRect.Min;
		ViewportExtent = View.PrevViewInfo.TemporalAAHistory.ViewportRect.Size();
		PrevColorBufferSize = View.PrevViewInfo.TemporalAAHistory.ReferenceBufferSize;
	}
	else if (View.PrevViewInfo.ScreenSpaceRayTracingInput.IsValid())
	{
		InputColor = GraphBuilder.RegisterExternalTexture(View.PrevViewInfo.ScreenSpaceRayTracingInput);
		ViewportOffset = View.PrevViewInfo.ViewRect.Min;
		ViewportExtent = View.PrevViewInfo.ViewRect.Size();
		PrevColorBufferSize = InputColor->Desc.Extent;
	}*/
	if (View.PrevViewInfo.ScreenSpaceRayTracingInput.IsValid())
	{
		InputColor = GraphBuilder.RegisterExternalTexture(View.PrevViewInfo.ScreenSpaceRayTracingInput);
		ViewportOffset = View.PrevViewInfo.ViewRect.Min;
		ViewportExtent = View.PrevViewInfo.ViewRect.Size();
		PrevColorBufferSize = InputColor->Desc.Extent;
	}
	PassParameters->SceneColorTexture = InputColor;
	PassParameters->SceneColorSampler = TStaticSamplerState<SF_Point>::GetRHI();
	PassParameters->SmartVoxelizeGBufferLightingScale = GSmartVoxelizeGBufferLightingScale;

	FSmartVoxelizeGBufferCS::FPermutationDomain PermutationVector;
	PermutationVector.Set<FSmartVoxelizeGBufferCS::FVoxelizeGBufferWriteColor>(GSmartVoxelizeGBufferWriteColor > 0);

	auto ComputeShader = View.ShaderMap->GetShader<FSmartVoxelizeGBufferCS>(PermutationVector);
	const FIntPoint VoxelizeGBufferViewportDimensions(
		FMath::DivideAndRoundUp(View.ViewRect.Size().X, GSmartVoxelizeGBufferTileSize * 8),
		FMath::DivideAndRoundUp(View.ViewRect.Size().Y, GSmartVoxelizeGBufferTileSize * 8));
	FComputeShaderUtils::AddPass(
		GraphBuilder,
		RDG_EVENT_NAME("Fast Voxelization"),
		ERDGPassFlags::Compute,
		ComputeShader,
		PassParameters,
		FIntVector(VoxelizeGBufferViewportDimensions.X, VoxelizeGBufferViewportDimensions.Y, 1));

}

extern void UpdateVoxelizeScene(
	FRDGBuilder& GraphBuilder,
	FViewInfo& View,
	FVoxelLightingState& VoxelState,
	FSmartTracingInputs& TracingInputs,
	int32 ClipmapIndex,
	const FSmartVoxelLightingClipmapState& Clipmap,
	FClipmapRenderInfo* ClipmapRenderInfo,
	bool bForceFullClipmapUpdate
);

void FSmartVoxelRenderer::VoxelizeScene(
	FRDGBuilder& GraphBuilder,
	FSmartSceneContext& SceneContext,
	FViewInfo& View,
	FInstanceCullingManager& InstanceCullingManager,
	FSmartTracingInputs& TracingInputs,
	int32 ClipmapIndex,
	bool bForceFullClipmapUpdate
)
{
	SCOPE_CYCLE_COUNTER(STAT_SmartGI_Voxelize);

	FVoxelLightingState& VoxelState = const_cast<FVoxelLightingState&>(Scene->VoxelState);

	FClipmapRenderInfo* ClipmapRenderInfo = ClipmapRenderInfos[ClipmapIndex];

	const FSmartVoxelLightingClipmapState& Clipmap = VoxelState.VoxelLightingClipmapState[ClipmapIndex];

	if(Clipmap.UpdateBounds.Num() > 0)
	{
		SCOPE_CYCLE_COUNTER(STAT_SmartGI_VoxelizeClear);

		UpdateVoxelizeScene(GraphBuilder,View,VoxelState,TracingInputs,ClipmapIndex,Clipmap, ClipmapRenderInfo,bForceFullClipmapUpdate);
	}

	FSceneRenderer* Renderer = SceneRenderer;
	FRHICommandListImmediate& RHICmdList = FRHICommandListExecutor::GetImmediateCommandList();

#if WITH_EDITOR
	if (GDynamicRHI->GetName() == FString(TEXT("Vulkan")) && RHICmdList.Bypass())
	{
		//return;	
	}
#endif // WITH_EDITOR

	if(Clipmap.UpdateBounds.Num() > 0)
	{
		MeshCollector.Start(RHICmdList, DynamicVertexBuffer, DynamicIndexBuffer, DynamicReadBuffer);
		
		BuildVoxelizeCommands(GraphBuilder,View,InstanceCullingManager,TracingInputs,ClipmapIndex,Clipmap.UpdateBounds);

		MeshCollector.Finish();
		DynamicIndexBuffer.Commit();
		DynamicVertexBuffer.Commit();
		DynamicReadBuffer.Commit(RHICmdList);
	}

	auto& VisibleMeshDrawCommands = ClipmapRenderInfo->VisibleMeshDrawCommands;
	auto& VoxelizeMeshCommandPass = ClipmapRenderInfo->VoxelizeMeshCommandPass;

	if ((VisibleMeshDrawCommands.Num() > 0 || VoxelizeMeshCommandPass.HasAnyDraw()) && (bForceFullClipmapUpdate || !(GSmartVisualizeIncrementUpdate && GSmartDebugIncrementUpdate)))
	{
		FSmartVoxelizePassParameters* PassParameters = GraphBuilder.AllocParameters<FSmartVoxelizePassParameters>();
		FSmartVoxelizePassParameters* DynamicPassParameters = GraphBuilder.AllocParameters<FSmartVoxelizePassParameters>();
		PassParameters->View = View.ViewUniformBuffer;
		PassParameters->Scene = GetSceneUniformBufferRef(GraphBuilder, View);
		PassParameters->PS.RWAlbedoTexture = VoxelState.AlbedoTexture.GetUAV(GraphBuilder);
		PassParameters->PS.RWNormalTexture = VoxelState.NormalTexture.GetUAV(GraphBuilder);
		PassParameters->PS.RWEmissiveTexture = VoxelState.EmissiveTexture.GetUAV(GraphBuilder);
		PassParameters->PS.RWOpacityTexture = VoxelState.OpacityTexture.GetUAV(GraphBuilder);		

		FRHIBuffer* PrimitiveIdVertexBuffer = nullptr;
		FInstanceCullingResult InstanceCullingResult;		
		if (Scene->GPUScene.IsEnabled())
		{
			static const FName PassName(GetMeshPassName(EMeshPass::SmartVoxelize));
			ClipmapRenderInfo->InstanceCullingContext = MakeUnique<FInstanceCullingContext>(PassName, View.GetShaderPlatform(), nullptr, TArrayView<const int32>(&View.GPUSceneViewId, 1), nullptr);
			
			int32 MaxInstances = 0;
			int32 VisibleMeshDrawCommandsNum = 0;
			int32 NewPassVisibleMeshDrawCommandsNum = 0;
			
			ClipmapRenderInfo->InstanceCullingContext->SetupDrawCommands(VisibleMeshDrawCommands, true, &Scene->GPUScene.GetScene(), MaxInstances, VisibleMeshDrawCommandsNum, NewPassVisibleMeshDrawCommandsNum);
			ClipmapRenderInfo->InstanceCullingContext->BuildRenderingCommands(GraphBuilder, Scene->GPUScene, View.DynamicPrimitiveCollector.GetInstanceSceneDataOffset(), View.DynamicPrimitiveCollector.NumInstances(), InstanceCullingResult);
			VoxelizeMeshCommandPass.BuildRenderingCommands(GraphBuilder, Scene->GPUScene, ClipmapRenderInfo->InstanceCullingDrawParams);
		}
		else
		{
			auto& MeshDrawPrimitiveIds = ClipmapRenderInfo->MeshDrawPrimitiveIds;
			check(MeshDrawPrimitiveIds.Num() == ClipmapRenderInfo->VisibleMeshDrawCommands.Num());
			// Prepare primitive Id VB for rendering mesh draw commands.
			if (MeshDrawPrimitiveIds.Num() > 0)
			{
				const uint32 PrimitiveIdBufferDataSize = MeshDrawPrimitiveIds.Num() * sizeof(int32);

				FPrimitiveIdVertexBufferPoolEntry Entry = GPrimitiveIdVertexBufferPool.Allocate(GraphBuilder.RHICmdList, PrimitiveIdBufferDataSize);
				PrimitiveIdVertexBuffer = Entry.BufferRHI;

				void* RESTRICT Data = GraphBuilder.RHICmdList.LockBuffer(PrimitiveIdVertexBuffer, 0, PrimitiveIdBufferDataSize, RLM_WriteOnly);
				FMemory::Memcpy(Data, MeshDrawPrimitiveIds.GetData(), PrimitiveIdBufferDataSize);
				GraphBuilder.RHICmdList.UnlockBuffer(PrimitiveIdVertexBuffer);

				GPrimitiveIdVertexBufferPool.ReturnToFreeList(Entry);
			}	
		}

		InstanceCullingResult.GetDrawParameters(PassParameters->InstanceCullingDrawParams);

		int32 RasterizationFactor = FMath::Pow(2.0f, ClipmapIndex + 1);
		FIntVector VoxelGridResolution = TracingInputs.VoxelGridResolution;
		FIntPoint VoxelRasterResolution;

		for (int32 Direction = 0; Direction < 3; ++Direction)
		{
			if(Direction == 0)
			{
				VoxelRasterResolution = FIntPoint(VoxelGridResolution.Y, VoxelGridResolution.Z);
			}
			else if(Direction == 1)
			{
				VoxelRasterResolution = FIntPoint(VoxelGridResolution.X, VoxelGridResolution.Z);
			}
			else
			{
				VoxelRasterResolution = FIntPoint(VoxelGridResolution.Y, VoxelGridResolution.X);
			}

			VoxelRasterResolution *= RasterizationFactor;
			if (GRHIRequiresRenderTargetForPixelShaderUAVs || IsAndroidOpenGLESPlatform(View.GetShaderPlatform()))
			{
				FRDGTextureDesc DummyDesc = FRDGTextureDesc::Create2D(
					VoxelRasterResolution,
					PF_R8G8B8A8,
					FClearValueBinding::Black,
					TexCreate_RenderTargetable,
					1,
					1);
				PassParameters->PS.RenderTargets[0] = FRenderTargetBinding(GraphBuilder.CreateTexture(DummyDesc, TEXT("Dummy0")), ERenderTargetLoadAction::ENoAction);
			}

			auto BindRenderTargets = [](FMeshCommandOneFrameArray& MeshDrawCommands, FSmartVoxelizePassParameters* PassParameters)
			{
				for (int32 DrawCommandIndex = 0; DrawCommandIndex < MeshDrawCommands.Num(); DrawCommandIndex++)
				{
					const FVisibleMeshDrawCommand& VisibleMeshDrawCommand = MeshDrawCommands[DrawCommandIndex];
					int32 DataOffset = 0;
					FMeshDrawShaderBindings& ShaderBindings = const_cast<FMeshDrawShaderBindings&>(VisibleMeshDrawCommand.MeshDrawCommand->ShaderBindings);
					// GetSingleShaderBindings need to be called in order, so skip vertex stage first.
					ShaderBindings.GetSingleShaderBindings(SF_Vertex, DataOffset);
					FMeshDrawSingleShaderBindings SingleShaderBindings = ShaderBindings.GetSingleShaderBindings(SF_Pixel, DataOffset);
					SingleShaderBindings.Add(0, PassParameters->PS.RWAlbedoTexture->GetRHI());
					SingleShaderBindings.Add(1, PassParameters->PS.RWNormalTexture->GetRHI());
					SingleShaderBindings.Add(2, PassParameters->PS.RWEmissiveTexture->GetRHI());
					SingleShaderBindings.Add(3, PassParameters->PS.RWOpacityTexture->GetRHI());
				}
			};

			FSmartClipmapParameters* ClipmapParameters = GraphBuilder.AllocParameters<FSmartClipmapParameters>();
			ClipmapParameters->ClipmapIndex = ClipmapIndex;
			ClipmapParameters->ClipmapGridResolution = TracingInputs.VoxelGridResolution;
			ClipmapParameters->ClipmapWorldToUVScale = (FVector3f)TracingInputs.ClipmapWorldToUVScale[ClipmapIndex];
			ClipmapParameters->ClipmapWorldToUVBias = (FVector3f)TracingInputs.ClipmapWorldToUVBias[ClipmapIndex];
			ClipmapParameters->Direction = Direction;

			if (ClipmapRenderInfo->VisibleMeshDrawCommands.Num() > 0)
			{
				GraphBuilder.AddPass(
					RDG_EVENT_NAME("VoxelizeScene Clipmap:%d Axis:%d", ClipmapIndex, Direction),
					PassParameters,
					ERDGPassFlags::Raster | ERDGPassFlags::NeverCull,
					[this, Renderer, &View, Direction, BindRenderTargets, PassParameters, PrimitiveIdVertexBuffer, ClipmapRenderInfo, ClipmapParameters, VoxelRasterResolution](const FRDGPass* InPass, FRHICommandListImmediate& RHICmdList)
					{
						SCOPE_CYCLE_COUNTER(STAT_SmartGI_VoxelizeDraw);
						RHICmdList.SetViewport(0.0f, 0.0f, 0.0f, VoxelRasterResolution.X, VoxelRasterResolution.Y, 1.0f);

						Renderer->Scene->UniformBuffers.SmartClipmapUniformBuffer.UpdateUniformBufferImmediate(RHICmdList, *ClipmapParameters);
						auto& VisibleMeshDrawCommands = ClipmapRenderInfo->VisibleMeshDrawCommands;

						if (Direction == 0)
						{
							BindRenderTargets(VisibleMeshDrawCommands, PassParameters);
						}

						FGraphicsMinimalPipelineStateSet GraphicsMinimalPipelineStateSet;
						const FViewInfo* ViewInfo = static_cast<const FViewInfo*>(&View);
						if (Scene->GPUScene.IsEnabled())
						{
							ClipmapRenderInfo->InstanceCullingContext->SubmitDrawCommands(
								VisibleMeshDrawCommands,
								GraphicsMinimalPipelineStateSet,
								GetMeshDrawCommandOverrideArgs(PassParameters->InstanceCullingDrawParams),
								0,
								VisibleMeshDrawCommands.Num(),
								1,
								RHICmdList);
						}
						else
						{
							FMeshDrawCommandSceneArgs SceneArgs;
							SceneArgs.PrimitiveIdsBuffer = PrimitiveIdVertexBuffer;
							SubmitMeshDrawCommandsRange(
								VisibleMeshDrawCommands,
								GraphicsMinimalPipelineStateSet,
								SceneArgs,
								FInstanceCullingContext::GetInstanceIdBufferStride(Scene->GetShaderPlatform()),
								false,
								0,
								VisibleMeshDrawCommands.Num(),
								1,
								RHICmdList);
						}
					});
			}			

			/* // Disable dynamic elements voxelization currently as it may voxelize geometries more than once
			if (ClipmapRenderInfo->VoxelizeMeshCommandPass.HasAnyDraw())
			{
				*DynamicPassParameters = *PassParameters;
				DynamicPassParameters->InstanceCullingDrawParams = ClipmapRenderInfo->InstanceCullingDrawParams;

				GraphBuilder.AddPass(
					RDG_EVENT_NAME("VoxelizeDynamic Clipmap:%d Axis:%d", ClipmapIndex, Direction),
					DynamicPassParameters,
					ERDGPassFlags::Raster | ERDGPassFlags::NeverCull,
					[this, Renderer, &View, Direction, DynamicPassParameters, BindRenderTargets, ClipmapRenderInfo, ClipmapParameters, VoxelRasterResolution](const FRDGPass* InPass, FRHICommandListImmediate& RHICmdList)
					{
						SCOPE_CYCLE_COUNTER(STAT_SmartGI_VoxelizeDraw);
						RHICmdList.SetViewport(0.0f, 0.0f, 0.0f, VoxelRasterResolution.X, VoxelRasterResolution.Y, 1.0f);

						Renderer->Scene->UniformBuffers.SmartClipmapUniformBuffer.UpdateUniformBufferImmediate(RHICmdList, *ClipmapParameters);

						if (Direction == 0)
						{
							BindRenderTargets(ClipmapRenderInfo->VoxelizeMeshCommandPass.GetMeshDrawCommands(), DynamicPassParameters);
						}

						if (Scene->GPUScene.IsEnabled())
						{
							// Draw dynamic mesh elements
							ClipmapRenderInfo->VoxelizeMeshCommandPass.DispatchDraw(nullptr, RHICmdList, &(DynamicPassParameters->InstanceCullingDrawParams));
						}
					});
			}
			*/
		}

		ClipmapRenderInfo->bSceneUpdated = true;
	}
}

void FSmartVoxelRenderer::ModifyViewForVoxelize(int32 ClipmapIndex,FInstanceCullingManager& InstanceCullingManager) const
{
	FVoxelLightingState& VoxelState = const_cast<FVoxelLightingState&>(Scene->VoxelState);
	
	FViewInfo& View = *ClipmapRenderInfos[ClipmapIndex]->VoxelizePassView;

	const FSmartVoxelLightingClipmapState& Clipmap = VoxelState.VoxelLightingClipmapState[ClipmapIndex];

	FVector ViewOrigin = FVector(Clipmap.Center.X, Clipmap.Center.Y, Clipmap.Center.Z + Clipmap.Extent.Z);
	// Use top view matrix
	FMatrix ViewRotationMatrix = FMatrix(
					FPlane(1, 0, 0, 0),
					FPlane(0, -1, 0, 0),
					FPlane(0, 0, -1, 0),
					FPlane(0, 0, 0, 1));
	FMatrix ViewMatrix = FTranslationMatrix(-ViewOrigin) * ViewRotationMatrix;

	const float NearPlane = 0;
	const float FarPlane = 2.0f * Clipmap.Extent.Z;
	const float ZScale = 1.0f / (FarPlane - NearPlane);
	const float ZOffset = -NearPlane;
	FMatrix ProjectionMatrix = FReversedZOrthoMatrix(
				Clipmap.Extent.X,
				Clipmap.Extent.Y,
				ZScale,
				ZOffset
				);
	View.ViewMatrices.HackOverrideMatrixForSmartVoxelize(ViewMatrix, ProjectionMatrix);
	View.GPUSceneViewId = InstanceCullingManager.RegisterView(View);
}

void FSmartVoxelRenderer::GatherDynamicMeshElements(int32 ClipmapIndex, TArray<FPrimitiveSceneInfo*>& VisiblePrimitives, FInstanceCullingManager& InstanceCullingManager)
{
    FViewInfo* VoxelizePassView = ClipmapRenderInfos[ClipmapIndex]->VoxelizePassView;

    // Simple elements not supported in voxelize passes
	FSimpleElementCollector SimpleElementCollector;
#if UE_ENABLE_DEBUG_DRAWING
	FSimpleElementCollector DebugSimpleElementCollector;
#endif

    MeshCollector.ClearViewMeshArrays();
	MeshCollector.AddViewMeshArrays(
		VoxelizePassView,
        &ClipmapRenderInfos[ClipmapIndex]->DynamicMeshElements, 
		&SimpleElementCollector,
		&VoxelizePassView->DynamicPrimitiveCollector
	#if UE_ENABLE_DEBUG_DRAWING
		, &DebugSimpleElementCollector
	#endif
	);

    const uint32 PrimitiveCount = VisiblePrimitives.Num();
    TArray<const FSceneView*> ReusedViewsArray;
    ReusedViewsArray.Add(VoxelizePassView);

    for (uint32 PrimitiveIndex = 0; PrimitiveIndex < PrimitiveCount; ++PrimitiveIndex)
    {
        const FPrimitiveSceneInfo* PrimitiveSceneInfo = VisiblePrimitives[PrimitiveIndex];
        const FPrimitiveSceneProxy* PrimitiveSceneProxy = PrimitiveSceneInfo->Proxy;

        // Lookup the primitive's cached view relevance
        FPrimitiveViewRelevance ViewRelevance = VoxelizePassView->PrimitiveViewRelevanceMap[PrimitiveSceneInfo->GetIndex()];

        if (!ViewRelevance.bInitializedThisFrame)
        {
            // Compute the subject primitive's view relevance since it wasn't cached
            ViewRelevance = PrimitiveSceneInfo->Proxy->GetViewRelevance(VoxelizePassView);
        }

        if (ViewRelevance.bDynamicRelevance)
        {
            MeshCollector.SetPrimitive(PrimitiveSceneInfo->Proxy, PrimitiveSceneInfo->DefaultDynamicHitProxyId);

            PrimitiveSceneInfo->Proxy->GetDynamicMeshElements(ReusedViewsArray, SceneRenderer->ViewFamily, 0x1, MeshCollector);
        }
    }

    int32 NumDynamicSubjectMeshElements = MeshCollector.GetMeshElementCount(0);
    //MeshCollector.ProcessTasks();

    FMeshPassProcessor* MeshPassProcessor = FPassProcessorManager::CreateMeshPassProcessor(EShadingPath::Deferred, EMeshPass::SmartVoxelize, Scene->GetFeatureLevel(), Scene, VoxelizePassView->GetPrimarySceneView(), nullptr);
    TArray<const FStaticMeshBatch*, SceneRenderingAllocator> DynamicMeshCommandBuildRequests;
	TArray<EMeshDrawCommandCullingPayloadFlags, SceneRenderingAllocator> DynamicMeshCommandBuildFlags;

	TArray<int32, TInlineAllocator<2> > ViewIds;
	//ViewIds.Add(VoxelizePassView->GPUSceneViewId);
	ViewIds.Add(0);
	EInstanceCullingMode InstanceCullingMode = EInstanceCullingMode::Normal;

    FMeshCommandOneFrameArray VisibleMeshDrawCommands;
	
	int32 NumDynamicMeshCommandBuildRequestElements = 0;
	EInstanceCullingFlags CullingFlags = EInstanceCullingFlags::None;

	EMeshPass::Type PassType = EMeshPass::SmartVoxelize;
	FName PassName(GetMeshPassName(PassType));
	
    ClipmapRenderInfos[ClipmapIndex]->VoxelizeMeshCommandPass.DispatchPassSetup(
        SceneRenderer->Scene,
		*VoxelizePassView,
		FInstanceCullingContext(PassName, SceneRenderer->Scene->GetShaderPlatform(), &InstanceCullingManager, ViewIds, nullptr, InstanceCullingMode, CullingFlags),
		EMeshPass::Num,
		FExclusiveDepthStencil::DepthNop_StencilNop,
		MeshPassProcessor,
        ClipmapRenderInfos[ClipmapIndex]->DynamicMeshElements,
        nullptr,
        NumDynamicSubjectMeshElements,
        DynamicMeshCommandBuildRequests,
		DynamicMeshCommandBuildFlags,
		NumDynamicMeshCommandBuildRequestElements,
        VisibleMeshDrawCommands);
}

class FSmartVoxelizeMeshProcessor : public FMeshPassProcessor
{
public:

	FSmartVoxelizeMeshProcessor(const FScene* Scene, const FSceneView* InViewIfDynamicMeshCommand, const FMeshPassProcessorRenderState& InPassDrawRenderState, FMeshPassDrawListContext* InDrawListContext);

	virtual void AddMeshBatch(const FMeshBatch& RESTRICT MeshBatch, uint64 BatchElementMask, const FPrimitiveSceneProxy* RESTRICT PrimitiveSceneProxy, int32 StaticMeshId = -1) override final;

	FMeshPassProcessorRenderState PassDrawRenderState;
};


bool GetSmartVoxelizeShaders(
	const FMaterial& Material,
	FVertexFactoryType* VertexFactoryType,
	TShaderRef<FSmartVoxelizeVS>& VertexShader,
	TShaderRef<FSmartVoxelizePS>& PixelShader)
{
	FMaterialShaderTypes ShaderTypes;
	ShaderTypes.AddShaderType<FSmartVoxelizeVS>();
	ShaderTypes.AddShaderType<FSmartVoxelizePS>();

	FMaterialShaders Shaders;
	if (!Material.TryGetShaders(ShaderTypes, VertexFactoryType, Shaders))
	{
		return false;
	}

	Shaders.TryGetVertexShader(VertexShader);
	Shaders.TryGetPixelShader(PixelShader);

	return true;
}


void FSmartVoxelizeMeshProcessor::AddMeshBatch(const FMeshBatch& RESTRICT MeshBatch, uint64 BatchElementMask, const FPrimitiveSceneProxy* RESTRICT PrimitiveSceneProxy, int32 StaticMeshId)
{
	if (MeshBatch.bUseForMaterial
		&& SmartGI::ShouldRenderSmartGI()
		&& DoesPlatformSupportSmartGI(GetFeatureLevelShaderPlatform(FeatureLevel))
		&& (PrimitiveSceneProxy && PrimitiveSceneProxy->AffectsDynamicIndirectLighting()))
	{
		const FMaterialRenderProxy* MaterialRenderProxy = MeshBatch.MaterialRenderProxy;
		while (MaterialRenderProxy)
		{
			const FMaterial* Material = MaterialRenderProxy->GetMaterialNoFallback(FeatureLevel);
			if (Material)
			{
				auto TryAddMeshBatch = [this](const FMeshBatch& RESTRICT MeshBatch, uint64 BatchElementMask, const FPrimitiveSceneProxy* RESTRICT PrimitiveSceneProxy, int32 StaticMeshId, const FMaterialRenderProxy& MaterialRenderProxy, const FMaterial& Material) -> bool
				{
					const EBlendMode BlendMode = Material.GetBlendMode();
					const FMaterialShadingModelField ShadingModels = Material.GetShadingModels();
					if (ShadingModels.HasShadingModel(MSM_SingleLayerWater))
					{
						return false;
					}
					const bool bIsTranslucent = IsTranslucentBlendMode(BlendMode);
					const FMeshDrawingPolicyOverrideSettings OverrideSettings = ComputeMeshOverrideSettings(MeshBatch);
					const ERasterizerFillMode MeshFillMode = ComputeMeshFillMode(Material, OverrideSettings);
					//const ERasterizerCullMode MeshCullMode = ComputeMeshCullMode(MeshBatch, Material, OverrideSettings);
					const ERasterizerCullMode MeshCullMode = CM_None;
					
					if (!bIsTranslucent
						&& ShouldIncludeDomainInMeshPass(Material.GetMaterialDomain()))
					{
						const FVertexFactory* VertexFactory = MeshBatch.VertexFactory;
						FVertexFactoryType* VertexFactoryType = VertexFactory->GetType();
						constexpr bool bMultiViewCapture = false;

						TMeshProcessorShaders<FSmartVoxelizeVS, FSmartVoxelizePS> PassShaders;

						if (!GetSmartVoxelizeShaders(
							Material,
							VertexFactory->GetType(),
							PassShaders.VertexShader,
							PassShaders.PixelShader))
						{
							return false;
						}

						FMeshMaterialShaderElementData ShaderElementData;
						ShaderElementData.InitializeMeshMaterialData(ViewIfDynamicMeshCommand, PrimitiveSceneProxy, MeshBatch, StaticMeshId, false);

						const FMeshDrawCommandSortKey SortKey = CalculateMeshStaticSortKey(PassShaders.VertexShader, PassShaders.PixelShader);

						BuildMeshDrawCommands(
							MeshBatch,
							BatchElementMask,
							PrimitiveSceneProxy,
							MaterialRenderProxy,
							Material,
							PassDrawRenderState,
							PassShaders,
							MeshFillMode,
							MeshCullMode,
							SortKey,
							EMeshPassFeatures::Default,
							ShaderElementData);
					}

					return true;
				};

				if (TryAddMeshBatch(MeshBatch, BatchElementMask, PrimitiveSceneProxy, StaticMeshId, *MaterialRenderProxy, *Material))
				{
					break;
				}
			};

			MaterialRenderProxy = MaterialRenderProxy->GetFallback(FeatureLevel);
		}
	}
}

FSmartVoxelizeMeshProcessor::FSmartVoxelizeMeshProcessor(const FScene* Scene, const FSceneView* InViewIfDynamicMeshCommand, const FMeshPassProcessorRenderState& InPassDrawRenderState, FMeshPassDrawListContext* InDrawListContext)
	: FMeshPassProcessor(EMeshPass::SmartVoxelize, Scene, Scene->GetFeatureLevel(), InViewIfDynamicMeshCommand, InDrawListContext)
	, PassDrawRenderState(InPassDrawRenderState)
{}

FMeshPassProcessor* CreateSmartVoxelizePassProcessor(ERHIFeatureLevel::Type FeatureLevel, const FScene* Scene, const FSceneView* InViewIfDynamicMeshCommand, FMeshPassDrawListContext* InDrawListContext)
{
	FMeshPassProcessorRenderState PassState;

	// Write and test against depth
	PassState.SetDepthStencilState(TStaticDepthStencilState<false, CF_Always>::GetRHI());

	PassState.SetBlendState(TStaticBlendState<>::GetRHI());

	return new FSmartVoxelizeMeshProcessor(Scene, InViewIfDynamicMeshCommand, PassState, InDrawListContext);
}

FRegisterPassProcessorCreateFunction RegisterSmartVoxelizePass(&CreateSmartVoxelizePassProcessor, EShadingPath::Deferred, EMeshPass::SmartVoxelize, EMeshPassFlags::CachedMeshCommands);

FRegisterPassProcessorCreateFunction RegisterMobileSmartVoxelizePass(&CreateSmartVoxelizePassProcessor, EShadingPath::Mobile, EMeshPass::SmartVoxelize, EMeshPassFlags::CachedMeshCommands);

