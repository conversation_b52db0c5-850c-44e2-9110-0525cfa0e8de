#include "SmartHardwareRayTracingCommon.h"
#include "RendererPrivate.h"
#include "ScenePrivate.h"
#include "SceneUtils.h"
#include "PipelineStateCache.h"
#include "ShaderParameterStruct.h"
#include "ComponentRecreateRenderStateContext.h"
#include "SmartCommon.h"
#if H3D_SMARTGI_USEGI

static TAutoConsoleVariable<int32> CVarSmartUseHardwareRayTracing(
	TEXT("r.SmartGI.HardwareRayTracing"),
	0,
	TEXT("Uses Hardware Ray Tracing for Smart features, when available.\n")
	TEXT("Smart will fall back to Software Ray Tracing otherwise.\n")
	TEXT("Note: Hardware ray tracing has significant scene update costs for\n")
	TEXT("scenes with more than 100k instances."),
	FConsoleVariableDelegate::CreateLambda([](IConsoleVariable* InVariable)
		{
			// Recreate proxies so that FPrimitiveSceneProxy::UpdateVisibleInSmartScene() can pick up any changed state
			FGlobalComponentRecreateRenderStateContext Context;
		}),
	ECVF_Scalability | ECVF_RenderThreadSafe
);

static TAutoConsoleVariable<int32> CVarSmartUseHardwareRayTracingInline(
	TEXT("r.SmartGI.HardwareRayTracing.Inline"),
	0,
	TEXT("Uses Hardware Inline Ray Tracing for selected Smart passes, when available.\n"),
	ECVF_RenderThreadSafe | ECVF_Scalability
);

static TAutoConsoleVariable<float> CVarSmartHardwareRayTracingPullbackBias(
	TEXT("r.SmartGI.HardwareRayTracing.PullbackBias"),
	8.0,
	TEXT("Determines the pull-back bias when resuming a screen-trace ray (default = 8.0)"),
	ECVF_RenderThreadSafe
);

static TAutoConsoleVariable<int32> CVarSmartHardwareRayTracingMaxIterations(
	TEXT("r.SmartGI.HardwareRayTracing.MaxIterations"),
	8192,
	TEXT("Limit number of ray tracing traversal iterations on supported platfoms.\n"
		"Incomplete misses will be treated as hitting a black surface (can cause overocculsion).\n"
		"Incomplete hits will be treated as a hit (can cause leaking)."),
	ECVF_RenderThreadSafe
);

TAutoConsoleVariable<float> CVarSmartHardwareRayTracingMinTraceDistanceToSampleSurfaceCache(
	TEXT("r.SmartGI.HardwareRayTracing.MinTraceDistanceToSampleSurfaceCache"),
	10.0f,
	TEXT("Ray hit distance from which we can start sampling surface cache in order to fix feedback loop where surface cache texel hits itself and propagates lighting."),
	ECVF_Scalability | ECVF_RenderThreadSafe
);

static TAutoConsoleVariable<int32> CVarSmartThreadGroupSize32(
	TEXT("r.SmartGI.ThreadGroupSize32"),
	1,
	TEXT("Whether to prefer dispatches in groups of 32 threads on HW which supports it (instead of standard 64)."),
	ECVF_Scalability | ECVF_RenderThreadSafe
);

bool SmartHardwareRayTracing::IsInlineSupported()
{
	return GRHISupportsInlineRayTracing;
}

bool SmartHardwareRayTracing::IsRayGenSupported()
{
	// Indirect RayGen dispatch is required for Smart RayGen shaders
	return GRHISupportsRayTracingShaders && GRHISupportsRayTracingDispatchIndirect;
}

bool SmartHardwareRayTracing::UseHardwareRayTracing(const FSceneViewFamily& ViewFamily)
{
#if RHI_RAYTRACING

	return IsRayTracingEnabled()
		&& SmartHardwareRayTracing::IsRayGenSupported()
		&& CVarSmartUseHardwareRayTracing.GetValueOnAnyThread() != 0
		// Smart HWRT does not support split screen yet, but stereo views can be allowed
		&& (ViewFamily.Views.Num() == 1 || (ViewFamily.Views.Num() == 2 && IStereoRendering::IsStereoEyeView(*ViewFamily.Views[0])));
#else
	return false;
#endif
}


SMARTCORE_API bool SmartHardwareRayTracing::UseHardwareInlineRayTracing(const FSceneViewFamily& ViewFamily)
{
#if RHI_RAYTRACING
	if (SmartHardwareRayTracing::UseHardwareRayTracing(ViewFamily)
		&& SmartHardwareRayTracing::IsInlineSupported()
		// Can't disable inline tracing if RayGen isn't supported
		&& (CVarSmartUseHardwareRayTracingInline.GetValueOnRenderThread() != 0 || !SmartHardwareRayTracing::IsRayGenSupported()))
	{
		return true;
	}
#endif

	return false;
}

#if RHI_RAYTRACING

FSmartHardwareRayTracingShaderBase::FSmartHardwareRayTracingShaderBase() = default;
FSmartHardwareRayTracingShaderBase::FSmartHardwareRayTracingShaderBase(const ShaderMetaType::CompiledShaderInitializerType& Initializer)
	: FGlobalShader(Initializer)
{
}

void FSmartHardwareRayTracingShaderBase::ModifyCompilationEnvironment(const FGlobalShaderPermutationParameters& Parameters, Smart::ERayTracingShaderDispatchType ShaderDispatchType, FShaderCompilerEnvironment& OutEnvironment)
{
	FGlobalShader::ModifyCompilationEnvironment(Parameters, OutEnvironment);
	OutEnvironment.SetDefine(TEXT("SMART_HARDWARE_RAYTRACING"), 1);
	// GPU Scene definitions
	OutEnvironment.SetDefine(TEXT("VF_SUPPORTS_PRIMITIVE_SCENE_DATA"), 1);
	// Inline
	const bool bInlineRayTracing = ShaderDispatchType == Smart::ERayTracingShaderDispatchType::Inline;
	if (bInlineRayTracing)
	{
		OutEnvironment.SetDefine(TEXT("SMART_HARDWARE_INLINE_RAYTRACING"), 1);
		OutEnvironment.CompilerFlags.Add(CFLAG_InlineRayTracing);
		OutEnvironment.CompilerFlags.Add(CFLAG_HLSL2021);
	}
}

void FSmartHardwareRayTracingShaderBase::ModifyCompilationEnvironmentInternal(Smart::ERayTracingShaderDispatchType ShaderDispatchType, Smart::ERayTracingShaderDispatchSize Size, bool UseThreadGroupSize64, FShaderCompilerEnvironment& OutEnvironment)
{
	if (DispatchSize == Smart::ERayTracingShaderDispatchSize::DispatchSize1D)
	{
		OutEnvironment.SetDefine(TEXT("UE_RAY_TRACING_DISPATCH_1D"), 1);
	}

	const bool bInlineRayTracing = ShaderDispatchType == Smart::ERayTracingShaderDispatchType::Inline;
	if (bInlineRayTracing && !UseThreadGroupSize64)
	{
		OutEnvironment.CompilerFlags.Add(CFLAG_Wave32);
	}
}

FIntPoint FSmartHardwareRayTracingShaderBase::GetThreadGroupSizeInternal(Smart::ERayTracingShaderDispatchType ShaderDispatchType, Smart::ERayTracingShaderDispatchSize ShaderDispatchSize, bool UseThreadGroupSize64)
{
	// Current inline ray tracing implementation requires 1:1 mapping between thread groups and waves.
	const bool bInlineRayTracing = ShaderDispatchType == Smart::ERayTracingShaderDispatchType::Inline;
	if (bInlineRayTracing)
	{
		switch (ShaderDispatchSize)
		{
		case Smart::ERayTracingShaderDispatchSize::DispatchSize2D: return UseThreadGroupSize64 ? FIntPoint(8, 8) : FIntPoint(8, 4);
		case Smart::ERayTracingShaderDispatchSize::DispatchSize1D: return UseThreadGroupSize64 ? FIntPoint(64, 1) : FIntPoint(32, 1);
		default:
			checkNoEntry();
		}
	}

	return FIntPoint(1, 1);
}

bool FSmartHardwareRayTracingShaderBase::ShouldCompilePermutation(const FGlobalShaderPermutationParameters& Parameters, Smart::ERayTracingShaderDispatchType ShaderDispatchType)
{
	const bool bInlineRayTracing = ShaderDispatchType == Smart::ERayTracingShaderDispatchType::Inline;
	if (bInlineRayTracing)
	{
		return IsRayTracingEnabledForProject(Parameters.Platform) && DoesPlatformSupportSmartGI(Parameters.Platform) && RHISupportsRayTracing(Parameters.Platform) && RHISupportsInlineRayTracing(Parameters.Platform);
	}
	else
	{
		return ShouldCompileRayTracingShadersForProject(Parameters.Platform) && DoesPlatformSupportSmartGI(Parameters.Platform);
	}
}

bool FSmartHardwareRayTracingShaderBase::UseThreadGroupSize64(EShaderPlatform ShaderPlatform)
{
	return RHISupportsWaveSize64(ShaderPlatform) && GRHISupportsWaveOperations && GRHIMinimumWaveSize <= 32 && CVarSmartThreadGroupSize32.GetValueOnRenderThread() != 0;
}

SMARTCORE_API void SetSmartHardwareRayTracingSharedParameters(
	FRDGBuilder& GraphBuilder,
	const FSceneTextureParameters& SceneTextures,
	const FViewInfo& View,
	const FSmartTracingParameters& TracingParameters,
	const FPathTracingSkylight& SkylightParameters,
	const FSkyLightData& SkyLightData,
	FSmartHardwareRayTracingShaderBase::FSharedParameters* SharedParameters)
{
	SharedParameters->SceneTextures = SceneTextures;
	SharedParameters->SceneTexturesStruct = View.GetSceneTextures().UniformBuffer;
	SharedParameters->Substrate = Substrate::BindSubstrateGlobalUniformParameters(View);

	//SharedParameters->ViewUniformBuffer = View.ViewUniformBuffer;
	checkf(View.HasRayTracingScene(), TEXT("TLAS does not exist. Verify that the current pass is represented in Smart::AnySmartHardwareRayTracingPassEnabled()."));
	SharedParameters->TLAS = View.GetRayTracingSceneLayerViewChecked(ERayTracingSceneLayer::Base);

	// Lighting data
	SharedParameters->LightGridParameters = View.RayTracingLightGridUniformBuffer;
	//SharedParameters->ReflectionCapture = View.ReflectionCaptureUniformBuffer;
	SharedParameters->Forward = View.ForwardLightingResources.ForwardLightUniformBuffer;
	// Use surface cache, instead
	SharedParameters->TracingParameters = TracingParameters;

	// Skylight
	SharedParameters->SkyLightData = CreateUniformBufferImmediate(SkyLightData, EUniformBufferUsage::UniformBuffer_SingleDraw);
	SharedParameters->SkyLightParameters = SkylightParameters;

	// Inline
	SharedParameters->HitGroupData = View.GetPrimaryView()->LumenHardwareRayTracingHitDataBuffer ? GraphBuilder.CreateSRV(View.GetPrimaryView()->LumenHardwareRayTracingHitDataBuffer) : nullptr;
	SharedParameters->LumenHardwareRayTracingUniformBuffer = View.GetPrimaryView()->LumenHardwareRayTracingUniformBuffer ? View.GetPrimaryView()->LumenHardwareRayTracingUniformBuffer : nullptr;
	checkf(View.RayTracingSceneInitTask == nullptr, TEXT("RayTracingSceneInitTask must be completed before creating SRV for RayTracingSceneMetadata."));
	SharedParameters->RayTracingSceneMetadata = View.GetRayTracingSceneChecked()->GetOrCreateMetadataBufferSRV(GraphBuilder.RHICmdList);
}

#endif // RHI_RAYTRACING
#endif

SMARTCORE_API FSmartHZBScreenTraceParameters SetupHZBScreenTraceParameters(
	FRDGBuilder& GraphBuilder,
	const FViewInfo& View,
	FRDGTextureRef CurrentSceneColor,
	FRDGTextureRef SceneDepthTexture,
	FIntPoint FullResBufferExtent)
{
	FRDGTextureRef InputColor = CurrentSceneColor;
	FIntPoint ViewportOffset = View.ViewRect.Min;
	FIntPoint ViewportExtent = View.ViewRect.Size();
	FIntPoint PrevColorBufferSize = FullResBufferExtent;

	if (View.PrevViewInfo.CustomSSRInput.IsValid())
	{
		InputColor = GraphBuilder.RegisterExternalTexture(View.PrevViewInfo.CustomSSRInput.RT[0]);
		ViewportOffset = View.PrevViewInfo.CustomSSRInput.ViewportRect.Min;
		ViewportExtent = View.PrevViewInfo.CustomSSRInput.ViewportRect.Size();
		PrevColorBufferSize = InputColor->Desc.Extent;
	}
	else if (View.PrevViewInfo.TSRHistory.IsValid())
	{
		InputColor = GraphBuilder.RegisterExternalTexture(View.PrevViewInfo.TSRHistory.ColorArray);
		ViewportOffset = View.PrevViewInfo.TSRHistory.OutputViewportRect.Min;
		ViewportExtent = View.PrevViewInfo.TSRHistory.OutputViewportRect.Size();
		PrevColorBufferSize = InputColor->Desc.Extent;
	}
	else if (View.PrevViewInfo.TemporalAAHistory.IsValid())
	{
		InputColor = GraphBuilder.RegisterExternalTexture(View.PrevViewInfo.TemporalAAHistory.RT[0]);
		ViewportOffset = View.PrevViewInfo.TemporalAAHistory.ViewportRect.Min;
		ViewportExtent = View.PrevViewInfo.TemporalAAHistory.ViewportRect.Size();
		PrevColorBufferSize = View.PrevViewInfo.TemporalAAHistory.ReferenceBufferSize;
	}
	else if (View.PrevViewInfo.ScreenSpaceRayTracingInput.IsValid())
	{
		InputColor = GraphBuilder.RegisterExternalTexture(View.PrevViewInfo.ScreenSpaceRayTracingInput);
		ViewportOffset = View.PrevViewInfo.ViewRect.Min;
		ViewportExtent = View.PrevViewInfo.ViewRect.Size();
		PrevColorBufferSize = InputColor->Desc.Extent;
	}

	FSmartHZBScreenTraceParameters Parameters;

	{
		const FVector2D HZBUvFactor(
			float(View.ViewRect.Width()) / float(2 * View.HZBMipmap0Size.X),
			float(View.ViewRect.Height()) / float(2 * View.HZBMipmap0Size.Y));
		Parameters.HZBUvFactorAndInvFactor = FVector4f(
			HZBUvFactor.X,
			HZBUvFactor.Y,
			1.0f / HZBUvFactor.X,
			1.0f / HZBUvFactor.Y);

		const FVector4f ScreenPositionScaleBias = GetScreenPositionScaleBias(FullResBufferExtent, View.ViewRect);
		const FVector2f HZBUVToScreenUVScale = FVector2f(1.0f / HZBUvFactor.X, 1.0f / HZBUvFactor.Y) * FVector2f(2.0f, -2.0f) * FVector2f(ScreenPositionScaleBias.X, ScreenPositionScaleBias.Y);
		const FVector2f HZBUVToScreenUVBias = FVector2f(-1.0f, 1.0f) * FVector2f(ScreenPositionScaleBias.X, ScreenPositionScaleBias.Y) + FVector2f(ScreenPositionScaleBias.W, ScreenPositionScaleBias.Z);
		Parameters.HZBUVToScreenUVScaleBias = FVector4f(HZBUVToScreenUVScale, HZBUVToScreenUVBias);
	}

	{
		const float InvPrevColorBufferSizeX = 1.0f / PrevColorBufferSize.X;
		const float InvPrevColorBufferSizeY = 1.0f / PrevColorBufferSize.Y;

		Parameters.PrevScreenPositionScaleBias = FVector4f(
			ViewportExtent.X * 0.5f * InvPrevColorBufferSizeX,
			-ViewportExtent.Y * 0.5f * InvPrevColorBufferSizeY,
			(ViewportExtent.X * 0.5f + ViewportOffset.X) * InvPrevColorBufferSizeX,
			(ViewportExtent.Y * 0.5f + ViewportOffset.Y) * InvPrevColorBufferSizeY);

		FIntPoint ViewportOffsetForDepth = View.PrevViewInfo.ViewRect.Min;
		FIntPoint ViewportExtentForDepth = View.PrevViewInfo.ViewRect.Size();

		const float InvBufferSizeX = 1.0f / FullResBufferExtent.X;
		const float InvBufferSizeY = 1.0f / FullResBufferExtent.Y;

		Parameters.PrevScreenPositionScaleBiasForDepth = FVector4f(
			ViewportExtentForDepth.X * 0.5f * InvBufferSizeX,
			-ViewportExtentForDepth.Y * 0.5f * InvBufferSizeY,
			(ViewportExtentForDepth.X * 0.5f + ViewportOffsetForDepth.X) * InvBufferSizeX,
			(ViewportExtentForDepth.Y * 0.5f + ViewportOffsetForDepth.Y) * InvBufferSizeY);
	}

	FScreenPassTextureViewportParameters PrevSceneColorParameters = GetScreenPassTextureViewportParameters(FScreenPassTextureViewport(InputColor, FIntRect(ViewportOffset, ViewportOffset + ViewportExtent)));
	Parameters.PrevSceneColorBilinearUVMin = PrevSceneColorParameters.UVViewportBilinearMin;
	Parameters.PrevSceneColorBilinearUVMax = PrevSceneColorParameters.UVViewportBilinearMax;

	Parameters.PrevSceneColorPreExposureCorrection = InputColor != CurrentSceneColor ? View.PreExposure / View.PrevViewInfo.SceneColorPreExposure : 1.0f;

	Parameters.PrevSceneColorTexture = InputColor;
	Parameters.HistorySceneDepth = View.ViewState->SmartGI.DepthHistoryRT ? GraphBuilder.RegisterExternalTexture(View.ViewState->SmartGI.DepthHistoryRT) : SceneDepthTexture;

	SMARTCORE_API extern int32 GSmartUseHierarchicalScreenTraces;
	if (GSmartUseHierarchicalScreenTraces)
	{
		checkf(View.ClosestHZB, TEXT("Smart screen tracing: ClosestHZB was not setup, should have been setup by FDeferredShadingSceneRenderer::RenderHzb"));
		Parameters.ClosestHZBTexture = View.ClosestHZB;
		Parameters.HZBBaseTexelSize = FVector2f(1.0f / View.ClosestHZB->Desc.Extent.X, 1.0f / View.ClosestHZB->Desc.Extent.Y);
	}

	return Parameters;
}