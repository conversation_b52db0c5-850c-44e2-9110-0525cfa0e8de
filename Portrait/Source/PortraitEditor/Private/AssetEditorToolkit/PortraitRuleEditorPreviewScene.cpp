#include "PortraitRuleEditorPreviewScene.h"

#include "PortraitEditorSettings.h"
#include "Asset/PortraitRule.h"
#include "Engine/LevelStreamingDynamic.h"

FPortraitRuleEditorPreviewScene::FPortraitRuleEditorPreviewScene(ConstructionValues CVS)
	:FPreviewScene(CVS)
{
	if (GEditor != nullptr)
	{
		PreviewWorld->ChangeFeatureLevel(GEditor->DefaultWorldFeatureLevel);
	}

}

FPortraitRuleEditorPreviewScene::~FPortraitRuleEditorPreviewScene()
{
	RemoveCurrentLevel();
}

void FPortraitRuleEditorPreviewScene::Tick(float DeltaTime)
{
	UpdateCaptureContents();

	if (FaceMeshComponent != nullptr && PortraitRule.IsValid())
	{
		for (auto MorphTarget : PortraitRule.Get()->PreviewMorphTargetNames)
		{
			FaceMeshComponent->SetMorphTarget(MorphTarget.MorphTargetName, MorphTarget.Value);
		}
		auto OwnerActor = FaceMeshComponent->GetOwner();
		if (IsValid(OwnerActor))
		{
			OwnerActor->SetActorTransform(UPortraitEditorSettings::Get()->PreviewCharacterTransform);
		}
	}
}

TStatId FPortraitRuleEditorPreviewScene::GetStatId() const
{
	return TStatId();
}

FLinearColor FPortraitRuleEditorPreviewScene::GetBackgroundColor() const
{
	// return FPreviewScene::GetBackgroundColor();
	return FLinearColor(0.0f, 0.0f, 0.0f, 1.0f);
}

void FPortraitRuleEditorPreviewScene::ChangePreviewScene(FSoftObjectPath WorldPath)
{
	FScopedSlowTask ExploreFoldersTask(1, INVTEXT("Loading PreviewMap ... "));
	ExploreFoldersTask.MakeDialog();

	RemoveCurrentLevel();
	
	TSoftObjectPtr<UWorld> NewWorld = TSoftObjectPtr<UWorld>(WorldPath);

	bool bSuccess;
	const TObjectPtr<ULevelStreamingDynamic> NewLevel = ULevelStreamingDynamic::LoadLevelInstanceBySoftObjectPtr(PreviewWorld,
		NewWorld,
		FTransform::Identity,
		bSuccess,
		TEXT(""),
		nullptr,
		true);
	
	if (bSuccess)
	{
		CurrentLevel = NewLevel;
		PreviewWorld->FlushLevelStreaming();
	}
	
	ExploreFoldersTask.EnterProgressFrame();
}

void FPortraitRuleEditorPreviewScene::RemoveCurrentLevel()
{
	if (CurrentLevel != nullptr)
	{
		CurrentLevel->SetIsRequestingUnloadAndRemoval(true);
		PreviewWorld->FlushLevelStreaming();
		CurrentLevel = nullptr;
	}

}

void FPortraitRuleEditorPreviewScene::SetFaceMeshComponent(USkeletalMeshComponent* MeshComp)
{
	FaceMeshComponent = MeshComp;
}

USkeletalMeshComponent* FPortraitRuleEditorPreviewScene::GetFaceMeshComponent() const
{
	return FaceMeshComponent.Get();
}

void FPortraitRuleEditorPreviewScene::SetPortraitRule(UPortraitRule* InRule)
{
	PortraitRule = InRule;
}
